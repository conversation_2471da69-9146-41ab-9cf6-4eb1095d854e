version '1.0.2-SNAPSHOT'
jar.enabled = true
bootJar.enabled = false
java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}
dependencies {
    //公司通用工具包
    api ('com.ddmc:utils:1.1.1-SNAPSHOT') {
        exclude group: 'com.alibaba', module: 'druid'
    }
    //网关
    api('com.ddmc:gateway-starter:1.1.1-SNAPSHOT') {
        exclude group: 'com.ddmc', module: 'core'
        exclude group: 'com.alibab', module: 'fastjson'
    }
    //Http工具包
    api group: 'org.apache.httpcomponents', name: 'httpclient'
    // https://mvnrepository.com/artifact/com.google.guava/guava
    api group: 'com.google.guava', name: 'guava'

    // https://mvnrepository.com/artifact/org.apache.skywalking/apm-toolkit-logback-1.x
    api group: 'org.apache.skywalking', name: 'apm-toolkit-logback-1.x', version: '6.5.0'

    // csv文件导出
    api 'com.opencsv:opencsv:5.0'
    api('com.google.code.gson:gson')
    //分页插件 page helper
    api('com.github.pagehelper:pagehelper-spring-boot-starter:1.2.10') {
        exclude group: 'org.mybatis'
    }
    api('com.fasterxml.jackson.core:jackson-databind')
    api ("com.fasterxml.jackson.datatype:jackson-datatype-jdk8")
    api ("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")

    api("com.fasterxml.jackson.dataformat:jackson-dataformat-xml")
    api('javax.ws.rs:jsr311-api')
    api('org.apache.commons:commons-lang3')
    api('io.swagger:swagger-annotations:1.5.9')

    //pinyin
    api group: 'com.belerweb', name: 'pinyin4j', version: '2.5.1'


}