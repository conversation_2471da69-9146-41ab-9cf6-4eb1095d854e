package com.ddmc.ims.common.errorcode;


import lombok.Getter;

/**
 * 网关通用的异常代码.
 */
@Getter
public enum GatewayErrorCode implements ErrorCode {

    /**
     * 认证失败, 用户身份信息缺失或不合法.
     */
    BAD_AUTHENTICATION(1, "BadAuthentication"),

    /**
     * 鉴权失败, 用户没有访问权限.
     */
    ACCESS_DENIED(2, "AccessDenied"),
    ;

    private static final int DOMAIN_CODE = 2;

    private final int code;

    private final String msg;

    GatewayErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    @Override
    public int getDomainCode() {
        return DOMAIN_CODE;
    }

}
