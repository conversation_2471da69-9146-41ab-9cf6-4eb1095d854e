package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;
import java.util.Objects;

public enum InventoryStatusEnum implements IntegerEnumInterface {

    /**
     * 库存状态:0-未知，1-可用，2-不可用
     */
    UN_KNOW(0, "未知"),

    AVAILABLE(1, "可用"),

    NOT_AVAILABLE(2, "不可用"),

    ;

    private final Integer code;

    private final String desc;

    InventoryStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static InventoryStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (InventoryStatusEnum s : InventoryStatusEnum.values()) {
            if (s.getCode().equals(code)) {
                return s;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
