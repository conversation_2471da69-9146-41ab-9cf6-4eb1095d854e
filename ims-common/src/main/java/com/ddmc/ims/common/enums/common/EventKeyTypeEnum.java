package com.ddmc.ims.common.enums.common;

import com.ddmc.ims.common.enums.StringEnumInterface;

public enum EventKeyTypeEnum implements StringEnumInterface {
    SNAPSHOT_INVENTORY_DIFF("SNAPSHOT_INVENTORY_DIFF", "差异库存处理"),
    SEND_ASYNC_CONFIRM("SEND_ASYNC_CONFIRM", "发送异步确认事件"),
    FMS_COMPARE_NOTIFY("FMS_COMPARE_NOTIFY", "财务比对通知"),
    TRANSFER_OUT_CHANGE_NOTIFY("TRANSFER_OUT_CHANGE_NOTIFY", "调拨出库变更通知");

    private final String code;

    private final String desc;

    EventKeyTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
