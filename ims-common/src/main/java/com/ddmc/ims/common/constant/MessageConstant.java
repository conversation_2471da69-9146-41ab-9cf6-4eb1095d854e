package com.ddmc.ims.common.constant;

public class MessageConstant {

    private MessageConstant(){
    }

    /**
     * 仓库信息数据同步
     */
    public static final String WAREHOUSE_INFO_INPUT = "wmsServiceStoreInput";

    /**
     * 仓库覆盖门店同步
     */
    public static final String WAREHOUSE_STATION_CHANGE_INPUT = "warehouseStationChangeInput";

    /**
     * 批次创建同步
     */
    public static final String LOT_CREATE_INPUT = "lotCreateInput";

    /**
     * requene 批次信息队列
     */
    public static final String IMS_LOT_INFO_OUTPUT = "imsLotInfoOutput";


    /**
     * requene 批次信息队列
     */
    public static final String IMS_LOT_INFO_INPUT = "imsLotInfoInput";

    /**
     * 仓库信息数据同步推送 - RocketMQ
     */
    public static final String STORE_INFO_CHANGE_OUTPUT = "storeInfoChangeOutput";

    /**
     * 推送待启用仓库消息
     */
    public static final String STORE_WAIT_ENABLE = "scmBaseServiceStoreWaitEnable";

    /**
     * 推送待启用仓库消息 - RocketMQ
     */
    public static final String STORE_WAIT_ENABLE_OUTPUT = "storeWaitEnableOutput";

    /**
     * 仓库覆盖门店通知
     */
    public static final String WAREHOUSE_STATION_CHANGE = "warehouse.station.change";

    /**
     * 仓库覆盖门店通知 - RocketMQ
     */
    public static final String WAREHOUSE_STATION_CHANGE_OUTPUT = "warehouseStationChangeOutput";

    /**
     * 存货门店信息同步
     */
    public static final String STORE_SKU_INFO = "storeSkuInfo";

    /**
     * 存货门店信息同步 - RocketMQ
     */
    public static final String STORE_SKU_INFO_INPUT = "storeSkuInfoInput";

    /**
     * 商品可采信息同步
     */
    public static final String SKU_SUPPLY_CHAIN_INPUT = "skuSupplyChainInput";

    /**
     * 商品可采信息同步 - RocketMQ
     */
    public static final String SUPPLY_CHAIN_INPUT = "supplyChainInput";

    /**
     * 制造关系同步
     */
    public static final String PRODUCTION_RELATION_INPUT = "productionRelationInput";

    /**
     * 商品来源数据同步-推送
     */
    public static final String SKU_SOURCE_OUTPUT = "skuSourceOutput";

    /**
     * 品牌变更事件
     */
    public static final String BRAND_CHANGE_OUTPUT = "brandChangeOutput";

    /**
     * 大区可采信息同步 - RocketMQ
     */
    public static final String REGION_SKU_PRODUCER_INFO_INPUT = "regionSkuProducerInfoInput";

    /**
     * 采购分类同步 - RocketMQ
     */
    public static final String PURCHASE_CATEGORY_INPUT = "purchaseCategoryInput";

    /**
     * rocket 鲜食早上好门店关系商品同步
     */
    public static final String BREAKFAST_STORE_SKU_SOURCE_INPUT = "breakfastStoreSkuSourceInput";

    /**
     * 商品来源数据计算
     */
    public static final String SKU_SOURCE_CALCULATE_OUTPUT = "skuSourceCalculateOutput";

    /**
     * 商品来源数据计算
     */
    public static final String SKU_SOURCE_CALCULATE_INPUT = "skuSourceCalculateInput";

    /**
     * 站点变更消息信息同步 - RocketMQ
     */
    public static final String STATION_TOPIC_CHANGE_MESSAGE_INPUT = "stationTopicChangeMessageInput";

    /**
     * common告警 - RocketMQ
     */
    public static final String SCM_ALERT_MSG_OUTPUT = "scmAlertMsgOutput";

    /**
     * 参数配置中心消息下发
     */
    public static final String SCM_PARAM_CHANGE_INPUT = "scmParamChangeInput";

    public static final String IMS_WITH_GSS_INVENTORY_CHECK_OUTPUT = "imsWithGssInventoryCheckOutput";

    public static final String IMS_WITH_GSS_INVENTORY_CHECK_INPUT = "imsWithGssInventoryCheckInput";

    /**
     * 异步确认消息
     */
    public static final String IMS_CONFIRM_INPUT = "imsConfirmInput";

    /**
     * 发送异步确认消息
     */
    public static final String IMS_CONFIRM_OUTPUT = "imsConfirmOutput";
}
