package com.ddmc.ims.common;


import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithSku;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class LogicInventoryLocationFreeAndLockDto {


    private Long warehouseId;

    private Long cargoOwnerId;

    private String logicInventoryLocationCode;

    private Long skuId;

    private BigDecimal freeQty;
    private BigDecimal lockQty;

    public LogicInventoryLocationFreeAndLockDto(Long warehouseId, Long cargoOwnerId,
        String logicInventoryLocationCode, Long skuId, BigDecimal freeQty, BigDecimal lockQty) {
        this.warehouseId = warehouseId;
        this.cargoOwnerId = cargoOwnerId;
        this.logicInventoryLocationCode = logicInventoryLocationCode;
        this.skuId = skuId;
        this.freeQty = freeQty;
        this.lockQty = lockQty;
    }

    public LogicInventoryLocationWithSku locationWithSku() {
        return new LogicInventoryLocationWithSku(
            new LogicInventoryLocation(warehouseId, cargoOwnerId, logicInventoryLocationCode), skuId);
    }


}
