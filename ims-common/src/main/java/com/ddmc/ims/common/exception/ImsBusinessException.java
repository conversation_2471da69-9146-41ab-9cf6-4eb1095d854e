package com.ddmc.ims.common.exception;

import com.ddmc.core.view.compat.CodeMsg;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.errorcode.ErrorCode;

/**
 * 系统业务异常
 **/
public class ImsBusinessException extends RuntimeException {

    private final transient CodeMsg codeMsg;

    public ImsBusinessException(String errorMsg) {
        super(errorMsg);
        this.codeMsg = new CodeMsg(CommonErrorCode.ILLEGAL_PARAMETER.getErrorCode(), errorMsg);
    }

    public ImsBusinessException(String errorCode, Throwable e) {
        super(errorCode, e);
        this.codeMsg = null;
    }

    public ImsBusinessException(Integer errorCode, String errorMsg) {
        super(errorMsg);
        this.codeMsg = new CodeMsg(errorCode, errorMsg);
    }

    public ImsBusinessException(Throwable e) {
        super(e);
        this.codeMsg = null;
    }

    public ImsBusinessException(CodeMsg codeMsg) {
        super(codeMsg.getMsg());
        this.codeMsg = codeMsg;
    }

    public ImsBusinessException(ErrorCode errorCode) {
        this(new CodeMsg(errorCode.getErrorCode(), errorCode.getMsg()));
    }

    public ImsBusinessException(ErrorCode errorCode, Object... args) {
        this(new CodeMsg(errorCode.getErrorCode(), String.format(errorCode.getMsg(), args)));
    }

    public CodeMsg getCodeMsg() {
        return codeMsg;
    }
}
