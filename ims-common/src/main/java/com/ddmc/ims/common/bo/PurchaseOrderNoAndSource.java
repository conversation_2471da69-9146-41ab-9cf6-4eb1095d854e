package com.ddmc.ims.common.bo;

import java.util.Objects;
import lombok.Getter;

/**
 * 调拨来源和单号,不可变类，请勿添加set方法
 */
@Getter
public class PurchaseOrderNoAndSource {

    private final String orderSource;

    private final String orderNo;


    public PurchaseOrderNoAndSource(String orderSource, String orderNo) {
        this.orderSource = orderSource;
        this.orderNo = orderNo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PurchaseOrderNoAndSource)) {
            return false;
        }
        PurchaseOrderNoAndSource that = (PurchaseOrderNoAndSource) o;
        return Objects.equals(orderSource, that.orderSource) && Objects.equals(orderNo, that.orderNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderSource, orderNo);
    }
}
