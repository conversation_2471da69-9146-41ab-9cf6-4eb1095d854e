package com.ddmc.ims.common.util;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class PatternUtils {

    private PatternUtils() {
    }

    private static final Pattern THREE_CONSECUTIVE_HYPHENS_PATTERN = Pattern.compile("^(?:[^-]*-){3}[^-]*$");


    /**
     * 判断是否为四要素
     *
     System.out.println(hasExactlyThreeHyphens("ASN0000123")); false
     System.out.println(hasExactlyThreeHyphens("D0123123124sdasd")); false
     System.out.println(hasExactlyThreeHyphens("123132-434-1313123-1")); ture
     System.out.println(hasExactlyThreeHyphens("1-17-20230819-1")); true
     System.out.println(hasExactlyThreeHyphens("abc-def-ghi-j-k-l-mno")); false
     System.out.println(hasExactlyThreeHyphens("Only-one-hyphen")); false
     System.out.println(hasExactlyThreeHyphens("Only-two-hyphen-s")); false

     * @param inputString inputString
     * @return result
     */
    public static boolean isExactlyThreeHyphens(String inputString) {
        return THREE_CONSECUTIVE_HYPHENS_PATTERN.matcher(inputString).matches();
    }

}
