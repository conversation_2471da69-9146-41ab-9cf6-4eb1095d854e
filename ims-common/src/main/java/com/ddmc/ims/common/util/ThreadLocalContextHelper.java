package com.ddmc.ims.common.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Slf4j
public class ThreadLocalContextHelper {

    private ThreadLocalContextHelper() {
    }

    private static final Set<ThreadLocalClearFunction> clearFunSet = Collections.synchronizedSet(new HashSet<>());

    /**
     * @param fun 注册需要在线程结束时清除内容的方法
     */
    public static void registerClearFunction(ThreadLocalClearFunction fun) {
        if (log.isDebugEnabled()) {
            log.debug("ThreadLocalContextHelper registerClearFunction, class={}", fun.getClass());
        }
        clearFunSet.add(fun);
    }

    public static void clearAll() {
        if (log.isDebugEnabled()) {
            log.debug("ThreadLocalContextHelper clearAll size={}", clearFunSet.size());
        }
        clearFunSet.forEach(context -> {
            try {
                if (log.isDebugEnabled()) {
                    log.debug("ThreadLocalContextHelper clearAll context={}", context.getClass());
                }
                context.clear();
            } catch (Exception e) {
                log.warn("clear threadLocal context error", e);
            }
        });
    }
}
