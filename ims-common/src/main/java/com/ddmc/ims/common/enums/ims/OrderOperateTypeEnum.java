package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;
import java.util.Objects;

/**
 * 库存作业类型
 *
 * <AUTHOR>
 */
public enum OrderOperateTypeEnum implements IntegerEnumInterface {

    APPLY_OUT_OF_STOCK(0, "申请出库"),
    MODIFY_OUT_QUANTITY(1, "修改计划出库数量"),
    CANCEL_OUT_APPLY(2, "取消出库申请"),
    OUT_OF_STOCK(3, "出库"),
    FINISH_OUT_OF_STOCK(4, "完成出库"),
    APPLY_IN_OF_STOCK(5, "申请入库"),
    CANCEL_IN_STOCK_APPLY(6, "取消入库申请"),
    MODIFY_ARRIVE_TIME(7, "修改到货时间"),
    IN_STOCK(8, "入库"),
    FINISH_IN_OF_STOCK(9, "完成入库"),
    ADJUSTMENT(10, "执行调整"),
    FINISH_PRODUCTION(11, "完成制造单"),
    MODIFY_EXPECT_OUT_TIME(12, "修改计划出库时间"),
    TRANSFER_REJECT(13, "调拨拒收"),
    CLEAR_PLAN_IN(14, "清理计划入库量"),
    CLEAR_BOOKED_PLAN_IN(15, "清理预约入库量"),
    ;

    private final Integer code;

    private final String desc;

    OrderOperateTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public static OrderOperateTypeEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (OrderOperateTypeEnum s : OrderOperateTypeEnum.values()) {
            if (s.getCode().equals(code)) {
                return s;
            }
        }
        return null;
    }
}
