package com.ddmc.ims.common.exception;

import com.ddmc.core.view.compat.CodeMsg;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.errorcode.ErrorCode;

/**
 * 远程调用异常
 **/
public class ImsRemoteInvocationException extends RuntimeException {

    private final transient CodeMsg codeMsg;

    public ImsRemoteInvocationException(String errorMsg) {
        super(errorMsg);
        this.codeMsg = new CodeMsg(CommonErrorCode.ILLEGAL_PARAMETER.getErrorCode(), errorMsg);
    }

    public ImsRemoteInvocationException(String errorCode, Throwable e) {
        super(errorCode, e);
        this.codeMsg = null;
    }

    public ImsRemoteInvocationException(Integer errorCode, String errorMsg) {
        super(errorMsg);
        this.codeMsg = new CodeMsg(errorCode, errorMsg);
    }

    public ImsRemoteInvocationException(Throwable e) {
        super(e);
        this.codeMsg = null;
    }

    public ImsRemoteInvocationException(CodeMsg codeMsg) {
        super(codeMsg.getMsg());
        this.codeMsg = codeMsg;
    }

    public ImsRemoteInvocationException(ErrorCode errorCode) {
        this(new CodeMsg(errorCode.getErrorCode(), errorCode.getMsg()));
    }

    public ImsRemoteInvocationException(ErrorCode errorCode, Object... args) {
        this(new CodeMsg(errorCode.getErrorCode(), String.format(errorCode.getMsg(), args)));
    }

    public CodeMsg getCodeMsg() {
        return codeMsg;
    }
}
