package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;

public enum CredentialStatus implements IntegerEnumInterface {
    INIT(0, "初始化"),
    CONFIRM_SUCCESS(1, "确认"),
    CANCEL(2, "已取消");

    private Integer code;

    private String desc;

    CredentialStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
