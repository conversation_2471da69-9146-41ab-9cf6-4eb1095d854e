package com.ddmc.ims.common.util;

import com.github.pagehelper.PageInfo;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 分页插件工具类
 * <AUTHOR>
 */
public class PageUtils {

    private PageUtils() {
    }

    /**
     * 将List对象泛型中的PO对象转化为VO对象
     *
     * @param from List<PO>对象
     * @param <P> PO类型
     * @param <V> VO类型
     * @return PageInfo<VO> 对象
     */
    public static <P, V> PageInfo<V> page2PageInfoVo(List<P> from) {
        return pageInfo2PageInfoVo(PageInfo.of(from));
    }

    /**
     * 将List对象泛型中的PO对象转化为VO对象
     *
     * @param from List<PO>对象
     * @param to 转换后VO对象
     * @param <P> PO类型
     * @param <V> VO类型
     * @return PageInfo<VO> 对象
     */
    public static <P, V> PageInfo<V> page2PageInfoVo(List<P> from, List<V> to) {
        return pageInfo2PageInfoVo(PageInfo.of(from), to);
    }

    /**
     * 将PageInfo对象泛型中的PO对象转化为VO对象
     *
     * @param pageInfo PageInfo<PO>对象
     * @param <P> PO类型
     * @param <V> VO类型
     * @return PageInfo<VO> 对象
     */
    public static <P, V> PageInfo<V> pageInfo2PageInfoVo(PageInfo<P> pageInfo) {
        return pageInfo2PageInfoVo(pageInfo, Collections.emptyList());
    }

    /**
     * 将PageInfo对象泛型中的PO对象转化为VO对象
     *
     * @param pageInfo pageInfo PageInfo<PO>对象
     * @param list 转换后VO对象
     * @param <P> PO类型
     * @param <V> VO类型
     * @return PageInfo<VO> 对象
     */
    public static <P, V> PageInfo<V> pageInfo2PageInfoVo(PageInfo<P> pageInfo, List<V> list) {
        PageInfo<V> result = PageInfo.of(list);
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setSize(pageInfo.getSize());
        result.setPages(pageInfo.getPages());
        result.setPrePage(pageInfo.getPrePage());
        result.setNextPage(pageInfo.getNextPage());
        result.setIsFirstPage(pageInfo.isIsFirstPage());
        result.setIsLastPage(pageInfo.isIsLastPage());
        result.setHasPreviousPage(pageInfo.isHasPreviousPage());
        result.setHasNextPage(pageInfo.isHasNextPage());
        result.setNavigatePages(pageInfo.getNavigatePages());
        result.setNavigatepageNums(pageInfo.getNavigatepageNums());
        result.setNavigateFirstPage(pageInfo.getNavigateFirstPage());
        result.setNavigateLastPage(pageInfo.getNavigateLastPage());

        return result;
    }

    public static <T> List<T> page(List<T> list, int pageNum, int pageSize) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        int count = list.size(); //记录总数
        int pageCount; //页数
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }

        int fromIndex = 0; //开始索引
        int toIndex = 0; //结束索引

        if (pageNum > pageCount) {
            pageNum = pageCount;
        }
        if (pageNum != pageCount) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }

        return list.subList(fromIndex, toIndex);
    }


}