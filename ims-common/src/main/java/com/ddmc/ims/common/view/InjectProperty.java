package com.ddmc.ims.common.view;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 在field上增加此注解，表示依据{@link #id()} 指定的属性的值得到id，再依据id获取关联对象，并将关联对象的指定属性赋值为当前属性。
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface InjectProperty {

    /**
     *
     * @return 关联的资源类型
     */
    BaseInfoType type();

    /**
     * @return 从当前对象的那个属性获取关联对象的 id
     */
    String id();

    /**
     * @return 将关联对象的哪个属性的值赋值为当前属性
     */
    String property();
}
