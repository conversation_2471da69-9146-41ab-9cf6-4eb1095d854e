package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;

public enum InventoryOperateTypeEnum implements IntegerEnumInterface {

    ADD(0, "库存增加"),

    DECREASE(1, "库存减少");

    private final Integer code;

    private final String desc;

    InventoryOperateTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
