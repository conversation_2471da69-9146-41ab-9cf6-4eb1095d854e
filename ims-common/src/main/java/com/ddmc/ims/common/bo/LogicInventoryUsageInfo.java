package com.ddmc.ims.common.bo;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogicInventoryUsageInfo {

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 逻辑库位编码
     */
    private String logicInventoryLocationCode;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 用途编码
     */
    private String usageCode;

    /**
     * 数量
     */
    private BigDecimal qty;


}
