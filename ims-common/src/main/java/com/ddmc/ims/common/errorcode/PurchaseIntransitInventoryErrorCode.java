package com.ddmc.ims.common.errorcode;


import lombok.Getter;

/**
 * 仓库相关异常代码.
 */
@Getter
public enum PurchaseIntransitInventoryErrorCode implements ErrorCode {

    IN_SKU_NOT_EXISTS(1, "入库货品（%s）在采购单中不存在"),
    ;

    private static final int DOMAIN_CODE = 2;

    private final int code;

    private final String msg;

    PurchaseIntransitInventoryErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    @Override
    public int getDomainCode() {
        return DOMAIN_CODE;
    }

}
