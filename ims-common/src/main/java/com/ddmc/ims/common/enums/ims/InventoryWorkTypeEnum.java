package com.ddmc.ims.common.enums.ims;


import com.ddmc.ims.common.enums.IntegerEnumInterface;

/**
 * 库存作业类型
 * <AUTHOR>
 */
public enum InventoryWorkTypeEnum implements IntegerEnumInterface {

    FREE(0, "可作业"),

    FROZEN(1, "不可作业");

    private final Integer code;

    private final String desc;

    InventoryWorkTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
