package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.StringEnumInterface;
import java.util.Objects;

/**
 * 凭证扩展字段表
 */
public enum CredentialHeaderExtEnum implements StringEnumInterface {

    ORI_DELIVERY_DATE("ORI_DELIVERY_DATE", "原始履约日期"),

    ;

    private final String code;

    private final String desc;

    CredentialHeaderExtEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public static CredentialHeaderExtEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (CredentialHeaderExtEnum s : CredentialHeaderExtEnum.values()) {
            if (s.getCode().equals(code)) {
                return s;
            }
        }
        return null;
    }

}
