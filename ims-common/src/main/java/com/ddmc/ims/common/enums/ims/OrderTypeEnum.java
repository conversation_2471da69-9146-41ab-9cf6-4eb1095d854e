package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.StringEnumInterface;
import java.util.Objects;

public enum OrderTypeEnum implements StringEnumInterface {

    TRANSFER_ORDER("TRANSFER_ORDER", "调拨单"),
    LOT_STOCK_CHANGE("LOT_STOCK_CHANGE","库存异动"),
    INIT_CREDENTIAL_DIFF("INIT_CREDENTIAL_DIFF","初始化财务库存差异"),

    DO("SO001", "C端销售"),
    DO_REFUND("SO002", "C端退回"),
    BUSINESS_SALE("SO003", "B端销售"),
    BUSINESS("SO004", "B端退回"),
    BUSINESS_OUTBOUND("SO005", "B端出库"),
    PURCHASE("PO001", "采购入库"),
    PURCHASE_REFUND("PO002", "采购退货"),
    TRANSFER_OUTBOUND("DB001", "调拨出库发货单"),
    TRANSFER_DO_OUTBOUND("DB002", "调拨出库Do单"),
    TRANSFER_INBOUND("DB003", "调拨入库"),

    SELF_USE_OUTBOUND("LY001", "领用出库"),
    PROFIT_LOSS_OUTBOUND("SY001", "损益出库"),
    COUNT("PD001", "盘点"),
    ARRIVAL_ADJUST("FBTZ001", "分拨入库调整"),
    AGRICULTURAL_MATERIAL_OUTBOUND("NZ001", "农资出库"),
    AGRICULTURAL_MATERIAL_REFUND("NZ002", "农资返库"),
    PURCHASE_COLLECT_INBOUND("NZ003", "采收入库"),
    MANUFACTURE_SELF_USE("MM001", "生产领用"),
    PRODUCT_INBOUND("MM002", "成品入库"),
    BASIC_POSITIVE_MANUFACTURE("MM003", "基础正向制造"),
    BASIC_REVERSE_MANUFACTURE("MM004", "基础方向制造"),
    POSITIVE_MANUFACTURE("MM005", "正加工制造"),
    REVERSE_MANUFACTURE("MM006", "反加工制造"),
    MATERIAL_RETURN("MM007", "原料退回入库"),
    MATERIAL_OUT("MM008", "原料领用出库"),
    UN_MANUFACTURE_MATERIAL_INBOUND("MM009", "反加工-原料入库"),
    REVERSE_MANUFACTURE_FEED("MM010", "反加工制造投料"),
    REVERSE_MANUFACTURE_RETURN("MM011", "反加工制造退料"),

    RENT_BASKET_OUTBOUND("ZK001", "租筐出库"),
    RETURN_BASKET_INBOUND("HK001", "还筐入库"),
    THIRD_PARTY_OUTBOUND("SF001", "三方出库"),
    // 库内转移
    INNER_BOUND_TRANSFER("ZY001", "库内转移"),
    // 冻结锁定
    FREEZE_LOCK("DS001", "冻结锁定"),
    // 批次调整
    LOT_ADJUST("PCTZ001", "批次调整"),
    ;


    private final String code;

    private final String desc;

    OrderTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }



    public static boolean isTransfer(String orderType) {
        return OrderTypeEnum.TRANSFER_OUTBOUND.getCode().equals(orderType) ||
            OrderTypeEnum.TRANSFER_DO_OUTBOUND.getCode().equals(orderType) ||
            OrderTypeEnum.TRANSFER_INBOUND.getCode().equals(orderType);
    }

    public static boolean isTransferOut(String orderType) {
        return OrderTypeEnum.TRANSFER_OUTBOUND.getCode().equals(orderType) ||
            OrderTypeEnum.TRANSFER_DO_OUTBOUND.getCode().equals(orderType);
    }


    public static OrderTypeEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (OrderTypeEnum s : OrderTypeEnum.values()) {
            if (s.getCode().equals(code)) {
                return s;
            }
        }
        return null;
    }
}
