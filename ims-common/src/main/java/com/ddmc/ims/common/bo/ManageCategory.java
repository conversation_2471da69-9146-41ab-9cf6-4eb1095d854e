package com.ddmc.ims.common.bo;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 买菜三级分类返回数据结构
 */
@Data
public class ManageCategory {

    @ApiModelProperty(value = "采购分类列表数据")
    private List<ProductAdmPurchaseCategory> rows;

    @ApiModelProperty(value = "采购分类总数量")
    private Long total;

    @Data
    public static class ProductAdmPurchaseCategory {

        @ApiModelProperty("分类ID")
        private Long id;

        @ApiModelProperty("分类名")
        private String text;

        @ApiModelProperty("路径")
        private String path;

        @ApiModelProperty("父级ID")
        private Long parent;

        @ApiModelProperty("开关状态-买菜写死只会返回open")
        private String state;

        @ApiModelProperty("子类二级列表")
        private List<ProductAdmPurchaseCategory> children;
    }
}
