package com.ddmc.ims.common.constant;

import com.ddmc.ims.common.enums.common.EventEntityStatusEnum;
import com.google.common.collect.ImmutableList;

public class EventBusConstants {

    private EventBusConstants() {}

    public static final ImmutableList<Integer> WAIT_STATUS_LIST = ImmutableList.<Integer>builder()
        .add(EventEntityStatusEnum.DEFAULT.getCode())
        .add(EventEntityStatusEnum.ERROR.getCode())
        .build();

    public static final ImmutableList<Integer> WAIT_STATUS_LIST_MANUAL = ImmutableList.<Integer>builder()
        .add(EventEntityStatusEnum.DEFAULT.getCode())
        .add(EventEntityStatusEnum.PROCESSING.getCode())
        .add(EventEntityStatusEnum.ERROR.getCode())
        .build();

}
