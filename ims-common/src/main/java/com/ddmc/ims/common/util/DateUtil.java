package com.ddmc.ims.common.util;

import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
@Slf4j
public class DateUtil {

    private DateUtil() {
    }

    public static void checkDateRange(Date stareDate, Date endDate, Integer rang) {
        if (endDate.before(stareDate)) {
            throw new ImsBusinessException(CommonErrorCode.DATE_TIME_ERROR);
        }
        Date maxEndDate = DateUtils.addDays(stareDate, rang);
        if (maxEndDate.before(endDate)) {
            throw new ImsBusinessException(CommonErrorCode.DATE_TIME_RANGE_OVER,
                rang + "天");
        }
    }


    public static boolean checkSameDay(Date date1, Date date2) {
        if(Objects.isNull(date1) || Objects.isNull(date2)){
            return false;
        }
        return DateUtils.truncate(date1, Calendar.DATE).equals(DateUtils.truncate(date2, Calendar.DATE));

    }

    public static int compareDay(Date date1, Date date2) {
        if(Objects.isNull(date1) || Objects.isNull(date2)){
            log.error("[日期存在为空情况] 日期1 {} 日期2 {}", date1,date2);
            throw new ImsBusinessException("日期存在为空");
        }
        return DateUtils.truncate(date1, Calendar.DATE).compareTo(DateUtils.truncate(date2, Calendar.DATE));

    }


    public static Date getNowHourDate() {
        LocalDateTime now = CurrentDateUtil.newLocalDate().truncatedTo(ChronoUnit.HOURS);
        return Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getNowMinusHourDate(int minusHour) {
        LocalDateTime now = CurrentDateUtil.newLocalDate().minusHours(minusHour).truncatedTo(ChronoUnit.HOURS);
        return Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
    }


    public static int getDateHour(Date date) {
        LocalDateTime localDateTime = date.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime();
        return localDateTime.getHour();
    }




    /**
     * 获得某天+ plus的 最小时间
     *
     * @param date 2021-09-01 10:23:15
     * @param plus 1
     * @return 2021-09-02 00:00:00
     */
    public static Date getPlusStartOfDay(Date date, int plus) {
        LocalDateTime localDateTime = LocalDateTime
            .ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.plusDays(plus).with(LocalTime.MIN);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }


    /**
     * 获得某天+ plus的 最大时间
     *
     * @param date 2021-09-01 10:23:15
     * @param plus 1
     * @return 2021-09-02 23:59:59.999
     */
    public static Date getPlusEndOfDay(Date date, int plus) {
        LocalDateTime localDateTime = LocalDateTime
            .ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.plusDays(plus).with(LocalTime.MAX).withNano(0);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

}
