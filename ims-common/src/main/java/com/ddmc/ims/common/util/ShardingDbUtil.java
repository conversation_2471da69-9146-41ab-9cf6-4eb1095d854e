package com.ddmc.ims.common.util;


import lombok.extern.slf4j.Slf4j;

/**
 * 分片工具类
 * <p>
 * 计算库和表的分片位置
 * <p>
 * 要先将shardingId做hash值计算，充分打乱，再取模。
 * 库和表的hash算法要不一样，防止一条记录落在相同的dbIndex和tableIndex。
 * <p>
 * hash值计算参考：https://www.jb51.net/article/124819.htm
 */
@Slf4j
public class ShardingDbUtil {


    /**
     * 根据逻辑表名和index，返回实际的表名
     */
    public static String getActualTableName(String logicalTableName, String index) {
        return logicalTableName + "_" + index;
    }

    /**
     * @param orderNo    单号
     * @param beginIndex the beginning index, inclusive.
     * @param endIndex   the ending index, exclusive.
     * @return 根据单号，计算实际表的编号
     */
    public static String getActualTableIndex(String orderNo, int beginIndex, int endIndex) {
        return orderNo.substring(beginIndex, endIndex);
    }

    /**
     * 根据分片值和表个数，计算实际表的编号
     */
    public static String getActualTableIndex(Long shardingInfo, Integer tableNum) {
        return String.valueOf((intHash(shardingInfo.intValue())) % tableNum);
    }

    public static void main(String[] args) {
        String orderNo = "XX200702000001";
        log.info(ShardingDbUtil.getActualTableIndex(orderNo, 2, 6));
    }

    private static int intHash(int key) {
        key += ~(key << 15);
        key ^= (key >>> 10);
        key += (key << 3);
        key ^= (key >>> 6);
        key += ~(key << 11);
        key ^= (key >>> 16);
        return Math.abs(key);
    }
}
