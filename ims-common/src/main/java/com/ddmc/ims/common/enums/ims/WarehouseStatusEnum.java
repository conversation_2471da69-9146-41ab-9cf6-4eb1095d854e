package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

public enum WarehouseStatusEnum implements IntegerEnumInterface {

    YES(1, "启用"),

    NO(0, "废弃");

    private final Integer code;

    private final String desc;

    WarehouseStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public static String getDescFromCode(Integer code, String defaultIfNull) {
        WarehouseStatusEnum warehouseStatusEnum = fromCode(code);
        if (warehouseStatusEnum == null) {
            return defaultIfNull;
        } else {
            return warehouseStatusEnum.desc;
        }
    }

    public static WarehouseStatusEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (WarehouseStatusEnum status : WarehouseStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }


    @JsonCreator
    public static WarehouseStatusEnum forName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (WarehouseStatusEnum c : values()) {
            if (c.name().equals(name)) {
                return c;
            }
        }
        return null;
    }
}
