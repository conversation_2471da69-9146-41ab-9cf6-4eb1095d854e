package com.ddmc.ims.common.util;

import com.ddmc.ims.common.enums.IntegerEnumInterface;
import com.ddmc.ims.common.enums.StringEnumInterface;
import com.ddmc.ims.common.exception.ImsBusinessException;
import org.apache.commons.lang3.StringUtils;

public class EnumUtils {

    private EnumUtils() {
    }

    /**
     * @param value 字符串值
     * @param type  枚举类型
     * @param <E>   类型
     * @return 依据字符串值获取对应的枚举对象。当枚举类型为 {@link IntegerEnumInterface} 或 {@link StringEnumInterface} 时，
     * 用code值与value匹配。否则用name与value匹配
     */
    public static <E> E getEnum(String value, Class<E> type) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        if (IntegerEnumInterface.class.isAssignableFrom(type)) {
            return getEnum(type, Integer.parseInt(value));
        } else if (StringEnumInterface.class.isAssignableFrom(type)) {
            return getEnum(type, value);
        } else if (Enum.class.isAssignableFrom(type)) {
            E[] all = type.getEnumConstants();
            for (E e : all) {
                if (((Enum<?>) e).name().equals(value)) {
                    return e;
                }
            }
        }
        throw new ImsBusinessException("unknown enum value: " + value + ", type: " + type.getName());
    }

    private static <E> E getEnum(Class<E> type, int code) {
        for (E e : type.getEnumConstants()) {
            if (((IntegerEnumInterface) e).getCode() == code) {
                return e;
            }
        }
        return null;
    }

    public static <E> E getEnum(Class<E> type, String code) {
        for (E e : type.getEnumConstants()) {
            if (((StringEnumInterface) e).getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
