package com.ddmc.ims.common.constant;


public final class LocalParamsConstants {

    private LocalParamsConstants() {
    }

    public static final String PREFIX = "ims.";

    public static final String WECHAT_NOTIFY_KEY_COMMON = PREFIX + "wechat.notifyKey.common";

    public static final String BASE_SWITCH_ALERT_TO_COMMON_SERVICE = PREFIX + "switch.alertToCommonService";

    public static final String BASE_SWITCH_SCM_ALERT_MSG_SEND_MESSAGE = PREFIX + "switch.scmAlertMsgSendMessage";


    /**
     * 校验凭证状态的时间偏移量
     */
    public static final String CREDENTIAL_STATUS_CHECK_OFFSET_ON_SECOND = PREFIX + "credentialStatusCheckOffsetOnSecond";

    /**
     * 校验凭证状态的时间间隔
     */
    public static final String CREDENTIAL_STATUS_CHECK_RANGE_ON_SECOND = PREFIX + "credentialStatusCheckRangeOnSecond";

    /**
     * 校验凭证状态的数据查询条数
     */
    public static final String CREDENTIAL_STATUS_CHECK_LIMIT = PREFIX + "credentialStatusCheckLimit";


    /**
     * 单据类型凭证操作命令配置_new
     */
    public static final String BIZ_ORDER_OPERATOR_COMMAND_NEW = PREFIX + "bizOrderOperatorCommandNew";

    /**
     * 单据类型凭证操作命令配置_调拨兼容
     */
    public static final String BIZ_ORDER_OPERATOR_COMMAND_COMPATIBLE = PREFIX + "bizOrderOperatorCommandCompatible";


    /**
     * 默认逻辑库位用途转换配置
     */
    public static final String DEFAULT_LOCATION_USAGE_CODE_CONFIG = PREFIX + "defaultLocationUsageCodeConfig";


    public static final String FMS_SNAPSHOT_STOP = PREFIX + "fmsSnapshotStop";



    /**
     * 是否中断逻辑库存小时快照
     */
    public static final String IS_INTERRUPT_SNAPSHOT_PER_HOUR = PREFIX + "isInterruptSnapshotPerHour";


    /**
     * 逻辑库存小时快照自定义时间
     */
    public static final String SNAPSHOT_PER_HOUR_DATE_TIME = PREFIX + "snapshotPerHourDateTime";


    /**
     * 逻辑库存小时快照sleep间隔时间
     */
    public static final String SNAPSHOT_PER_HOUR_SLEEP_INTERVAL = PREFIX + "snapshotPerHourSleepInterval";

    /**
     * 逻辑库存小时快照只清除开关
     */
    public static final String SNAPSHOT_PER_HOUR_ONLY_CLEAR = PREFIX + "snapshotPerHourOnlyClear";





    public static final String DELETE_CREDENTIAL_HEAD = PREFIX + "deleteCredentialHead";


    /**
     * 单据类型凭证操作命令配置
     */
    public static final String SNAPSHOT_NOTIFY_FMS = PREFIX + "snapshotNotifyFms";


    /**
     * 库存异动重放中断标识
     */
    public static final String STOCK_TRANSACTION_LOG_REPLAY_INTERRUPT = PREFIX + "StockTransactionLogReplayInterrupt";

    /**
     * 库存异动重放睡眠时间
     */
    public static final String STOCK_TRANSACTION_LOG_REPLAY_SLEEP = PREFIX + "StockTransactionLogReplaySleep";

    /**
     * 库存快照需要清除的仓库
     */
    public static final String SKU_INVENTORY_SNAPSHOT_CLEAR_WAREHOUSE = PREFIX + "SkuInventorySnapshotClearWarehouse";


    public static final String SKU_INVENTORY_SNAPSHOT_PER_HOUR_REPLAY = PREFIX + "SkuInventorySnapshotPerHourReplay";


    public static final String SKU_INVENTORY_SNAP_PER_HOUR_DELETE_ID = PREFIX + "SkuInventorySnapPerHourDeleteId";


    /**
     * 异步confirm的单据配置
     */
    public static final String ASYNC_CONFIRM_ORDER_TYPE = PREFIX + "asyncConfirmOrderType";


    /**
     * 业务库存快照、财务口径快照需要排除的仓库
     */
    public static final String SNAPSHOT_EXCLUDE_WAREHOUSE_ID = PREFIX + "snapshotExcludeWarehouseId";




    /**
     * 是否插入用途维度快照
     */
    public static final String ENABLE_INSERT_USAGE_SNAPSHOT = PREFIX + "enableInsertUsageSnapshot";

    /**
     * 逻辑库存小时维度快照间隔
     */
    public static final String FMS_BUSINESS_DATE_AHEAD_END_DATE = PREFIX + "fmsBusinessDateAheadEndDate";

    /**
     * 开启imsConfig调拨场景查询
     */
    public static final String ENABLE_TRANSFER_SCENE = PREFIX + "enableTransferScene";


    /**
     * 入库优先级
     */
    public static final String TRANSFER_IN_USAGE_PRIORITY = PREFIX + "transferInUsagePriority";


    /**
     * 出库优先级
     */
    public static final String TRANSFER_OUT_USAGE_PRIORITY = PREFIX + "transferOutUsagePriority";

    /**
     * 逻辑库位存在多个用途时入库默认用途
     */
    public static final String INBOUND_DEFAULT_USAGE = PREFIX + "inboundDefaultUsage";


    /**
     * 逻辑库位存在多个用途时出库默认用途
     */
    public static final String OUTBOUND_DEFAULT_USAGE = PREFIX + "outboundDefaultUsage";



    public static final String ENABLE_HANDLE_TRANSFER_IN_DETAIL = PREFIX + "EnableHandleTransferInDetail";

    public static final String NOT_EXISTS_SKU_IDS = "imsAdmin.notExistSkuIds";
    
    public static final String BOOKING_CHANGE_TRIGGER_TYPE = PREFIX + "bookingChangeTriggerType";


    /**
     * 凭证业务时间延时分钟数自动更新成当前时间
     */
    public static final String CREDENTIAL_BUSINESS_TIME_DELAY = PREFIX + "credentialBusinessTimeDelay";

    /**
     * 逻辑库存小时快照sleep间隔时间
     */
    public static final String TRANSFER_SNAPSHOT_PER_HOUR_SLEEP_INTERVAL = PREFIX + "transferSnapshotPerHourSleepInterval";

    /**
     * 逻辑库存小时是否停止
     */
    public static final String TRANSFER_SNAPSHOT_PER_HOUR_STOP = PREFIX + "transferSnapshotPerHourStop";

    /**
     * 删除凭证时，获取最大id循环次数
     */
    public static final String DELETE_CREDENTIAL_QUERY_MAX_ID_COUNT = PREFIX + "deleteCredentialQueryMaxIdCount";

    /**
     * 删除凭证时的分批数量
     */
    public static final String DELETE_CREDENTIAL_BATCH_SIZE = PREFIX + "deleteCredentialBatchSize";

}
