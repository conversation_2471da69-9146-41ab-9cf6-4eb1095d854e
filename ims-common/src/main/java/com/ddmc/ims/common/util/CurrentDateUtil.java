package com.ddmc.ims.common.util;

import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;
import java.util.Date;

@UtilityClass
public class CurrentDateUtil {

    public static long currentTimeMillis(){
        return System.currentTimeMillis();
    }

    public static Date newDate(){
        return new Date();
    }

    public static LocalDateTime newLocalDate(){
        return LocalDateTime.now();
    }
}
