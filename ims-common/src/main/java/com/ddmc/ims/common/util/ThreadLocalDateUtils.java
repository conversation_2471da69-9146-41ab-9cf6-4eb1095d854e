package com.ddmc.ims.common.util;

import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.utils.BusinessException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.time.DateUtils;

public class ThreadLocalDateUtils {

    private ThreadLocalDateUtils() {
    }

    public static final String DATE_YMD = "yyyy-MM-dd";
    public static final String DATE_YMD_HM = "yyyy-MM-dd HH:mm";
    public static final String DATE_YMD_HMS = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_YMD_2 = "yyyyMMdd";

    private static final ThreadLocal<Map<String, SimpleDateFormat>> formatCache = new ThreadLocal<>();

    static {
        ThreadLocalContextHelper.registerClearFunction(ThreadLocalDateUtils::clear);
    }

    private static SimpleDateFormat get(String pattern) {
        Map<String, SimpleDateFormat> map = formatCache.get();
        if (map == null) {
            map = new HashMap<>();
            formatCache.set(map);
        }
        return map.computeIfAbsent(pattern, e -> new SimpleDateFormat(pattern));
    }

    public static Date parse(String value, String pattern) {
        try {
            return get(pattern).parse(value);
        } catch (Exception e) {
            throw new BusinessException("parse date error, format: " + pattern + ", value: " + value);
        }
    }

    public static Date parseYmd(String value) {
        try {
            return get(DATE_YMD).parse(value);
        } catch (Exception e) {
            throw new BusinessException("parse date error, format: yyMMdd, value: " + value);
        }
    }

    public static Date parseYmdhm(String value) {
        try {
            return get(DATE_YMD_HM).parse(value);
        } catch (Exception e) {
            throw new BusinessException("parse date error, format: yyMMdd HH:mm, value: " + value);
        }
    }

    public static Date parseYmd2(String value) {
        try {
            return get(DATE_YMD_2).parse(value);
        } catch (Exception e) {
            throw new BusinessException("parse date error, format: yyMMdd, value: " + value);
        }
    }


    public static Date parseYmdhms(String value) {
        try {
            return get(DATE_YMD_HMS).parse(value);
        } catch (Exception e) {
            throw new BusinessException("parse date error, format: yyMMdd HH:mm:ss, value: " + value);
        }
    }

    public static String format(Date date, String pattern) {
        return get(pattern).format(date);
    }

    public static String formatYmd(Date date) {
        return get(DATE_YMD).format(date);
    }

    public static String formatYmdhm(Date date) {
        return get(DATE_YMD_HM).format(date);
    }

    public static String formatYmdhms(Date date) {
        return get(DATE_YMD_HMS).format(date);
    }

    public static void clear() {
        formatCache.remove();
    }

    /**
     * 获取当前日期
     *
     * @return 返回数据
     */
    public static String getDateYmd() {
        DateFormat format = new SimpleDateFormat(DATE_YMD);
        return format.format(CurrentDateUtil.newDate());
    }

    public static String formatYmd2(Date date) {
        return get(DATE_YMD_2).format(date);
    }


    /**
     * 获取过去的指定日期
     *
     * @param pastDays 之前多少天 T-n
     * @return 返回计算后的日期
     */
    public static String getPastDaysYmd(int pastDays) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_YMD);
        Calendar time = Calendar.getInstance();
        time.add(Calendar.DATE, pastDays);
        return format.format(time.getTime());
    }

    public static void checkEffectDate(Date effectDateTime) {
        if(Objects.isNull(effectDateTime)){
            throw new ImsBusinessException("查询日期不能为空");
        }
        Date nowBeginDate = DateUtils.truncate(CurrentDateUtil.newDate(), Calendar.DATE);
        if (nowBeginDate.after(effectDateTime)) {
            throw new ImsBusinessException("查询日期不能小于当天");
        }
    }




    /**
     * 获得某天+ plus的 最小时间
     *
     * @param date 2021-09-01 10:23:15
     * @param plus 1
     * @return 2021-09-02 00:00:00
     */
    public static Date getPlusStartOfDay(Date date, int plus) {
        LocalDateTime localDateTime = LocalDateTime
            .ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.plusDays(plus).with(LocalTime.MIN);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }






}
