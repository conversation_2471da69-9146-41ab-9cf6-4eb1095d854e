package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;
import java.util.Objects;

public enum DeliveryModeEnum implements IntegerEnumInterface {

    ONE_DELIVERY(0, "一配"),

    TOW_DELIVERY(1, "二配");

    private final Integer code;

    private final String desc;

    DeliveryModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static DeliveryModeEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (DeliveryModeEnum s : DeliveryModeEnum.values()) {
            if (s.getCode().equals(code)) {
                return s;
            }
        }
        return null;
    }

    public static String getDescFromCode(Integer code, String defaultIfNull) {
        DeliveryModeEnum barCodeEnum = fromCode(code);
        if (barCodeEnum == null) {
            return defaultIfNull;
        } else {
            return barCodeEnum.desc;
        }
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
