package com.ddmc.ims.common.errorcode;

import lombok.Getter;

/**
 * 通用错误码
 */
@Getter
public enum CommonErrorCode implements com.ddmc.ims.common.errorcode.ErrorCode {
    /**
     * 版本号未传
     */
    VERSION_NOT_PRESENT(6, "VersionNotPresent"),

    /**
     * 参数类型和要求不匹配
     */
    REQUEST_PARAM_ERROR(7, "REQUEST_PARAM_TYPE_ERROR"),

    WAREHOUSE_UNSELECTED(8, "未选择仓库，请先前往选择"),

    WAREHOUSE_CHANGED(9, "您的仓库权限已经被管理员变更，请重新选择或询问管理员"),

    SSO_USER_FETCH_ERROR(10, "获取sso用户信息失败"),

    SEND_CODE_LIMIT(11, "验证码发送次数已达今日上限"),

    NON_LOGIN(12, "用户未登录"),

    REQUEST_SIGN_ERROR(13, "验签失败"),

    PRODUCT_DETAIL_NOT_EXIST(14, "商品明细不存在"),

    PRODUCT_ID_NOT_EXIST(15, "商品id不能为空"),

    EXCLUDE_PURCHASE_CATEGORY(16, "商品(%s)所属水产海鲜的商品不能添加"),

    PURCHASE_NOT_EXIST(17, "商品(%s)的采购分类不存在"),

    VENDOR_ID_NOT_EXIST(18, "供应商ID(%s)不存在"),

    VENDOR_CONTRACT_CATEGORY_NOT_EXIST(19, "供应商ID(%s)合同采购分类数据不存在"),

    EXCEL_DATA_ERROR(20, "导出Excel数据错误"),

    EXCEL_TEMPLATE_ERROR(21, "文件模板不合格"),

    EXCEL_DATA_PARSE_ERROR(22, "文件数据解析失败"),

    FILE_NOT_SUPPORT_ERROR(23, "不支持的文件类型"),

    FILE_DATA_EMPTY_ERROR(24, "文件数据为空"),

    PRODUCTS_NOT_EXIST(25, "商品不存在"),

    STORE_NOT_ENABLE(27, "该仓库没有开通调拨服务"),

    AQUATIC_STORE_DISABLE(29, "水产仓库未开启，请确定仓库"),

    VENDOR_DISTRIBUTION_NOT_EXIST(30, "供应商配送属性数据不存在"),

    PRODUCT_DISTRIBUTION_NOT_EXIST(31, "商品配送属性数据不存在"),

    OPERATOR_IS_ERROR(32, "操作用户信息不存在"),

    PARAMS_IS_NOT_EXIST(33, "参数不能为空"),

    VENDOR_NOT_EXIST(34, "供应商不存在"),

    NO_CHECKOUT_NO_FOR_INTERNEL_SALE(35, "增加入库单时，内部销售缺少出库单单号"),

    PARAM_ERR_WITHONEMSG(198, "参数错误,(%s)"),

    ERROR_FILE_TYPE(199, "文件格式有误!"),

    PICKUP_BEYOND_SCOPE(200, "当前位置不在发货方可提货范围内!"),

    DELIVERY_BEYOND_SCOPE(201, "当前位置不在收货方送达范围内!"),

    GENERATE_NO_ERROR(202, "生成单号失败"),

    SCM_RESPONSE_ERROR(203, "scm服务响应错误:%s"),

    OPERATOR_NOT_EXIST(204, "操作人信息不存在"),

    BATCH_RESPONSE_ERROR(205, "batch服务响应错误:%s"),

    DARWIN_RESPONSE_ERROR(206, "darwin服务响应错误"),

    VMS_RESPONSE_ERROR(207, "vms服务响应错误:%s"),

    UPDATE_DATA_FAIL(208, "更新数据失败"),

    ILLEGAL_PARAMETER(301, "参数错误:%s"),
    STORE_NOT_EXIST(302, "仓库ID(%s)不存在"),
    PRODUCT_NOT_EXIST(303, "商品ID(%s)不存在"),
    PRODUCT_STATUS_ERROR(304, "商品ID(%s)不是正常状态"),
    PRODUCT_NOT_PROCUREMENT(307, "商品ID(%s)不可采购"),
    ID_NOT_EXIST(305, "id不存在"),
    PARAMS_IS_ERROR(306, "参数错误"),

    NOT_SWITCH_STORE(307, "仓库ID(%s)未切换到scm-so-service服务，不可受理"),

    REQUIRED_BUSINESSID(308, "出库类型为(%s)时，客户id必传"),

    REQUIRED_CHECKIN_STORE_ID(309, "出库类型为(%s)时，入库仓必传"),

    PRODUCT_NOT_EXISTS(310, "商品(%s)不存在"),

    CHECKOUT_NOT_EXISTS(311, "出库单(%s)不存在，或与仓库(%s)不匹配"),

    PRODUCT_REPEAT(312, "商品重复"),

    DATE_TIME_ERROR(313, "结束时间早于开始时间"),

    DATE_TIME_RANGE_OVER(314, "时间范围超过(%s)的限制"),

    DATE_TIME_NULL(315, "开始时间或结束时间为空"),

    PRODUCT_OVER(316, "商品数量超过（%s）的限制"),

    CHECKOUT_STATUS_ERR(317, "出库单状态为（%s）不支持当前操作"),


    NOT_SUPPORT_CANCEL(318, "出库单为（%s）状态，不支持废弃操作"),

    CALL_CANCEL_DO_ERR(319, "调用wms Do单废弃接口失败，订单号为（%s）,wms返回（%s）"),
    CALL_CANCEL_ASN_ERR(325, "调用wms Asn单废弃接口失败，订单号为（%s）,wms返回（%s）"),
    INTERNEL_SALE_PARAMS_IS_ERROR(320, "参数错误:(%s)"),

    TOTAL_PRICE_ERROR(321, "单价 * 数量 != 总金额"),


    PRODUCT_BATCH_REPEAT(322, "商品批次重复"),

    CHECKOUTTYPE_ERR(323, "出库单类型错误，(%s)"),

    PRODUCT_NOT_IN_CHECKOUT(324, "出库单不包含商品(%s)"),

    CHECKIN_NOT_EXISTS(326, "入库单(%s)不存在，或与仓库(%s)不匹配"),

    CHECKIN_STATUS_ERR(327, "入库单状态为（%s）不支持当前操作"),

    ASN_STATUS_ERR(328, "ASN单状态为（%s）不支持当前操作"),

    QUERY_TRANSFER_ERR(329, "查询调拨单失败:(%s)"),

    TRANSFER_NOT_EXISTS(330, "调拨单不存在"),

    TRANSFER_STATUS_ERR(331, "调拨单未完成"),

    STORE_NOT_METCH(332, "入库仓和调拨单仓库不匹配"),

    PRODUCT_NOT_EXISTS_IN_CHECKOUT(333, "出库单中不包含商品(%s)"),

    CHECKIN_NO_NOT_EXISTS_FOR_UDPATE(334, "更新入库单，入库单号比传"),

    PARAM_IS_NULL(335, "请求参数不能为空"),

    CAN_CREATE_ADJUST_ORDER(336, "只有已完成的出库单（内部销售或渠道销售）才能创建调整单"),

    ADJUST_ORDER_FOOL_PROOL_ERR(337, "修改后单价不能超过原价的(%s)倍"),

    NOT_SUPPORT_CHECKINTYPE(327, "不支持的入库单类型"),

    PRODUCT_CHECKIN_NUMBER_OVER(328, "商品入库数量大于原出库单实际出库数量：原出库单商品(%s)实际出库(%s),累计入库(%s),当前入库(%s)"),

    INVALID_BUSINESS_ID(329, "无效的客户id(%s)"),

    REF_ORDER_NO_EXISTS(330, "关联单号(%s)不存在"),

    STORE_TYPE_NOT_MATCH(331, "仓库(%s)不是总仓，不可受理"),

    WEB_CREATE_CANCEL_BIG_CLIENT_NOT_SUPPORT(340, "暂不支持手动创建或取消大客户销售出库单"),

    SCM_BASE_ERROR(998, "调用scm基础服务（%s）错误"),

    SYS_ERR(999, "系统忙"),

    FMS_SERVICE_CALL_ERROR(993, "fms服务调用异常"),

    CLOUD_SERVICE_CALL_ERROR(996, "CLOUD服务调用异常"),

    GPS_CALL_ERROR(997, "%sGPS服务调用异常"),

    GSS_CALL_ERROR(995, "GSS服务调用异常"),

    ACTION_TOO_FAST(994, "操作太快，请稍后重试"),
    ACTION_TOO_MUCH(999, "当前操作人数多过，请稍后重试"),

    PMS_COMMON_SERVER_ERROR(211, "PMS-COMMON服务调用异常: %s"),

    IMS_CONFIG_SERVER_ERROR(213, "IMS-CONFIG服务调用异常: %s"),

    FMS_SERVER_ERROR(212, "FMS服务调用异常: %s"),

    DUC_SERVER_ERROR(232, "DUC服务调用异常: %s"),

    EXIST_SAME_TEMPLATE(234, "存在相同配置模板，请勿重复创建！"),

    EXIST_TEMPLATE_DETAIL(235,"存在配置中的逻辑库位，可能会对业务产生影响，请清理后再删除！"),

    HAVE_EXIST_SAME_LOGIC_INVENTORY_CODE(236,"该类型模板下，逻辑库位编码已存在，请勿重复新增！"),

    PES_CALL_ERROR(1998, "pes-service服务调用异常"),

    LOT_CALL_ERROR(1999, "lot-service服务调用异常"),

    NOT_EXIST_LOGIC_INVENTORY_TEMPLATE(237,"逻辑库位模板不存在！"),

    NOT_EXIST_LOGIC_INVENTORY_TEMPLATE_DETAIL(238,"模板下逻辑库位不存在！"),

    PARAMS_ILLEGAL(239,"参数不合法！"),

    EXIST_WAREHOUSE_LOGIC_INVENTORY(240,"在该逻辑库位下已存在库存信息，不允许删除！"),

    EXIST_PURCHASE_LOGIC_INVENTORY(241,"在该逻辑库位下已存在在途信息，不允许删除！"),

    CONFIG_WAREHOUSE_SKU_INVENTORY_SHADING_ERR(246,"配置warehouse_sku_inventory分表规则错误！"),

    INVENTORY_NOT_EXIST_SKU(247, "在库库存不存在商品: %s"),

    LOGIC_INVENTORY_NOT_EXIST(248, "仓库(%s)/货主(%s)/逻辑库位编码(%s)错误，无法处理"),

    PURCHASE_IN_TRANSIT_INVENTORY_NOT_EXIST(249, "采购单号+来源系统不存在未关闭采购在途，无法处理"),

    TRANSFER_IN_TRANSIT_INVENTORY_NOT_EXIST(250, "发货单号不存在未关闭在途（待收）明细，无法处理"),

    TRANSFER_IN_TRANSIT_INVENTORY_NOT_CLOSE(251, "调拨单号+来源系统+货品ID存在未关闭调拨在途（待收）明细，无法处理"),

    INVENTORY_ALLOC_FULL_FAIL(252,"占用失败,商品id(%s)可用数量小于需求数量"),
    INVENTORY_ALLOC_PART_FAIL(253,"占用失败,商品id(%s)可用数量小于等于0"),

    INVENTORY_ALLOC_NOT_EXIST(254,"不存在未关闭占用明细，无法处理"),

    INVENTORY_LOCATION_IS_NOT_EQUALS(255,"修改占用失败，逻辑库位信息与已有的数据不一致"),

    INVENTORY_ALLOC_ORDER_INFO_EXIST(256,"存在相同订单的未关闭占用明细，无法处理"),

    CREDENTIAL_REPEAT(256,"凭证重复"),

    TRANSFER_ORDER_NOT_EXISTS(257,"调拨单不存在"),

    TRANSFER_IS_EXISTS(258, "调拨单已存在"),

    TRANSFER_INTRANSIT_IS_NOT_NULL(259,"该订单存在在途数量不为0的商品，无法进行完成操作"),

    TRANSFER_INTRANSIT_ALLOC_IS_NOT_NULL(259,"该订单存在已经出库的数量，无法进行取消操作"),

    TRANSFER_CANCELED(331, "调拨单已经取消"),

    TRANSFER_FINISHED(332, "调拨单已经完成"),

    NOT_SUPPORT_TRANSFER_ORDER_STATUS(333, "不支持的调拨单状态"),

    CANNOT_MODIFY_DELIVERY_MODE(334, "修改调拨信息时不能修改调拨模式"),

    CANNOT_MODIFY_LOGIC_INVENTORY_LOCATION(335, "修改调拨信息时不能修改调拨逻辑库位"),

    INVENTORY_ALLOC_DETAIL_NOT_EXIST(254,"占用明细不存在，无法处理"),


    REQUEST_REPEAT(335, "请求重复"),

    TRANSFER_INTRANSIT_NOT_EXIST_SKU(335, "在途库存中不存在商品 %s"),

    CREDENTIAL_IS_EXISTS(258, "凭证已加存在"),

    SKU_IS_NULL_OR_REPEAT(259, "商品id为空或重复"),
    LOT_NO_IS_BLANK(336, "批次编号不能为空"),

    PURCHASE_NOT_EXISTS(337, "采购单不存在"),

    PURCHASE_CANCELED(338, "采购单已经取消"),

    PURCHASE_FINISHED(339, "采购单已经完成"),

    NOT_SUPPORT_PURCHASE_ORDER_STATUS(340, "不支持的采购单状态"),

    CANNOT_MODIFY_PURCHASE_LOGIC_INVENTORY_LOCATION(341, "修改采购信息时不能修改采购逻辑库位"),

    CANNOT_MODIFY_PURCHASE_DETAIL(342, "修改采购信息时不能修改采购商品明细"),

    PURCHASE_INTRANSIT_IS_NOT_NULL(343,"该订单存在在途数量不为0的商品，无法进行完成操作"),

    PURCHASE_INTRANSIT_ALLOC_IS_NOT_NULL(344,"该订单存在已经出库的数量，无法进行取消操作"),


    EXIST_CREDENTIAL(345, "已经存在凭证"),

    CREDENTIAL_EXIST_MULTIPLE_WAREHOUSE(346, "凭证中存在多个仓库"),

    NOT_EXIST_CREDENTIAL(347, "根据幂等id获取不到凭证"),

    EVENT_BUS_SAVE_ERROR(209, "保存eventBus事件异常"),

    EVENT_BUS_COMPLETE_ERROR(210, "完成eventBus事件异常"),

    CREDENTIAL_CANCELED(211, "凭证已经取消"),

    CREDENTIAL_CONFIRMED(212, "凭证已经确认"),

    CALL_OCS_ERROR(213, "调用单据中心接口异常"),

    ERROR_BIZ_ORDER_TYPE(214,"不支持的单据类型，请前往配置中心配置"),

    DAY_END_TIME_IS_NULL(215,"归结日期为空"),

    EXPECT_ARRIVE_TIME_IS_NULL(217, "期望到库时间为空"),

    WMS_QUERY_SERVER_ERROR(218,"wms query服务调用异常"),


    FSS_QUERY_SERVER_ERROR(219, "fss query服务调用异常"),

    STOCK_TRANSACTION_LOG_REPEAT(216, "库存异动日志重复"),

    COMMON_MULTIPLE_ORDER(220, "命令中存在多个订单号"),

    CLEAN_TRANSFER_IN_TRANSIT_OPERATE_CODE_ERROR(221,"清理调拨在途不支持的单据操作类型"),

    USAGE_DETAIL_CONVERT_MORE_ONE(222,"用途详情转换器多于一个"),

    TRANSFER_GROUP_NO_NOT_EXIST(223,"调拨在途GroupDo明细不存在"),

    EXCEPTION_OUT_TIME_IS_NULL(224,"期望出库时间为空"),
    LOT_ID_ILLEGAL(225, "批次id格式不正确"),
    LOT_ID_NOT_EXIST(226, "批次不存在:%s"),

    TRANSFER_IN_LOGIC_EMPTY(227, "调拨入库逻辑库位为空"),

    BSS_CALL_ERROR(228, "bss服务调用异常"),

    NOT_SUPPORT_TYPE(229, "不支持的操作类型"),

    DARWIN_SERVER_ERROR(230, "darwin服务调用异常"),

    VENDOR_SERVER_ERROR(231, "vendor服务调用异常"),

    ;


    private static final int DOMAIN_CODE = 0;

    private int code;
    private String msg;

    CommonErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getDomainCode() {
        return DOMAIN_CODE;
    }
}
