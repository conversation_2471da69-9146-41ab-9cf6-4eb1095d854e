package com.ddmc.ims.common.util;

import com.ddmc.ims.common.exception.ImsBusinessException;

/**
 * 实际变更数据行数校验器
 */
public class RealChangeRowNumberChecker {

    private RealChangeRowNumberChecker(){}

    /**
     * 实际变更数量和期望变更数量不一致时，抛出异常
     * @param expected 期望变更数据行数
     * @param real 实际变更数据行数
     * @param message 异常前缀
     */
    public static void checkAndException(int expected, int real, String message) {
        if (expected != real) {
            throw new ImsBusinessException(message + ";期望更新" + expected + "条数据，实际更新" + real + "条数据");
        }
    }
}
