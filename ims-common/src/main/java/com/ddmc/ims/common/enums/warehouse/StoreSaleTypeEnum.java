package com.ddmc.ims.common.enums.warehouse;

import com.ddmc.ims.common.enums.IntegerEnumInterface;
import lombok.Getter;

/**
 * 销售仓类型
 *
 * <AUTHOR>
 */
@Getter
public enum StoreSaleTypeEnum implements IntegerEnumInterface {
    STORE_SALE_TYPE_NOT_SET(0, "未设置"),
    STORE_SALE_TYPE_SHARED(1, "共享仓"),
    STORE_SALE_TYPE_PULL_NEW(2, "拉新仓"),
    STORE_SALE_TYPE_OFFICE(3, "办公室仓"),
    STORE_SALE_TYPE_EQUIPMENT(4, "装备仓"),
    STORE_SALE_TYPE_OFFLINE_STORE(5,"线下店");

    private final Integer code;

    private final String desc;

    StoreSaleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static StoreSaleTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StoreSaleTypeEnum item : StoreSaleTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
