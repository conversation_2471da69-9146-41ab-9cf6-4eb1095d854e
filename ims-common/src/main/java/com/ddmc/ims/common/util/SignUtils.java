package com.ddmc.ims.common.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

public final class SignUtils {

    private SignUtils() {
    }

    public static String signForSSO(Map<String, String> params, String privateKey) {
        params.put("private_key", privateKey);
        String sign = makeSign(null, params);
        params.remove("private_key");
        return sign;
    }

    /**
     * 生成SCM的签名.
     */
    public static String signForSCM(Map<String, String> params, String privateKey) {
        params.put("token", privateKey);
        String sign = makeSign(null, params);
        params.remove("token");
        return sign;
    }

    /**
     * 生成BATCH的签名
     */
    public static String signForBatch(Map<String, String> params, String token) {

        return makeSign(token, params);
    }

    private static String makeSign(String prefix, Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        List<String> signParts = new ArrayList<>(keys.size());
        for (String key : keys) {
            signParts.add(key + "=" + params.get(key));
        }
        String toSign = StringUtils.join(signParts, "&");
        return prefix == null ? DigestUtils.md5Hex(toSign) : DigestUtils.md5Hex(prefix + toSign);
    }
}
