package com.ddmc.ims.common.bo;


import lombok.Getter;

import java.util.Objects;
@Getter
public class LogicInventoryLocationWithSku {

    protected final LogicInventoryLocation logicInventoryLocation;

    protected final Long skuId;

    public LogicInventoryLocationWithSku(LogicInventoryLocation logicInventoryLocation, Long skuId) {
        this.logicInventoryLocation = logicInventoryLocation;
        this.skuId = skuId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LogicInventoryLocationWithSku that = (LogicInventoryLocationWithSku) o;
        return Objects.equals(logicInventoryLocation, that.logicInventoryLocation) && Objects.equals(skuId, that.skuId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(logicInventoryLocation, skuId);
    }
}
