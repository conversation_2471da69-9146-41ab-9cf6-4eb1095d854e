package com.ddmc.ims.common.util;

import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.response.base.ValidationResult;
import com.google.common.collect.Maps;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.groups.Default;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 校验工具类
 */
public class ValidationUtils {

    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    private ValidationUtils() {
    }

    public static <T> void validateEntityAndThrowsError(T obj) {
        validateEntityAndThrowsError(obj, Default.class);
    }

    public static <T> void validateEntityAndThrowsError(T obj, Class<?>... groups) {
        ValidationResult result = validateEntity(obj, groups);
        if (result.isHasErrors()) {
            String errorMsg = StringUtils.join(result.getErrorMsg().values(), ",");
            throw new ImsBusinessException(CommonErrorCode.ILLEGAL_PARAMETER, errorMsg);
        }
    }

    public static <T> ValidationResult validateEntity(T obj) {
        return validateEntity(obj, Default.class);
    }

    public static <T> ValidationResult validateEntity(T obj, Class<?>... groups) {
        ValidationResult result = new ValidationResult();
        Set<ConstraintViolation<T>> set = validator.validate(obj, groups);
        if (CollectionUtils.isNotEmpty(set)) {
            result.setHasErrors(true);
            Map<String, String> errorMsg = Maps.newHashMapWithExpectedSize(set.size());
            for (ConstraintViolation<T> cv : set) {
                errorMsg.put(cv.getPropertyPath().toString(), cv.getMessage());
            }
            result.setErrorMsg(errorMsg);
        }
        return result;
    }

    public static <T> ValidationResult validateProperty(T obj, String propertyName) {
        ValidationResult result = new ValidationResult();
        Set<ConstraintViolation<T>> set = validator.validateProperty(obj, propertyName, Default.class);
        if (CollectionUtils.isNotEmpty(set)) {
            result.setHasErrors(true);
            Map<String, String> errorMsg = new HashMap<>();
            for (ConstraintViolation<T> cv : set) {
                errorMsg.put(propertyName, cv.getMessage());
            }
            result.setErrorMsg(errorMsg);
        }
        return result;
    }

}

