package com.ddmc.ims.common.util;

import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * admin
 */
@Slf4j
public class LotIdUtils {

    public static final int LOT_ID_MIN_LENGTH = 19;

    public static final long DEFAULT_WAREHOUSE_ID = 0L;
    /**
     * 仓库id开始位置
     */
    private static final int WAREHOUSE_ID_START_INDEX = 6;
    /**
     * 仓库id结束位置
     */
    private static final int WAREHOUSE_ID_END_INDEX = 11;


    public static Long getWarehouseIdFromLotId(String lotId) {
        if (lotId.length() < LOT_ID_MIN_LENGTH) {
            return DEFAULT_WAREHOUSE_ID;
        }

        String warehouseId = lotId.substring(WAREHOUSE_ID_START_INDEX, WAREHOUSE_ID_END_INDEX + 1);
        if (!StringUtils.isNumeric(warehouseId)) {
            throw new ImsBusinessException(CommonErrorCode.LOT_ID_ILLEGAL);
        }
        return Long.parseLong(warehouseId);
    }

}
