package com.ddmc.ims.common.enums.manufacture;

import com.ddmc.ims.common.enums.IntegerEnumInterface;

public enum BomSkuTypeEnum implements IntegerEnumInterface {
    /**
     * 成品
     */
    PRODUCT(100, "成品"),

    /**
     * 原料
     */
    MATERIAL(200, "原料"),

    /**
     * 耗材
     */
    CONSUMABLE(300,"耗材")

    ;
    private final Integer code;
    private final String desc;

    BomSkuTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
