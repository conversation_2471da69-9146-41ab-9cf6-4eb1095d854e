package com.ddmc.ims.common.util;

import java.util.Map;
import java.util.concurrent.Callable;
import org.slf4j.MDC;

public class ThreadMdcUtil {

    private ThreadMdcUtil() {
    }

    public static <T> Callable<T> wrap(final Callable<T> callable, final Map<String, String> context) {
        if (callable == null) {
            throw new NullPointerException();
        }
        return () -> {
            if (context == null) {
                MDC.clear();
            } else {
                MDC.setContextMap(context);
            }
            try {
                return callable.call();
            } finally {
                MDC.clear();
            }
        };
    }

    public static Runnable wrap(final Runnable runnable, final Map<String, String> context) {
        if (runnable == null) {
            throw new NullPointerException();
        }
        return () -> {
            if (context == null) {
                MDC.clear();
            } else {
                MDC.setContextMap(context);
            }
            try {
                runnable.run();
            } finally {
                MDC.clear();
            }
        };

    }

}
