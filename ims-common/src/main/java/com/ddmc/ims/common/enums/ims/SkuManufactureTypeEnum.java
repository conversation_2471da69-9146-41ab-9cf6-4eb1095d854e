package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;
import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum SkuManufactureTypeEnum implements IntegerEnumInterface {

    /**
     * 商品类型:0-未知，1-成品，2-原料
     */
    UN_KNOW(0, "未知"),

    PRODUCT(1, "成品"),

    MATERIAL(2, "原料"),


        ;

    private final Integer code;

    private final String desc;

    SkuManufactureTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SkuManufactureTypeEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (SkuManufactureTypeEnum s : SkuManufactureTypeEnum.values()) {
            if (s.getCode().equals(code)) {
                return s;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}
