package com.ddmc.ims.common.util.user;

import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.ThreadLocalContextHelper;

public class UserContextUtils {

    private UserContextUtils() {
    }

    /**
     * 用户上下文线程副本
     */
    private static final ThreadLocal<CurrentUser> userContextThreadLocal;

    static {
        userContextThreadLocal = new ThreadLocal<>();
        ThreadLocalContextHelper.registerClearFunction(UserContextUtils::clear);
    }

    /**
     * 清除用户登录信息 通常在业务逻辑结束后执行（在拦截器执行）
     */
    public static void clear() {
        userContextThreadLocal.remove();
    }

    /**
     * 设置用户登录信息 通常在业务逻辑开始前设置（在拦截器执行）
     */
    public static void setUserContext(CurrentUser currentUser) {
        userContextThreadLocal.set(currentUser);
    }

    /**
     * 获取当前登录用户
     *
     * @return 当前登录用户
     * @throws ImsBusinessException (GatewayErrorCode.BAD_AUTHENTICATION)
     */
    public static CurrentUser getUserContextForceLogin() {
        if (userContextThreadLocal.get() == null) {
            throw new ImsBusinessException("获取用户信息失败");
        }
        return userContextThreadLocal.get();
    }

    /**
     * 获取当前登录用户
     *
     * @return 当前用户信息(可能不存在)
     */
    public static CurrentUser get() {
        return userContextThreadLocal.get();
    }

    public static CurrentUser getSysUserContext() {
        CurrentUser currentUser = new CurrentUser();
        currentUser.setUserId("sys");
        currentUser.setUserName("sys");
        return currentUser;
    }
}
