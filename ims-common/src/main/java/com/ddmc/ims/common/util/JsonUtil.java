package com.ddmc.ims.common.util;

import static org.apache.commons.lang3.exception.ExceptionUtils.rethrow;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public final class JsonUtil {

    private JsonUtil() {
    }

    private static final ObjectMapper mapper = new ObjectMapper();
    private static final String STANDARD_FORMAT = "yyyy-MM-dd HH:mm:ss";

    static {
        // 对象的所有字段全部列入
        mapper.setSerializationInclusion(Include.ALWAYS);
        // 取消默认转换timestamps形式
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        // 忽略空Bean转换json错误
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // 所有日期格式都统一为以下的样式，即yyyy-MM-dd HH:mm:ss
        mapper.setDateFormat(new SimpleDateFormat(STANDARD_FORMAT));
        // 忽略在json字符串中存在，但在Java对象中不存在对应属性的情况。防止报错
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String toJson(Object obj) {
        if (Objects.isNull(obj)) {
            return StringUtils.EMPTY;
        }
        try {
            return obj instanceof String ? (String) obj : mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("对象转json字符串转换失败", e);
        }
        return StringUtils.EMPTY;
    }

    public static <T> T fromJson(String json, Class<T> classOfT) {
        try {
            if (StringUtils.isBlank(json)) {
                return null;
            }
            return mapper.readValue(json, classOfT);
        } catch (Exception e) {
            log.error("json字符串转对象转换失败", e);
        }

        return null;
    }

    public static <T> T fromJson(String json, TypeReference<?> valueTypeRef) {
        try {
            if (StringUtils.isBlank(json)) {
                return null;
            }
            return mapper.readValue(json, valueTypeRef);
        } catch (Exception e) {
            log.error("json字符串转对象转换失败", e);
        }

        return null;
    }

    public static <T> List<T> parseList(String json, Class<T> type) {
        return parseCollection(json, List.class, type);
    }

    public static <T> Set<T> parseSet(String json, Class<T> type) {
        return parseCollection(json, Set.class, type);
    }

    private static <V, C extends Collection<?>, T> V parseCollection(
        String json, Class<C> collectionType, Class<T> elementType) {
        try {
            TypeFactory typeFactory = mapper.getTypeFactory();
            CollectionType javaType = typeFactory.constructCollectionType(collectionType, elementType);
            return mapper.readValue(json, javaType);
        } catch (IOException e) {
            return rethrow(e);
        }
    }

}
