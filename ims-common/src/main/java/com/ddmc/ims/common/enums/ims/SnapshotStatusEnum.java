package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;

/**
 * <AUTHOR>
 */
public enum SnapshotStatusEnum implements IntegerEnumInterface {

    INIT(0, "初始化"),

    COMPLETE(1, "已完成"),

    PROCESSING(2, "处理中")

    ;

    private final Integer code;

    private final String desc;

    SnapshotStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }


    public static SnapshotStatusEnum fromCode(Integer code) {
        for (SnapshotStatusEnum c : SnapshotStatusEnum.values()) {
            if (c.getCode().equals(code)) {
                return c;
            }
        }
        return null;
    }
}
