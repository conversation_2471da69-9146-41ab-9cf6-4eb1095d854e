package com.ddmc.ims.common.bo;


import java.util.Objects;
import lombok.Getter;

@Getter
public class LogicInventoryLocationWithUsageAndTodaySale {

    private final boolean todaySale;
    private final LogicInventoryLocationWithUsage locationWithUsage;

    public LogicInventoryLocationWithUsageAndTodaySale(
        LogicInventoryLocationWithUsage locationWithUsage, boolean todaySale) {
        this.todaySale = todaySale;
        this.locationWithUsage = locationWithUsage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LogicInventoryLocationWithUsageAndTodaySale)) {
            return false;
        }
        LogicInventoryLocationWithUsageAndTodaySale that = (LogicInventoryLocationWithUsageAndTodaySale) o;
        return todaySale == that.todaySale && Objects.equals(locationWithUsage, that.locationWithUsage);
    }

    @Override
    public int hashCode() {
        return Objects.hash(todaySale, locationWithUsage);
    }
}
