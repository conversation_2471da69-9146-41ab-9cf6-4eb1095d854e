package com.ddmc.ims.common.bo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Objects;
import lombok.Getter;

@Getter
public class LogicInventoryLocationWithUsage extends LogicInventoryLocationWithSku{

    private final String usage;

    public LogicInventoryLocationWithUsage(LogicInventoryLocation logicInventoryLocation, Long skuId, String usage) {
        super(logicInventoryLocation, skuId);
        this.usage = usage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LogicInventoryLocationWithUsage)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        LogicInventoryLocationWithUsage location = (LogicInventoryLocationWithUsage) o;
        return Objects.equals(usage, location.usage);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), usage);
    }

    @JsonIgnore
    public LogicInventoryLocationWithSku getLogicInventoryLocationWithSku(){
        return  new LogicInventoryLocationWithSku(this.getLogicInventoryLocation(),
            this.getSkuId());
    }
}
