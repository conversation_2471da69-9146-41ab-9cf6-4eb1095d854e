package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;

/**
 * <AUTHOR>
 */

public enum SnapshotTypeEnum implements IntegerEnumInterface {

    FMS(0, "财务口径"),

    INVENTORY_HOUR(2,"逻辑库存-小时"),

    TRANSFER_HOUR(3,"调拨在途库存-小时")
    ;

    private final Integer code;

    private final String desc;

    SnapshotTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }


    public static SnapshotTypeEnum fromCode(Integer code) {
        for (SnapshotTypeEnum c : SnapshotTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c;
            }
        }
        return null;
    }
}
