package com.ddmc.ims.common.bo;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

@Getter
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SkuIdAndLotIdPair {

    private final Long skuId;

    private final String lotId;

    public SkuIdAndLotIdPair(Long skuId, String lotId) {
        this.skuId = skuId;
        this.lotId = lotId;
    }
}
