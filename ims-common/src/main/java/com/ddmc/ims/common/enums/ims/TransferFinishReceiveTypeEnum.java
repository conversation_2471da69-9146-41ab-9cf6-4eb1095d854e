package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;

/**
 * <AUTHOR>
 */

public enum TransferFinishReceiveTypeEnum implements IntegerEnumInterface {

    NO(0, "未完成"),

    YES(1, "已完成");

    private final Integer code;

    private final String desc;

    TransferFinishReceiveTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
