package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;

/**
 * <AUTHOR>
 */
public enum WarehouseTypeEnum implements IntegerEnumInterface {

    STORE_TYPE_GENERAL(1, "总仓存储"),
    STORE_TYPE_TEMP(2, "临时存储"),
    STORE_TYPE_SALE(3, "销售存储"),
    STORE_TYPE_TEST(4, "测试存储"),
    STORE_TYPE_SCRAP(5, "回收存储"),
    STORE_TYPE_CENTER(6, "前置中心仓库")
    ;

    private final Integer code;

    private final String desc;

    WarehouseTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescFromCode(Integer code, String defaultIfNull) {
        WarehouseTypeEnum barCodeEnum = fromCode(code);
        if (barCodeEnum == null) {
            return defaultIfNull;
        } else {
            return barCodeEnum.desc;
        }
    }

    public static WarehouseTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WarehouseTypeEnum warehouseTypeEnum : WarehouseTypeEnum.values()) {
            if (warehouseTypeEnum.code.equals(code)) {
                return warehouseTypeEnum;
            }
        }
        return null;
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}

