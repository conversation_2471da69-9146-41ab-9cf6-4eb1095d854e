package com.ddmc.ims.common.constant;

import com.ddmc.ims.common.bo.LogicInventoryLocation;

/**
 * 业务常量静态类
 */
public final class CommonConstants {

    private CommonConstants() {
    }

    public static final String RT = "RT";

    /**
     * 数字-1
     */
    public static final Long NUMBER_NEGATIVE_NOT = -1L;
    /**
     * 数字60
     */
    public static final int NUMBER_SIXTY = 60;

    /**
     * 数字10
     */
    public static final int NUMBER_TEN = 10;

    /**
     * 批量更新每批最大数据量
     */
    public static final int BATCH_UPDATE_DB_100 = 100;


    /**
     * 批量更新每批最大数据量
     */
    public static final int BATCH_UPDATE_DB_300 = 300;

    /**
     * 批量插入每批最大数据量
     */
    public static final int BATCH_INSERT_DB_100 = 100;

    /**
     * 批量插入每批最大数据量
     */
    public static final int BATCH_INSERT_DB_300 = 300;


    /**
     * 批量查询每批最大数据量
     */
    public static final int BATCH_SELECT_DB_50 = 50;

    /**
     * 批量查询每批最大数据量
     */
    public static final int BATCH_SELECT_DB_200 = 200;

    /**
     * 批量查询每批最大数据量
     */
    public static final int BATCH_SELECT_STORE_100 = 100;

    /**
     * 批量查询每批最大数据量
     */
    public static final int BATCH_SELECT_STORE_500 = 500;

    public static final int BATCH_SELECT_500 = 500;


    public static final String IMS_DB_TRANSACTION_MANAGER = "imsTransactionManager";

    public static final String IMS_MONITOR_DB_TRANSACTION_MANAGER = "imsMonitorTransactionManager";


    public static final LogicInventoryLocation EMPTY_LOGIC_INVENTORY_LOCATION = new LogicInventoryLocation(-999L,-999L,"DEFAULT");

    //通用逻辑库位编码
    public static final String COMMON_LOGIC_INVENTORY_LOCATION_CODE = "COMMON";

    //加工中逻辑库位编码
    public static final String PROCESSING_LOGIC_INVENTORY_LOCATION_CODE = "PROCESSING";


    public static final String GOOD_PRODUCT = "GOOD_PRODUCT";



    public static final int WAREHOUSE_MOD_LOCK_2048 = 2048;

    public static final String DEFAULT_BOOKING_INVENTORY_CHANGE_TYPE = "DEFAULT";


    /**
     * 确认凭证时单据维度限流资源前缀
     */
    public static final String CONFIRM_INTERFACE_ORDER_TYPE_LIMIT_PREFIX = "credential_";
}
