package com.ddmc.ims.common.errorcode;

import com.ddmc.core.view.compat.CodeMsg;

/**
 * 错误码登记地址：https://cfl.corp.100.me/pages/viewpage.action?pageId=16686184
 */
public interface ErrorCode {

    /**
     * 获取code，三位
     */
    int getCode();

    /**
     * 获取异常消息
     */
    String getMsg();

    /**
     * 获取领域code，不超过三位
     */
    int getDomainCode();


    default int getErrorCode() {
        return 1000000 + this.getDomainCode() * 1000 + getCode();
    }

    default CodeMsg toCodeMsg() {
        return new CodeMsg(getErrorCode(), getMsg());
    }
}
