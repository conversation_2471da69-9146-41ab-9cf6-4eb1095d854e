package com.ddmc.ims.common.util;

import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;

public class RequestIpUtils {

    private static final String UNKNOWN_IP = "unknown";

    private RequestIpUtils() {
    }

    /**
     * @param request 请求
     * @return 获取当前请求的客户端IP地址
     */
    public static String getIPAddress(HttpServletRequest request) {
        //X-Forwarded-For：Squid 服务代理，X-Real-IP：nginx服务代理，Proxy-Client-IP：apache 服务代理，WL-Proxy-Client-IP：weblogic 服务代理，HTTP_CLIENT_IP：有些代理服务器
        return tryGetIp(request, "X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP");
    }

    private static String tryGetIp(HttpServletRequest request, String... headers) {
        for (String header : headers) {
            String ipAddresses = request.getHeader(header);
            if (StringUtils.isNotEmpty(ipAddresses) && !UNKNOWN_IP.equalsIgnoreCase(ipAddresses)) {
                //有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
                return ipAddresses.split(",")[0];
            }
        }
        return request.getRemoteAddr();
    }

}
