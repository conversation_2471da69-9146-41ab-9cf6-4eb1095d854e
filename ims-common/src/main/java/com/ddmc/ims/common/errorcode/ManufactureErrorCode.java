package com.ddmc.ims.common.errorcode;


public enum ManufactureErrorCode implements ErrorCode {

    BOM_PRODUCT_CANNOT_LESS_OR_EQUAL_ZERO(1, "bom中成品的数量不能小于等于0，%s"),
    MANUFACTURE_ORDER_BO_IS_NULL(2, "制造单模型BO不能为空!"),
    QUERY_MANUFACTURE_ERROR(3, "根据计划单号（%s）查询生产计划失败!"),
    MATERIAL_NOT_EXISTS_IN_DEMAND_USAGE_QTY(4, "生成制造单完成时，用途原料明细中没有原料数据"),
    UN_MATERIAL_NOT_EXISTS_IN_DEMAND_USAGE_QTY(5, "反加工原料投料净值和制造单结果不一致"),
    ;

    private static final int DOMAIN_CODE = 8;

    private final int code;
    private final String msg;

    ManufactureErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public int getDomainCode() {
        return DOMAIN_CODE;
    }
}
