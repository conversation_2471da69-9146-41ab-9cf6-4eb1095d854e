package com.ddmc.ims.common.util;

import com.ddmc.core.view.compat.ResponseBaseVo;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 */
@Slf4j
public class ResponseParserUtil {

    private ResponseParserUtil() {
    }

    public static <T> T parseResponseBaseVoNoError(ResponseBaseVo<T> responseBaseVo) {
        if (responseBaseVo != null && responseBaseVo.isSuccess()) {
            return responseBaseVo.getData();
        }
        return null;
    }
}
