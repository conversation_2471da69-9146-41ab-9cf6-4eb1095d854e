package com.ddmc.ims.common.util.user;

import java.util.List;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class CurrentUser {

    /**
     * 登录用户id
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * email
     */
    private String email;

    /**
     * 仓库id
     */
    private List<String> warehouseIds;

    /**
     * sso的通行令牌
     */
    private String ssoToken;

    /**
     * sso通行令牌的有效时间
     */
    private long timeout;

    /**
     * 所有仓库权限
     */
    private boolean hasAllPrivilege = false;
}
