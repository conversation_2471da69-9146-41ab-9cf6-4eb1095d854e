package com.ddmc.ims.common.enums.ims;

import com.ddmc.ims.common.enums.IntegerEnumInterface;

public enum AllocDimensionEnum implements IntegerEnumInterface {

    SKU(0, "占用到货品维度"),

    LOT(1, "占用到效期批次维度"),
    ;

    private final Integer code;

    private final String desc;

    AllocDimensionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }


    public static AllocDimensionEnum fromCode(Integer code) {
        for (AllocDimensionEnum c : AllocDimensionEnum.values()) {
            if (c.getCode().equals(code)) {
                return c;
            }
        }
        return null;
    }
}
