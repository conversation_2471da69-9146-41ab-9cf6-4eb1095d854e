package com.ddmc.ims.common.errorcode;


import lombok.Getter;

/**
 * 仓库相关异常代码.
 */
@Getter
public enum WarehouseErrorCode implements ErrorCode {

    STORE_ID_IS_NULL(1, "仓库id不能为空"),
    ;

    private static final int DOMAIN_CODE = 2;

    private final int code;

    private final String msg;

    WarehouseErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    @Override
    public int getDomainCode() {
        return DOMAIN_CODE;
    }

}
