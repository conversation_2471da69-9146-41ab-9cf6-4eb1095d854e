package com.ddmc.ims.common.enums.common;


import com.ddmc.ims.common.enums.IntegerEnumInterface;

public enum EventEntityStatusEnum implements IntegerEnumInterface {

    DEFAULT(0, "待处理"),
    CONSUMED(1, "已处理"),
    PROCESSING(2, "处理中"),
    ERROR(3, "异常中"),
    ;

    private final Integer code;

    private final String desc;

    EventEntityStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescFromCode(Integer code, String defaultIfNull) {
        EventEntityStatusEnum eventEntityStatusEnum = fromCode(code);
        if (eventEntityStatusEnum == null) {
            return defaultIfNull;
        } else {
            return eventEntityStatusEnum.desc;
        }
    }

    public static EventEntityStatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (EventEntityStatusEnum eventEntityStatusEnum : EventEntityStatusEnum.values()) {
            if (eventEntityStatusEnum.code.equals(code)) {
                return eventEntityStatusEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}