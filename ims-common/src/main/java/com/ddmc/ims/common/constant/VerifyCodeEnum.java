package com.ddmc.ims.common.constant;

import com.ddmc.ims.common.enums.StringEnumInterface;
import lombok.Getter;

/**
 * 校验Code
 *
 * <AUTHOR>
 */
@Getter
public enum VerifyCodeEnum implements StringEnumInterface {


    OCS_COMMON_WAREHOUSE("OCS-COMMON-WAREHOUSE", "仓库信息同步"),

    WMS_ASN_INBOUND("WMS-ASN-INBOUND", "WMS-ASN-入库"),
    WMS_MANUFACTURE_INBOUND("WMS-MANUFACTURE-INBOUND", "WMS-生产制造-入库"),
    WMS_MANUFACTURE_OUTBOUND("WMS_MANUFACTURE_OUTBOUND", "WMS-生产制造-出库"),
    WMS_DO_OUTBOUND("WMS-DO-OUTBOUND", "WMS-DO-出库"),
    WMS_INVOICE_OUTBOUND("WMS-INVOICE-OUTBOUND", "WMS-发货单-出库"),

    LOT_STOCK_CHANGE("LOT-STOCK-CHANGE", "库存异动"),
    MES_MANUFACTURE_ORDER("MES-MANUFACTURE-ORDER", "MES-制造单"),
    FDC_COUNT_CHANGE("FDC_COUNT_CHANGE", "FDC盘点"),
    WMS_COUNT_CHANGE("WMS_COUNT_CHANGE", "WMS盘点"),
    FDC_MANUFACTURE_ORDER("FDC_MANUFACTURE_ORDER", "FDC基础制造"),
    FDC_DO_ORDER("FDC_DO_ORDER", "FDC-C端出库"),
    FDC_OUTBOUND_ORDER("FDC_OUTBOUND_ORDER", "FDC-出库"),
    FDC_INBOUND_ORDER("FDC_INBOUND_ORDER", "FDC-入库"),
    FMS_FINANCE_CERTIFICATE_RESULT("FMS_FINANCE_CERTIFICATE_RESULT", "FMS-凭证回执结果"),

    WAREHOUSE_INVENTORY_COMPARE("WAREHOUSE_INVENTORY_COMPARE", "库存比对"),

    ;

    /**
     * code
     */
    private final String code;

    /**
     * desc
     */
    private final String desc;

    VerifyCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
