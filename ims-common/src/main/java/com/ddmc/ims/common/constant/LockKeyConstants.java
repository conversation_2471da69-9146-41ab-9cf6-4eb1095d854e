package com.ddmc.ims.common.constant;

public final class LockKeyConstants {

    private LockKeyConstants() {
    }

    /**
     * 所有key统一加前缀
     */
    public static final String PREFIX = "scmImsService:";

    /**
     * 仓库变更锁
     */
    public static final String WAREHOUSE_CHANGE_LOCK = ":warehouseChange";

    /**
     * 批次变更锁
     */
    public static final String LOT_CHANGE_LOCK = ":lotChange";

    /**
     * 仓库覆盖门店全局锁
     */
    public static final String WAREHOUSE_STATION_CHANGE_LOCK = ":warehouseStationChange";

    /**
     * 逻辑库位模板保存key
     */
    public static final String ADD_LOGIC_INVENTORY_TEMPLATE = PREFIX + "addLogicInventoryTemplate";

    /**
     * 逻辑库位模板保存key
     */
    public static final String LOGIC_INVENTORY_TEMPLATE = PREFIX + "logicInventoryTemplate";


    /**
     * 库存占用锁
     * allocLock:（仓库id）:（货主id）:（逻辑库位编码）
     */
    public static final String ALLOC_LOCK = PREFIX + "allocLock:%s:%s:%s";

    /**
     * 仓库货品级别锁
     *
     * warehouseSkuLock:(仓库id):(货品id)
     */
    public static final String WAREHOUSE_SKU_LOCK = PREFIX + "warehouseSkuLock:%s:%s";


}
