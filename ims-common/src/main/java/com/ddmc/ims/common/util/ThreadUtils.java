package com.ddmc.ims.common.util;

import com.ddmc.ims.common.exception.ImsBusinessException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class ThreadUtils {

    private static final long THREAD_TIME_OUT = 5;

    private ThreadUtils() {
    }

    /**
     * 获取异步执行结果
     *
     * @param future 结果
     * @param timeout 超时时间
     * @param unit 超时单位
     * @return 获取结果
     */
    public static <T> T getFutureResult(Future<T> future, long timeout, TimeUnit unit) {
        if (Objects.isNull(future)) {
            return null;
        }
        try {
            return future.get(timeout, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("ThreadUtils message[{}]", e.getMessage());
        } catch (ExecutionException e) {
            log.error("ThreadUtils error", e);
        } catch (TimeoutException e) {
            log.error("ThreadUtils timeout message[{}]", e.getMessage());
        }
        return null;
    }

    /**
     * 获取异步执行结果(默认超时5s)
     *
     * @param future 结果
     * @return 获取结果
     */
    public static <T> T getFutureResult(Future<T> future) {
        return getFutureResult(future, THREAD_TIME_OUT, TimeUnit.SECONDS);
    }

    /**
     * 获取异步执行结果(默认超时5s)
     *
     * @param future 结果
     * @return 获取结果
     */
    public static <T> List<T> getFutureListResult(Future<? extends List<T>> future) {
        List<T> futureResult = getFutureResult(future, THREAD_TIME_OUT, TimeUnit.SECONDS);
        if (Objects.isNull(futureResult)) {
            return Collections.emptyList();
        }
        return futureResult;
    }

    /**
     * 获取异步执行结果(默认超时5s)
     *
     * @param future 结果
     * @return 获取结果
     */
    public static <K, V> Map<K, V> getFutureMapResult(Future<? extends Map<K, V>> future) {
        Map<K, V> futureResult = getFutureResult(future, THREAD_TIME_OUT, TimeUnit.SECONDS);
        if (Objects.isNull(futureResult)) {
            return Collections.emptyMap();
        }
        return futureResult;
    }

    public static <T> T getFutureResultAllowException(Future<T> future) {
        return getFutureResultAllowException(future, THREAD_TIME_OUT, TimeUnit.SECONDS);
    }

    /**
     * 获取异步执行结果
     *
     * @param future 结果
     * @param timeout 超时时间
     * @param unit 超时单位
     * @return 获取结果
     */
    public static <T> T getFutureResultAllowException(Future<T> future, long timeout, TimeUnit unit) {
        if (Objects.isNull(future)) {
            return null;
        }
        try {
            return future.get(timeout, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("ThreadUtils message[{}]", e.getMessage());
            throw new ImsBusinessException("ThreadUtils InterruptedException", e);
        } catch (ExecutionException e) {
            log.error("ThreadUtils error", e);
            throw new ImsBusinessException("ThreadUtils ExecutionException", e);
        } catch (TimeoutException e) {
            log.error("ThreadUtils timeout message[{}]", e.getMessage());
            throw new ImsBusinessException("ThreadUtils TimeoutException", e);
        }
    }

    /**
     * 获取异步执行结果(默认超时5s)
     *
     * @param future 结果
     * @return 获取结果
     */
    public static <K, V> Map<K, V> getFutureMapResultAllowException(Future<? extends Map<K, V>> future) {
        Map<K, V> futureResult = getFutureResultAllowException(future, THREAD_TIME_OUT, TimeUnit.SECONDS);
        if (Objects.isNull(futureResult)) {
            return Collections.emptyMap();
        }
        return futureResult;
    }


    /**
     * 获取异步执行结果，抛出异常(默认超时5s)
     *
     * @param future 结果
     * @return 获取结果
     */
    public static <T> List<T> getFutureListResultAllowException(Future<? extends List<T>> future) {
        List<T> futureResult = getFutureResultAllowException(future, THREAD_TIME_OUT, TimeUnit.SECONDS);
        if (Objects.isNull(futureResult)) {
            return Collections.emptyList();
        }
        return futureResult;
    }


}
