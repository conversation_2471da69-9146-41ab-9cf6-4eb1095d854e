package com.ddmc.ims.common.enums.common;


import com.ddmc.ims.common.enums.IntegerEnumInterface;

public enum EventExecuteServiceNameEnum implements IntegerEnumInterface {

    SNAPSHOT_INVENTORY_DIFF(0, "快照差异处理"),
    TRANSFER_OUT_CHANGE_NOTIFY(1, "调拨出库变更通知");

    private final Integer code;

    private final String desc;

    EventExecuteServiceNameEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescFromCode(Integer code, String defaultIfNull) {
        EventExecuteServiceNameEnum eventEntityStatusEnum = fromCode(code);
        if (eventEntityStatusEnum == null) {
            return defaultIfNull;
        } else {
            return eventEntityStatusEnum.desc;
        }
    }

    public static EventExecuteServiceNameEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (EventExecuteServiceNameEnum eventEntityStatusEnum : EventExecuteServiceNameEnum.values()) {
            if (eventEntityStatusEnum.code.equals(code)) {
                return eventEntityStatusEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}