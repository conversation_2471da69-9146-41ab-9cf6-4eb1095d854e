package com.ddmc.ims.common.errorcode;

import com.ddmc.ims.common.exception.ImsBusinessException;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

/**
 * Asserts
 *
 * <AUTHOR>
 */
public final class Asserts {

    private Asserts() {
    }

    public static void isFalse(boolean expression, ErrorCode code) {
        if (expression) {
            throw new ImsBusinessException(code);
        }
    }

    public static void isFalse(boolean expression, ErrorCode code, Object... arguments) {
        if (expression) {
            throw new ImsBusinessException(code, arguments);
        }
    }

    public static void isFalseIfLog(boolean expression, ErrorCode code, LogConsumer logConsumer,
        Object... arguments) {
        Asserts.isTrueIfLog(!expression, code, logConsumer, arguments);
    }

    /**
     * 是否大于
     *
     * @param source source
     * @param target target
     * @param code code
     * @param arguments argument
     * @param <T> T
     */
    public static <T extends Comparable<T>> void isMoreThan(T source, T target, ErrorCode code,
        Object... arguments) {
        Asserts.isTrue(source.compareTo(target) > 0, code, arguments);
    }

    /**
     * 是否大于等于
     *
     * @param source source
     * @param target target
     * @param code code
     * @param arguments argument
     * @param <T> T
     */
    public static <T extends Comparable<T>> void isMoreThanEquals(T source, T target, ErrorCode code,
        Object... arguments) {
        Asserts.isTrue(source.compareTo(target) >= 0, code, arguments);
    }

    /**
     * 是否大于等于
     *
     * @param source source
     * @param target target
     * @param code code
     * @param logConsumer 日志打印
     * @param arguments argument
     * @param <T> T
     */
    public static <T extends Comparable<T>> void isMoreThanEqualsIfLog(T source, T target, ErrorCode code,
        LogConsumer logConsumer,
        Object... arguments) {
        Asserts.isTrueIfLog(source.compareTo(target) >= 0, code, logConsumer, arguments);
    }

    /**
     * 是否小于
     *
     * @param source source
     * @param target target
     * @param code code
     * @param arguments argument
     * @param <T> T
     */
    public static <T extends Comparable<T>> void isLessThan(T source, T target, ErrorCode code,
        Object... arguments) {
        Asserts.isTrue(source.compareTo(target) < 0, code, arguments);
    }

    /**
     * 是否小于等于
     *
     * @param source source
     * @param target target
     * @param code code
     * @param arguments argument
     * @param <T> T
     */
    public static <T extends Comparable<T>> void isLessThanEquals(T source, T target, ErrorCode code,
        Object... arguments) {
        Asserts.isTrue(source.compareTo(target) <= 0, code, arguments);
    }


    /**
     * 是否小于等于
     *
     * @param source source
     * @param target target
     * @param code code
     * @param arguments argument
     * @param <T> T
     */
    public static <T extends Comparable<T>> void isLessThanEqualsIfLog(T source, T target, ErrorCode code,
        LogConsumer logConsumer,
        Object... arguments) {
        Asserts.isTrueIfLog(source.compareTo(target) <= 0, code, logConsumer, arguments);
    }

    /**
     * 是否小于等于
     *
     * @param source source
     * @param target target
     * @param code code
     * @param arguments argument
     * @param <T> T
     */
    public static <T extends Comparable<T>> void isEquals(T source, T target, ErrorCode code,
        Object... arguments) {
        Asserts.isTrue(source.compareTo(target) == 0, code, arguments);
    }

    /**
     * 是否小于等于
     *
     * @param source source
     * @param target target
     * @param code code
     * @param arguments argument
     * @param <T> T
     */
    public static <T extends Comparable<T>> void isEquals(T source, T target, ErrorCode code,
        LogConsumer logConsumer, Object... arguments) {
        Asserts.isTrue(source.compareTo(target) == 0, code, logConsumer, arguments);
    }

    public static void isTrue(boolean expression, ErrorCode code) {
        if (!expression) {
            throw new ImsBusinessException(code);
        }
    }

    public static void isTrueIfLog(boolean expression, ErrorCode code, LogConsumer logConsumer) {
        if (!expression) {
            if (Objects.nonNull(logConsumer)) {
                logConsumer.log();
            }
            throw new ImsBusinessException(code);
        }
    }

    /**
     * 断言
     * 主要使用固定异常信息的模板类型。
     *
     * @param code {@link ErrorCode} message 需要提供模板功能
     * @param expression 布尔表达式
     * @param arguments 占位值
     * @date 2019/11/7 14:00
     */
    public static void isTrueIfLog(boolean expression, ErrorCode code, LogConsumer logConsumer,
        Object... arguments) {
        if (!expression) {
            if (Objects.nonNull(logConsumer)) {
                logConsumer.log();
            }
            throw new ImsBusinessException(code, arguments);
        }
    }

    /**
     * 断言
     * 主要使用固定异常信息的模板类型。
     *
     * @param code {@link ErrorCode} message 需要提供模板功能
     * @param expression 布尔表达式
     * @param arguments 占位参数
     * @date 2019/11/7 14:00
     */
    public static void isTrue(boolean expression, ErrorCode code, Object... arguments) {
        if (!expression) {
            throw new ImsBusinessException(code, arguments);
        }
    }


    /**
     * 断言字符串不为空,如果为空打印日志并throw exception
     *
     * @param source source
     * @param code code
     * @param logConsumer log function
     * @param arguments replace hold arguments
     */
    public static void notEmptyIfLog(String source, ErrorCode code, LogConsumer logConsumer,
        Object... arguments) {
        Asserts.isTrueIfLog(StringUtils.isNotBlank(source), code, logConsumer, arguments);
    }

    /**
     * 断言字符串非空
     *
     * @param source 字符串
     * @param code 异常码
     * @param arguments 占位参数
     */
    public static void notEmpty(String source, ErrorCode code, Object... arguments) {
        Asserts.isTrue(StringUtils.isNotBlank(source), code, arguments);
    }


    public static void isNull(@Nullable Object object, ErrorCode exceptionCode, Object... arguments) {
        Asserts.isTrue(Objects.isNull(object), exceptionCode, arguments);
    }

    public static void isNullIfLog(@Nullable Object object, ErrorCode exceptionCode, LogConsumer logConsumer,
        Object... arguments) {
        Asserts.isTrueIfLog(Objects.isNull(object), exceptionCode, logConsumer, arguments);
    }


    /**
     * 断言非空对象
     *
     * @param object obj
     * @param exceptionCode exceptionCode
     * @param arguments arguments
     */
    public static void notNull(@Nullable Object object, ErrorCode exceptionCode, Object... arguments) {
        Asserts.isTrue(Objects.nonNull(object), exceptionCode, arguments);
    }

    public static void notNullIfLog(@Nullable Object object, ErrorCode exceptionCode, LogConsumer logConsumer,
        Object... arguments) {
        Asserts.isTrueIfLog(Objects.nonNull(object), exceptionCode, logConsumer, arguments);
    }

    public static void notEmpty(Collection<?> collection, ErrorCode errorCode) {
        Asserts.isTrue(CollectionUtils.isNotEmpty(collection), errorCode);
    }

    public static void notEmptyIfLog(Collection<?> collection, ErrorCode errorCode,
        LogConsumer logConsumer) {
        Asserts.isTrueIfLog(CollectionUtils.isNotEmpty(collection), errorCode,
            logConsumer);
    }

    public static void notEmptyIfLog(Collection<?> collection, ErrorCode errorCode,
        LogConsumer logConsumer, Object... arguments) {
        Asserts.isTrueIfLog(!CollectionUtils.isEmpty(collection), errorCode, logConsumer, arguments);
    }

    public static void notEmpty(Collection<?> collection, ErrorCode errorCode, Object... arguments) {
        Asserts.isTrue(CollectionUtils.isNotEmpty(collection),
            errorCode, arguments);
    }


    public static void isEmpty(Collection<?> collection, ErrorCode errorCode, Object... arguments) {
        Asserts.isTrue(CollectionUtils.isEmpty(collection), errorCode, arguments);
    }

    public static void isEmpty(Map<?, ?> map, ErrorCode errorCode, Object... arguments) {
        Asserts.isTrue(MapUtils.isEmpty(map), errorCode, arguments);
    }

    public static void isNotEmpty(Map<?, ?> map, ErrorCode errorCode, Object... arguments) {
        Asserts.isTrue(MapUtils.isNotEmpty(map), errorCode, arguments);
    }

}
