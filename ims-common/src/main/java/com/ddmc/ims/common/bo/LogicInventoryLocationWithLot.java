package com.ddmc.ims.common.bo;

import lombok.Getter;

import java.util.Objects;

@Getter
public class LogicInventoryLocationWithLot extends LogicInventoryLocationWithSku{

    private final String lotId;
    private final String usage;

    public LogicInventoryLocationWithLot(LogicInventoryLocation logicInventoryLocation, Long skuId, String lotId, String usage ) {
        super(logicInventoryLocation, skuId);
        this.lotId = lotId;
        this.usage = usage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        LogicInventoryLocationWithLot that = (LogicInventoryLocationWithLot) o;
        return Objects.equals(lotId, that.lotId) && Objects.equals(usage, that.usage);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), lotId, usage);
    }
}
