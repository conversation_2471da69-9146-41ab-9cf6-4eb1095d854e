package com.ddmc.ims.common.enums.common;

import com.ddmc.ims.common.enums.IntegerEnumInterface;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

public enum YesNoEnum implements IntegerEnumInterface {

    YES(1, "是"),

    NO(0, "否");

    private final Integer code;

    private final String desc;

    YesNoEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public static String getDescFromCode(Integer code, String defaultIfNull) {
        YesNoEnum yesNoEnum = fromCode(code);
        if (yesNoEnum == null) {
            return defaultIfNull;
        } else {
            return yesNoEnum.desc;
        }
    }

    public static YesNoEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (YesNoEnum status : YesNoEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }


    @JsonCreator
    public static com.ddmc.ims.common.enums.common.YesNoEnum forName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (com.ddmc.ims.common.enums.common.YesNoEnum c : values()) {
            if (c.name().equals(name)) {
                return c;
            }
        }
        return null;
    }
}
