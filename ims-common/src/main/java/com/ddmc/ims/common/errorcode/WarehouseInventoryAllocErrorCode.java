package com.ddmc.ims.common.errorcode;


import lombok.Getter;

/**
 * 仓库相关异常代码.
 */
@Getter
public enum WarehouseInventoryAllocErrorCode implements ErrorCode {

    SKU_ID_NOT_MATCH(1, "货品id和入参不匹配"),

    DEMAND_OR_ALLOC_DETAIL_IS_NULL(2, "需求日期或占用明细不能为空"),

    GET_LOCK_ERROR(3, "获取分布式锁失败：%s"),

    DEMAND_TIME_CANNOT_CHANGE_FOR_TRANSFER_ORDER(4, "修改调拨单计划量时需求日期不可变")
    ;

    private static final int DOMAIN_CODE = 2;

    private final int code;

    private final String msg;

    WarehouseInventoryAllocErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    @Override
    public int getDomainCode() {
        return DOMAIN_CODE;
    }

}
