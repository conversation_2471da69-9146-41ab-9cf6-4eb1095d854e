package com.ddmc.ims.request.inventory;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "库存用途详情查询请求")
public class InventoryUsageDetailQueryRequest extends InventoryDetailQueryRequest{


    @ApiModelProperty(value = "调拨在途查询参数")
    private TransferIntransitUsageQueryRequest transferRequest;

    @ApiModelProperty("采购在途查询参数")
    private PurchaseIntransitQueryRequest purchaseRequest;


}
