package com.ddmc.ims.request.inventory;

import com.ddmc.ims.request.common.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import lombok.Data;

@Data
@ApiModel(value = "库存列表查询请求")
public class InventoryQueryRequest extends PageRequest {

    @ApiModelProperty("区域id")
    private Integer zoneId;

    @ApiModelProperty("城市id集合")
    private List<String> cityIds;

    @ApiModelProperty("仓库类型")
    private Integer warehouseType;

    @ApiModelProperty("仓库id集合")
    @NotEmpty(message = "仓库不能为空")
    private List<Long> warehouseIds;

    @ApiModelProperty("货主id集合")
    private List<Long> cargoOwnerIds;

    @ApiModelProperty("采购分类ids")
    private List<Long> manageCategoryIds;

    @ApiModelProperty("负责组集合")
    @Size(max = 5, message = "最多选择5个负责组")
    private List<String> categoryOwnerGroupIds;

    @ApiModelProperty("商品ids")
    private List<Long> skuIds;

    @ApiModelProperty("商品关键词")
    private String skuName;

    @ApiModelProperty("状态：0-正常，1-废弃")
    private Integer status;

    @ApiModelProperty("商品类型：1-成品，2-原料")
    private Integer funcType;

    @ApiModelProperty("是否可售卖 0-否，1-是")
    private Integer skuSaleStatus;

    @ApiModelProperty("储存条件 1-常温 2-冷藏 3-冷冻 4-常温避光 5-冷藏避光 6-冰鲜")
    private List<Long> storageType;

    @ApiModelProperty("是否排除0库存 0-不排除 1-排除")
    private Integer isExclude;


}
