package com.ddmc.ims.request.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 操作人
 */
@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperatorRequest {

    @NotEmpty(message = "用户id不能为空")
    @ApiModelProperty(value = "操作人id", required = true)
    private String uid;

    @ApiModelProperty(value = "操作人名称")
    private String name;

    @ApiModelProperty(value = "操作人时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;

    public OperatorRequest(String uid, String name) {
        this.uid = uid;
        this.name = name;
    }
}
