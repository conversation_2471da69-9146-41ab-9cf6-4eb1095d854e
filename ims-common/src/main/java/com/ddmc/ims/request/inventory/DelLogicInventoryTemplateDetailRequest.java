package com.ddmc.ims.request.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "删除逻辑库位详情请求")
public class DelLogicInventoryTemplateDetailRequest {

    @ApiModelProperty(value = "逻辑库位详情id", required = true)
    @NotNull(message = "逻辑库位详情id不能为空")
    private Long templateDetailId;

}
