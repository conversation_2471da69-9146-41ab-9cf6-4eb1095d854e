package com.ddmc.ims.request.credential.oc;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CredentialDetailRequest {

    @ApiModelProperty("来源逻辑库位")
    @NotNull(message = "来源逻辑库位不能为空")
    private LogicInventoryLocation fromLocation;

    @ApiModelProperty("目标逻辑库位")
    @NotNull(message = "目标逻辑库位不能为空")
    private LogicInventoryLocation toLocation;

    @ApiModelProperty("货品id")
    @NotNull(message = "货品id不能为空")
    private Long skuId;

    @ApiModelProperty("批次id")
    private String lotId;

    @ApiModelProperty("货品数量")
    @NotNull(message = "货品数量不能为空")
    private BigDecimal qty;

    @ApiModelProperty("需求日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date demandDate;

}
