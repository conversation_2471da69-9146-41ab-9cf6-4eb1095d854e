package com.ddmc.ims.request.inventory;

import com.ddmc.ims.common.enums.common.YesNoEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "保存逻辑库位详情请求")
public class SaveLogicInventoryTemplateDetailRequest {

    @ApiModelProperty(value = "逻辑库位模板id", required = true)
    @NotNull(message = "逻辑库位模板id不能为空")
    private Long templateId;

    @ApiModelProperty(value = "逻辑库位模板详情", required = true)
    @NotEmpty(message = "逻辑库位模板详情不能为空")
    @Valid
    private List<LogicInventoryTemplateDetailRequest> templateDetailList;

    @Data
    @ApiModel
    public static class LogicInventoryTemplateDetailRequest {

        @ApiModelProperty(value = "逻辑库位详情id")
        private Long templateDetailId;
        @ApiModelProperty(value = "逻辑库位编码", required = true)
        @NotNull(message = "逻辑库位编码不能为空")
        private String logicInventoryCode;
        @ApiModelProperty(value = "是否关联实物库存", required = true)
        @NotNull(message = "是否关联实物库存不能为空")
        private YesNoEnum refRealInventory;
    }


}
