package com.ddmc.ims.request.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "库存详情查询请求")
public class InventoryDetailQueryRequest{

    @NotNull(message = "货主id不能为空")
    @ApiModelProperty("货主id")
    private Long cargoOwnerId;

    @NotNull(message = "仓库id不能为空")
    @ApiModelProperty("仓库id")
    private Long warehouseId;

    @NotNull(message = "商品id不能为空")
    @ApiModelProperty("商品id")
    private Long skuId;

    @NotBlank(message = "逻辑库位编码不能为空")
    @ApiModelProperty("逻辑库位编码")
    private String logicInventoryLocationCode;

    @NotBlank(message = "类型编码不能为空")
    @ApiModelProperty("类型编码")
    private String typeCode;

}
