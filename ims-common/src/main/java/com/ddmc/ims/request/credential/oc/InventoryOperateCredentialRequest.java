package com.ddmc.ims.request.credential.oc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class InventoryOperateCredentialRequest {

    @ApiModelProperty("幂等主键")
    @NotNull(message = "幂等主键id不能为空")
    private String idempotentId;

    @ApiModelProperty("订单来源")
    @NotNull(message = "订单来源不能为空")
    private String orderSource;

    @ApiModelProperty("订单来源单号")
    @NotNull(message = "订单来源单号不能为空")
    private String orderNo;

    @ApiModelProperty("订单类型")
    @NotNull(message = "订单类型不能为空")
    private String orderType;

    @ApiModelProperty("订单操作类型")
    @NotNull(message = "订单操作类型不能为空")
    private Integer orderOperateType;

    @ApiModelProperty("业务时间")
    @NotNull(message = "业务时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date businessTime;

    @ApiModelProperty("执行单号来源")
    private String exeOrderSource;

    @ApiModelProperty("执行单号")
    private String exeOrderNo;

    @ApiModelProperty("唯一单号")
    private String seqNo;

    @ApiModelProperty("是否库存异动")
    protected Integer isStockChange;

    @ApiModelProperty("仓库id")
    @NotNull(message = "仓库id不能为空")
    private Long warehouseId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("归结日期")
    private Date dayEndTime;
}
