package com.ddmc.ims.request.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "调拨在途明细查询请求")
public class TransferIntransitQueryRequest {

    @ApiModelProperty(value = "计划出库日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectOutTime;


}
