package com.ddmc.ims.request.inventory;

import com.ddmc.ims.request.common.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class QueryLogicInventoryTemplateListRequest extends PageRequest {

    @ApiModelProperty(value = "仓库主体code")
    private String warehouseCorporationCode;

    @ApiModelProperty(value = "货主id")
    private Long cargoOwnerId;

    @ApiModelProperty(value = "仓库类型")
    private Integer warehouseType;

}
