package com.ddmc.ims.request.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.Valid;
import lombok.Data;

@Data
@ApiModel(value = "库存单据详情查询请求")
public class InventoryOrderDetailQueryRequest extends InventoryDetailQueryRequest {


    @ApiModelProperty("库存总量查询参数")
    private WarehouseSkuInventoryQueryRequest inventoryRequest;

    @ApiModelProperty("待发量查询参数")
    private WaitAllocQueryRequest waitAllocRequest;

    @ApiModelProperty("调拨在途查询参数")
    private TransferIntransitQueryRequest transferRequest;

    @Valid
    @ApiModelProperty("采购在途查询参数")
    private PurchaseIntransitQueryRequest purchaseRequest;

    @ApiModelProperty("预占库存查询参数")
    private PreemptInventoryQueryRequest preemptRequest;

}
