package com.ddmc.ims.request.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class QueryLogicInventoryTemplateDetailRequest {

    @ApiModelProperty(value = "仓库主体code", required = true)
    @NotNull(message = "仓库主体code不能为空")
    private String warehouseCorporationCode;

    @ApiModelProperty(value = "货主id", required = true)
    @NotNull(message = "货主id不能为空")
    private Long cargoOwnerId;

    @ApiModelProperty(value = "仓库类型", required = true)
    @NotNull(message = "仓库类型不能为空")
    private Integer warehouseType;

}
