package com.ddmc.ims.request.credential.oc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OutboundCredentialRequest extends InventoryOperateCredentialRequest {

 @ApiModelProperty("配置模式")
 private Integer deliveryMode;

 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
 private Date expectInTime;

 @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
 private Date expectOutTime;

 private List<CredentialDetailRequest> operateDetails;

}
