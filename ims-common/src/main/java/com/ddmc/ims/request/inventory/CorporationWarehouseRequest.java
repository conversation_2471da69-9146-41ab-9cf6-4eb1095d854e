package com.ddmc.ims.request.inventory;

import com.ddmc.ims.common.enums.ims.WarehouseStatusEnum;
import com.ddmc.ims.request.common.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "合作仓库信息查询请求")
public class CorporationWarehouseRequest extends PageRequest {

    @ApiModelProperty(value = "仓库主体code", required = true)
    @NotNull(message = "仓库主体code不能为空")
    private String warehouseCorporationCode;

    @ApiModelProperty(value = "货主id", required = true)
    @NotNull(message = "货主id不能为空")
    private Long cargoOwnerId;

    @ApiModelProperty(value = "仓库类型", required = true)
    @NotNull(message = "仓库类型不能为空")
    private Integer warehouseType;

    @ApiModelProperty(value = "区域id")
    private Integer zoneId;

    @ApiModelProperty(value = "城市id")
    private String cityId;

    @ApiModelProperty(value = "仓库状态")
    private WarehouseStatusEnum status;

}
