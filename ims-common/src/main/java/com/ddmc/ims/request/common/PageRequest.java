package com.ddmc.ims.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.Positive;
import lombok.Data;

@Data
@ApiModel
public class PageRequest {

    @Positive(message = "当前页码必须是正整数")
    @ApiModelProperty(value = "页码", example = "1")
    private int pageNo = 1;

    @Positive(message = "每页条数必须是正整数")
    @ApiModelProperty(value = "页数", example = "20")
    private int pageSize = 20;

    @ApiModelProperty(hidden = true)
    public int getStartIndex() {
        return (pageNo - 1) * pageSize;
    }

    protected String filterEmpty(String content) {
        return "".equals(content) ? null : content;
    }

    public int skipCount() {
        return pageSize * (pageNo - 1);
    }
}
