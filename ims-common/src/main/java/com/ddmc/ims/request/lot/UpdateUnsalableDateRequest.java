package com.ddmc.ims.request.lot;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "更新临期日期请求信息")
public class UpdateUnsalableDateRequest {

    @ApiModelProperty(value = "生产日期")
    @NotNull(message = "生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date manufactureDate;

    @ApiModelProperty(value = "货品id")
    @NotNull(message = "货品id不能为空")
    private Long skuId;
}
