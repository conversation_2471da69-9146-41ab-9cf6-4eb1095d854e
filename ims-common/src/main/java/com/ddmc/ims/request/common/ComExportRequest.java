package com.ddmc.ims.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("PMS导出参数类")
public class ComExportRequest<T> extends PageRequest {

    @NotNull(message = "任务类型不能为空")
    @ApiModelProperty(value = "任务类型", required = true)
    private String taskType;

    @ApiModelProperty(value = "导出文件类型：CSV，TXT", required = true)
    private String exportFileTypeEnum;

    @Valid
    @ApiModelProperty("参数")
    @NotNull(message = "'查询参数'不能为空")
    private T params;

}
