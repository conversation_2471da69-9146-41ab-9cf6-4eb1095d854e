package com.ddmc.ims.request.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(value = "采购在途明细查询请求")
public class PurchaseIntransitQueryRequest {


    @NotNull(message = "期望到货开始日期不能为空")
    @ApiModelProperty(value = "期望到货开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectArriveStartTime;


    @NotNull(message = "期望到货结束日期不能为空")
    @ApiModelProperty(value = "期望到货结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectArriveEndTime;


}
