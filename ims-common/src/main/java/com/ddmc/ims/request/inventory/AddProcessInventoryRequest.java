package com.ddmc.ims.request.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class AddProcessInventoryRequest {

    @ApiModelProperty(value = "仓库id", required = true)
    @NotNull(message = "仓库id不能为空")
    private Long warehouseId;
    @ApiModelProperty(value = "货品id", required = true)
    @NotNull(message = "货品id不能为空")
    private Long skuId;
    @ApiModelProperty(value = "数量", required = true)
    @NotNull(message = "数量不能为空")
    private BigDecimal qty;
}
