package com.ddmc.ims.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@Data
@ApiModel("附件上传信息")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UploadFileInfo {

    @ApiModelProperty("附件id")
    private Long attachmentId;

    @ApiModelProperty("附件标题")
    @Size(max = 64, message = "附件标题不能超过64个字符")
    private String title;

    @ApiModelProperty("文件url")
    @NotEmpty(message = "文件url不能为空")
    @Size(max = 255, message = "url长度不能超过256个字符")
    private String url;
}
