package com.ddmc.ims.request.credential.oc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class InboundCredentialRequest extends InventoryOperateCredentialRequest {

    @ApiModelProperty("期望到库")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectArriveTime;

    @ApiModelProperty("操作详情")
    private List<CredentialDetailRequest> operateDetails;

}
