package com.ddmc.ims.request.snopshot;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "财务日结日期请求")
public class DayEndTimeNoticeRequest {

    @ApiModelProperty(value = "日结日期")
    @NotNull(message = "日结日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dayEndTime;

    @ApiModelProperty(value = "状态")
    @NotNull(message = "状态")
    private String status;
}
