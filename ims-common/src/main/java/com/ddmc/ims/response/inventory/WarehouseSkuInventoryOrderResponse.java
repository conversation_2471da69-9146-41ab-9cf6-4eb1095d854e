package com.ddmc.ims.response.inventory;

import com.ddmc.ims.common.view.BaseInfoInjectAble;
import com.ddmc.ims.common.view.BaseInfoType;
import com.ddmc.ims.common.view.InjectProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "库存总量单据明细响应")
public class WarehouseSkuInventoryOrderResponse implements BaseInfoInjectAble {

    @ApiModelProperty(value = "批次id")
    private String lotId;

    @ApiModelProperty(value = "批次库存数量")
    private BigDecimal qty;

    @ApiModelProperty("入库日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkInDate;

    @ApiModelProperty("生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date manufactureDate;

    @ApiModelProperty("临期日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date unsalableDate;

    @ApiModelProperty("供应商id")
    private Long vendorId;

    @ApiModelProperty("供应商")
    @InjectProperty(type = BaseInfoType.VENDOR, id = "vendorId", property = "vendorName")
    private String vendorName;

    @ApiModelProperty("产地")
    private String productionPlace;

    @ApiModelProperty("来源单号")
    private String origNo;

}
