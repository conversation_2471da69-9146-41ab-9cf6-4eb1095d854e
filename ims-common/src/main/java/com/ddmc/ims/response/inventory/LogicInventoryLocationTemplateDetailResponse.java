package com.ddmc.ims.response.inventory;

import com.ddmc.ims.common.enums.common.YesNoEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class LogicInventoryLocationTemplateDetailResponse {

    @ApiModelProperty(value = "逻辑库位详情id")
    private Long templateDetailId;
    @ApiModelProperty(value = "模板id")
    private Long templateId;
    @ApiModelProperty(value = "逻辑库位编码code")
    private String logicInventoryCode;
    @ApiModelProperty(value = "逻辑库位编码名称")
    private String logicInventoryCodeName;
    @ApiModelProperty(value = "是否关联实物库存")
    private YesNoEnum refRealInventory;
    @ApiModelProperty(value = "是否关联实物库存描述")
    private String refRealInventoryDesc;


}
