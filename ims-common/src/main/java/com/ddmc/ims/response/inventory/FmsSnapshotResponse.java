package com.ddmc.ims.response.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "财务快照响应")
@Builder
public class FmsSnapshotResponse {

    @ApiModelProperty(value = "快照日期")
    @NotNull(message = "快照日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date snapshotDate;

    @ApiModelProperty(value = "仓库id")
    @NotNull(message = "仓库id不能为空")
    private Long warehouseId;


    @ApiModelProperty(value = "仓库id")
    private Long skuId;



    @ApiModelProperty(value = "期末数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "当日入库数量")
    private BigDecimal inQty;


    @ApiModelProperty(value = "当日出库数量")
    private BigDecimal outQty;


}
