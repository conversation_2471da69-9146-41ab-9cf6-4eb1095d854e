package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class LogicInventoryLocationTemplateResponse {

    @ApiModelProperty(value = "模板id")
    private Long templateId;
    @ApiModelProperty(value = "仓库主体编码")
    private String warehouseCorporationCode;
    @ApiModelProperty(value = "仓库主体名称")
    private String warehouseCorporationName;
    @ApiModelProperty(value = "货主id")
    private Long cargoOwnerId;
    @ApiModelProperty(value = "货主编码")
    private String cargoOwnerCode;
    @ApiModelProperty(value = "货主名称")
    private String cargoOwnerName;
    @ApiModelProperty(value = "仓库类型")
    private Integer warehouseType;
    @ApiModelProperty(value = "仓库类型描述")
    private String warehouseTypeDesc;

}
