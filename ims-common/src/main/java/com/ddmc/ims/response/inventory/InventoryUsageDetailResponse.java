package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "库存用途详情响应")
public class InventoryUsageDetailResponse {


    @ApiModelProperty(value = "库存用途详情")
    private List<WarehouseSkuInventoryUsageResponse> inventoryDetailList;

    @ApiModelProperty(value = "待发量用途详情")
    private List<LogicInventoryUsageDetailResponse> waitAllocDetailList;

    @ApiModelProperty(value = "调拨在途用途详情")
    private List<TransferIntransitUsageResponse> transferDetailList;

    @ApiModelProperty(value = "采购在途用途详情")
    private List<LogicInventoryUsageDetailResponse> purchaseDetailList;

    @ApiModelProperty(value = "预占库存用途详情")
    private List<LogicInventoryUsageDetailResponse> preemptDetailList;


}
