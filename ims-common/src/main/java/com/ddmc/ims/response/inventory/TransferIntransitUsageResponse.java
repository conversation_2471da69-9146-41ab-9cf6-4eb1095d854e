package com.ddmc.ims.response.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "调拨在途用途明细响应")
public class TransferIntransitUsageResponse{


    @ApiModelProperty("用途编码")
    private String usageCode;

    @ApiModelProperty("用途编码名称")
    private String usageCodeName;

    @ApiModelProperty(value = "计划出库日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectOutTime;

    @ApiModelProperty(value = "已发未收数量")
    private BigDecimal intransitQty;

    @ApiModelProperty(value = "计划未出数量")
    private BigDecimal waitAllocQty;


}
