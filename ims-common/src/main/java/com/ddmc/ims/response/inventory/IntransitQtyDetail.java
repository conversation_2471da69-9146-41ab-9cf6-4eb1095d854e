package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "数量详情")
public class IntransitQtyDetail {

    @ApiModelProperty(value = "总量")
    private BigDecimal totalQty;

    @ApiModelProperty(value = "用途数量详情")
    private List<LogicInventoryUsageDetailResponse> usageDetailResponseList;


}
