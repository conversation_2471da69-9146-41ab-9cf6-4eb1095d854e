package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class CorporationAndCargoOwnerResponse {

    @ApiModelProperty(value = "合作主体编号")
    private String warehouseCorporationCode;

    @ApiModelProperty(value = "合作主体名称")
    private String corporationName;

    @ApiModelProperty(value = "货主信息")
    private List<CargoOwnerResponse> cargoOwnerList;



}



