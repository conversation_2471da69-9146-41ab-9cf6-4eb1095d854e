package com.ddmc.ims.response.inventory;

import com.ddmc.ims.common.view.BaseInfoInjectAble;
import com.ddmc.ims.common.view.BaseInfoType;
import com.ddmc.ims.common.view.InjectProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "待发量单据明细响应")
public class WaitAllocOrderResponse implements BaseInfoInjectAble {


    @ApiModelProperty(value = "作业单号")
    private String orderNo;

    @ApiModelProperty(value = "入库仓id")
    private Long toWarehouseId;

    @ApiModelProperty(value = "入库仓名称")
    @InjectProperty(type = BaseInfoType.STORE, id = "toWarehouseId", property = "name")
    private String toWarehouseName;

    @ApiModelProperty(value = "计划出库日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectOutTime;

    @ApiModelProperty(value = "业务类型")
    private Integer deliveryMode;

    @ApiModelProperty(value = "业务类型名称：一配、二配")
    private String deliveryModeName;

    @ApiModelProperty(value = "计划出库数量详情")
    private IntransitQtyDetail waitAllocDetail;

}
