package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "库存单据详情响应")
public class InventoryOrderDetailResponse {


    @ApiModelProperty(value = "库存总量详情")
    private List<WarehouseSkuInventoryOrderResponse> inventoryList;

    @ApiModelProperty(value = "待发量详情")
    private List<WaitAllocOrderResponse> waitAllocList;

    @ApiModelProperty(value = "调拨在途详情")
    private List<TransferIntransitOrderResponse> transferList;

    @ApiModelProperty(value = "采购在途详情")
    private List<PurchaseIntransitOrderResponse> purchaseList;

    @ApiModelProperty(value = "预占库存详情")
    private List<PreemptInventoryOrderResponse> preemptList;


}
