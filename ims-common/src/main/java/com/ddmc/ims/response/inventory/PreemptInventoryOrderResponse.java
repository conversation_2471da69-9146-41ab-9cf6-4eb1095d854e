package com.ddmc.ims.response.inventory;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "预占库存单据明细响应")
public class PreemptInventoryOrderResponse {

    @ApiModelProperty(value = "单号")
    private String orderNo;

    /**
     * @see com.ddmc.ims.common.enums.ims.OrderTypeEnum
     */
    @ApiModelProperty(value = "单据类型")
    private String orderType;

    @ApiModelProperty(value = "库存需求日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date demandTime;

    @ApiModelProperty(value = "占用数量")
    private BigDecimal allocQty;

}
