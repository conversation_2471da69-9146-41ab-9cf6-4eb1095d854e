package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
@ApiModel(value = "库存总量用途详情")
public class WarehouseSkuInventoryUsageResponse{


    @ApiModelProperty("用途编码")
    private String usageCode;

    @ApiModelProperty("用途编码名称")
    private String usageCodeName;

    @ApiModelProperty("可用数量")
    private BigDecimal freeQty;

    @ApiModelProperty("冻结数量")
    private BigDecimal frozenQty;


}
