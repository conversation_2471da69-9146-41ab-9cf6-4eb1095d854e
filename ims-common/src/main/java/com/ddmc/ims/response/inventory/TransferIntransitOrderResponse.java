package com.ddmc.ims.response.inventory;

import com.ddmc.ims.common.view.BaseInfoInjectAble;
import com.ddmc.ims.common.view.BaseInfoType;
import com.ddmc.ims.common.view.InjectProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "调拨在途单据明细响应")
public class TransferIntransitOrderResponse implements BaseInfoInjectAble {

    @ApiModelProperty(value = "作业单号")
    private String orderNo;

    @ApiModelProperty(value = "出库仓id")
    private Long fromWarehouseId;

    @ApiModelProperty(value = "出库仓名称")
    @InjectProperty(type = BaseInfoType.STORE, id = "fromWarehouseId", property = "name")
    private String fromWarehouseName;

    @ApiModelProperty(value = "计划出库日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectOutTime;

    @ApiModelProperty(value = "业务类型")
    private Integer deliveryMode;

    @ApiModelProperty(value = "业务类型名称：一配、二配")
    private String deliveryModeName;

    @ApiModelProperty(value = "已发未收数量详情")
    private IntransitQtyDetail intransitDetail;

    @ApiModelProperty(value = "计划未出数量详情")
    private IntransitQtyDetail waitAllocDetail;


}
