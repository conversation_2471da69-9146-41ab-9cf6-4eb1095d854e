package com.ddmc.ims.response.base;

import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 参数校验结果类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResult {

    /**
     * 校验结果是否有错
     */
    private boolean hasErrors;

    /**
     * 校验错误信息
     * <p>字段 : 错误信息</p>
     */
    private Map<String, String> errorMsg;

    /**
     * 转换错误信息
     *
     * @return 错误信息
     */
    public String toErrorMsgString() {

        if (MapUtils.isEmpty(errorMsg)) {
            return StringUtils.EMPTY;
        }
        return errorMsg.entrySet().stream().map(e -> e.getKey() + ":" + e.getValue()).collect(Collectors.joining(","));
    }


}
