package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "逻辑库位数量类型详情响应")
public class LogicInventoryQtyTypeResponse {

    @ApiModelProperty("类型编码")
    private String typeCode;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("类型总数量")
    private BigDecimal totalQty;

    @ApiModelProperty("该类型下的用途详情集合")
    private List<LogicInventoryUsageDetailResponse> usageDetailList;


}
