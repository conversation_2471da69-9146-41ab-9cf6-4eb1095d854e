package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "逻辑库位详情响应")
public class LogicInventoryDetailResponse {


    @ApiModelProperty("逻辑库位编码")
    private String logicInventoryLocationCode;

    @ApiModelProperty("逻辑库位编码名称")
    private String logicInventoryLocationName;

    @ApiModelProperty("逻辑库位数量种类详情集合")
    private List<LogicInventoryQtyTypeResponse> logicInventoryQtyTypeList;


}
