package com.ddmc.ims.response.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel(value = "采购在途单据明细响应")
public class PurchaseIntransitOrderResponse {

    @ApiModelProperty(value = "采购单号")
    private String orderNo;

    @ApiModelProperty(value = "预计到货日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectArriveTime;

    @ApiModelProperty(value = "预约到货日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vendorOrderTime;

    @ApiModelProperty(value = "采购计划详情")
    private IntransitQtyDetail planDetail;

    @ApiModelProperty(value = "采购在途详情")
    private IntransitQtyDetail intransitDetail;

    @ApiModelProperty(value = "采购已入详情")
    private IntransitQtyDetail inDetail;

}
