package com.ddmc.ims.response.inventory;

import com.ddmc.ims.common.view.BaseInfoInjectAble;
import com.ddmc.ims.common.view.BaseInfoType;
import com.ddmc.ims.common.view.InjectProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "库存列表查询响应")
public class InventoryQueryResponse implements BaseInfoInjectAble {

    @ApiModelProperty("区域id")
    private String zoneId;

    @ApiModelProperty("区域名称")
    @InjectProperty(type = BaseInfoType.ZONE, id = "zoneId", property = "zoneName")
    private String zoneName;

    @ApiModelProperty("城市id")
    private String cityId;

    @ApiModelProperty("城市名称")
    @InjectProperty(type = BaseInfoType.CITY, id = "cityId", property = "name")
    private String cityName;

    @ApiModelProperty("仓库id")
    private Long warehouseId;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("仓库类型")
    private Integer warehouseType;

    @ApiModelProperty("仓库类型名称")
    private String warehouseTypeName;

    @ApiModelProperty("货主id")
    private Long cargoOwnerId;

    @ApiModelProperty("货主名称")
    @InjectProperty(type = BaseInfoType.CARGO_OWNER, id = "cargoOwnerId", property = "cargoOwnerName")
    private String cargoOwnerName;

    @ApiModelProperty("商品id")
    private Long skuId;

    @ApiModelProperty("商品名称")
    private String skuName;

    @ApiModelProperty("商品类型：成品，原料")
    private String skuFuncType;

    @ApiModelProperty("一级采购分类id")
    private Long onePurchaseCategoryId;

    @ApiModelProperty("一级采购分类名称")
    @InjectProperty(type = BaseInfoType.CATEGORY_NAME, id = "onePurchaseCategoryId", property = "text")
    private String onePurchaseCategoryName;

    @ApiModelProperty(value = "二级采购分类id")
    private Long twoPurchaseCategoryId;

    @ApiModelProperty("二级采购分类名称")
    @InjectProperty(type = BaseInfoType.CATEGORY_NAME, id = "twoPurchaseCategoryId", property = "text")
    private String twoPurchaseCategoryName;

    @ApiModelProperty(value = "三级采购分类id")
    private Long threePurchaseCategoryId;

    @ApiModelProperty(value = "三级采购分类名称")
    @InjectProperty(type = BaseInfoType.CATEGORY_NAME, id = "threePurchaseCategoryId", property = "text")
    private String threePurchaseCategoryName;

    @ApiModelProperty("负责组id")
    private String responsibleGroupId;

    @ApiModelProperty("负责组名称")
    @InjectProperty(type = BaseInfoType.RESPONSIBLE_GROUP, id = "responsibleGroupId", property = "name")
    private String responsibleGroupName;

    @ApiModelProperty("逻辑库位详情")
    private List<LogicInventoryDetailResponse> logicInventoryDetailList;


}
