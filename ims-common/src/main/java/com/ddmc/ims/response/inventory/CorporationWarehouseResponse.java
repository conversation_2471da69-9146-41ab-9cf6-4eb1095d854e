package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class CorporationWarehouseResponse {

    @ApiModelProperty(value = "仓库id")
    private Long id;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "区域id")
    private Integer zoneId;

    @ApiModelProperty(value = "区域名称")
    private String zoneName;


    @ApiModelProperty(value = "城市id")
    private String cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;


    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态")
    private String statusDesc;


    @ApiModelProperty(value = "仓库类型:1总仓存储2临时存储3销售4测试5报废存储")
    private Integer type;

    @ApiModelProperty(value = "仓库类型描述")
    private String typeDesc;


}
