package com.ddmc.ims.response.inventory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
@ApiModel(value = "逻辑库位用途详情")
public class LogicInventoryUsageDetailResponse {

    @ApiModelProperty("用途编码")
    private String usageCode;

    @ApiModelProperty("用途编码名称")
    private String usageCodeName;

    @ApiModelProperty("数量")
    private BigDecimal qty;

}
