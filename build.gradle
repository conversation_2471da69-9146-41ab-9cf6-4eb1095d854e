buildscript {
    apply from:"${rootDir}/gradle/include/ext.gradle"

    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'//10秒钟，可自定义，如10,'minutes'10分钟，10,'hours'10小时
    }

    repositories {
        maven {
            allowInsecureProtocol = true
            url nexusUrl
        }
    }

    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}"
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.8"
        classpath "com.diffplug.gradle:goomph:3.40.0"
        classpath "com.ddmc:middleware-gradle-plugin:${middlewarePluginVersion}"
    }
}

apply from: "${rootDir}/gradle/include/sonar.gradle"

allprojects {
    group = 'com.ddmc'
    version = '1.0.0-SNAPSHOT'

    repositories {
        mavenLocal()
        maven {
            allowInsecureProtocol = true
            url nexusUrl
        }
        mavenCentral()
        maven {
            allowInsecureProtocol = true
            url "https://plugins.gradle.org/m2/"
        }
    }

    apply plugin: "com.diffplug.eclipse.apt"
}

subprojects {
    apply from: "${rootDir}/gradle/include/public.gradle"
    apply from: "${rootDir}/gradle/include/checkstyle.gradle"
    apply plugin: 'com.ddmc.sdkVersionControl'

    MiddlewareConfig {
        // warn or error，默认warn，error直接抛异常阻断执行
        conflictSeverityLevel = "warn"
        // 默认为false，开启后执行checkConflict会在当前目录下生成conflict.txt文件，与console打印的内容一致
        conflictFileEnable = false
        // 需要排除检查的jar包，多个用分号分隔
        excludePackage ="org.eclipse;org.jetbrains.kotlin"
        // bom版本信息，格式：group:module:version，分号间隔，与引入依赖格式一致
        bomInfo = "com.ddmc:middleware-bom:${middlewareBomVersion};" +
                "com.ddmc:third-party-bom:${thirdPartyBomVersion};" +
                "com.ddmc:middleware-gradle-plugin:${middlewarePluginVersion}"
        // warn or error，默认warn，error直接抛异常阻断执行
        snapshotSeverityLevel = "warn"
    }

    dependencies {
        annotationProcessor("org.projectlombok:lombok:1.18.20")
        compileOnly("org.projectlombok:lombok:1.18.20")
        testAnnotationProcessor('org.projectlombok:lombok:1.18.20')
        testCompileOnly("org.projectlombok:lombok:1.18.20")
        testImplementation group: 'junit', name: 'junit', version: '4.12'
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
    }

    task sourceJar(type: Jar, dependsOn: classes) {
        archiveClassifier = 'sources'
        from sourceSets.main.allSource
    }

    task javadocJar(type: Jar, dependsOn: javadoc) {
        archiveClassifier = 'javadoc'
        from javadoc.destinationDir
    }

    tasks.withType(Javadoc) {
        options.addStringOption('Xdoclint:none', '-quiet')
        options.addStringOption('encoding', 'UTF-8')
        options.addStringOption('charSet', 'UTF-8')
    }

    publishing {
        if (project.gradle.getGradleVersion() != '7.2') {
            throw new Exception('Your gradle version must be 7.2')
        }
        apply from: "${rootDir}/gradle/include/conf.gradle"

        publications{
            jar(MavenPublication) {
                // MavenPublication 中有一些属性，主要包括groupId，artifactId，version,from,artifact
                // 其中groupId，artifactId，version，artifact都是选填的，不填默认去取项目的相关信息；
                groupId project.group // 项目的group
                artifactId project.name //项目name
                version project.version
                //如果打成war包填写components.web，如果打成jar包填写components.java
                from components.java
                versionMapping {
                    usage('java-api') {
                        fromResolutionOf('runtimeClasspath')
                    }
                    usage('java-runtime') {
                        fromResolutionResult()
                    }
                }
                artifact sourceJar
                artifact javadocJar
            }
        }

        repositories {
            maven {
                allowInsecureProtocol = true
                if (project.version.endsWith('-SNAPSHOT')) {
                    url = nexusSnapshotUrl
                } else {
                    url = nexusReleaseUrl
                }
                credentials {
                    username nexusUsername
                    password nexusPassword
                }
            }
        }
    }
}

apply from: "${rootDir}/gradle/include/sonar.gradle"