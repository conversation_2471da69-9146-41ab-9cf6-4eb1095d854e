version '1.0.1-SNAPSHOT'
jar.enabled = true
bootJar.enabled = false
//1、添加
configurations {
    mybatisGenerator
}

dependencies {
    // mybatis and mysql
    api('org.springframework.boot:spring-boot-starter-jdbc')
    api('org.mybatis:mybatis-spring:1.3.2')
    api('org.mybatis:mybatis:3.4.6')
    api('mysql:mysql-connector-java')
    api('org.mybatis.spring.boot:mybatis-spring-boot-starter:2.0.0')

    //mybatis-plus
    api ('com.baomidou:mybatis-plus-boot-starter:3.3.1') {
        exclude group: 'com.github.jsqlparser'
        exclude group: 'org.mybatis'
    }
    api ('com.baomidou:mybatis-plus-generator:3.3.1'){
        exclude group: 'com.github.jsqlparser'
    }
    testImplementation('org.apache.velocity:velocity-engine-core:2.0')

    //sharding-jdbc
    api('org.apache.shardingsphere:sharding-jdbc-core:4.0.0-RC2')

    //mapper依赖于entity
    api project(':ims-common')

    mybatisGenerator 'org.mybatis.generator:mybatis-generator-core:1.3.6'
    mybatisGenerator 'mysql:mysql-connector-java'
    mybatisGenerator 'tk.mybatis:mapper:3.5.2'
}


def getDbProperties = {
    def properties = new Properties()
    file("src/main/resources/generator/config.properties").withInputStream { inputStream ->
        properties.load(inputStream)
    }
    properties
}

task mybatisGenerate {
    doLast {
        def properties = getDbProperties()
        ant.properties['targetProject'] = projectDir.path
        ant.properties['driverClass'] = properties.getProperty("jdbc.driverClassName")
        ant.properties['connectionURL'] = properties.getProperty("jdbc.url")
        ant.properties['userId'] = properties.getProperty("jdbc.username")
        ant.properties['password'] = properties.getProperty("jdbc.password")
        ant.properties['src_main_java'] = sourceSets.main.java.srcDirs[0].path
        ant.properties['src_main_resources'] = sourceSets.main.resources.srcDirs[0].path
        ant.properties['modelPackage'] = properties.getProperty("package.model")
        ant.properties['mapperPackage'] = properties.getProperty("package.mapper")
        ant.properties['sqlMapperPackage'] = properties.getProperty("package.xml")
        ant.taskdef(
                name: 'mbgenerator',
                classname: 'org.mybatis.generator.ant.GeneratorAntTask',
                classpath: configurations.mybatisGenerator.asPath
        )
        ant.mbgenerator(overwrite: true,
                configfile: 'src/main/resources/generator/generatorConfig.xml', verbose: true) {
            propertyset {
                propertyref(name: 'targetProject')
                propertyref(name: 'userId')
                propertyref(name: 'driverClass')
                propertyref(name: 'connectionURL')
                propertyref(name: 'password')
                propertyref(name: 'src_main_java')
                propertyref(name: 'src_main_resources')
                propertyref(name: 'modelPackage')
                propertyref(name: 'mapperPackage')
                propertyref(name: 'sqlMapperPackage')
            }
        }
    }
}

