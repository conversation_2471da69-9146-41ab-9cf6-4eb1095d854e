package com.ddmc.ims.dal.model.ims;

import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 调拨出库凭证
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TransferOutCredential extends TransferOrderBaseCredential{

    /**
     * 期望到货时间
     */
    private Date expectArriveTime;

    /**
     * 是否完成出库
     */
    private Integer finishOut;


    /**
     * 关联do单来源
     */
    private String refDoSource;

    /**
     * 关联do单号
     */
    private String refDoNo;

}
