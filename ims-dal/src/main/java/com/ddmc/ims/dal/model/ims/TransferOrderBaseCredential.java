package com.ddmc.ims.dal.model.ims;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 调整调拨状态凭证
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TransferOrderBaseCredential extends BaseCredential {

    /**
     * 来源仓库id
     */
    protected Long fromWarehouseId;

    /**
     * 来源货主id
     */
    protected Long fromCargoOwnerId;

    /**
     * 来源逻辑库位编码
     */
    protected String fromLogicInventoryLocationCode;

    /**
     * 目标仓库id
     */
    protected Long toWarehouseId;

    /**
     * 目标货主id
     */
    protected Long toCargoOwnerId;

    /**
     * 目标逻辑库位
     */
    protected String toLogicInventoryLocationCode;
}
