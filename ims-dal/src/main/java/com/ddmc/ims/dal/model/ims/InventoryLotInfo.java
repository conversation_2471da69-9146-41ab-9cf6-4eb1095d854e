package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@Accessors(chain = true)
public class InventoryLotInfo implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 批次id
     */
    private String lotId;

    /**
     * scm批次号，日期
     */
    private String scmLotNo;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 临期日期
     */
    private Date unsalableDate;

    /**
     * 生产日期
     */
    private Date receiveDate;

    /**
     * 生产日期
     */
    private Date manufactureDate;

    /**
     * origNo
     */
    private String purchaseNo;

    /**
     * 产地编码
     */
    private String regionCodePath;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InventoryLotInfo lotInfo = (InventoryLotInfo) o;
        return Objects.equals(skuId, lotInfo.skuId) && Objects.equals(warehouseId, lotInfo.warehouseId)
            && Objects.equals(lotId, lotInfo.lotId) && Objects.equals(vendorId, lotInfo.vendorId)
            && Objects.equals(unsalableDate, lotInfo.unsalableDate) && Objects.equals(manufactureDate, lotInfo.manufactureDate)
            && Objects.equals(regionCodePath, lotInfo.regionCodePath);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuId, warehouseId, lotId, vendorId, unsalableDate, manufactureDate, regionCodePath);
    }
}
