package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryInDetail;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 调拨在途出库明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface TransferIntransitInventoryInDetailMapper extends BaseMapper<TransferIntransitInventoryInDetail> {


    /**
     * 根据单号查询
     *
     * @param refOrderNo refOrderNo
     * @param refOrderSource refOrderSource
     * @param refExeOrderNo refExeOrderNo
     * @param refExeOrderSource refExeOrderSource
     * @return result
     */
    List<TransferIntransitInventoryInDetail> selectByRefOrderNoAndRefOrderSourceAndRefExeOrderNoAndRefExeOrderSource(
        @Param("refOrderNo") String refOrderNo, @Param("refOrderSource") String refOrderSource,
        @Param("refExeOrderNo") String refExeOrderNo, @Param("refExeOrderSource") String refExeOrderSource, @Param("skuIds") List<Long> skuIds);


    /**
     * 批量插入
     *
     * @param list list
     * @return result
     */
    int insertList(@Param("list") List<TransferIntransitInventoryInDetail> list);

    /**
     * 批量更新调拨在途出库数据
     *
     * @param list 调拨在途出库库存
     * @return 影响行数
     */
    int batchUpdate(@Param("items") List<TransferIntransitInventoryInDetail> list);


    /**
     * 根据单据号删除单据信息
     * @param refOrderNo 单据号
     * @param refOrderSource 单据来源
     */
    void deleteByRefOrderNoAndSource(@Param("refOrderNo") String refOrderNo, @Param("refOrderSource") String refOrderSource);


}
