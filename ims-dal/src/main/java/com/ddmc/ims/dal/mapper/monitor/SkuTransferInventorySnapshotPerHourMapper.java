package com.ddmc.ims.dal.mapper.monitor;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.monitor.SkuTransferInventorySnapshotPerHour;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 逻辑库存在途快照 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
public interface SkuTransferInventorySnapshotPerHourMapper extends BaseMapper<SkuTransferInventorySnapshotPerHour> {

    List<SkuTransferInventorySnapshotPerHour> selectBySnapshotDateTimeAndToWarehouseId(
        @Param("snapshotDateTime") Date snapshotDateTime, @Param("toWarehouseId") Long toWarehouseId);


    void insertList(@Param("list")List<SkuTransferInventorySnapshotPerHour> skuTransferInventorySnapshotPerHours);

    void deleteByToWarehouseIdInAndSnapshotDateTime(@Param("toWarehouseId") Long toWarehouseId ,@Param("snapshotDateTime") Date snapshotDateTime);


    List<SkuTransferInventorySnapshotPerHour> getByMinId(@Param("maxId") Long maxId, @Param("limit") int limit);

}
