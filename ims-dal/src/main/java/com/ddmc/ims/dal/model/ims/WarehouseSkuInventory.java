package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@Accessors(chain = true)
public class WarehouseSkuInventory implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
      private Long id;

    /**
     * 逻辑库位编码
     */
    private String logicInventoryLocationCode;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 可用数量
     */
    private BigDecimal freeQty;

    /**
     * 冻结数量
     */
    private BigDecimal frozenQty;

    /**
     * 占用数量，最多扣减为0
     */
    private BigDecimal allocQty;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Integer version;

    private String usageCode;

    @JsonIgnore
    public BigDecimal getSaleQty(){
        return this.freeQty.subtract(this.allocQty);
    }


}
