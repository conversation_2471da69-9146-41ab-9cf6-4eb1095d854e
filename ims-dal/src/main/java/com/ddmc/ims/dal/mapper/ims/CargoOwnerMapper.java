package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.CargoOwner;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface CargoOwnerMapper extends BaseMapper<CargoOwner> {

    /**
     * 根据货主id获取货主信息
     *
     * @param cargoOwnerIds 货主id列表
     * @return 货主信息
     */
    List<CargoOwner> listAllByIds(@Param("ids") Collection<Long> cargoOwnerIds);

    /**
     * 获取所有货主信息
     *
     * @return 返回数据
     */
    List<CargoOwner> listAllCargoOwner();

    CargoOwner selectByCorporationCode(@Param("corporationCode") String corporationCode);
}
