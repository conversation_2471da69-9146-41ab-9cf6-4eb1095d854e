package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 凭证头信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CredentialHeader implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 幂等id
     */
    private String idempotentId;

    /**
     * 库存操作仓库id,分表逻辑
     */
    private Long warehouseId;

    /**
     * 凭证执行状态
     */
    private CredentialStatus status;

    /**
     * 来源source
     */
    private String orderSource;

    /**
     * 计划单单号
     */
    private String orderNo;

    /**
     * 执行单单号
     */
    private String exeOrderSource;

    /**
     * 执行单单号
     */
    private String exeOrderNo;


    /**
     * 单据类型
     */
    private String orderType;

    /**
     * 单据操作类型
     */
    private Integer orderOperateType;

    /**
     * 业务操作时间
     */
    private Date businessTime;

    /**
     * 操作流水号
     */
    private String seqNo;

    /**
     * 期望出库时间
     */
    private Date expectOutTime;

    /**
     * 配送模式
     */
    private Integer deliveryMode;

    /**
     * 期望到货时间
     */
    private Date expectArriveTime;

    /**
     * 期望入库时间
     */
    private Date expectInTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Deprecated
    private YesNoEnum isStockChange;
    /**
     * 日清日期
     */
    private Date endDateTime;


    /**
     * 履约日期
     */
    private Date deliveryDate;



    /**
     * 凭证详情
     */
    @TableField(exist = false)
    private List<CredentialDetail> credentialDetailList;

    @TableField(exist = false)
    private List<CredentialUseageDetail> credentialUseageDetailList;

    @TableField(exist = false)
    private List<CredentialHeaderExt> credentialHeaderExtList;

    /**
     * 取CredentialHeaderExt原始履约日期
     */
    @TableField(exist = false)
    private Date oriDeliveryDate;


    @ApiModelProperty("库存异动编码")
    private String stockSourceType;

}
