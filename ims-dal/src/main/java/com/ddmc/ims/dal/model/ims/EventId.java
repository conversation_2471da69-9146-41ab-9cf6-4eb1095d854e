package com.ddmc.ims.dal.model.ims;

import com.ddmc.ims.common.enums.common.EventExecuteServiceNameEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EventId implements Serializable {

    private Long id;

    private Long parentId = 0L;

    private String keyType;

    private String keyId;

    private Date earliestExeDate;

    //是否需要指定商品来源独立线程池处理
    private Boolean skuSourceThreadPool = false;

    //指定独立线程池名称
    private EventExecuteServiceNameEnum eventExecuteServiceName;



}
