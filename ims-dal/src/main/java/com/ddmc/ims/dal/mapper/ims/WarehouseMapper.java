package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.condition.CorporationWarehouseCondition;
import com.ddmc.ims.dal.model.ims.Warehouse;
import com.github.pagehelper.Page;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface WarehouseMapper extends BaseMapper<Warehouse> {


    List<Warehouse> getAllWarehouses();

    Page<Warehouse> pageListByCondition(@Param("condition") CorporationWarehouseCondition condition);

    List<Long> selectIdByCorporationCodeAndWarehouseType(@Param("corporationCode") String corporationCode,@Param("warehouseType") Integer warehouseType);


    /**
     * 获取所有仓库Id
     * @return 仓库Id列表
     */
    List<Long> selectAllId();

    /**
     * 对仓库增加共享锁
     * @param warehouseId 仓库id
     */
    Long addShareLock(@Param("warehouseId") Long warehouseId);

    /**
     * 对仓库增加悲观锁
     * @param warehouseId 仓库id
     */
    Long addLock(@Param("warehouseId") Long warehouseId);

    /**
     * 根据区域，城市id和仓库类型查询
     * @param zoneId 区域id
     * @param cityIds 城市
     * @param warehouseType 仓库类型
     * @return 仓库
     */
    List<Warehouse> getByZoneIdAndCityIdsAndWarehouseType(@Param("zoneId")Integer zoneId, @Param("cityIds") List<String> cityIds,
        @Param("warehouseType") Integer warehouseType);


}
