package com.ddmc.ims.dal.condition;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public class BomNoAndVersionPair {

    private final String bomNo;

    private final Long bomVersion;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BomNoAndVersionPair that = (BomNoAndVersionPair) o;
        return bomNo.equals(that.bomNo) && bomVersion.equals(that.bomVersion);
    }

    @Override
    public int hashCode() {
        return Objects.hash(bomNo, bomVersion);
    }
}
