package com.ddmc.ims.dal.model.monitor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 逻辑库存在途快照
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SkuTransferInventorySnapshotPerHour implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 来源仓库id
     */
    private Long fromWarehouseId;

    /**
     * 逻辑库位编码
     */
    private String fromLogicLocationCode;

    /**
     * 货主id
     */
    private Long fromCargoOwnerId;

    /**
     * 目标仓库id
     */
    private Long toWarehouseId;

    /**
     * 逻辑库位编码
     */
    private String toLogicLocationCode;

    /**
     * 货主id
     */
    private Long toCargoOwnerId;

    /**
     * 在途数量，已出未收量
     */
    private BigDecimal transferIntransitQty;

    /**
     * 快照日期-精确到小时
     */
    private Date snapshotDateTime;

    /**
     * 创建时间
     */
    private Date createTime;




}
