package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ArcNumberRegister implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 注册码
     */
    private String registerCode;

    /**
     * 描述
     */
    private String description;

    /**
     * 循环周期 0-不循环 N-隔N天重新从0开始
     */
    private Integer cycle;

    /**
     * 循环周期单位
     */
    private String cycleUnit;

    /**
     * 初始值
     */
    private Integer initValue;


}
