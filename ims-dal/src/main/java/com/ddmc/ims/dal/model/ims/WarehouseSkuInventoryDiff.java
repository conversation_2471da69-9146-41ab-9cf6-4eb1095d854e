package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商品仓库库存表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WarehouseSkuInventoryDiff implements Serializable {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 比对时间
     */
    private Date checkDate;

    /**
     * 比对次数
     */
    private Integer triggerCount;

    /**
     * 良品库存
     */
    private BigDecimal gssQty;

    /**
     * 良品库存
     */
    private BigDecimal imsQty;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
