package com.ddmc.ims.dal.condition;

import com.ddmc.ims.common.enums.ims.WarehouseStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CorporationWarehouseCondition {

    @ApiModelProperty(value = "仓库主体code")
    private String warehouseCorporationCode;

    @ApiModelProperty(value = "仓库类型")
    private Integer warehouseType;

    @ApiModelProperty(value = "区域id")
    private Integer zoneId;

    @ApiModelProperty(value = "城市id")
    private String cityId;

    @ApiModelProperty(value = "仓库状态")
    private WarehouseStatusEnum status;

}
