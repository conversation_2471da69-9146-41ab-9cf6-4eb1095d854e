package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 凭证用途详情信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
public interface CredentialUseageDetailMapper extends BaseMapper<CredentialUseageDetail> {

    /**
     * 批量插入用途维度凭证信息
     */
    void batchInsert(@Param("items") List<CredentialUseageDetail> credentialUseageDetailList);

    /**
     * 根据凭证id查询用途维度数据
     */
    List<CredentialUseageDetail> selectByCredentialHeaderIds(@Param("headIds") List<Long> headIds);

    /**
     * 根据凭证id删除凭证信息
     *
     * @param headerId 凭证id
     */
    void deleteByCredentialHeaderId(@Param("headerId") Long headerId);


    /**
     * 根据凭证id删除凭证信息
     *
     * @param headerIds 凭证id
     */
    void deleteByCredentialHeaderIds(@Param("headerIds") List<Long> headerIds);

    /**
     * 根据凭证id删除凭证信息
     *
     * @param minId 最小id
     * @param maxId 最大id
     */
    void deleteByIds(@Param("minId") Long minId, @Param("maxId") Long maxId);

    void updateCommandTypeByCredentialHeadId(@Param("headerIds") List<Long> headerIds);
}
