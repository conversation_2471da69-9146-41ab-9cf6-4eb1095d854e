package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.MdBomCity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface MdBomCityMapper extends BaseMapper<MdBomCity> {

    /**
     * 批量插入
     *
     * @param list list
     * @return result
     */
    int insertList(@Param("list") List<MdBomCity> list);

    /**
     * 根据bomId + version + cityId查询bom
     */
    MdBomCity selectByBomIdAndVersionAndCity(@Param("bomId") String bomId, @Param("version") Long version,
        @Param("cityId") String cityId);

}
