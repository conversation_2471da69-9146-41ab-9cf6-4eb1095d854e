package com.ddmc.ims.dal.model.ims;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WarehouseSkuLotInventory implements Serializable {

    private static final long serialVersionUID=1L;
    @TableId(value = "id", type = IdType.AUTO)
      private Long id;

    /**
     * 逻辑库位编码
     */
    private String logicInventoryLocationCode;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 批次id
     */
    private String lotId;

    /**
     * 可用数量
     */
    private BigDecimal freeQty;

    /**
     * 冻结数量
     */
    private BigDecimal frozenQty;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Integer version;


}
