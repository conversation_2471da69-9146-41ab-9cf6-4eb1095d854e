package com.ddmc.ims.dal.mapper.ims;

import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.model.ims.EventEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface EventMapper {

    /**
     * 根据日期区间和keyType类型查询ids
     *
     * @param createTimeStart 创建开始时间
     * @param createTimeEnd 创建结束时间
     * @param keyTypeList 类型集合
     * @return ids
     */
    List<Long> listIdsByTimeAndType(@Param("createTimeStart") Date createTimeStart,
        @Param("createTimeEnd") Date createTimeEnd
        , @Param("keyTypeList") List<String> keyTypeList);

    List<EventEntity> listByCondition(@Param("condition") ListEventCondition condition);

    List<EventEntity> listUnHandled(@Param("triggerCount") Integer triggerCount,
        @Param("createTimeEnd") Date createTimeEnd);

    int insertBatch(@Param("entities") List<EventEntity> entities);

    int update(@Param("id") Long id,
        @Param("newStatus") Integer newStatus,
        @Param("oldStatus") Integer oldStatus);

    int batchUpdateStatus(@Param("eventIdList") List<Long> eventIdList,
        @Param("newStatus") Integer newStatus,
        @Param("oldStatus") Integer oldStatus);

    int updateStatus(@Param("eventIdList") List<Long> eventIdList,
        @Param("newStatus") Integer newStatus);

    int plusTriggerCount(@Param("id") Long id);

    EventEntity getById(@Param("id") Long id);

    List<EventEntity> getByIds(@Param("ids") List<Long> ids);

    int deleteByEndDate(@Param("endDate") Date endDate, @Param("limit") Integer limit);

    int plusTriggerCountAndStatusProcessing(@Param("id") Long id);

    int batchPlusTriggerCountAndStatusProcessing(@Param("eventIdList") List<Long> eventIdList);

    /**
     * 查询当前数据库时间，精确到毫秒级别
     *
     * @return 当前时间
     */
    Date selectNowMilliSecond();

    EventEntity getAllByKeyTypeAndKeyId(@Param("keyType") String keyType, @Param("keyId") String keyId);

    EventEntity getOneWaitingProcessByKeyTypeAndKeyIdAndStatus(@Param("keyType") String keyType, @Param("keyId") String keyId);

    Long getMinId();

    List<EventEntity> getGreaterEventEntity(@Param("minId") Long minId, @Param("limit") Integer limit);

    int deleteByIds(@Param("ids") List<Long> ids);

    List<EventEntity> getProcessing();
}
