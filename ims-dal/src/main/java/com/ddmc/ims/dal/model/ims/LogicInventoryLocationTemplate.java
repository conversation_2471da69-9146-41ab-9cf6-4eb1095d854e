package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LogicInventoryLocationTemplate implements Serializable {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 仓库主体编码
     */
    private String warehouseCorporationCode;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 仓库类型；1-大仓；3-前置仓
     */
    private Integer warehouseType;

    private Date createTime;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    private Date updateTime;

    /**
     * 创建人id
     */
    private String updateUserId;

    /**
     * 创建人姓名
     */
    private String updateUserName;


}
