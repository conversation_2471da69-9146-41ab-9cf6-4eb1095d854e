package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ddmc.ims.common.util.CurrentDateUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class EventEntity {

    /**
     * 事件id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父事件id
     */
    private Long parentId;

    /**
     * 事件类型名称
     */
    private String keyType;

    /**
     * 事件内容主键
     */
    private String keyId;

    /**
     * 类型
     */
    private String type;

    /**
     * 消息事件
     */
    private String event;

    /**
     * 状态 1:消费;0:未消费
     */
    private Integer status;

    /**
     * 触发的次数
     */
    private Integer triggerCount;

    /**
     * 最早执行时间
     */
    private Date earliestExeDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否延时队列，最早执行时间晚于创建时间
     * @return 是否延时队列
     */
    public boolean isDelayEvent() {
        return Objects.nonNull(this.earliestExeDate)
            && this.earliestExeDate.after(this.createTime);
    }

    /**
     * 是否达到执行条件
     * @return 是否达到
     */
    public boolean needExeEvent() {
        return Objects.isNull(this.earliestExeDate)
            || !this.earliestExeDate.after(CurrentDateUtil.newDate());
    }
}
