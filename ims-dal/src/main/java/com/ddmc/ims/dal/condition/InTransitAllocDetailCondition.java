package com.ddmc.ims.dal.condition;

import java.util.Date;
import java.util.Objects;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class InTransitAllocDetailCondition {

    /**
     * 逻辑库位编码
     */
    private String logicInventoryLocationCode;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 货品id
     */
    private Long skuId;


    /**
     * 履约日期
     */
    private Date deliveryDate;


    /**
     * 源履约日期
     */
    private Date oriDeliveryDate;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof InTransitAllocDetailCondition)) {
            return false;
        }
        InTransitAllocDetailCondition that = (InTransitAllocDetailCondition) o;
        return Objects.equals(logicInventoryLocationCode, that.logicInventoryLocationCode) && Objects
            .equals(warehouseId, that.warehouseId) && Objects.equals(cargoOwnerId, that.cargoOwnerId)
            && Objects.equals(skuId, that.skuId) && Objects.equals(deliveryDate, that.deliveryDate)
            && Objects.equals(oriDeliveryDate, that.oriDeliveryDate);
    }

    @Override
    public int hashCode() {
        return Objects
            .hash(logicInventoryLocationCode, warehouseId, cargoOwnerId, skuId, deliveryDate, oriDeliveryDate);
    }
}
