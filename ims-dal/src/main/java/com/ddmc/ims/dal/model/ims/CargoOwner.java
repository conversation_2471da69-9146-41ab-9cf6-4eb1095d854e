package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CargoOwner implements Serializable {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主体编码
     */
    private String cargoOwnerCode;

    /**
     * 货主名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    private Date createTime;

    /**
     * 创建人id
     */
    private String createUserId;


}
