package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 凭证头信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SnapshotCredentialDiff implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 凭证id
     */
    private Long credentialId;
    /**
     * 计划单来源
     */
    private String orderSource;

    /**
     * 计划单单号
     */
    private String orderNo;

    /**
     * 执行单来源
     */
    private String exeOrderSource;

    /**
     * 执行单单号
     */
    private String exeOrderNo;

    /**
     * 业务操作时间
     */
    private Date businessTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 日清日期
     */
    private Date endDateTime;


}
