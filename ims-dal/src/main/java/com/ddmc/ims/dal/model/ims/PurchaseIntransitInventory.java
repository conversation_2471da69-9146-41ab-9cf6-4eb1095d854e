package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PurchaseIntransitInventory implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 逻辑库位编码
     */
    private String logicInventoryLocationCode;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 采购单号
     */
    private String orderNo;

    /**
     * 期望到货时间
     */
    private Date expectArriveTime;

    /**
     * 期望到货数量
     */
    private BigDecimal planQty;

    /**
     * 在途数量
     */
    private BigDecimal intransitQty;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 库存用途, 单个
     */
    private String usageCode;

    /**
     * 单据标记：预售-presale,非预售-none
     */
    private String orderTag;


    /**
     * 预约在途数量
     */
    private BigDecimal bookedIntransitQty;

}
