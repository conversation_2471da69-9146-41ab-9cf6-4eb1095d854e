package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ddmc.ims.common.enums.warehouse.StoreSaleTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@Accessors(chain = true)
public class Warehouse implements Serializable {

    private static final long serialVersionUID=1L;
    public static final Integer WAREHOUSE_TYPE_SALE = 3;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private String name;

    /**
     * 仓库主体编码
     */
    private String corporationCode;

    /**
     * 区域id
     */
    private Integer zoneId;

    /**
     * 城市id
     */
    private String cityId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 仓库类型:1总仓存储2临时存储3销售4测试5报废存储
     */
    private Integer type;

    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    private Date updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 门店类型 0 未设置 1：共享仓 2：拉新仓 3：办公室仓 4：装备仓 5：线下店
     */
    private Integer saleType;

    /**
     * 站点Id
     */
    private String stationId;
    /**
     * 供货大区
     */
    private Integer generalRegionId;


    /**
     * 是否销售仓库
     *
     * @return bool
     */
    public boolean isSaleWarehouse() {
        return Warehouse.WAREHOUSE_TYPE_SALE.equals(this.type);
    }

    /**
     * 判断是否为线下店
     *
     * @return result
     */
    public boolean isOfflineWarehouse() {
        return Objects.equals(StoreSaleTypeEnum.STORE_SALE_TYPE_OFFLINE_STORE.getCode(), this.saleType);
    }



    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        Warehouse warehouse = (Warehouse) o;
        return Objects.equals(id, warehouse.id) && Objects.equals(name, warehouse.name)
            && Objects.equals(corporationCode, warehouse.corporationCode) && Objects.equals(zoneId, warehouse.zoneId)
            && Objects.equals(cityId, warehouse.cityId) && Objects.equals(status, warehouse.status)
            && Objects.equals(type, warehouse.type) && Objects.equals(saleType, warehouse.saleType)
            && Objects.equals(stationId, warehouse.stationId)
            && Objects.equals(generalRegionId, warehouse.generalRegionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, corporationCode, zoneId, cityId, status, type, saleType, stationId, generalRegionId);
    }
}
