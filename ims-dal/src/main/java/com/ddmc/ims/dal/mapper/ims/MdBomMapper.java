package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.condition.BomNoAndVersionPair;
import com.ddmc.ims.dal.model.ims.MdBom;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * bom关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface MdBomMapper extends BaseMapper<MdBom> {

    /**
     * 根据bomId和版本号查询制造bom信息，不考虑bom状态
     *
     * @param bomId bomId
     * @param version bom版本号
     * @return bom详情
     */
    List<MdBom> getByBomIdAndVersion(@Param("bomId") String bomId, @Param("version") Long version);

    /**
     * 批量根据bomId + bomVersion查询bom信息
     *
     * @param bomNoAndVersionPairs bomId + bomVersion对
     * @return bom
     */
    List<MdBom> batchGetByBomId(@Param("pair") List<BomNoAndVersionPair> bomNoAndVersionPairs);

    /**
     * 统计数量
     *
     * @param bomId bomId
     * @param version version
     * @return result
     */
    Long countByBomIdAndVersion(@Param("bomId") String bomId, @Param("version") Long version);


    /**
     * 批量插入
     *
     * @param list list
     * @return result
     */
    int insertList(@Param("list") List<MdBom> list);

    /**
     * 查询成品sku生效的最新一个bom关系
     */
    MdBom selectLastEffectiveSkuBom(@Param("corporationCode") String corporationCode, @Param("skuId") Long skuId,
        @Param("bomStatus") Integer bomStatus, @Param("skuType") Integer skuType,
        @Param("effectiveTime") Date effectiveTime);


    /**
     * 批量更新状态
     *
     * @param updatedBomStatus updatedBomStatus
     * @param idCollection idCollection
     * @return result
     */
    int updateBomStatusByIdIn(@Param("updatedBomStatus") Integer updatedBomStatus,
        @Param("idCollection") Collection<Long> idCollection);


    /**
     * 查询成品sku生效的最新一个bom关系
     */
    MdBom selectLastEffectiveSkuBomBySku(@Param("skuId") Long skuId, @Param("bomStatus") Integer bomStatus,
        @Param("skuType") Integer skuType);

    List<MdBom> selectLastEffectiveSkuBomBySkuIds(@Param("corporationCode") String corporationCode,
                                                  @Param("skuIds") List<Long> skuIds,
                                                  @Param("bomStatus") Integer bomStatus,
                                                  @Param("skuType") Integer skuType);
}
