package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.bo.TransferOrderNoAndSource;
import com.ddmc.ims.common.bo.WarehouseSkuCargoOwnerInfo;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.request.inventory.InventoryDetailQueryRequest;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface TransferIntransitInventoryMapper extends BaseMapper<TransferIntransitInventory> {

    /**
     * 查询仓库下是否存在调拨在途数据
     *
     * @param warehouseIds 仓库列表
     * @param cargoOwnerId 货主
     * @param logicInventoryCode 逻辑库位编码
     */
    Long selectOneIdByWarehouseIdsAndCargoOwnerId(@Param("warehouseIds") List<Long> warehouseIds,
        @Param("cargoOwnerId") Long cargoOwnerId, @Param("logicInventoryCode") String logicInventoryCode);

    /**
     * 根据调拨单号和来源获取调拨在途数据
     *
     * @param transferOrderNoAndSource 调拨单号+来源
     * @return 调拨在途数据
     */
    List<TransferIntransitInventory> selectTransferIntransitInventory(
        @Param("transferOrderNoAndSource") TransferOrderNoAndSource transferOrderNoAndSource);

    /**
     * 批量更新调拨在途数据
     *
     * @param warehouseSkuInventories 调拨在途库存
     * @return 影响行数
     */
    int batchUpdate(@Param("items") List<TransferIntransitInventory> warehouseSkuInventories);

    /**
     * 根据调拨单号和来源获取调拨在途数据
     *
     * @param transferOrderNoAndSource 调拨单号+来源
     * @return 调拨在途数据
     */
    List<TransferIntransitInventory> listByOrderSourceAndNoAndSkuIds(
        @Param("transferOrderNoAndSource") TransferOrderNoAndSource transferOrderNoAndSource,
        @Param("skuIds") List<Long> skuIds);


    /**
     * 批量插入
     *
     * @param items items
     * @return result
     */
    int batchInsert(@Param("items") List<TransferIntransitInventory> items);


    void updateWaitAllocQtyToZero(@Param("transferOrderNoAndSource") TransferOrderNoAndSource transferOrderNoAndSource);


    void updateWaitAllocQtyToZeroBySku(
        @Param("transferOrderNoAndSource") TransferOrderNoAndSource transferOrderNoAndSource,
        @Param("skuIds") List<Long> skuIds);



    /**
     * 批量累加计划出库量与待出库数量
     *
     * @param list list
     * @return result
     */
    int batchAddPlanQtyWaitAllocQty(@Param("list") List<TransferIntransitInventory> list);

    /**
     * 批量更新待出库梳理
     *
     * @param list list
     * @return result
     */
    int batchUpdateWaitAllocQty(@Param("list") List<TransferIntransitInventory> list);

    /**
     * 批量更新待出库计划量
     *
     * @param list list
     * @return result
     */
    int batchUpdateWaitAllocQtyAndPlanQty(@Param("list") List<TransferIntransitInventory> list);




    /**
     * 批量更新已发待收量
     *
     * @param list list
     * @return result
     */
    int batchUpdateInTransitQty(@Param("list") List<TransferIntransitInventory> list);


    /**
     * 查询在途信息
     * @param conditionList 条件
     * @return 在途库存
     */
    List<TransferIntransitInventory> selectByToWarehouseSkuOwnerInfo(@Param("conditionList") List<WarehouseSkuCargoOwnerInfo> conditionList);


    /**
     * 根据四要素+期望出库时间+用途查询
     * @return 在途库存
     */
    List<TransferIntransitInventory> selectByToWarehouseSkuOwnerLocationOutTime(@Param("request") InventoryDetailQueryRequest request,
        @Param("expectOutTime") Date expectOutTime, @Param("usageCode") String usageCode);


    /**
     * 根据四要素查询
     * @return 待发量
     */
    List<TransferIntransitInventory> selectByFromWarehouseSkuOwnerLocation(@Param("request") InventoryDetailQueryRequest request);

    /**
     * 通过创建时间查在该时间前最大的id
     *
     * @param maxCreateTime maxCreateTime
     * @return result
     */
    Long selectOneIdByCreateTimeBefore(@Param("maxCreateTime") Date maxCreateTime);


    /**
     * 按最大id进行删除
     *
     * @param id 最大id
     * @return 影响条数
     */
    Long deleteByMaxId(@Param("id") Long id);

    Long getMinId();

    List<TransferIntransitInventory> getByMinId(@Param("maxId") Long maxId, @Param("limit") int limit);


    List<TransferIntransitInventory> selectIdByMinIdLimit200(@Param("minId") Long minId);


    /**
     * 批量更新调拨在途数据
     *
     * @param warehouseSkuInventories 调拨在途库存
     * @return 影响行数
     */
    int batchUpdateExceptTime(@Param("items") List<TransferIntransitInventory> warehouseSkuInventories);



    /**
     * 批量更新调拨在途数据
     *
     * @param warehouseSkuInventories 调拨在途库存
     * @return 影响行数
     */
    int batchUpdateDeliverMode(@Param("items") List<TransferIntransitInventory> warehouseSkuInventories);

    /**
     * 根据调拨单号和来源获取调拨在途数据
     *
     * @param transferOrderNoAndSource 调拨单号+来源
     * @return 调拨在途数据
     */
    TransferIntransitInventory selectOneTransferIntransitInventory(
        @Param("transferOrderNoAndSource") TransferOrderNoAndSource transferOrderNoAndSource);

    List<String> selectByToWarehouseIdAndTime(@Param("toWarehouseId") Long toWarehouseId,@Param("minUpdateTime") Date minUpdateTime,@Param("maxCreateTime")  Date maxCreateTime);
}
