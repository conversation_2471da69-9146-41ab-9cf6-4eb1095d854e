package com.ddmc.ims.dal.mapper.monitor;
import java.util.Collection;

import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Date;

import com.ddmc.ims.dal.model.monitor.SkuInventoryInOutSnapshot;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 库存出入库量小时快照 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
public interface SkuInventoryInOutSnapshotMapper extends BaseMapper<SkuInventoryInOutSnapshot> {


    /**
     * 根据快照时间查询，按sku_id, warehouse_id, logic_location_code, cargo_owner_id, lot_id聚合in_qty,out_qty
     * 时间为左开右闭
     *
     * @param minSnapshotDateTime minSnapshotDateTime
     * @param maxSnapshotDateTime maxSnapshotDateTime
     * @param warehouseId warehouseId
     * @return result
     */
    List<SkuInventoryInOutSnapshot> selectBySnapshotDateTimeBetween(
        @Param("minSnapshotDateTime") Date minSnapshotDateTime, @Param("maxSnapshotDateTime") Date maxSnapshotDateTime,
        @Param("warehouseId") Long warehouseId);


    /**
     * 批量插入
     *
     * @param list list
     * @return result
     */
    int insertList(@Param("list") List<SkuInventoryInOutSnapshot> list);


    List<SkuInventoryInOutSnapshot> selectBySnapshotDateTimeAndWarehouseId(@Param("snapshotDateTime") Date snapshotDateTime, @Param("warehouseId") Long warehouseId);

    /**
     * 按最小id进行删除
     *
     * @param id 最小id
     * @return 影响条数
     */
    Long deleteByMinId(@Param("id") Long id);

    /**
     * 根据仓库+快照时间删除
     *
     * @param warehouseIdCollection warehouseIdCollection
     * @param snapshotDateTime snapshotDateTime
     * @return result
     */
    int deleteByWarehouseIdInAndSnapshotDateTime(@Param("warehouseIdCollection") Collection<Long> warehouseIdCollection,
        @Param("snapshotDateTime") Date snapshotDateTime);


    /**
     * 通过创建时间查在该时间前最大的id
     *
     * @param maxCreateTime maxCreateTime
     * @return result
     */
    Long selectOneIdByCreateTimeBefore(@Param("maxCreateTime") Date maxCreateTime);


    /**
     * 按最大id进行删除
     *
     * @param id 最大id
     * @return 影响条数
     */
    Long deleteByMaxId(@Param("id") Long id);


    /**
     * 查询距离当前最大的快照时间
     *
     * @param warehouseId warehouseId
     * @param nowSnapshotDateTime nowSnapshotDateTime
     * @return result
     */
    Date selectOneMaxSnapshotDateTimeByWarehouseId(@Param("warehouseId") Long warehouseId,
        @Param("nowSnapshotDateTime") Date nowSnapshotDateTime);


}
