package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.bo.PurchaseOrderNoAndSource;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 凭证头信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
public interface CredentialHeaderMapper extends BaseMapper<CredentialHeader> {

    CredentialHeader selectByIdempotentId(@Param("idempotentId") String idempotentId);

    /**
     * 根据凭证的幂等键确认凭证
     * @param idempotentId 幂等键
     * @return 实际变更的数据行数
     */
    int confirmCredentialByIdempotentId(@Param("idempotentId") String idempotentId);

    List<CredentialHeader> getInitCredentialHeaders(@Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime,
                                                    @Param("limit") int limit);

    CredentialHeader listByExeOrderSourceAndNoAndOrderOperateType(@Param("exeOrderSource")String exeOrderSource, @Param("exeOrderNo") String exeOrderNo, @Param("orderOperateType") Integer orderOperateType);

    List<CredentialHeader> selectByIds(@Param("ids") List<Long> ids);


    /**
     * 查询仓库下时间范围内已完成的凭证头信息
     *
     * @param minBusinessTime 开始时间
     * @param maxBusinessTime 结束时间
     * @param warehouseId 仓库id
     * @return result
     */
    List<CredentialHeader> selectWarehouseCompleteHeaderByBusinessTime(@Param("minBusinessTime") Date minBusinessTime,
        @Param("maxBusinessTime") Date maxBusinessTime, @Param("warehouseId") Long warehouseId);

    Long addLock(@Param("exeOrderSource") String exeOrderSource,@Param("exeOrderNo") String exeOrderNo);


    /**
     * 查询仓库下时间范围内已完成的凭证头最小id
     *
     * @param minBusinessTime 开始时间
     * @param maxBusinessTime 结束时间
     * @param warehouseId 仓库id
     * @return result
     */
    Long selectMinIdByWarehouseCompleteHeaderByBusinessTime(@Param("warehouseId") Long warehouseId,
        @Param("minBusinessTime") Date minBusinessTime, @Param("maxBusinessTime") Date maxBusinessTime);


    /**
     * 查询仓库下时间范围内已完成的凭证头最大id
     *
     * @param minBusinessTime 开始时间
     * @param maxBusinessTime 结束时间
     * @param warehouseId 仓库id
     * @return result
     */
    Long selectMaxIdByWarehouseCompleteHeaderByBusinessTime(@Param("warehouseId") Long warehouseId,
        @Param("minBusinessTime") Date minBusinessTime, @Param("maxBusinessTime") Date maxBusinessTime);


    /**
     * 查询id范围内的仓库凭证
     *
     * @param warehouseId warehouseId
     * @param minId minId
     * @param maxId maxId
     * @return result
     */
    List<CredentialHeader> selectByWarehouseIdAndIdBetween(@Param("warehouseId") Long warehouseId,
        @Param("minId") Long minId, @Param("maxId") Long maxId);


    /**
     * 查询id与Business时间范围内的仓库凭证
     *
     * @param warehouseId warehouseId
     * @param minId minId
     * @param maxId maxId
     * @return result
     */
    List<CredentialHeader> selectByWarehouseIdAndIdAndBusinessBetween(@Param("warehouseId") Long warehouseId,
        @Param("minId") Long minId, @Param("maxId") Long maxId, @Param("minBusinessTime") Date minBusinessTime,
        @Param("maxBusinessTime") Date maxBusinessTime);



    /**
     * 通过seqNo与orderSource查询
     *
     * @param seqNo seqNo
     */
    List<CredentialHeader> selectBySeqNo(@Param("seqNo") String seqNo);


    List<Long> selectIdByMaxIdLimit100(@Param("maxId") Long maxId);


    Set<String> selectSeqNoBySeqNoIn(@Param("seqNoCollection")Collection<String> seqNoCollection);


    List<CredentialHeader> listByWarehouseIdAndOrderOperateTypeAndEndDateTime(@Param("warehouseId") Long warehouseId,@Param("orderType") String orderType,@Param("minBusinessTime") Date minBusinessTime,@Param("endDateTime") Date endDateTime);
    /**
     * 根据时间范围查询最大凭证ID
     * @param startCreateTime 开始时间
     * @param endCreateTime 结束时间
     * @return 在时间范围内的最大凭证id
     */
    Long selectMaxIdByCreateTime(@Param("startCreateTime") Date startCreateTime, @Param("endCreateTime") Date endCreateTime);

    /**
     * 根据操作类型和单号批量查询
     */
    List<CredentialHeader> selectByOrderNoAndSource(@Param("orderOperateType") Integer orderOperateType,
        @Param("orderNoAndSourceList") List<PurchaseOrderNoAndSource> orderNoAndSourceList);

    List<CredentialHeader> selectIdByMinIdLimit200(@Param("minId") Long minId);


    CredentialHeader selectByOrderNoAndExeOrderNoAndOrderTypeAndOrderOperateType(@Param("orderNo") String orderNo,
        @Param("exeOrderNo") String exeOrderNo, @Param("orderType") String orderType,
        @Param("orderOperateType") Integer orderOperateType);


    List<CredentialHeader> selectByOrderNosAndBusinessBetween(@Param("orderNos")List<String> orderNos, @Param("minBusinessTime") Date minBusinessTime,@Param("maxBusinessTime") Date maxBusinessTime
    ,@Param("orderOperateTypes") Collection<Integer> orderOperateTypes);

    List<String> selectOrderNoByWarehouseIdAndTime(@Param("warehouseId") Long warehouseId, @Param("orderOperateTypes") Set<Integer> orderOperateTypes, @Param("minBusinessTime") Date minBusinessTime,@Param("maxBusinessTime") Date maxBusinessTime);
}
