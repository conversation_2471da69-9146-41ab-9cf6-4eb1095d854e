package com.ddmc.ims.dal.mapper.ims;

import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Collection;

import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 在库库存占用表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
public interface WarehouseSkuInventoryAllocDetailMapper extends BaseMapper<WarehouseSkuInventoryAllocDetail> {


    /**
     * 通过单号+sku查询
     *
     * @param orderNo orderNo
     * @param orderSource orderSource
     * @param skuIdCollection skuIdCollection
     * @return result
     */
    List<WarehouseSkuInventoryAllocDetail> selectByOrderNoAndOrderSourceAndSkuIdIn(@Param("orderNo") String orderNo,
        @Param("orderSource") String orderSource, @Param("skuIdCollection") Collection<Long> skuIdCollection);

    /**
     * 通过exe单号+sku查询
     *
     * @param exeOrderNo exeOrderNo
     * @param skuIdCollection skuIdCollection
     * @return result
     */
    List<WarehouseSkuInventoryAllocDetail> selectByExeOrderNoAndExeOrderSourceAndSkuIdIn(
        @Param("exeOrderNo") String exeOrderNo,
        @Param("skuIdCollection") Collection<Long> skuIdCollection);


    /**
     * 批量插入
     *
     * @param list list
     * @return result
     */
    int insertList(@Param("list") List<WarehouseSkuInventoryAllocDetail> list);


    /**
     * 根据exeOrderNo删除
     *
     * @param exeOrderNo exeOrderNo
     * @return result
     */
    int deleteByExeOrderNo(@Param("exeOrderNo") String exeOrderNo);

    /**
     * 根据orderNo和orderSource删除
     *
     * @param orderNo orderNo
     * @param orderSource orderSource
     * @return result
     */
    int deleteByOrderNoAndOrderSource(@Param("orderNo") String orderNo, @Param("orderSource") String orderSource);


    /**
     * 根据orderNo和orderSource与skuId删除
     *
     * @param orderNo orderNo
     * @param orderSource orderSource
     * @param skuIdCollection skuIdCollection
     * @return result
     */
    int deleteByOrderNoAndOrderSourceAndSkuIdIn(@Param("orderNo") String orderNo,
        @Param("orderSource") String orderSource, @Param("skuIdCollection") Collection<Long> skuIdCollection);




}
