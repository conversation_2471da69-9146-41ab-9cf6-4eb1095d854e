package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.WarehouseSkuCargoOwnerInfo;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.request.inventory.InventoryDetailQueryRequest;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface WarehouseSkuInventoryMapper extends BaseMapper<WarehouseSkuInventory> {

    Long selectOneIdByWarehouseIdsAndCargoOwnerId(@Param("warehouseIds") List<Long> warehouseIds, @Param("cargoOwnerId") Long cargoOwnerId, @Param("logicInventoryCode") String logicInventoryCode);

    List<WarehouseSkuInventory> getByLogicInventoryLocationAndSkuIds(@Param("logicInventoryLocation") LogicInventoryLocation logicInventoryLocation,
                                                                     @Param("skuIds") Collection<Long> skuIds);

    /**
     * 批量更新库存
     * @param warehouseSkuInventories 需要更新的批次库存
     * @return 实际更新的数据条数
     */
    int batchUpdate(@Param("location") LogicInventoryLocation logicInventoryLocation, @Param("items") List<WarehouseSkuInventory> warehouseSkuInventories);

    /**
     * 批量插入货品库存
     * @param warehouseSkuInventories 需要更新的批次库存
     * @return 实际更新的数据条数
     */
    int batchInsert(@Param("items") List<WarehouseSkuInventory> warehouseSkuInventories);

    /**
     * 批量插入货品库存
     * @param ids 需要更新的批次库存
     * @return 实际更新的数据条数
     */
    int batchDelete(@Param("location") LogicInventoryLocation location, @Param("items") List<Long> ids);

    List<WarehouseSkuInventory> selectWarehouseIdAndSkuIds(@Param("warehouseId") Long warehouseId,
        @Param("codes") Collection<String> code, @Param("skuIds")List<Long> skuIds);

    List<Long> selectSkuIdByWarehouseId(@Param("warehouseId") Long warehouseId);

    int addLock(@Param("warehouseId") Long warehouseId,
                 @Param("codes") List<String> code, @Param("skuIds") Collection<Long> skuIds);

    List<WarehouseSkuInventory> getProcessingWarehouseSkuInventory(@Param("minId") Long minId);

    int deleteByWarehouseSkuInventoryId(@Param("ids") List<Long> ids);

    /**
     * 通过逻辑库位信息与商品id查询
     *
     * @param skuIdCollection skuIdCollection
     * @param logicInventoryLocation logicInventoryLocation
     * @return result
     */
    List<WarehouseSkuInventory> selectBySkuIdInAndLogicInventoryLocation(
        @Param("skuIdCollection") Collection<Long> skuIdCollection,
        @Param("logicInventoryLocation") LogicInventoryLocation logicInventoryLocation);

    /**
     * 根据skuId和仓库Id查询
     * @param skuIds skuId集合
     * @param warehouseIds 仓库id集合
     * @param cargoOwnerIds 货主id集合
     * @param logicInventoryLocationCode 逻辑库位编码
     * @param isExcludeZeroStock 是否排除0
     * @return result
     */
    List<WarehouseSkuCargoOwnerInfo> selectWarehouseSkuOwnerInfo(@Param("skuIds") List<Long> skuIds,
        @Param("warehouseIds") List<Long> warehouseIds, @Param("cargoOwnerIds") List<Long> cargoOwnerIds,
        @Param("logicInventoryLocationCode") String logicInventoryLocationCode,
        @Param("isExcludeZeroStock") boolean isExcludeZeroStock);


    /**
     * 查询库存信息
     * @param conditionList 条件
     * @return 库存信息
     */
    List<WarehouseSkuInventory> selectByWarehouseSkuOwnerInfo(@Param("conditionList") List<WarehouseSkuCargoOwnerInfo> conditionList);

    /**
     * 根据四要素查询
     * @return 库存信息
     */
    List<WarehouseSkuInventory> selectByFromWarehouseSkuOwnerLocation(@Param("request") InventoryDetailQueryRequest request);


    List<WarehouseSkuInventory> getByWarehouseIdList(@Param("warehouseId") Long warehouseId,
        @Param("codes") List<String> code, @Param("skuIds") List<Long> skuIds);



}
