package com.ddmc.ims.dal.mapper.ims;

import com.ddmc.ims.dal.model.ims.LogicInventoryLocationCode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface LogicInventoryLocationCodeMapper extends BaseMapper<LogicInventoryLocationCode> {

    /**
     * 获取所有逻辑库位编码
     *
     * @return 返回数据
     */
    List<LogicInventoryLocationCode> listAll();

    /**
     * 根据逻辑库位编码id获取编码信息
     *
     * @param codes 逻辑库位编码id
     * @return 逻辑库位编码信息
     */
    List<LogicInventoryLocationCode> listLogicInventoryCodeByCodes(@Param("codes") List<String> codes);
}
