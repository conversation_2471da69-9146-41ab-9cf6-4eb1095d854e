package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WarehouseSkuInventoryAllocDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 逻辑库位编码
     */
    private String logicInventoryLocationCode;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 实际占用数量
     */
    private BigDecimal allocQty;

    /**
     * 库存需求日期
     */
    private Date demandTime;

    /**
     * 占用类型，0-占用到货品维度；1-占用到效期批次维度
     */
    private Integer allocDimension;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Integer version;


    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 用途类型
     */
    private String usageCode;


    /**
     * 执行单单号
     */
    private String exeOrderSource;

    /**
     * 执行单单号
     */
    private String exeOrderNo;


}
