package com.ddmc.ims.dal.model.monitor;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 库存出入库量小时快照
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SkuInventoryInOutSnapshot implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 逻辑库位编码
     */
    private String logicLocationCode;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 批次id
     */
    private String lotId;

    /**
     * 入库数量
     */
    private BigDecimal inQty;

    /**
     * 出库数量
     */
    private BigDecimal outQty;

    /**
     * 快照日期-精确到小时(如果小时为0的话则表示为昨天一整天的量)
     */
    private Date snapshotDateTime;

    /**
     * 创建时间
     */
    private Date createTime;


}
