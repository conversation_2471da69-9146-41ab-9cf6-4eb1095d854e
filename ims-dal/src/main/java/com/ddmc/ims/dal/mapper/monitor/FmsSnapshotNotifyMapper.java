package com.ddmc.ims.dal.mapper.monitor;

import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.Date;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 财务快照通知 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-19
 */
public interface FmsSnapshotNotifyMapper extends BaseMapper<FmsSnapshotNotify> {

    FmsSnapshotNotify selectByDayEndDate(@Param("dayEndDate") Date dayEndDate);

    int updateStatusByDayEndDate(@Param("dayEndDate")Date dayEndDate,@Param("status") String status);

    void deleteByDayEndDate(@Param("dayEndDate") Date dayEndDate);
}
