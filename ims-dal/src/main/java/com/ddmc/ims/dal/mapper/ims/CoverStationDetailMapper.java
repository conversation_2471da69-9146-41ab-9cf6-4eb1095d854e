package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.CoverStationDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface CoverStationDetailMapper extends BaseMapper<CoverStationDetail> {

    /**
     * 根据大仓查询覆盖的门店明细
     * @param warehouseId 大仓id
     * @return 门店
     */
    List<CoverStationDetail> getByWarehouseId(@Param("warehouseId") Long warehouseId);

    /**
     * 批量查询大仓覆盖门店详情
     * @param items 详情信息
     * @return 插入数据量
     */
    int batchInsert(@Param("items") List<CoverStationDetail> items);
}
