package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.SkuManufactureTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 凭证详情信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CredentialDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 凭证头id
     */
    private Long credentialHeaderId;

    /**
     * 库存操作仓库id,分表逻辑
     */
    private Long warehouseId;

    private String fromLogicLocationCode;

    /**
     * 来源货主id
     */
    private Long fromCargoOwnerId;

    /**
     * 来源仓库id
     */
    private Long fromWarehouseId;

    private String toLogicLocationCode;

    /**
     * 目标货主id
     */
    private Long toCargoOwnerId;

    /**
     * 目标仓库id
     */
    private Long toWarehouseId;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 批次id
     */
    private String lotId;

    /**
     * 数量，保留3位小数
     */
    private BigDecimal qty;

    /**
     * 库存需求日期时间
     */
    private Date demand;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 库存状态:0-未知，1-可用，2-不可用
     */
    private InventoryStatusEnum inventoryStatus;

    /**
     * 商品类型:0-未知，1-成品，2-原料
     */
    private SkuManufactureTypeEnum skuType;
    /**
     * 用途优先级，批量，隔开
     */
    private String usageCode;

    /**
     * 入库库存状态:0-未知，1-可用，2-不可用
     */
    private InventoryStatusEnum toInventoryStatus;

    /**
     * 单据标记：预售-presale,非预售-none
     */
    private String orderTag;

    /**
     * 目标用途优先级，批量，隔开
     */
    private String toUsageCode;


    /**
     * 命令类型
     */
    private String commandType;

    /**
     * 是否今日上架
     */
    private Boolean todaySale;


    @JsonIgnore
    public List<String> getUsageList() {
        if (StringUtils.isBlank(usageCode)) {
            return Collections.singletonList("");
        }
        return Lists.newArrayList(usageCode.split(","));
    }

    @JsonIgnore
    public List<String> getToUsageList() {
        if (StringUtils.isBlank(toUsageCode)) {
            return Collections.singletonList("");
        }
        return Lists.newArrayList(toUsageCode.split(","));
    }


    @JsonIgnore
    public LogicInventoryLocation getFromLogicInventoryLocation() {

        return new LogicInventoryLocation(fromWarehouseId, fromCargoOwnerId, fromLogicLocationCode);
    }


}
