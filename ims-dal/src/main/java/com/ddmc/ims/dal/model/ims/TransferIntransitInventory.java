package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TransferIntransitInventory implements Serializable {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出库库位货主
     */
    private Long fromCargoOwnerId;

    /**
     * 出库仓库id
     */
    private Long fromWarehouseId;

    /**
     * 出库库位编码
     */
    private String fromLogicInventoryLocationCode;

    /**
     * 入库库位货主
     */
    private Long toCargoOwnerId;

    /**
     * 入库逻辑库位编码
     */
    private String toLogicInventoryLocationCode;

    /**
     * 入库仓库id
     */
    private Long toWarehouseId;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 管理单据号
     */
    private String orderNo;

    /**
     * 0-一配；1-二配
     */
    private Integer deliveryMode;

    /**
     * 期望出库时间
     */
    private Date expectOutTime;

    /**
     * 期望入库时间
     */
    private Date expectInTime;

    /**
     * 期望调拨数量
     */
    private BigDecimal planQty;

    /**
     * 在途数量
     */
    private BigDecimal intransitQty;

    /**
     * 已经出库数量
     */
    private BigDecimal allocQty;

    /**
     * 待出库数量
     */
    private BigDecimal waitAllocQty;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 用途优先级，批量，隔开
     */
    private String usageCode;

    /**
     * 目标用途
     */
    private String toUsageCode;



}
