package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 凭证详情信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
public interface CredentialDetailMapper extends BaseMapper<CredentialDetail> {

    List<CredentialDetail> selectByCredentialHeaderId(@Param("credentialHeaderId") Long credentialHeaderId);

    int batchInsert(@Param("items") List<CredentialDetail> credentialDetails);

    int deleteByCredentialHeaderId(@Param("credentialHeaderId") Long credentialHeaderId);

    int deleteByHeaderIds(@Param("credentialHeaderIds") List<Long> credentialHeaderIds);

    List<CredentialDetail> selectByCredentialHeaderIds(@Param("headIds") List<Long> headIds);

    List<CredentialDetail> getByMinId(@Param("minId") Long minId);

}
