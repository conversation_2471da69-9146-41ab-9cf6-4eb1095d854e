package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.WarehouseModLock;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface WarehouseModLockMapper extends BaseMapper<WarehouseModLock> {


    int addLock(@Param("warehouseId") Long warehouseId, @Param("modNumList") Set<Long> modNumList);

    List<WarehouseModLock> selectByWarehouseId(@Param("warehouseId") Long warehouseId );

    void batchInsert(@Param("items") List<WarehouseModLock> insertWarehouseModLocks);
}
