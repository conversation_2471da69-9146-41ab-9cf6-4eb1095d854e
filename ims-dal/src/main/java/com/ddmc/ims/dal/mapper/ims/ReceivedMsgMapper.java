package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.ReceivedMsgBusinessEnum;
import com.ddmc.ims.dal.model.ims.ReceivedMsg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
public interface ReceivedMsgMapper extends BaseMapper<ReceivedMsg> {

    /**
     * 根据业务类型查询未处理的消息
     * @param receivedMsgBusinessEnum 业务类型
     * @param id 最小id（不包含）
     * @return 主键id
     */
    List<Long> getWaitingHandleMsg(@Param("businessType")ReceivedMsgBusinessEnum receivedMsgBusinessEnum, @Param("lastId") Long id);

    void completeReceivedMsg(@Param("id") Long id);
}
