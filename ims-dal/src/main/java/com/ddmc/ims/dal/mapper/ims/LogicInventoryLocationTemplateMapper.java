package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.condition.LogicInventoryTemplateCondition;
import com.ddmc.ims.dal.model.ims.LogicInventoryLocationTemplate;
import com.github.pagehelper.Page;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface LogicInventoryLocationTemplateMapper extends BaseMapper<LogicInventoryLocationTemplate> {

    /**
     * 根据货主名称获取货主对应的所有逻辑库位模板
     *
     * @return 逻辑库位模板
     */
    List<LogicInventoryLocationTemplate> listAll();

    /**
     * 根据条件（仓库主体编码 or 货主id or仓库类型）获取模板信息
     * @param condition 仓库主体编码 or 货主id or 仓库类型
     * @return 模板信息
     */
    Page<LogicInventoryLocationTemplate> listByCondition(
        @Param("condition") LogicInventoryTemplateCondition condition);

    /**
     * 根据仓库主体编码+货主id+仓库类型获取模板信息
     * @param warehouseCorporationCode 仓库主体编码
     * @param cargoOwnerId 货主id
     * @param warehouseType 仓库类型
     * @return 模板信息
     */
    LogicInventoryLocationTemplate selectByCorporationCodeAndCargoIdAndType(
        @Param("warehouseCorporationCode") String warehouseCorporationCode, @Param("cargoOwnerId") Long cargoOwnerId,
        @Param("warehouseType") Integer warehouseType);
}
