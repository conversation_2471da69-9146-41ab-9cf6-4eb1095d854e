package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.dal.condition.InTransitAllocDetailCondition;
import com.ddmc.ims.dal.model.ims.IntransitInventoryAllocDetail;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 在途库存占用效期表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface IntransitInventoryAllocDetailMapper extends BaseMapper<IntransitInventoryAllocDetail> {

    List<IntransitInventoryAllocDetail> selectSkuIdByWarehouseIdAndDeliveryDateAndAllocMoreZero(@Param("warehouseId") Long warehouseId,
        @Param("deliveryDate") Date deliveryDate);

    List<IntransitInventoryAllocDetail> selectLessDeliveryDateByWarehouseIdAndSkuIdsAndAllocMoreZero(
        @Param("warehouseId") Long warehouseId, @Param("deliveryDate") Date deliveryDate,
        @Param("skuIds") List<Long> skuIds);


    List<IntransitInventoryAllocDetail> selectByLocationAndOriDeliveryDate(
        @Param("location") LogicInventoryLocation location, @Param("oriDeliveryDate") Date oriDeliveryDate, @Param("skuIds") List<Long> skuIds);


    List<IntransitInventoryAllocDetail> selectByWarehouseIdAndSkuIdsAndOriDeliveryDate(
        @Param("warehouseId") Long warehouseId, @Param("deliveryDate") Date deliveryDate,
        @Param("oriDeliveryDate") Date oriDeliveryDate, @Param("skuIds") List<Long> skuIds);


    List<IntransitInventoryAllocDetail> selectByLogicInventoryLocationAndDateAndSkuIds(
        @Param("location") LogicInventoryLocation location, @Param("deliveryDate") Date deliveryDate,
        @Param("oriDeliveryDate") Date oriDeliveryDate,
        @Param("skuIds") List<Long> skuIds);

    int batchUpdate(@Param("items") List<IntransitInventoryAllocDetail> updateIntransitInventoryAllocDetailList);

    int batchUpdateToTransferInQtyAndAllocQty(@Param("items") List<IntransitInventoryAllocDetail> updateIntransitInventoryAllocDetailList);



    int batchUpdateToTransferInQtyAndTransferInNotAllocQty(@Param("items") List<IntransitInventoryAllocDetail> items);


    int batchUpdateToTransferInQtyAndToAllocQty(@Param("items") List<IntransitInventoryAllocDetail> items);


    void batchInsert(@Param("items") List<IntransitInventoryAllocDetail> intransitInventoryAllocDetails);


    List<IntransitInventoryAllocDetail> selectByConditions(
        @Param("conditions") List<InTransitAllocDetailCondition> conditions);

    /**
     * 通过创建时间查在该时间前最大的id
     *
     * @param maxCreateTime maxCreateTime
     * @return result
     */
    Long selectOneIdByCreateTimeBefore(@Param("maxCreateTime") Date maxCreateTime);


    List<IntransitInventoryAllocDetail> selectByMaxIdLimit200(@Param("maxId") Long maxId);

}
