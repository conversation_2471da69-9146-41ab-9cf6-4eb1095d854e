package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.bo.TransferOrderNoAndSource;
import com.ddmc.ims.dal.condition.SkuAndManufactureDateCondition;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryLotDetail;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface TransferIntransitInventoryLotDetailMapper extends BaseMapper<TransferIntransitInventoryLotDetail> {


    int batchUpdate(@Param("items") List<TransferIntransitInventoryLotDetail> transferIntransitInventoryLotDetails);

    int batchInsert(@Param("items") List<TransferIntransitInventoryLotDetail> transferIntransitInventoryLotDetails);

    /**
     * 根据单号与sku与生产日期查询
     *
     * @param transferOrderNoAndSource transferOrderNoAndSource
     * @param conditions conditions
     * @return result
     */
    List<TransferIntransitInventoryLotDetail> selectByOrderSourceAndNoAndLotSkuIds(
        @Param("transferOrderNoAndSource") TransferOrderNoAndSource transferOrderNoAndSource,
        @Param("conditions") List<SkuAndManufactureDateCondition> conditions);


    /**
     * 根据单号与sku
     *
     * @param transferOrderNoAndSource transferOrderNoAndSource
     * @param skuIds skuIds
     * @return result
     */
    List<TransferIntransitInventoryLotDetail> selectByOrderSourceAndNoAndSkuIds(
        @Param("transferOrderNoAndSource") TransferOrderNoAndSource transferOrderNoAndSource,
        @Param("conditions") List<Long> skuIds);

    List<TransferIntransitInventoryLotDetail> getByMinId(@Param("maxId") Long maxId, @Param("limit") int limit);

    /**
     * 根据单据号删除单据信息
     */
    void deleteByOrderSourceAndNo(@Param("transferOrderNoAndSource") TransferOrderNoAndSource transferOrderNoAndSource);
}
