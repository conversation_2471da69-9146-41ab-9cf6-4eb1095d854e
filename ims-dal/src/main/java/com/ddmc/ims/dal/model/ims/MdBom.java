package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ddmc.ims.common.enums.manufacture.BomSkuTypeEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * bom关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MdBom implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * bomId
     */
    private String bomId;

    /**
     * 10-启用,20-废弃
     */
    private Integer bomStatus;

    /**
     * 生效开始时间
     */
    private Date effectStartTime;

    /**
     * 生效结束时间
     */
    private Date effectEndTime;

    /**
     * 原料比成品：10-多对一 20-一对多 30-一对一
     */
    private Integer manufactureType;

    /**
     * 公司主体
     */
    private String corporationCode;


    /**
     * 版本号
     */
    private Long version;

    /**
     * 货品Id
     */
    private Long skuId;


    /**
     * 货品单位
     */
    private String skuUnit;

    /**
     * 100-成品,200-原料,300-耗材
     */
    private BomSkuTypeEnum skuType;

    /**
     * 配比数量（例：100表示100个原料对应一个成品）
     */
    private BigDecimal proportion;

    /**
     * 采购分摊比例
     */
    private Integer apportion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
