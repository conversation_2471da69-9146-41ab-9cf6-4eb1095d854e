package com.ddmc.ims.dal.mapper.monitor;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 异动任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface SnapshotTaskMapper extends BaseMapper<SnapshotTask> {


    Integer countByWarehouseIdAndSnapshotTimeAndStatus(@Param("warehouseId") Long warehouseId,
        @Param("snapshotTime") Date snapshotTime, @Param("snapshotType") Integer snapshotType,
        @Param("status") Integer status);

    void batchInsert(@Param("items") List<SnapshotTask> taskList);

    List<SnapshotTask> selectBySnapshotTypeAndSnapshotTimeAndStatus(@Param("snapshotType") Integer snapshotType,
        @Param("snapshotTime") Date snapshotTime,@Param("warehouseIds") List<Long> warehouseIds, @Param("status") Integer status);

    List<SnapshotTask> selectBySnapshotTypeAndSnapshotTime(@Param("snapshotType") Integer snapshotType,
        @Param("snapshotTime") Date snapshotTime);


    /**
     * 根据仓库，类型与状态查询
     *
     * @param warehouseIdCollection warehouseIdCollection
     * @param snapshotType snapshotType
     * @param snapshotTime snapshotTime
     * @return result
     */
    List<SnapshotTask> selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(
        @Param("warehouseIdCollection") Collection<Long> warehouseIdCollection,
        @Param("snapshotType") SnapshotTypeEnum snapshotType, @Param("snapshotTime") Date snapshotTime);




    Integer updateStatusById(@Param("id") Long taskId, @Param("newStatus") SnapshotStatusEnum newStatus, @Param("oldStatus") SnapshotStatusEnum oldStatus);


    /**
     * 根据时间类型状态查询
     *
     * @param snapshotTime snapshotTime
     * @param snapshotType snapshotType
     * @param status status
     * @return result
     */
    List<SnapshotTask> selectBySnapshotTimeAndSnapshotTypeAndStatus(@Param("snapshotTime") Date snapshotTime,
        @Param("snapshotType") SnapshotTypeEnum snapshotType, @Param("status") SnapshotStatusEnum status);


    SnapshotTask selectOneBySnapshotTypeAndSnapshotTimeAndStatus(@Param("snapshotType") SnapshotTypeEnum snapshotType, @Param("snapshotTime") Date snapshotTime,@Param("statusList") List<SnapshotStatusEnum> statusList);

    /**
     * 根据仓库、快照时间、类型删除
     *
     * @param warehouseIdCollection warehouseIdCollection
     * @param snapshotTime snapshotTime
     * @param snapshotType snapshotType
     * @return result
     */
    int deleteByWarehouseIdInAndSnapshotTimeAndSnapshotType(
        @Param("warehouseIdCollection") Collection<Long> warehouseIdCollection,
        @Param("snapshotTime") Date snapshotTime, @Param("snapshotType") SnapshotTypeEnum snapshotType);


    int deleteBySnapshotTimeAndSnapshotType(
        @Param("snapshotTime") Date snapshotTime, @Param("snapshotType") SnapshotTypeEnum snapshotType);



}
