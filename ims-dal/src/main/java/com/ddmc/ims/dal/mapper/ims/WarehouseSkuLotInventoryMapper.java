package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.SkuIdAndLotIdPair;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.ddmc.ims.request.inventory.InventoryDetailQueryRequest;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface WarehouseSkuLotInventoryMapper extends BaseMapper<WarehouseSkuLotInventory> {

    /**
     * 通过逻辑库位信息与商品id查询
     *
     * @param skuIdCollection skuIdCollection
     * @param logicInventoryLocation logicInventoryLocation
     * @return result
     */
    List<WarehouseSkuLotInventory> selectBySkuIdInAndLogicInventoryLocation(
        @Param("skuIdCollection") Collection<Long> skuIdCollection,
        @Param("logicInventoryLocation") LogicInventoryLocation logicInventoryLocation);

    List<WarehouseSkuLotInventory> getByLogicInventoryLocationAndLotIds(
        @Param("logicInventoryLocation") LogicInventoryLocation logicInventoryLocation,
        @Param("skuAndLotIdPairs") List<SkuIdAndLotIdPair> skuIdAndLotIdPairs);

    /**
     * 批量更新库存
     *
     * @param warehouseSkuLotInventories 需要更新的批次库存
     * @return 实际更新的数据条数
     */
    int batchUpdate(@Param("location") LogicInventoryLocation logicInventoryLocation,
        @Param("items") List<WarehouseSkuLotInventory> warehouseSkuLotInventories);

    /**
     * 批量更新库存
     *
     * @param ids 需要更新的批次库存
     * @return 实际更新的数据条数
     */
    int batchDelete(@Param("location") LogicInventoryLocation logicInventoryLocation, @Param("items") List<Long> ids);

    /**
     * 批量插入货品库存
     *
     * @param warehouseSkuLotInventories 需要更新的批次库存
     * @return 实际更新的数据条数
     */
    int batchInsert(@Param("items") List<WarehouseSkuLotInventory> warehouseSkuLotInventories);

    /**
     * 查询批次库存
     *
     * @param skuIdAndLotIdPairs 货品批次列表
     * @param logicInventoryLocation 逻辑库位
     * @return 响应
     */
    List<WarehouseSkuLotInventory> selectBySkuIdAndLotIdPairAndLogicInventoryLocation(
        @Param("items") Collection<SkuIdAndLotIdPair> skuIdAndLotIdPairs,
        @Param("logicInventoryLocation") LogicInventoryLocation logicInventoryLocation);


    /**
     * 查询仓库对应的skuId
     *
     * @param logicInventoryLocation logicInventoryLocation
     * @return result
     */
    List<Long> selectDistinctSkuIdByWarehouseId(
        @Param("logicInventoryLocation") LogicInventoryLocation logicInventoryLocation);


    /**
     * 查询sku的批次库存
     *
     * @param request 请求
     * @return result
     */
    List<WarehouseSkuLotInventory> listSkuLotInventory(@Param("request") InventoryDetailQueryRequest request);


    List<WarehouseSkuLotInventory> getByWarehouseIdList(@Param("warehouseId") Long warehouseId,
        @Param("codes") List<String> code, @Param("skuIds") List<Long> skuIds);


    List<String> getLotIdByWarehouseIdAndSkuIdAndLogicCode(
        @Param("warehouseId") Long warehouseId, @Param("skuId") Long skuId,@Param("codes") List<String> code);


}
