package com.ddmc.ims.dal.model.monitor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 逻辑库存每小时快照
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SkuInventorySnapshotUsagePerHour implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 逻辑库位编码
     */
    private String logicLocationCode;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 用途
     */
    private String usageCode;

    /**
     * 可用数量
     */
    private BigDecimal freeQty;

    /**
     * 冻结数量
     */
    private BigDecimal frozenQty;

    /**
     * 在途数量，已出未收量
     */
    private BigDecimal transferIntransitQty;

    /**
     * 快照日期-精确到小时
     */
    private Date snapshotDateTime;

    /**
     * 创建时间
     */
    private Date createTime;


}
