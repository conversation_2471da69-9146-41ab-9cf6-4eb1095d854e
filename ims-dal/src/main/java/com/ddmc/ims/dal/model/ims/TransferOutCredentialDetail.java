package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 调拨出库凭证详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TransferOutCredentialDetail implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 调拨出库凭证id
     */
    private Long transferOutCredentialId;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 出库数量
     */
    private BigDecimal outQty;

    /**
     * 批次id
     */
    private String lotId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createUserName;


}
