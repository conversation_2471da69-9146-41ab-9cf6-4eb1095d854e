package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.conditions.WarehouseIdLotIdPair;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface InventoryLotInfoMapper extends BaseMapper<InventoryLotInfo> {

    /**
     * 根据仓库id + 批次id查询批次信息
     * @param warehouseId 仓库id
     * @param lotId 标准批次id
     * @return 库存批次信息
     */
    InventoryLotInfo getByWarehouseIdAndLotId(@Param("warehouseId") Long warehouseId, @Param("lotId") String lotId);

    /**
     * 批量根据仓库id + 批次id查询批次信息
     * @param warehouseIdLotIdPairs 仓库 + 批次对
     * @return 库存批次信息
     */
    List<InventoryLotInfo> batchGetByWarehouseIdAndLotId(@Param("items") List<WarehouseIdLotIdPair> warehouseIdLotIdPairs);

    /**
     * 批量根据仓库id + 批次id查询批次信息
     * @param warehouseId 仓库
     * @param lotIds 批次id
     * @return 库存批次信息
     */
    List<InventoryLotInfo> batchGetByWarehouseIdAndLotIds(@Param("warehouseId") Long warehouseId, @Param("lotIds") List<String> lotIds);

    /**
     * 根据货品id + 批量仓库id + 生产日期查询库存批次信息
     * @param skuId 货品id
     * @param warehouseIds 仓库id集合
     * @param manufactureDate 生产日期
     * @return 库存批次信息
     */
    List<InventoryLotInfo> getBySkuIdAndWarehouseIdsAndManufactureDate(@Param("skuId") Long skuId,
                                                                       @Param("warehouseIds") List<Long> warehouseIds,
                                                                       @Param("manufactureDate") Date manufactureDate);

    /**
     * 批量查询库存批次信息，库存批次表分表存储。要求插入的数据是同一个表的数据
     * @param inventoryLotInfos 库存批次信息
     * @return 实际插入数量
     */
    int batchInsert(@Param("items") List<InventoryLotInfo> inventoryLotInfos);

    /**
     * 批量更新库存批次信息，库存批次表分表存储。要求插入的数据是同一个表的数据
     * @param inventoryLotInfos 库存批次信息
     * @return 实际更新数量
     */
    int batchUpdate(@Param("items") List<InventoryLotInfo> inventoryLotInfos);

    int batchDelete(@Param("warehouseId") Long warehouseId, @Param("items") List<String> lotIds);

    /**
     * 根据货品id + 批量仓库id + 生产日期查询库存批次信息
     * @param skuIds 货品id集合
     * @param warehouseId 仓库id
     * @param manufactureDate 生产日期
     * @return 库存批次信息
     */
    List<String> getLotIdBySkuIdsAndWarehouseIdAndManufactureDate(@Param("warehouseId") Long warehouseId,
                                                                  @Param("skuIds") List<Long> skuIds,
                                                                  @Param("manufactureDate") Date manufactureDate);

    List<InventoryLotInfo> getInventoryLotInfoByTableIndex(@Param("tableIndex") Long tableIndex,
        @Param("skuIds") List<Long> skuIds,
        @Param("manufactureDate") Date manufactureDate);




    /**
     * 批量更新库存批次信息，库存批次表分表存储。要求插入的数据是同一个表的数据
     * @param inventoryLotInfos 库存批次信息
     * @return 实际更新数量
     */
    int batchUpdateByTableIndex(@Param("tableIndex") Long tableIndex, @Param("items") List<InventoryLotInfo> inventoryLotInfos);



    List<InventoryLotInfo> getInventoryLotInfoByTableIndex(@Param("tableIndex") Long tableIndex,
                                                           @Param("maxId") Long maxId,
                                                           @Param("limit") int limit);



}
