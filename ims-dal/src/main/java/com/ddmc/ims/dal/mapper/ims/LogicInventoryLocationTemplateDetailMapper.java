package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.LogicInventoryLocationTemplateDetail;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface LogicInventoryLocationTemplateDetailMapper extends BaseMapper<LogicInventoryLocationTemplateDetail> {

    /**
     * 根据逻辑库位模板获取模板下逻辑编码详情
     *
     * @param templateId 逻辑库位模板id
     * @return 模板下编码详情信息
     */
    List<LogicInventoryLocationTemplateDetail> listByTemplateId(@Param("templateId") Long templateId);

    /**
     * 批量保存逻辑库位模板详情信息
     *
     * @param addTemplateDetailList 逻辑库位模板下详情
     */
    int batchInsertList(@Param("items") List<LogicInventoryLocationTemplateDetail> addTemplateDetailList);

    /**
     * 批量修改逻辑库位模板下详情信息
     *
     * @param updateTemplateDetailList 逻辑库位模板下详情
     */
    int batchUpdateList(@Param("items") List<LogicInventoryLocationTemplateDetail> updateTemplateDetailList);

    /**
     * 根据逻辑库位模板id + 库位编码获取库位
     * @param templateId 模板id
     * @param logicInventoryCode 库位编码
     * @return 库位信息
     */
    LogicInventoryLocationTemplateDetail selectByTemplateIdAndLogicInventoryCode(@Param("templateId") Long templateId, @Param("logicInventoryCode") String logicInventoryCode);
}
