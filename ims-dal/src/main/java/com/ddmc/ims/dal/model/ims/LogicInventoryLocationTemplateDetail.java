package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LogicInventoryLocationTemplateDetail implements Serializable {

    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 逻辑库位编码
     */
    private String logicInventoryCode;

    /**
     * 逻辑库位模板id
     */
    private Long logicInventoryTemplateId;

    /**
     * 是否关联实物库存，0-不关联；1-关联
     */
    private YesNoEnum refRealInventory;

    private Date createTime;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    private Date updateTime;

    /**
     * 创建人id
     */
    private String updateUserId;

    /**
     * 创建人姓名
     */
    private String updateUserName;


}
