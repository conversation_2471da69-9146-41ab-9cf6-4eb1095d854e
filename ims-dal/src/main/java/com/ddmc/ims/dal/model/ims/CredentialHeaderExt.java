package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 凭证头信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class CredentialHeaderExt implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 凭证头id
     */
    private Long credentialHeaderId;

    /**
     * 扩展编码
     */
    private String extCode;

    /**
     * 扩展值
     */
    private String extValue;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    public CredentialHeaderExt(String extCode, String extValue) {
        this.extCode = extCode;
        this.extValue = extValue;
    }
}
