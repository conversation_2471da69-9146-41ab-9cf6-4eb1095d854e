package com.ddmc.ims.dal.mapper.monitor;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.condition.SnapshotFixCheckItem;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 逻辑库存每小时快照 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
public interface SkuInventorySnapshotPerHourMapper extends BaseMapper<SkuInventorySnapshotPerHour> {


    /**
     * 查询距离当前最大的快照时间
     *
     * @param warehouseId warehouseId
     * @param nowSnapshotDateTime nowSnapshotDateTime
     * @return result
     */
    Date selectOneMaxSnapshotDateTimeByWarehouseId(@Param("warehouseId") Long warehouseId,
        @Param("nowSnapshotDateTime") Date nowSnapshotDateTime);


    /**
     * 根据快照时间与仓库查询
     *
     * @param snapshotDateTime 快照时间
     * @param warehouseId 仓库id
     * @return result
     */
    List<SkuInventorySnapshotPerHour> selectBySnapshotDateTimeAndWarehouseId(
        @Param("snapshotDateTime") Date snapshotDateTime, @Param("warehouseId") Long warehouseId);


    /**
     * 根据快照时间+仓库+货品id查询
     *
     * @param snapshotDateTime snapshotDateTime
     * @param warehouseId warehouseId
     * @param skuIdCollection skuIdCollection
     * @return result
     */
    List<SkuInventorySnapshotPerHour> selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
        @Param("snapshotDateTime") Date snapshotDateTime, @Param("warehouseId") Long warehouseId,
        @Param("skuIdCollection") Collection<Long> skuIdCollection);





    /**
     * 批量插入
     *
     * @param list list
     * @return result
     */
    int insertList(@Param("list") List<SkuInventorySnapshotPerHour> list);



    /**
     * 按仓加快照时间删除快照数据
     *
     * @param warehouseId warehouseId
     * @param snapshotDateTime snapshotDateTime
     * @return result
     */
    int deleteByWarehouseIdAndSnapshotDateTime(@Param("warehouseId") Long warehouseId,
        @Param("snapshotDateTime") Date snapshotDateTime);


    /**
     * 批量按仓加快照时间删除快照数据
     *
     * @param warehouseIdCollection warehouseIdCollection
     * @param snapshotDateTime snapshotDateTime
     * @return result
     */
    int deleteByWarehouseIdInAndSnapshotDateTime(@Param("warehouseIdCollection") Collection<Long> warehouseIdCollection,
        @Param("snapshotDateTime") Date snapshotDateTime);


    /**
     * 按最小id进行删除
     *
     * @param id 最小id
     * @return 影响条数
     */
    Long deleteByMinId(@Param("id") Long id);

    /**
     * 按最大id进行删除
     *
     * @param id 最大id
     * @return 影响条数
     */
    Long deleteByMaxId(@Param("id") Long id);

    int updateByFixItem(@Param("fixItems") List<SnapshotFixCheckItem> fixCheckItems,
        @Param("snapshotDateTime") Date snapshotDateTime);


    List<SkuInventorySnapshotPerHour> selectByWarehouseIdAndLocationCode( @Param("warehouseId") Long warehouseId,  @Param("snapshotDate")Date snapshotDate, @Param("locationCode") String locationCode);


    /**
     * 通过创建时间查在该时间前最大的id
     *
     * @param maxCreateTime maxCreateTime
     * @return result
     */
    Long selectOneIdByCreateTimeBefore(@Param("maxCreateTime") Date maxCreateTime);

    List<SkuInventorySnapshotPerHour> getByMinId(@Param("maxId") Long maxId, @Param("limit") int limit);



}
