package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocExpireDetail;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface WarehouseSkuInventoryAllocExpireDetailMapper extends BaseMapper<WarehouseSkuInventoryAllocExpireDetail> {

    /**
     * 批量插入
     *
     * @param items items
     * @return result
     */
    int batchInsert(@Param("items") List<WarehouseSkuInventoryAllocExpireDetail> items);


    List<WarehouseSkuInventoryAllocExpireDetail> selectByWarehouseIdAndDemandTimeAndSkuIds(@Param("warehouseId")Long warehouseId, @Param("manufactureDate") Date manufactureDate, @Param("skuIds") List<Long> skuIds);

    List<WarehouseSkuInventoryAllocExpireDetail> selectByLogicInventoryLocationAndDateAndSkuIds(
        @Param("location") LogicInventoryLocation location, @Param("manufactureDate") Date manufactureDate,
        @Param("skuIds") List<Long> skuIds);

    int batchUpdate(@Param("items") List<WarehouseSkuInventoryAllocExpireDetail> items);
}
