package com.ddmc.ims.dal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import lombok.Data;

@Data
public class BasePO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 创建人名字
     */
    private String creator;

    /**
     * 修改人ID
     */
    private String updateBy;

    /**
     * 修改人名字
     */
    private String updater;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
