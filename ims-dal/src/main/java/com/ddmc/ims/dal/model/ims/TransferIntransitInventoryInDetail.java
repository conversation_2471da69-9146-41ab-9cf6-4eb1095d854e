package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 调拨在途出库明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TransferIntransitInventoryInDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单来源
     */
    private String refOrderSource;

    /**
     * 管理单据号
     */
    private String refOrderNo;

    /**
     * exe订单来源
     */
    private String refExeOrderSource;

    /**
     * exe管理单据号
     */
    private String refExeOrderNo;

    /**
     * 出库库位货主
     */
    private Long fromCargoOwnerId;

    /**
     * 出库仓库id
     */
    private Long fromWarehouseId;

    /**
     * 出库库位编码
     */
    private String fromLogicInventoryLocationCode;

    /**
     * 入库库位货主
     */
    private Long toCargoOwnerId;

    /**
     * 入库逻辑库位编码
     */
    private String toLogicInventoryLocationCode;

    /**
     * 入库仓库id
     */
    private Long toWarehouseId;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 1-一配；2-二配
     */
    private Integer deliveryMode;

    /**
     * 批次id
     */
    private String lotId;

    /**
     * 期望出库时间
     */
    private Date expectOutTime;

    /**
     * 期望入库时间
     */
    private Date expectInTime;

    /**
     * 出库时间
     */
    private Date inTime;

    /**
     * 出库数量
     */
    private BigDecimal inQty;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
