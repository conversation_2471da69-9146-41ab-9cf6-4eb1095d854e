package com.ddmc.ims.dal.model.monitor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 财务库存每日快照
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FmsSkuInventorySnapshotPerDay implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库id
     */
    private Long skuId;

    /**
     * 快照日期
     */
    private Date snapshotDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 数量
     */
    private BigDecimal qty;

    /**
     * 本日入库数量
     */
    private BigDecimal inQty;

    /**
     * 本日出库数量
     */
    private BigDecimal outQty;


    /**
     * 业务库存数量
     */
    private BigDecimal imsQty;

    /**
     * 加工中数量
     */
    private BigDecimal processingQty;

    /**
     * 在途数量
     */
    private BigDecimal transferIntransitQty;

    /**
     * 采购部分入库数量
     */
    private BigDecimal purchasePartInQty;


    /**
     * 调拨部分入数量
     */
    private BigDecimal transferPartInQty;


    /**
     * 反加工未完工提前入库数量
     */
    private BigDecimal reverseProcessingQty;



    /**
     * 其他差异数量
     */
    private BigDecimal otherDiffQty;




}
