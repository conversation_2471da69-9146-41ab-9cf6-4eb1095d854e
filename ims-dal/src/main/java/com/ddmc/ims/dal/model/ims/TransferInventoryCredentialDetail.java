package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 调整在库库存凭证明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TransferInventoryCredentialDetail implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 库存转移凭证id
     */
    private Long transferInventoryCredentialId;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 批次id
     */
    private String lotId;

    /**
     * 库存转移类型；0-可用转不可用；1-不可用转可用
     */
    private Integer inventoryTransferType;

    /**
     * 转移数量
     */
    private BigDecimal transferQty;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人id
     */
    private String createBy;

    /**
     * 操作人姓名
     */
    private String createUserName;


}
