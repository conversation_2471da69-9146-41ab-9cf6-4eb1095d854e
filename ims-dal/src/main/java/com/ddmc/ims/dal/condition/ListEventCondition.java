package com.ddmc.ims.dal.condition;

import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class ListEventCondition {

    private Long eventId;

    private Collection<Long> eventIdList;

    private Long parentEventId;

    private Integer status;

    private List<Integer> statusList;

    private Date createTimeStart;

    private Date createTimeEnd;

    private Integer triggerCount;

    private Integer triggerCountStart;

    private Integer triggerCountEnd;

    private Integer limit;

    private String keyType;

    private List<String> keyTypeList;

    private String keyId;
}
