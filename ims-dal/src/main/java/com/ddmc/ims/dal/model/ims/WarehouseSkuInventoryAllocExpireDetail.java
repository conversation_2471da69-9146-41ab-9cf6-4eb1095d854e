package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithUsage;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WarehouseSkuInventoryAllocExpireDetail implements Serializable {
    private static final long serialVersionUID=1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 逻辑库位编码
     */
    private String logicInventoryLocationCode;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 实际占用数量
     */
    private BigDecimal allocQty;

    /**
     * 生产日期
     */
    private Date manufactureDate;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 用途类型
     */
    private String usageCode;

    public LogicInventoryLocationWithUsage getLogicInventoryLocationWithUsage() {
        LogicInventoryLocation location = new LogicInventoryLocation(this.getWarehouseId(), this.getCargoOwnerId(),
            this.getLogicInventoryLocationCode());
        return new LogicInventoryLocationWithUsage(location, this.getSkuId(), this.getUsageCode());
    }

}
