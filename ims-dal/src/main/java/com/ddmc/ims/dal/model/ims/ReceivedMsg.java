package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ddmc.ims.common.ReceivedMsgBusinessEnum;
import com.ddmc.ims.common.enums.SystemTypeEnum;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ReceivedMsg implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 请求来源
     */
    private SystemTypeEnum requestSource;

    /**
     * 请求方单号
     */
    private String requestNo;

    /**
     * 业务类型
     */
    private ReceivedMsgBusinessEnum businessType;

    /**
     * 消息体
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态
     */
    private YesNoEnum status;


}
