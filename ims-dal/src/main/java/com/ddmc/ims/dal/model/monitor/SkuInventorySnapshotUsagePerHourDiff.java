package com.ddmc.ims.dal.model.monitor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 逻辑库存小时快照差异表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SkuInventorySnapshotUsagePerHourDiff implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 逻辑库位编码
     */
    private String logicLocationCode;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 用途
     */
    private String usageCode;

    /**
     * 快照id
     */
    private Long snapshotId;

    /**
     * 快照数量
     */
    private BigDecimal snapshotQty;

    /**
     * 凭证数量
     */
    private BigDecimal credentialQty;

    /**
     * 库存数量
     */
    private BigDecimal inventoryQty;

    /**
     * 比对次数
     */
    private Integer triggerCount;

    /**
     * 凭证开始时间
     */
    private Date credentialStartDate;

    /**
     * 凭证截止时间
     */
    private Date credentialEndDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
