package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 在途库存占用效期表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class IntransitInventoryAllocDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 逻辑库位编码
     */
    private String logicInventoryLocationCode;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 用途
     */
    private String usageCode;

    /**
     * 在途占用数量(待转锁)
     */
    private BigDecimal toAllocQty;

    /**
     * 已入未转锁
     */
    private BigDecimal transferInNotAllocQty;


    /**
     * 待入库数量
     */
    private BigDecimal toTransferInQty;


    /**
     * 源履约日期
     */
    private Date oriDeliveryDate;



    /**
     * 履约日期
     */
    private Date deliveryDate;

    /**
     * 是否今日上架
     */
    private boolean todaySale;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;





}
