package com.ddmc.ims.dal.conditions;

import lombok.Getter;

import java.util.Objects;

@Getter
public class WarehouseIdLotIdPair {

    private Long warehouseId;

    private String lotId;

    public WarehouseIdLotIdPair(Long warehouseId, String lotId) {
        this.warehouseId = warehouseId;
        this.lotId = lotId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WarehouseIdLotIdPair that = (WarehouseIdLotIdPair) o;
        return Objects.equals(warehouseId, that.warehouseId) && Objects.equals(lotId, that.lotId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(warehouseId, lotId);
    }
}
