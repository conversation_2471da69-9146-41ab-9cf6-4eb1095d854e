package com.ddmc.ims.dal.model.ims;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 调整调拨状态凭证
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BaseCredential implements Serializable {

    protected static final long serialVersionUID=1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    /**
     * 订单来源
     */
    protected String orderSource;

    /**
     * 订单号
     */
    protected String orderNo;
    

    /**
     * 业务操作时间
     */
    protected Date businessTime;

    /**
     * 创建时间
     */
    protected Date createTime;

    /**
     * 创建人id
     */
    protected String createBy;

    /**
     * 创建人姓名
     */
    protected String createUserName;


}
