package com.ddmc.ims.dal.helper;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标注在实体类的id属性上，指定该id的生成序列
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface IdGenerator {

    /**
     * @return 生成id的sequence
     */
    com.ddmc.ims.dal.helper.SequenceEnum value();
}
