package com.ddmc.ims.dal.condition;

import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class SnapshotFixUsageCheckItem {

    public SnapshotFixUsageCheckItem(Long skuId, Long warehouseId, String usageCode, BigDecimal inventoryQty,
        BigDecimal snapShotQty, BigDecimal credentialQty, Long cargoOwnerId, String logicInventoryLocationCode) {
        this.skuId = skuId;
        this.warehouseId = warehouseId;
        this.usageCode = usageCode;
        this.inventoryQty = inventoryQty;
        this.snapShotQty = snapShotQty;
        this.credentialQty = credentialQty;
        this.cargoOwnerId = cargoOwnerId;
        this.logicInventoryLocationCode = logicInventoryLocationCode;
    }

    /**
     * 货品id
     */
    private Long skuId;


    /**
     * 仓库id
     */
    private Long warehouseId;


    /**
     * 批次id
     */
    private String usageCode;

    /**
     * 货主id
     */
    private Long cargoOwnerId;

    /**
     * 逻辑库位编码
     */
    private String logicInventoryLocationCode;


    /**
     * 批次库存数量
     */
    private BigDecimal inventoryQty;

    /**
     * 快照数量
     */
    private BigDecimal snapShotQty;

    /**
     * 凭证数量
     */
    private BigDecimal credentialQty;

    /**
     * 待修复qty
     */
    private BigDecimal fixQty;


    /**
     * 快照id
     */
    private Long perHourId;

}
