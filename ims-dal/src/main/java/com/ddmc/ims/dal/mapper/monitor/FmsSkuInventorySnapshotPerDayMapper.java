package com.ddmc.ims.dal.mapper.monitor;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.monitor.FmsSkuInventorySnapshotPerDay;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 财务库存每日快照 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface FmsSkuInventorySnapshotPerDayMapper extends BaseMapper<FmsSkuInventorySnapshotPerDay> {



    List<FmsSkuInventorySnapshotPerDay> selectByWarehouseIdAndSnapshotDate(@Param("warehouseId") Long warehouseId,
        @Param("snapshotDate") Date snapshotDate);

    List<FmsSkuInventorySnapshotPerDay> selectByWarehouseIdAndSnapshotDateAndMinSkuId(@Param("warehouseId") Long warehouseId,
        @Param("snapshotDate") Date snapshotDate,@Param("skuId") Long skuId,@Param("limit") Integer limit);


    void batchInsert(@Param("items") List<FmsSkuInventorySnapshotPerDay> endFmsSkuInventorySnapshotPerDays);

    Integer countByWarehouseAndSnapshotDate(@Param("warehouseId") Long warehouseId,
        @Param("snapshotDate") Date snapshotDate);

    Integer deleteByWarehouseAndSnapshotDate(@Param("warehouseId") Long warehouseId,
        @Param("snapshotDate") Date snapshotDate);



    /**
     * 通过创建时间查在该时间前最大的id
     *
     * @param maxCreateTime maxCreateTime
     * @return result
     */
    Long selectOneIdByCreateTimeBefore(@Param("maxCreateTime") Date maxCreateTime);


    /**
     * 按最大id进行删除
     *
     * @param id 最大id
     * @return 影响条数
     */
    Long deleteByMaxId(@Param("id") Long id);


}
