package com.ddmc.ims.dal.mapper.monitor;

import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHourDiff;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 逻辑库存小时快照差异表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-13
 */
public interface SkuInventorySnapshotPerHourDiffMapper extends BaseMapper<SkuInventorySnapshotPerHourDiff> {

    /**
     * 批量插入
     *
     * @param list list
     * @return result
     */
    int insertList(@Param("list") List<SkuInventorySnapshotPerHourDiff> list);

    /**
     * 查询所有的仓库id
     *
     * @return warehouseId
     */
    List<Long> selectDistinctWarehouseId();


    /**
     * 通过仓库id查询
     *
     * @param warehouseId warehouseId
     * @return result
     */
    List<SkuInventorySnapshotPerHourDiff> selectByWarehouseId(@Param("warehouseId") Long warehouseId);

    /**
     * 根据仓库删除
     *
     * @param warehouseId 仓库id
     * @return result
     */
    int deleteByWarehouseId(@Param("warehouseId") Long warehouseId);

    /**
     * 按最大id进行删除
     *
     * @param id 最大id
     * @return 影响条数
     */
    Long deleteByMaxId(@Param("id") Long id);


}
