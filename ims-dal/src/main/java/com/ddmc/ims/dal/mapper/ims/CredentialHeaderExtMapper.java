package com.ddmc.ims.dal.mapper.ims;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeaderExt;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 凭证头扩展信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
public interface CredentialHeaderExtMapper extends BaseMapper<CredentialHeaderExt> {

    List<CredentialHeaderExt> selectByCredentialHeaderId(@Param("credentialHeaderId") Long credentialHeaderId);


    CredentialHeaderExt selectByCredentialHeaderIdAndCode(@Param("credentialHeaderId") Long credentialHeaderId,@Param("extCode") String extCode);


    int batchInsert(@Param("list") List<CredentialHeaderExt> list);


}
