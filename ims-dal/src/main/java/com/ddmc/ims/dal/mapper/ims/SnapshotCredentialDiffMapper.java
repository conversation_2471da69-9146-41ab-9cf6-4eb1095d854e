package com.ddmc.ims.dal.mapper.ims;
import java.util.Collection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.dal.model.ims.SnapshotCredentialDiff;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 凭证头信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface SnapshotCredentialDiffMapper extends BaseMapper<SnapshotCredentialDiff> {

    /**
     * 根据仓库+业务时间查询差异表中的记录
     * @param warehouseId 仓库id
     * @param date 日期
     * @return 响应
     */
    List<Long> selectByWarehouseIdAndBusinessTime(@Param("warehouseId") Long warehouseId,@Param("date") Date date);
    /**
     * 根据仓库+归结时间查询差异表中的记录
     * @param warehouseId 仓库id
     * @param date 日期
     * @return 响应
     */
    List<Long> selectByWarehouseIdAndEndDateTime(@Param("warehouseId") Long warehouseId,@Param("date") Date date);

    List<SnapshotCredentialDiff> listByExeOrderSourceAndNo(@Param("exeOrderSource")String exeOrderSource, @Param("exeOrderNo") String exeOrderNo);

    void updateEndDateTime(@Param("ids") List<Long> ids,@Param("endDateTime")  Date endDateTime);
    /**
     * 根据仓库+业务时间查询差异表中的记录
     * @param warehouseId 仓库id
     * @param date 日期
     * @return 响应
     */
    List<Long> selectByWarehouseIdAndEndDateTimeLessBusinessTime(@Param("warehouseId") Long warehouseId,@Param("date") Date date);
    /**
     * 根据仓库+业务时间+归结日期大于查询日期 查询差异数据
     * @param warehouseId 仓库id
     * @param date 日期
     * @return 响应
     */
    List<Long> selectByWarehouseIdAndBusinessTimeLessEndDateTime(@Param("warehouseId") Long warehouseId,@Param("date") Date date,@Param("orderSources") Set<String> orderSources);
    /**
     * 根据仓库+业务时间查询归结日期为空+业务时间小于等于查询日期 查询差异数据
     * @param warehouseId 仓库id
     * @param date 日期
     * @return 响应
     */
    List<Long> selectByWarehouseIdAndEndDateTimeIsNull(@Param("warehouseId") Long warehouseId,@Param("date") Date date,@Param("orderSources") Set<String> orderSources);

    /**
     * 根据凭证查询差异表中id
     *
     * @param credentialId 凭证id
     * @return 差异表id
     */
    Long selectIdByCredentialId(@Param("credentialId") Long credentialId);

    int deleteByMaxIdLimit500(@Param("endDateTime") Date endDateTime);


    /**
     * 通过CredentialId查询是否存在
     *
     * @param credentialIdCollection credentialIdCollection
     * @return result
     */
    Set<Long> selectCredentialIdByCredentialIdIn(
        @Param("credentialIdCollection") Collection<Long> credentialIdCollection);


}
