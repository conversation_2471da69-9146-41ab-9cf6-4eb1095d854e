package com.ddmc.ims.dal.mapper.ims;
import java.math.BigDecimal;
import java.util.Collection;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ddmc.ims.common.bo.WarehouseSkuCargoOwnerInfo;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.ims.request.inventory.InventoryDetailQueryRequest;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface PurchaseIntransitInventoryMapper extends BaseMapper<PurchaseIntransitInventory> {

    /**
     * 查询逻辑库位下是否存在在途库存数据
     * @param warehouseIds 仓库id列表
     * @param cargoOwnerId 货主id
     * @param logicInventoryCode 库位编码
     * @return 在库库存一条id
     */
    Long selectOneIdByWarehouseIdsAndCargoOwnerId(@Param("warehouseIds") List<Long> warehouseIds,
        @Param("cargoOwnerId") Long cargoOwnerId, @Param("logicInventoryCode") String logicInventoryCode);

    /**
     * 批量插入在途库存
     *
     * @param purchaseIntransitInventories 需要插入的在途库存
     * @return 实际更新的数据条数
     */
    int batchInsert(@Param("items") List<PurchaseIntransitInventory> purchaseIntransitInventories);


    /**
     * 批量更新在途库存
     *
     * @param purchaseIntransitInventories 需要更新的在途库存
     * @return 实际更新的数据条数
     */
    int batchUpdate(@Param("items") List<PurchaseIntransitInventory> purchaseIntransitInventories);



    /**
     * 根据来源source和采购单号获取在途库存
     * @param orderSource 来源source
     * @param purchaseNo 采购单号
     * @return 在途库存
     */
    List<PurchaseIntransitInventory> listByOrderSourceAndPurchaseNo(@Param("orderSource") String orderSource,
        @Param("purchaseNo") String purchaseNo);

    /**
     * 通过单号+来源+skuId查询
     *
     * @param orderSource orderSource
     * @param orderNo orderNo
     * @param skuIdCollection skuIdCollection
     * @return result
     */
    List<PurchaseIntransitInventory> selectByOrderSourceAndOrderNoAndSkuIdIn(@Param("orderSource") String orderSource,
        @Param("orderNo") String orderNo, @Param("skuIdCollection") Collection<Long> skuIdCollection);






    int updateExpectArriveTimeByOrder(@Param("expectArriveTime") Date expectArriveTime ,@Param("orderSource") String orderSource,
        @Param("orderNo") String orderNo);


    /**
     * 查询在途信息
     * @param conditionList 条件
     * @return 在途库存
     */
    List<PurchaseIntransitInventory> selectByWarehouseSkuOwnerInfo(@Param("conditionList") List<WarehouseSkuCargoOwnerInfo> conditionList);

    /**
     * 根据四要素+期望到货时间查询
     * @return 在途库存
     */
    List<PurchaseIntransitInventory> selectByWarehouseSkuOwnerLocationArriveTime(@Param("request") InventoryDetailQueryRequest request,
        @Param("expectArriveStartTime") Date expectArriveStartTime, @Param("expectArriveEndTime") Date expectArriveEndTime);


    /**
     * 更新预约在途量
     *
     * @param updatedBookedIntransitQty updatedBookedIntransitQty
     * @param idCollection idCollection
     * @return result
     */
    int updateBookedIntransitQtyByIdIn(@Param("updatedBookedIntransitQty") BigDecimal updatedBookedIntransitQty,
        @Param("idCollection") Collection<Long> idCollection);



}
