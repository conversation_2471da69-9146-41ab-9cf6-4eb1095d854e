package com.ddmc.ims.dal.condition;

import java.util.Date;
import java.util.Objects;
import lombok.Getter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Getter
@Accessors(chain = true)
public class SkuAndManufactureDateCondition {

    private final Long skuId;

    private final Date manufactureDate;

    public SkuAndManufactureDateCondition(Long skuId, Date manufactureDate) {
        this.skuId = skuId;
        this.manufactureDate = manufactureDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SkuAndManufactureDateCondition)) {
            return false;
        }
        SkuAndManufactureDateCondition that = (SkuAndManufactureDateCondition) o;
        return Objects.equals(skuId, that.skuId) && Objects
            .equals(manufactureDate, that.manufactureDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuId, manufactureDate);
    }
}
