<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
    PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
    "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!--MyBatis3Simple或者MyBatis3,生成的xml文件略有不同-->
    <context id="Mysql" targetRuntime="MyBatis3" defaultModelType="flat">
        <commentGenerator>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="false"></property>
            <property name="suppressDate" value="true"></property>
            <property name="javaFileEncoding" value="utf-8"/>
        </commentGenerator>

        <jdbcConnection driverClass="${driverClass}"
            connectionURL="${connectionURL}"
            userId="${userId}"
            password="${password}">
        </jdbcConnection>

        <!-- 默认false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer，为 true时把JDBC DECIMAL 和
            NUMERIC 类型解析为java.math.BigDecimal -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false" />
        </javaTypeResolver>

        <javaModelGenerator targetPackage="${modelPackage}" targetProject="${src_main_java}">
            <property name="enableSubPackages" value="true"></property>
            <property name="trimStrings" value="true"></property>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="${sqlMapperPackage}" targetProject="${src_main_resources}">
            <property name="enableSubPackages" value="true"></property>
        </sqlMapGenerator>

        <!--
        type=ANNOTATEDMAPPER表示不生成xml文件
        这里我用XMLMAPPER
        -->
        <javaClientGenerator targetPackage="${mapperPackage}" targetProject="${src_main_java}" type="XMLMAPPER">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--
         sql占位符，表示所有的表
         这里只用city表
        -->
        <!--<table tableName="%">-->
        <!--<generatedKey column="id" sqlStatement="Mysql" identity="true" />-->
        <!--</table>-->
        <table tableName="checkout"  enableCountByExample="false" enableUpdateByExample="false"
            enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>

        <table tableName="checkout_product"  enableCountByExample="false" enableUpdateByExample="false"
          enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>

        <table tableName="checkout_product_batch"  enableCountByExample="false" enableUpdateByExample="false"
          enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
    </context>
</generatorConfiguration>

