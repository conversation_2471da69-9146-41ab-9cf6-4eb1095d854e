<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.monitor.FmsSkuInventorySnapshotPerDayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.monitor.FmsSkuInventorySnapshotPerDay">
        <id column="id" property="id" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="sku_id" property="skuId" />
        <result column="snapshot_date" property="snapshotDate" />
        <result column="create_time" property="createTime" />
        <result column="qty" property="qty" />
        <result column="in_qty" property="inQty" />
        <result column="out_qty" property="outQty" />
        <result column="ims_qty" property="imsQty" />
        <result column="processing_qty" property="processingQty" />
        <result column="transfer_intransit_qty" property="transferIntransitQty" />
        <result column="purchase_part_in_qty" property="purchasePartInQty" />
        <result column="transfer_part_in_qty" property="transferPartInQty" />
        <result column="reverse_processing_qty" property="reverseProcessingQty" />
        <result column="other_diff_qty" property="otherDiffQty" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, warehouse_id, sku_id, snapshot_date, create_time, qty, in_qty, out_qty, ims_qty, processing_qty, transfer_intransit_qty, purchase_part_in_qty, transfer_part_in_qty, reverse_processing_qty, other_diff_qty
    </sql>

    <select id="selectByWarehouseIdAndSnapshotDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from fms_sku_inventory_snapshot_per_day
        where warehouse_id = #{warehouseId}
        and snapshot_date = #{snapshotDate}
    </select>

    <insert id="batchInsert">
        INSERT INTO fms_sku_inventory_snapshot_per_day(
        warehouse_id, sku_id, snapshot_date,  qty, in_qty, out_qty, ims_qty, processing_qty, transfer_intransit_qty, purchase_part_in_qty, transfer_part_in_qty, reverse_processing_qty, other_diff_qty
        )VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.warehouseId},
            #{item.skuId},
            #{item.snapshotDate},
            #{item.qty},
            #{item.inQty},
            #{item.outQty},
            #{item.imsQty},
            #{item.processingQty},
            #{item.transferIntransitQty},
            #{item.purchasePartInQty},
            #{item.transferPartInQty},
            #{item.reverseProcessingQty},
            #{item.otherDiffQty}
            )
        </foreach>
    </insert>

    <select id="selectByWarehouseIdAndSnapshotDateAndMinSkuId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from fms_sku_inventory_snapshot_per_day
        where warehouse_id = #{warehouseId}
        and snapshot_date = #{snapshotDate}
        and sku_id > #{skuId} order by sku_id
        limit #{limit}
    </select>

    <select id="countByWarehouseAndSnapshotDate" resultType="java.lang.Integer">
        select count(1) from fms_sku_inventory_snapshot_per_day
        where warehouse_id = #{warehouseId}
        and snapshot_date = #{snapshotDate}
    </select>

    <delete id="deleteByWarehouseAndSnapshotDate">
        delete from fms_sku_inventory_snapshot_per_day
        where warehouse_id = #{warehouseId}
        and snapshot_date = #{snapshotDate}
    </delete>


    <select id="selectOneIdByCreateTimeBefore" resultType="java.lang.Long">
        select max(id)
        from fms_sku_inventory_snapshot_per_day
        where create_time <![CDATA[<]]> #{maxCreateTime}
    </select>


    <delete id="deleteByMaxId">
        delete
        from fms_sku_inventory_snapshot_per_day
        where id <![CDATA[<=]]> #{id}
        limit 500
    </delete>


</mapper>
