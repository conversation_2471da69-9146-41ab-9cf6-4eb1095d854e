<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.monitor.SkuTransferInventorySnapshotPerHourMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.monitor.SkuTransferInventorySnapshotPerHour">
        <id column="id" property="id"/>
        <result column="sku_id" property="skuId"/>
        <result column="from_warehouse_id" property="fromWarehouseId"/>
        <result column="from_logic_location_code" property="fromLogicLocationCode"/>
        <result column="from_cargo_owner_id" property="fromCargoOwnerId"/>
        <result column="to_warehouse_id" property="toWarehouseId"/>
        <result column="to_logic_location_code" property="toLogicLocationCode"/>
        <result column="to_cargo_owner_id" property="toCargoOwnerId"/>
        <result column="transfer_intransit_qty" property="transferIntransitQty"/>
        <result column="snapshot_date_time" property="snapshotDateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sku_id, from_warehouse_id, from_logic_location_code, from_cargo_owner_id,
        to_warehouse_id,to_logic_location_code,to_cargo_owner_id, transfer_intransit_qty,
        snapshot_date_time, create_time
    </sql>

    <select id="selectBySnapshotDateTimeAndToWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sku_transfer_inventory_snapshot_per_hour
        where snapshot_date_time=#{snapshotDateTime} and to_warehouse_id=#{toWarehouseId}
    </select>

    <insert id="insertList">
        INSERT INTO sku_transfer_inventory_snapshot_per_hour(
        sku_id,
        from_warehouse_id,
        from_logic_location_code,
        from_cargo_owner_id,
        to_warehouse_id,
        to_logic_location_code,
        to_cargo_owner_id,
        transfer_intransit_qty,
        snapshot_date_time
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.skuId},
            #{element.fromWarehouseId},
            #{element.fromLogicLocationCode},
            #{element.fromCargoOwnerId},
            #{element.toWarehouseId},
            #{element.toLogicLocationCode},
            #{element.toCargoOwnerId},
            #{element.transferIntransitQty},
            #{element.snapshotDateTime}
            )
        </foreach>
    </insert>

    <delete id="deleteByToWarehouseIdInAndSnapshotDateTime">
        delete from sku_transfer_inventory_snapshot_per_hour
        where to_warehouse_id = #{toWarehouseId}
        and snapshot_date_time=#{snapshotDateTime}
    </delete>


    <select id="getByMinId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from sku_transfer_inventory_snapshot_per_hour
        where id > #{maxId} order by id asc limit #{limit}
    </select>

</mapper>
