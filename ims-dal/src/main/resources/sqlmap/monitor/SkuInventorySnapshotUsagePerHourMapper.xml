<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHour">
        <id column="id" property="id" />
        <result column="sku_id" property="skuId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="logic_location_code" property="logicLocationCode" />
        <result column="cargo_owner_id" property="cargoOwnerId" />
        <result column="usage_code" property="usageCode" />
        <result column="free_qty" property="freeQty" />
        <result column="frozen_qty" property="frozenQty" />
        <result column="transfer_intransit_qty" property="transferIntransitQty" />
        <result column="snapshot_date_time" property="snapshotDateTime" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sku_id, warehouse_id, logic_location_code, cargo_owner_id, usage_code, free_qty, frozen_qty, transfer_intransit_qty, snapshot_date_time, create_time
    </sql>

    <select id="selectOneMaxSnapshotDateTimeByWarehouseId" resultType="java.util.Date">
        select max(snapshot_date_time)
        from sku_inventory_snapshot_usage_per_hour
        where warehouse_id=#{warehouseId}
        and snapshot_date_time  <![CDATA[<]]> #{nowSnapshotDateTime}
    </select>

    <select id="selectBySnapshotDateTimeAndWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sku_inventory_snapshot_usage_per_hour
        where snapshot_date_time=#{snapshotDateTime} and warehouse_id=#{warehouseId}
    </select>

    <insert id="insertList">
        INSERT INTO sku_inventory_snapshot_usage_per_hour(
        sku_id,
        warehouse_id,
        logic_location_code,
        cargo_owner_id,
        usage_code,
        free_qty,
        frozen_qty,
        transfer_intransit_qty,
        snapshot_date_time
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.skuId},
            #{element.warehouseId},
            #{element.logicLocationCode},
            #{element.cargoOwnerId},
            #{element.usageCode},
            #{element.freeQty},
            #{element.frozenQty},
            #{element.transferIntransitQty},
            #{element.snapshotDateTime}
            )
        </foreach>
    </insert>

    <delete id="deleteByWarehouseIdAndSnapshotDateTime">
        delete from sku_inventory_snapshot_usage_per_hour
        where warehouse_id=#{warehouseId} and snapshot_date_time=#{snapshotDateTime}
    </delete>

    <delete id="deleteByWarehouseIdInAndSnapshotDateTime">
        delete from sku_inventory_snapshot_usage_per_hour
        where warehouse_id in
        <foreach item="item" index="index" collection="warehouseIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
        and snapshot_date_time=#{snapshotDateTime}
    </delete>

    <delete id="deleteByMinId">
        delete
        from sku_inventory_snapshot_usage_per_hour
        where id <![CDATA[>=]]> #{id}
        limit 1000
    </delete>

    <delete id="deleteByMaxId">
        delete
        from sku_inventory_snapshot_usage_per_hour
        where id <![CDATA[<=]]> #{id}
        limit 1000
    </delete>

    <select id="selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sku_inventory_snapshot_usage_per_hour
        where snapshot_date_time=#{snapshotDateTime} and warehouse_id=#{warehouseId} and sku_id in
        <foreach item="item" index="index" collection="skuIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateByFixItem">
        <foreach collection="fixItems" item="item" separator=";">
            update sku_inventory_snapshot_usage_per_hour set free_qty = #{item.fixQty}
            where warehouse_id = #{item.warehouseId} and sku_id=#{item.skuId}
            and usage_code = #{item.usageCode} and snapshot_date_time = #{snapshotDateTime} and id = #{item.perHourId}
        </foreach>
    </update>

    <select id="selectByWarehouseIdAndLocationCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from sku_inventory_snapshot_usage_per_hour where warehouse_id = #{warehouseId}
        and snapshot_date_time = #{snapshotDate} and logic_location_code = #{locationCode}
    </select>

    <select id="selectOneIdByCreateTimeBefore" resultType="java.lang.Long">
        select max(id)
        from sku_inventory_snapshot_usage_per_hour
        where create_time <![CDATA[<]]> #{maxCreateTime}
    </select>

    <select id="getByMinId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from sku_inventory_snapshot_usage_per_hour
        where id > #{maxId} order by id asc limit #{limit}
    </select>



</mapper>
