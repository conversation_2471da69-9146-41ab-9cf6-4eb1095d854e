<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourDiffMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHourDiff">
        <id column="id" property="id" />
        <result column="sku_id" property="skuId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="logic_location_code" property="logicLocationCode" />
        <result column="cargo_owner_id" property="cargoOwnerId" />
        <result column="lot_id" property="lotId" />
        <result column="snapshot_id" property="snapshotId" />
        <result column="snapshot_qty" property="snapshotQty" />
        <result column="credential_qty" property="credentialQty" />
        <result column="inventory_qty" property="inventoryQty" />
        <result column="trigger_count" property="triggerCount" />
        <result column="credential_start_date" property="credentialStartDate" />
        <result column="credential_end_date" property="credentialEndDate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sku_id, warehouse_id, logic_location_code, cargo_owner_id, lot_id, snapshot_id, snapshot_qty, credential_qty, inventory_qty, trigger_count, credential_start_date, credential_end_date, create_time, update_time
    </sql>

    <insert id="insertList">
        INSERT INTO sku_inventory_snapshot_per_hour_diff(
        sku_id,
        warehouse_id,
        logic_location_code,
        cargo_owner_id,
        lot_id,
        snapshot_id,
        snapshot_qty,
        credential_qty,
        inventory_qty,
        trigger_count,
        credential_start_date,
        credential_end_date
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.skuId},
            #{element.warehouseId},
            #{element.logicLocationCode},
            #{element.cargoOwnerId},
            #{element.lotId},
            #{element.snapshotId},
            #{element.snapshotQty},
            #{element.credentialQty},
            #{element.inventoryQty},
            #{element.triggerCount},
            #{element.credentialStartDate},
            #{element.credentialEndDate}
            )
        </foreach>
    </insert>

    <select id="selectDistinctWarehouseId" resultType="java.lang.Long">
        select distinct(warehouse_id)
        from sku_inventory_snapshot_per_hour_diff
    </select>

    <select id="selectByWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sku_inventory_snapshot_per_hour_diff
        where warehouse_id=#{warehouseId}
    </select>

    <delete id="deleteByWarehouseId">
        delete from sku_inventory_snapshot_per_hour_diff
        where warehouse_id=#{warehouseId}
    </delete>


    <delete id="deleteByMaxId">
        delete
        from sku_inventory_snapshot_per_hour_diff
        where id <![CDATA[<=]]> #{id}
        limit 1000
    </delete>

</mapper>
