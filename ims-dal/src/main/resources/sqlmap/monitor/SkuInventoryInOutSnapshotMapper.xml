<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.monitor.SkuInventoryInOutSnapshot">
        <id column="id" property="id"/>
        <result column="sku_id" property="skuId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="logic_location_code" property="logicLocationCode"/>
        <result column="cargo_owner_id" property="cargoOwnerId"/>
        <result column="lot_id" property="lotId"/>
        <result column="in_qty" property="inQty"/>
        <result column="out_qty" property="outQty"/>
        <result column="snapshot_date_time" property="snapshotDateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sku_id, warehouse_id, logic_location_code, cargo_owner_id, lot_id, in_qty, out_qty, snapshot_date_time,
        create_time
    </sql>

    <select id="selectBySnapshotDateTimeBetween" resultMap="BaseResultMap">
        select
        sku_id, warehouse_id, logic_location_code, cargo_owner_id, sum(in_qty) as in_qty, sum(out_qty) as out_qty
        from sku_inventory_in_out_snapshot
        where snapshot_date_time <![CDATA[>]]> #{minSnapshotDateTime} and snapshot_date_time <![CDATA[<=]]>
        #{maxSnapshotDateTime} and warehouse_id = #{warehouseId}
        group by sku_id, warehouse_id, logic_location_code, cargo_owner_id
    </select>

    <insert id="insertList">
        INSERT INTO sku_inventory_in_out_snapshot(
        sku_id,
        warehouse_id,
        logic_location_code,
        cargo_owner_id,
        lot_id,
        in_qty,
        out_qty,
        snapshot_date_time
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.skuId},
            #{element.warehouseId},
            #{element.logicLocationCode},
            #{element.cargoOwnerId},
            #{element.lotId},
            #{element.inQty},
            #{element.outQty},
            #{element.snapshotDateTime}
            )
        </foreach>
    </insert>


    <select id="selectBySnapshotDateTimeAndWarehouseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sku_inventory_in_out_snapshot
        where snapshot_date_time=#{snapshotDateTime} and warehouse_id=#{warehouseId}
    </select>


    <delete id="deleteByMinId">
        delete
        from sku_inventory_in_out_snapshot
        where id <![CDATA[>=]]> #{id}
        limit 1000
    </delete>

    <delete id="deleteByWarehouseIdInAndSnapshotDateTime">
        delete from sku_inventory_in_out_snapshot
        where warehouse_id in
        <foreach item="item" index="index" collection="warehouseIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
        and snapshot_date_time=#{snapshotDateTime}
    </delete>



    <select id="selectOneIdByCreateTimeBefore" resultType="java.lang.Long">
        select max(id)
        from sku_inventory_in_out_snapshot
        where create_time <![CDATA[<]]> #{maxCreateTime}
    </select>


    <delete id="deleteByMaxId">
        delete
        from sku_inventory_in_out_snapshot
        where id <![CDATA[<=]]> #{id}
        limit 1000
    </delete>


    <select id="selectOneMaxSnapshotDateTimeByWarehouseId" resultType="java.util.Date">
        select max(snapshot_date_time)
        from sku_inventory_in_out_snapshot
        where warehouse_id=#{warehouseId}
        and snapshot_date_time  <![CDATA[<]]> #{nowSnapshotDateTime}
    </select>

</mapper>
