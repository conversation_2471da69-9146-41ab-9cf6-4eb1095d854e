<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify">
        <id column="id" property="id" />
        <result column="day_end_date" property="dayEndDate" />
        <result column="status" property="status" />
        <result column="source" property="source" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, day_end_date, status, source, create_time, update_time
    </sql>

    <select id="selectByDayEndDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from fms_snapshot_notify
        where day_end_date = #{dayEndDate}
    </select>

    <update id="updateStatusByDayEndDate">
        update fms_snapshot_notify set status = #{status}
        where day_end_date = #{dayEndDate}
    </update>

    <delete id="deleteByDayEndDate">
        delete from fms_snapshot_notify where day_end_date = #{dayEndDate}
    </delete>

</mapper>
