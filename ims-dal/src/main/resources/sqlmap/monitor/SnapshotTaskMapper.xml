<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.monitor.SnapshotTask">
        <id column="id" property="id" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="snapshot_type" property="snapshotType" />
        <result column="snapshot_time" property="snapshotTime" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, warehouse_id, snapshot_type, snapshot_time, status, create_time, update_time
    </sql>

    <select id="countByWarehouseIdAndSnapshotTimeAndStatus" resultType="java.lang.Integer">
        select count(id)  from snapshot_task
        where warehouse_id = #{warehouseId}
        and status = #{status}
        and snapshot_type = #{snapshotType}
        and snapshot_time =#{snapshotTime}
    </select>

    <select id="selectBySnapshotTypeAndSnapshotTimeAndStatus" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from snapshot_task
        where  status = #{status}
        and snapshot_type = #{snapshotType}
        and snapshot_time = #{snapshotTime}
        AND warehouse_id in
        <foreach collection="warehouseIds" open="(" close=")" separator="," item="warehouseId">
            #{warehouseId}
        </foreach>
    </select>

    <select id="selectBySnapshotTypeAndSnapshotTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from snapshot_task
        where  snapshot_type = #{snapshotType}
        and snapshot_time = #{snapshotTime}
        limit 10000
    </select>


    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert ignore into snapshot_task
        (
        warehouse_id, snapshot_type, snapshot_time, status
        )
        values
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.warehouseId},
            #{item.snapshotType},
            #{item.snapshotTime},
            #{item.status}
            )
        </foreach>
    </insert>

    <update id="updateStatusById" >
        update snapshot_task set status = #{newStatus}
        where id = #{id} and status = #{oldStatus}
    </update>



    <select id="selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from snapshot_task
        where warehouse_id in
        <foreach item="item" index="index" collection="warehouseIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
        and snapshot_type=#{snapshotType} and snapshot_time=#{snapshotTime}
    </select>

    <select id="selectBySnapshotTimeAndSnapshotTypeAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from snapshot_task
        where snapshot_time=#{snapshotTime} and snapshot_type=#{snapshotType} and `status`=#{status}
    </select>

    <select id="selectOneBySnapshotTypeAndSnapshotTimeAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from snapshot_task
        where snapshot_time=#{snapshotTime} and snapshot_type=#{snapshotType}
        and `status` in
        <foreach item="status" collection="statusList" open="(" separator="," close=")">
            #{status}
        </foreach>
        limit 1

    </select>



    <delete id="deleteByWarehouseIdInAndSnapshotTimeAndSnapshotType">
        delete from snapshot_task
        where warehouse_id in
        <foreach item="item" index="index" collection="warehouseIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
        and snapshot_time=#{snapshotTime} and snapshot_type=#{snapshotType}
    </delete>

    <delete id="deleteBySnapshotTimeAndSnapshotType">
        delete from snapshot_task
        where  snapshot_time=#{snapshotTime} and snapshot_type=#{snapshotType}
    </delete>

</mapper>
