<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.TransferIntransitInventory">
        <id column="id" property="id" />
        <result column="from_cargo_owner_id" property="fromCargoOwnerId" />
        <result column="from_warehouse_id" property="fromWarehouseId" />
        <result column="from_logic_inventory_location_code" property="fromLogicInventoryLocationCode" />
        <result column="to_cargo_owner_id" property="toCargoOwnerId" />
        <result column="to_logic_inventory_location_code" property="toLogicInventoryLocationCode" />
        <result column="to_warehouse_id" property="toWarehouseId" />
        <result column="sku_id" property="skuId" />
        <result column="order_source" property="orderSource" />
        <result column="order_no" property="orderNo" />
        <result column="delivery_mode" property="deliveryMode" />
        <result column="expect_out_time" property="expectOutTime" />
        <result column="expect_in_time" property="expectInTime" />
        <result column="plan_qty" property="planQty" />
        <result column="intransit_qty" property="intransitQty" />
        <result column="alloc_qty" property="allocQty" />
        <result column="wait_alloc_qty" property="waitAllocQty" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="version" property="version" />
        <result column="usage_code" property="usageCode" />
        <result column="to_usage_code" property="toUsageCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, from_cargo_owner_id, from_warehouse_id, from_logic_inventory_location_code, to_cargo_owner_id, to_logic_inventory_location_code, to_warehouse_id, sku_id, order_source, order_no, delivery_mode, expect_out_time, expect_in_time, plan_qty, intransit_qty, alloc_qty, wait_alloc_qty, create_time, update_time, version,usage_code,to_usage_code
    </sql>

    <select id="selectOneIdByWarehouseIdsAndCargoOwnerId" resultType="java.lang.Long">
        select id from
        transfer_intransit_inventory
        where to_cargo_owner_id = #{cargoOwnerId}
        and to_logic_inventory_location_code = #{logicInventoryCode}
        and to_warehouse_id in
        <foreach collection="warehouseIds" item="warehouseId" separator="," open="(" close=")">
            #{warehouseId}
        </foreach>
        limit 1
    </select>

    <select id="selectTransferIntransitInventory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>  from
        transfer_intransit_inventory
        where order_source = #{transferOrderNoAndSource.orderSource}
        and order_no = #{transferOrderNoAndSource.orderNo}
    </select>

    <select id="listByOrderSourceAndNoAndSkuIds"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from
        transfer_intransit_inventory
        where order_source = #{transferOrderNoAndSource.orderSource}
        and order_no = #{transferOrderNoAndSource.orderNo}
        and sku_id in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>


    <update id="batchUpdate">
        <foreach collection="items" item="item" separator=";">
            update transfer_intransit_inventory set
            plan_qty = #{item.planQty},
            intransit_qty = #{item.intransitQty},
            alloc_qty = #{item.allocQty},
            wait_alloc_qty = #{item.waitAllocQty},
            version = version + 1
            where id = #{item.id}
        </foreach>
    </update>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO transfer_intransit_inventory(
        from_cargo_owner_id,
        from_warehouse_id,
        from_logic_inventory_location_code,
        to_cargo_owner_id,
        to_logic_inventory_location_code,
        to_warehouse_id,
        sku_id,
        order_source,
        order_no,
        delivery_mode,
        expect_out_time,
        expect_in_time,
        plan_qty,
        intransit_qty,
        alloc_qty,
        wait_alloc_qty,
        version,
        usage_code,
        to_usage_code
        )VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.fromCargoOwnerId},
            #{item.fromWarehouseId},
            #{item.fromLogicInventoryLocationCode},
            #{item.toCargoOwnerId},
            #{item.toLogicInventoryLocationCode},
            #{item.toWarehouseId},
            #{item.skuId},
            #{item.orderSource},
            #{item.orderNo},
            #{item.deliveryMode},
            #{item.expectOutTime},
            #{item.expectInTime},
            #{item.planQty},
            #{item.intransitQty},
            #{item.allocQty},
            #{item.waitAllocQty},
            #{item.version},
            #{item.usageCode},
            #{item.toUsageCode}
            )
        </foreach>
    </insert>

    <update id="updateWaitAllocQtyToZero">
        update transfer_intransit_inventory set
        wait_alloc_qty = 0,
        version = version + 1
        where order_source = #{transferOrderNoAndSource.orderSource}
        and order_no = #{transferOrderNoAndSource.orderNo}
    </update>

    <update id="updateWaitAllocQtyToZeroBySku">
        update transfer_intransit_inventory set
        wait_alloc_qty = 0,
        version = version + 1
        where order_source = #{transferOrderNoAndSource.orderSource}
        and order_no = #{transferOrderNoAndSource.orderNo}
        and sku_id in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </update>




    <update id="batchAddPlanQtyWaitAllocQty" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update transfer_intransit_inventory
            set plan_qty = plan_qty + #{item.planQty}, wait_alloc_qty = wait_alloc_qty + ${item.waitAllocQty},
            version = version + 1
            where id = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateWaitAllocQty" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update transfer_intransit_inventory
            set wait_alloc_qty = ${item.waitAllocQty},
            version = version + 1
            where id = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateWaitAllocQtyAndPlanQty" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update transfer_intransit_inventory
            set wait_alloc_qty = ${item.waitAllocQty},plan_qty = ${item.planQty},
            version = version + 1
            where id = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateInTransitQty" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update transfer_intransit_inventory
            set intransit_qty = ${item.intransitQty},
            version = version + 1
            where id = #{item.id}
        </foreach>
    </update>

    <select id="selectByToWarehouseSkuOwnerInfo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from transfer_intransit_inventory
        where (sku_id, to_warehouse_id, to_cargo_owner_id) in
        <foreach collection="conditionList" item="item" open="(" separator="," close=")">
            (#{item.skuId}, #{item.warehouseId}, #{item.cargoOwnerId})
        </foreach>
    </select>

    <select id="selectByToWarehouseSkuOwnerLocationOutTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from transfer_intransit_inventory
        where to_cargo_owner_id = #{request.cargoOwnerId}
        and to_warehouse_id = #{request.warehouseId}
        and sku_id = #{request.skuId}
        and to_logic_inventory_location_code = #{request.logicInventoryLocationCode}
        <if test="expectOutTime != null">
            and expect_out_time = #{expectOutTime}
        </if>
        <if test="usageCode != null and usageCode != ''">
            and usage_code = #{usageCode}
        </if>
    </select>

    <select id="selectByFromWarehouseSkuOwnerLocation" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from transfer_intransit_inventory
        where from_cargo_owner_id = #{request.cargoOwnerId}
        and from_warehouse_id = #{request.warehouseId}
        and sku_id = #{request.skuId}
        and from_logic_inventory_location_code = #{request.logicInventoryLocationCode}
    </select>

    <select id="selectOneIdByCreateTimeBefore" resultType="java.lang.Long">
        select max(id)
        from transfer_intransit_inventory
        where create_time <![CDATA[<]]> #{maxCreateTime}
    </select>


    <delete id="deleteByMaxId">
        delete
        from transfer_intransit_inventory
        where id <![CDATA[<=]]> #{id}
        limit 1000
    </delete>

    <select id="getMinId" resultType="java.lang.Long">
        select id from transfer_intransit_inventory order by id asc limit 1
    </select>

    <select id="getByMinId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from transfer_intransit_inventory
        where id > #{maxId} order by id asc limit #{limit}
    </select>



    <select id="selectIdByMinIdLimit200" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from transfer_intransit_inventory
        where id <![CDATA[>]]> #{minId} order by id   limit 200
    </select>




    <update id="batchUpdateExceptTime">
        <foreach collection="items" item="item" separator=";">
            update transfer_intransit_inventory set
            expect_in_time = #{item.expectInTime},
            expect_out_time = #{item.expectOutTime}
            where id = #{item.id}
        </foreach>
    </update>


    <update id="batchUpdateDeliverMode">
        <foreach collection="items" item="item" separator=";">
            update transfer_intransit_inventory set
            delivery_mode = #{item.deliveryMode}
            where id = #{item.id}
        </foreach>
    </update>

    <select id="selectOneTransferIntransitInventory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from transfer_intransit_inventory
        where order_source = #{transferOrderNoAndSource.orderSource}
        and order_no = #{transferOrderNoAndSource.orderNo} limit 1
    </select>


    <select id="selectByToWarehouseIdAndTime" resultType="java.lang.String">
        select distinct order_no from transfer_intransit_inventory
        where to_warehouse_id = #{toWarehouseId}
        and update_time <![CDATA[>=]]> #{minUpdateTime} and create_time <![CDATA[<]]> #{maxCreateTime}
    </select>

</mapper>
