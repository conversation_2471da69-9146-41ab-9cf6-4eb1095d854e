<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.LogicInventoryLocationTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.LogicInventoryLocationTemplate">
        <id column="id" property="id" />
        <result column="warehouse_corporation_code" property="warehouseCorporationCode" />
        <result column="cargo_owner_id" property="cargoOwnerId" />
        <result column="warehouse_type" property="warehouseType" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, warehouse_corporation_code, cargo_owner_id, warehouse_type, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

    <select id="listAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from logic_inventory_location_template limit 2000
    </select>

    <select id="listByCondition" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from logic_inventory_location_template
        <where>
            <if test="condition.warehouseCorporationCode !=null and condition.warehouseCorporationCode !=''">
                and warehouse_corporation_code = #{condition.warehouseCorporationCode}
            </if>
            <if test="condition.cargoOwnerId !=null and condition.cargoOwnerId !=''">
                and cargo_owner_id = #{condition.cargoOwnerId}
            </if>
            <if test="condition.warehouseType !=null and condition.warehouseType !=''">
                and warehouse_type = #{condition.warehouseType}
            </if>
        </where>
    </select>

    <select id="selectByCorporationCodeAndCargoIdAndType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from logic_inventory_location_template
        where warehouse_corporation_code = #{warehouseCorporationCode}
        and  cargo_owner_id = #{cargoOwnerId}
        and warehouse_type = #{warehouseType}
    </select>

</mapper>
