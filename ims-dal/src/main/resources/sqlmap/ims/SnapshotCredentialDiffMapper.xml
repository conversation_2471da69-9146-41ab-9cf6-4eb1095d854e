<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.SnapshotCredentialDiff">
        <id column="id" property="id" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="credential_id" property="credentialId" />
        <result column="order_source" property="orderSource" />
        <result column="order_no" property="orderNo" />
        <result column="exe_order_source" property="exeOrderSource" />
        <result column="exe_order_no" property="exeOrderNo" />
        <result column="business_time" property="businessTime" />
        <result column="end_date_time" property="endDateTime" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, warehouse_id, credential_id, order_source, order_no, exe_order_source, exe_order_no, business_time, end_date_time, create_time
    </sql>

    <select id="listByExeOrderSourceAndNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from snapshot_credential_diff
        where exe_order_source = #{exeOrderSource} and exe_order_no = #{exeOrderNo}
    </select>

    <select id="selectByWarehouseIdAndBusinessTime" resultType="java.lang.Long">
        select credential_id from snapshot_credential_diff
        where warehouse_id = #{warehouseId} and business_time = #{date}
    </select>

    <select id="selectByWarehouseIdAndEndDateTime" resultType="java.lang.Long">
        select credential_id from snapshot_credential_diff
        where warehouse_id = #{warehouseId} and end_date_time = #{date}
    </select>

    <update id="updateEndDateTime" >
        update snapshot_credential_diff set end_date_time = #{endDateTime}
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectByWarehouseIdAndEndDateTimeLessBusinessTime" resultType="java.lang.Long">
        select credential_id from snapshot_credential_diff
        where warehouse_id = #{warehouseId} and end_date_time = #{date}
        and business_time   <![CDATA[ > ]]>  #{date}
    </select>

    <select id="selectByWarehouseIdAndBusinessTimeLessEndDateTime" resultType="java.lang.Long">
        select credential_id from snapshot_credential_diff
        where warehouse_id = #{warehouseId}
        and business_time = #{date} and  end_date_time   <![CDATA[ > ]]>  #{date}
        and order_source in
        <foreach item="item" index="index" collection="orderSources"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByWarehouseIdAndEndDateTimeIsNull" resultType="java.lang.Long">
        select credential_id from snapshot_credential_diff
        where warehouse_id = #{warehouseId} and
        business_time <![CDATA[ <= ]]>  #{date} and  end_date_time  ='0000-00-00 00:00:00'
        and order_source in
        <foreach item="item" index="index" collection="orderSources"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectIdByCredentialId" resultType="java.lang.Long">
        select id from snapshot_credential_diff where credential_id = #{credentialId}
    </select>

    <delete id="deleteByMaxIdLimit500">
        delete from snapshot_credential_diff where end_date_time <![CDATA[ <= ]]> #{endDateTime}
        and end_date_time !='0000-00-00 00:00:00'
        limit 500
    </delete>

    <select id="selectCredentialIdByCredentialIdIn" resultType="java.lang.Long">
        select credential_id
        from snapshot_credential_diff
        where credential_id in
        <foreach item="item" index="index" collection="credentialIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
