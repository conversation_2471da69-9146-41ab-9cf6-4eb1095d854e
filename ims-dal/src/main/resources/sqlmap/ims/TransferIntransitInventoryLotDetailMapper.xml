<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryLotDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.TransferIntransitInventoryLotDetail">
        <id column="id" property="id" />
        <result column="transfer_intransit_inventory_id" property="transferIntransitInventoryId" />
        <result column="from_cargo_owner_id" property="fromCargoOwnerId" />
        <result column="from_warehouse_id" property="fromWarehouseId" />
        <result column="from_logic_inventory_location_code" property="fromLogicInventoryLocationCode" />
        <result column="to_cargo_owner_id" property="toCargoOwnerId" />
        <result column="to_logic_inventory_location_code" property="toLogicInventoryLocationCode" />
        <result column="to_warehouse_id" property="toWarehouseId" />
        <result column="order_source" property="orderSource" />
        <result column="order_no" property="orderNo" />
        <result column="sku_id" property="skuId" />
        <result column="lot_id" property="lotId" />
        <result column="out_time" property="outTime" />
        <result column="expect_in_time" property="expectInTime" />
        <result column="out_qty" property="outQty" />
        <result column="intransit_qty" property="intransitQty" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="version" property="version" />
        <result column="usage_code" property="usageCode" />
        <result column="unsalable_date" property="unsalableDate"/>
        <result column="manufacture_date" property="manufactureDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, transfer_intransit_inventory_id, from_cargo_owner_id, from_warehouse_id, from_logic_inventory_location_code, to_cargo_owner_id, to_logic_inventory_location_code, to_warehouse_id, order_source, order_no, sku_id, lot_id, out_time, expect_in_time, out_qty, intransit_qty, create_time, update_time, version,usage_code,unsalable_date,manufacture_date
    </sql>


    <update id="batchUpdate">
        <foreach collection="items" item="item" separator=";">
            update transfer_intransit_inventory_lot_detail set
            out_qty = #{item.outQty},
            intransit_qty = #{item.intransitQty},
            version = version + 1
            where id = #{item.id}
        </foreach>
    </update>


    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into transfer_intransit_inventory_lot_detail
        (
        transfer_intransit_inventory_id, from_cargo_owner_id, from_warehouse_id, from_logic_inventory_location_code, to_cargo_owner_id, to_logic_inventory_location_code, to_warehouse_id, order_source, order_no, sku_id, lot_id, out_time, expect_in_time, out_qty, intransit_qty, version,usage_code,unsalable_date,manufacture_date
        )
        values
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.transferIntransitInventoryId},
            #{item.fromCargoOwnerId},
            #{item.fromWarehouseId},
            #{item.fromLogicInventoryLocationCode},
            #{item.toCargoOwnerId},
            #{item.toLogicInventoryLocationCode},
            #{item.toWarehouseId},
            #{item.orderSource},
            #{item.orderNo},
            #{item.skuId},
            #{item.lotId},
            #{item.outTime},
            #{item.expectInTime},
            #{item.outQty},
            #{item.intransitQty},
            #{item.version},
            #{item.usageCode},
            #{item.unsalableDate},
            #{item.manufactureDate}
            )
        </foreach>
    </insert>


    <select id="selectByOrderSourceAndNoAndLotSkuIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from transfer_intransit_inventory_lot_detail
        where order_no = #{transferOrderNoAndSource.orderNo} and
        order_source = #{transferOrderNoAndSource.orderSource} and
        (sku_id,manufacture_date) in
        <foreach collection="conditions" item="item" open="(" close=")" separator=",">
            ( #{item.skuId} , #{item.manufactureDate})
        </foreach>
    </select>


    <select id="selectByOrderSourceAndNoAndSkuIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from transfer_intransit_inventory_lot_detail
        where order_no = #{transferOrderNoAndSource.orderNo} and
        order_source = #{transferOrderNoAndSource.orderSource} and
        sku_id in
        <foreach collection="conditions" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="getByMinId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from transfer_intransit_inventory_lot_detail
        where id > #{maxId} order by id asc limit #{limit}
    </select>


    <delete id="deleteByOrderSourceAndNo" >
        delete
        from transfer_intransit_inventory_lot_detail
        where order_no = #{transferOrderNoAndSource.orderNo} and
        order_source = #{transferOrderNoAndSource.orderSource}
    </delete>




</mapper>
