<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.LogicInventoryLocationTemplateDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.LogicInventoryLocationTemplateDetail">
        <id column="id" property="id"/>
        <result column="logic_inventory_code" property="logicInventoryCode"/>
        <result column="logic_inventory_template_id" property="logicInventoryTemplateId"/>
        <result column="ref_real_inventory" property="refRealInventory"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, logic_inventory_code, logic_inventory_template_id, ref_real_inventory, create_time,
        create_user_id, create_user_name, update_time, update_user_id, update_user_name
    </sql>

    <select id="listByTemplateId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from logic_inventory_location_template_detail
        where logic_inventory_template_id = #{templateId}
    </select>

    <insert id="batchInsertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO logic_inventory_location_template_detail (
        logic_inventory_code, logic_inventory_template_id, ref_real_inventory, create_time,
        create_user_id, create_user_name,update_time, update_user_id, update_user_name
        )VALUES
        <foreach collection="items" separator="," item="item">
            (
            #{item.logicInventoryCode},
            #{item.logicInventoryTemplateId},
            #{item.refRealInventory},
            #{item.createTime},
            #{item.createUserId},
            #{item.createUserName},
            #{item.updateTime},
            #{item.updateUserId},
            #{item.updateUserName}
            )
        </foreach>
    </insert>

    <update id="batchUpdateList">
        <foreach collection="items" item="item" separator=";">
            update logic_inventory_location_template_detail set ref_real_inventory = #{item.refRealInventory},
            update_user_id =  #{item.updateUserId},
            update_time = #{item.updateTime},
            update_user_name = #{item.updateUserName}
            where id = #{item.id}
        </foreach>
    </update>

    <select id="selectByTemplateIdAndLogicInventoryCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from logic_inventory_location_template_detail where logic_inventory_template_id = #{templateId}
        and logic_inventory_code = #{logicInventoryCode}
    </select>

</mapper>
