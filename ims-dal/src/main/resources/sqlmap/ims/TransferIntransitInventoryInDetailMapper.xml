<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryInDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.TransferIntransitInventoryInDetail">
        <id column="id" property="id" />
        <result column="ref_order_source" property="refOrderSource" />
        <result column="ref_order_no" property="refOrderNo" />
        <result column="ref_exe_order_source" property="refExeOrderSource" />
        <result column="ref_exe_order_no" property="refExeOrderNo" />
        <result column="from_cargo_owner_id" property="fromCargoOwnerId" />
        <result column="from_warehouse_id" property="fromWarehouseId" />
        <result column="from_logic_inventory_location_code" property="fromLogicInventoryLocationCode" />
        <result column="to_cargo_owner_id" property="toCargoOwnerId" />
        <result column="to_logic_inventory_location_code" property="toLogicInventoryLocationCode" />
        <result column="to_warehouse_id" property="toWarehouseId" />
        <result column="sku_id" property="skuId" />
        <result column="delivery_mode" property="deliveryMode" />
        <result column="lot_id" property="lotId" />
        <result column="expect_out_time" property="expectOutTime" />
        <result column="expect_in_time" property="expectInTime" />
        <result column="in_time" property="inTime" />
        <result column="in_qty" property="inQty" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ref_order_source, ref_order_no, ref_exe_order_source, ref_exe_order_no, from_cargo_owner_id, from_warehouse_id, from_logic_inventory_location_code, to_cargo_owner_id, to_logic_inventory_location_code, to_warehouse_id, sku_id, delivery_mode, lot_id, expect_out_time, expect_in_time, in_time, in_qty, create_time, update_time
    </sql>

    <select id="selectByRefOrderNoAndRefOrderSourceAndRefExeOrderNoAndRefExeOrderSource" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from transfer_intransit_inventory_in_detail
        where ref_order_no=#{refOrderNo} and ref_order_source=#{refOrderSource} and ref_exe_order_no=#{refExeOrderNo}
        and ref_exe_order_source=#{refExeOrderSource}
        and sku_id in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>

    <insert id="insertList">
        INSERT INTO transfer_intransit_inventory_in_detail(
        ref_order_source,
        ref_order_no,
        ref_exe_order_source,
        ref_exe_order_no,
        from_cargo_owner_id,
        from_warehouse_id,
        from_logic_inventory_location_code,
        to_cargo_owner_id,
        to_logic_inventory_location_code,
        to_warehouse_id,
        sku_id,
        delivery_mode,
        lot_id,
        expect_out_time,
        expect_in_time,
        in_time,
        in_qty
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.refOrderSource},
            #{element.refOrderNo},
            #{element.refExeOrderSource},
            #{element.refExeOrderNo},
            #{element.fromCargoOwnerId},
            #{element.fromWarehouseId},
            #{element.fromLogicInventoryLocationCode},
            #{element.toCargoOwnerId},
            #{element.toLogicInventoryLocationCode},
            #{element.toWarehouseId},
            #{element.skuId},
            #{element.deliveryMode},
            #{element.lotId},
            #{element.expectOutTime},
            #{element.expectInTime},
            #{element.inTime},
            #{element.inQty}
            )
        </foreach>
    </insert>


    <update id="batchUpdate">
        <foreach collection="items" item="item" separator=";">
            update transfer_intransit_inventory_in_detail set
            in_qty = #{item.inQty}
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteByRefOrderNoAndSource">
        delete from transfer_intransit_inventory_in_detail
        where ref_order_no=#{refOrderNo} and ref_order_source=#{refOrderSource}
    </delete>


</mapper>
