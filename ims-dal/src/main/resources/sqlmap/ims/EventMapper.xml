<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ddmc.ims.dal.mapper.ims.EventMapper">

    <insert id="insertBatch"  useGeneratedKeys="true" keyProperty="id">
        INSERT INTO event_entity
        (  parent_id, key_type, key_id, type, event, status, trigger_count, create_time, update_time , earliest_exe_date)
        VALUES
        <foreach collection="entities" separator="," item="it">
            (
            #{it.parentId},#{it.keyType},#{it.keyId},#{it.type},#{it.event},
            #{it.status},#{it.triggerCount},#{it.createTime},#{it.updateTime}, #{it.earliestExeDate}
            )
        </foreach>
    </insert>

    <update id="update">
        UPDATE event_entity
        SET status = #{newStatus}
        WHERE id = #{id}
          AND status = #{oldStatus}
    </update>

    <update id="batchUpdateStatus">
        UPDATE event_entity
        SET status = #{newStatus}
        WHERE id in
        <foreach collection="eventIdList" open="(" close=")" separator="," item="eventId">
            #{eventId}
        </foreach>
        AND status = #{oldStatus}
    </update>

    <update id="updateStatus">
        UPDATE event_entity
        SET status = #{newStatus}
        WHERE id in
        <foreach collection="eventIdList" open="(" close=")" separator="," item="eventId">
            #{eventId}
        </foreach>
    </update>

    <update id="plusTriggerCount">
        UPDATE event_entity
        SET trigger_count = trigger_count + 1
        WHERE id = #{id}
    </update>

    <update id="plusTriggerCountAndStatusProcessing">
        UPDATE event_entity
        SET trigger_count = trigger_count + 1,
        status = 2
        WHERE id = #{id} and
        status in (0,3)
    </update>

    <update id="batchPlusTriggerCountAndStatusProcessing">
        UPDATE event_entity
        SET trigger_count = trigger_count + 1,
        status = 2
        WHERE id in
        <foreach collection="eventIdList" open="(" close=")" separator="," item="eventId">
            #{eventId}
        </foreach>
        and status in (0,3)
    </update>

    <select id="listIdsByTimeAndType" resultType="long">
        SELECT
            id
        from event_entity
        where
            create_time >= #{createTimeStart}
            AND create_time &lt; #{createTimeEnd}
            <if test="keyTypeList != null and keyTypeList.size() != 0">
                AND `key_type` in
                <foreach collection="keyTypeList" open="(" close=")" separator="," item="keyType">
                    #{keyType}
                </foreach>
            </if>
        order by `key_type`,create_time ,id
    </select>
    <select id="listByCondition" resultType="com.ddmc.ims.dal.model.ims.EventEntity">
        SELECT
        id,
        parent_id,
        key_type,
        key_id,
        type,
        event,
        status,
        trigger_count,
        create_time,
        update_time,
        earliest_exe_date
        FROM event_entity
        <where>
            <if test="condition.eventId != null">
                AND id = #{condition.eventId}
            </if>
            <if test="condition.eventIdList != null and condition.eventIdList.size() != 0">
                AND id in
                <foreach collection="condition.eventIdList" open="(" close=")" separator="," item="eventId">
                    #{eventId}
                </foreach>
            </if>
            <if test="condition.parentEventId != null">
                AND parent_id = #{condition.parentEventId}
            </if>
            <if test="condition.keyType != null ">
                AND `key_type` = #{condition.keyType}
            </if>
            <if test="condition.keyTypeList != null and condition.keyTypeList.size() != 0">
                AND `key_type` in
                <foreach collection="condition.keyTypeList" open="(" close=")" separator="," item="keyType">
                    #{keyType}
                </foreach>
            </if>
            <if test="condition.keyId != null ">
                AND key_id = #{condition.keyId}
            </if>
            <if test="condition.status != null ">
                AND status = #{condition.status}
            </if>
            <if test="condition.statusList != null and condition.statusList.size() != 0">
                AND status in
                <foreach collection="condition.statusList" open="(" close=")" separator="," item="status">
                    #{status}
                </foreach>
            </if>
            <if test="condition.createTimeStart != null ">
                AND create_time >= #{condition.createTimeStart}
            </if>
            <if test="condition.createTimeEnd != null ">
                AND create_time &lt; #{condition.createTimeEnd}
            </if>
            <if test="condition.triggerCount != null ">
                AND trigger_count = #{condition.triggerCount}
            </if>
            <if test="condition.triggerCountStart != null ">
                AND trigger_count >= #{condition.triggerCountStart}
            </if>
            <if test="condition.triggerCountEnd != null ">
                AND trigger_count &lt; #{condition.triggerCountEnd}
            </if>
        </where>
        order by id
        <if test="condition.limit != null">
             LIMIT #{condition.limit}
        </if>
    </select>

    <select id="listUnHandled" resultType="com.ddmc.ims.dal.model.ims.EventEntity">
        SELECT
            id,
            parent_id,
            key_type,
            key_id,
            type,
            event,
            status,
            trigger_count,
            create_time,
            update_time,
            earliest_exe_date
        FROM event_entity
        WHERE status = 0
        AND (trigger_count >= #{triggerCount} OR create_time &lt; #{createTimeEnd})
    </select>

    <select id="getById" resultType="com.ddmc.ims.dal.model.ims.EventEntity">
        SELECT
        id,
        parent_id,
        key_type,
        key_id,
        type,
        event,
        status,
        trigger_count,
        create_time,
        update_time,
        earliest_exe_date
        FROM event_entity
        WHERE id = #{id}
    </select>
    <select id="getByIds" resultType="com.ddmc.ims.dal.model.ims.EventEntity">
        SELECT
        id,
        parent_id,
        key_type,
        key_id,
        type,
        event,
        status,
        trigger_count,
        create_time,
        update_time,
        earliest_exe_date
        FROM event_entity
        WHERE
        id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <delete id="deleteByEndDate" parameterType="map">
        delete from event_entity
        where create_time  <![CDATA[ < #{endDate} ]]> and status = 1
        order by create_time asc
        limit #{limit}
    </delete>

    <select id="selectNowMilliSecond" resultType="date">
        select now(3)
    </select>

    <select id="getAllByKeyTypeAndKeyId" resultType="com.ddmc.ims.dal.model.ims.EventEntity">
        select
            id,
            parent_id,
            key_type,
            key_id,
            type,
            event,
            status,
            trigger_count,
            create_time,
            update_time,
            earliest_exe_date
        from event_entity
        where `key_type` = #{keyType}
        and `key_id` = #{keyId}
    </select>

    <select id="getOneWaitingProcessByKeyTypeAndKeyIdAndStatus" resultType="com.ddmc.ims.dal.model.ims.EventEntity">
        select
        id,
        parent_id,
        key_type,
        key_id,
        type,
        event,
        status,
        trigger_count,
        create_time,
        update_time,
        earliest_exe_date
        from event_entity
        where `key_type` = #{keyType}
        and `key_id` = #{keyId}
        and status = 0
        order by create_time desc
        limit 1
    </select>

    <select id="getMinId" resultType="java.lang.Long">
        select id from event_entity order by id asc limit 1
    </select>

    <select id="getGreaterEventEntity" resultType="com.ddmc.ims.dal.model.ims.EventEntity">
        select
        id,
        parent_id,
        key_type,
        key_id,
        type,
        event,
        status,
        trigger_count,
        create_time,
        update_time,
        earliest_exe_date
        from event_entity where id > #{minId} order by id asc limit #{limit}
    </select>
    
    <delete id="deleteByIds">
        delete from event_entity where id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>


    <select id="getProcessing" resultType="com.ddmc.ims.dal.model.ims.EventEntity">
        select
        id,
        parent_id,
        key_type,
        key_id,
        type,
        event,
        status,
        trigger_count,
        create_time,
        update_time,
        earliest_exe_date
        from event_entity where status = 2 limit 500
    </select>
</mapper>