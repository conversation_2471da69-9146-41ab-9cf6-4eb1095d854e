<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.CredentialHeaderExtMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.CredentialHeaderExt">
        <id column="id" property="id" />
        <result column="credential_header_id" property="credentialHeaderId" />
        <result column="ext_code" property="extCode"/>
        <result column="ext_value" property="extValue"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, credential_header_id, ext_code, ext_value,  create_time, update_time
    </sql>

    <select id="selectByCredentialHeaderId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from credential_header_ext
        where credential_header_id = #{credentialHeaderId}
    </select>

    <select id="selectByCredentialHeaderIdAndCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from credential_header_ext
        where credential_header_id = #{credentialHeaderId}
        and ext_code = #{extCode} limit 1
    </select>





    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO credential_header_ext(
        credential_header_id, ext_code, ext_value
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.credentialHeaderId},
            #{element.extCode},
            #{element.extValue}
            )
        </foreach>
    </insert>




</mapper>
