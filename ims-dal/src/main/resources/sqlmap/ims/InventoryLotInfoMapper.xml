<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.InventoryLotInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.InventoryLotInfo">
        <id column="id" property="id" />
        <result column="sku_id" property="skuId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="lot_id" property="lotId" />
        <result column="scm_lot_no" property="scmLotNo" />
        <result column="vendor_id" property="vendorId" />
        <result column="unsalable_date" property="unsalableDate" />
        <result column="manufacture_date" property="manufactureDate" />
        <result column="region_code_path" property="regionCodePath" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sku_id, warehouse_id, lot_id, scm_lot_no, vendor_id, unsalable_date, manufacture_date, region_code_path, create_time, update_time
    </sql>

    <select id="getByWarehouseIdAndLotId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from inventory_lot_info
        where warehouse_id = #{warehouseId}
        and lot_id = #{lotId}
    </select>

    <select id="batchGetByWarehouseIdAndLotId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from inventory_lot_info
        where (warehouse_id, lot_id) in
        <foreach collection="items" item="item" open="(" close=")" separator=",">
            (#{item.warehouseId}, #{item.lotId})
        </foreach>
    </select>

    <select id="batchGetByWarehouseIdAndLotIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from inventory_lot_info
        where
        warehouse_id = #{warehouseId}
        and lot_id in
        <foreach collection="lotIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert ignore into inventory_lot_info (
        sku_id, warehouse_id, lot_id, scm_lot_no, vendor_id, unsalable_date, manufacture_date, region_code_path, create_time, update_time
        ) values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.skuId},
            #{item.warehouseId},
            #{item.lotId},
            #{item.scmLotNo},
            #{item.vendorId},
            #{item.unsalableDate},
            #{item.manufactureDate},
            #{item.regionCodePath},
            #{item.createTime},
            #{item.updateTime}
            )
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="items" item="item" separator=";">
            update inventory_lot_info set sku_id = #{item.skuId},
            warehouse_id = #{item.warehouseId},
            lot_id = #{item.lotId},
            scm_lot_no = #{item.scmLotNo},
            vendor_id = #{item.vendorId},
            unsalable_date = #{item.unsalableDate},
            manufacture_date = #{item.manufactureDate},
            region_code_path = #{item.regionCodePath},
            update_time = #{item.updateTime}
            where id = #{item.id} and warehouse_id = #{item.warehouseId}
        </foreach>
    </update>

    <select id="getBySkuIdAndWarehouseIdsAndManufactureDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from inventory_lot_info
        where sku_id = #{skuId}
        and  manufacture_date = #{manufactureDate}
        and warehouse_id in
        <foreach collection="warehouseIds" item="warehouseId" open="(" close=")" separator=",">
            #{warehouseId}
        </foreach>
    </select>

    <delete id="batchDelete">
        delete from inventory_lot_info where warehouse_id = #{warehouseId}
        and lot_id in <foreach collection="items" item="item" open="(" close=")" separator=",">
        #{item}
    </foreach>
    </delete>


    <select id="getLotIdBySkuIdsAndWarehouseIdAndManufactureDate" resultType="java.lang.String">
        select  lot_id
        from inventory_lot_info
        where warehouse_id = #{warehouseId}
        and  manufacture_date = #{manufactureDate}
        and sku_id in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>

    <select id="getInventoryLotInfoByTableIndex" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from inventory_lot_info_${tableIndex}
        where id > #{maxId} order by id asc limit #{limit}
    </select>

    <update id="batchUpdateByTableIndex">
        <foreach collection="items" item="item" separator=";">
            update inventory_lot_info_${tableIndex} set sku_id = #{item.skuId},
            warehouse_id = #{item.warehouseId},
            lot_id = #{item.lotId},
            scm_lot_no = #{item.scmLotNo},
            vendor_id = #{item.vendorId},
            unsalable_date = #{item.unsalableDate},
            manufacture_date = #{item.manufactureDate},
            region_code_path = #{item.regionCodePath},
            update_time = #{item.updateTime}
            where id = #{item.id} and warehouse_id = #{item.warehouseId}
        </foreach>
    </update>
</mapper>
