<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.WarehouseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.Warehouse">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="corporation_code" property="corporationCode" />
        <result column="city_id" property="cityId" />
        <result column="zone_id" property="zoneId" />
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="sale_type" property="saleType"/>
        <result column="station_id" property="stationId"/>
        <result column="general_region_id" property="generalRegionId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, corporation_code,zone_id, city_id, status, type, create_time, create_user, update_time, update_user,sale_type, station_id, general_region_id
    </sql>

    <select id="pageListByCondition" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from warehouse
        where corporation_code = #{condition.warehouseCorporationCode}
        and type = #{condition.warehouseType}
        <if test="condition.zoneId != null">
            and zone_id = #{condition.zoneId}
        </if>
        <if test="condition.cityId != null and condition.cityId !=''">
            and city_id = #{condition.cityId}
        </if>
        <if test="condition.status != null">
            and status = #{condition.status}
        </if>
    </select>

    <select id="selectIdByCorporationCodeAndWarehouseType" resultType="java.lang.Long">
        select id from warehouse
        where corporation_code = #{corporationCode}
        and type = #{warehouseType}

    </select>

    <select id="getAllWarehouses" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from warehouse
    </select>

    <select id="selectAllId" resultType="java.lang.Long">
        select id
        from warehouse
    </select>

    <select id="addLock" resultType="java.lang.Long">
        select id from warehouse where id = #{warehouseId} for update
    </select>

    <select id="addShareLock" resultType="java.lang.Long">
        select 1 from warehouse where id = #{warehouseId} lock in share mode
    </select>

    <select id="getByZoneIdAndCityIdsAndWarehouseType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from warehouse
        <where>
            <if test="zoneId != null">
                and zone_id = #{zoneId}
            </if>
            <if test="cityIds != null and cityIds.size() != 0">
                and city_id in
                <foreach collection="cityIds" open="(" close=")" separator="," item="cityId">
                    #{cityId}
                </foreach>
            </if>
            <if test="warehouseType != null">
                and `type` = #{warehouseType}
            </if>
        </where>

    </select>

</mapper>
