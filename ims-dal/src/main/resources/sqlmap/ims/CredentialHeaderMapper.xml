<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.CredentialHeader">
        <id column="id" property="id" />
        <result column="idempotent_id" property="idempotentId" />
        <result column="status" property="status"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="is_stock_change" property="isStockChange" />
        <result column="order_source" property="orderSource" />
        <result column="order_no" property="orderNo" />
        <result column="exe_order_source" property="exeOrderSource" />
        <result column="exe_order_no" property="exeOrderNo" />
        <result column="order_type" property="orderType" />
        <result column="order_operate_type" property="orderOperateType" />
        <result column="business_time" property="businessTime" />
        <result column="seq_no" property="seqNo" />
        <result column="expect_out_time" property="expectOutTime" />
        <result column="delivery_mode" property="deliveryMode" />
        <result column="expect_arrive_time" property="expectArriveTime" />
        <result column="expect_in_time" property="expectInTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="end_date_time" property="endDateTime"/>
        <result column="delivery_date" property="deliveryDate"/>
        <result column="stock_source_type" property="stockSourceType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, idempotent_id, status, is_stock_change, warehouse_id, order_source, order_no, exe_order_source, exe_order_no, order_type, order_operate_type, business_time, seq_no, expect_out_time, delivery_mode, expect_arrive_time, expect_in_time, create_time, update_time, end_date_time,delivery_date,stock_source_type
    </sql>

    <select id="selectByIdempotentId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from credential_header
        where idempotent_id = #{idempotentId}
    </select>

    <update id="confirmCredentialByIdempotentId">
        update credential_header set status = 1 where idempotent_id = #{idempotentId} and status = 0
    </update>

    <select id="getInitCredentialHeaders" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from credential_header
        where status = 0 and create_time between #{startTime} and #{endTime}
        limit #{limit}
    </select>

    <select id="listByExeOrderSourceAndNoAndOrderOperateType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from credential_header
        where exe_order_source = #{exeOrderSource} and exe_order_no = #{exeOrderNo}
        and order_operate_type = #{orderOperateType} limit 1
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from credential_header
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectWarehouseCompleteHeaderByBusinessTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from credential_header
        where business_time <![CDATA[>=]]> #{minBusinessTime} and business_time <![CDATA[<]]> #{maxBusinessTime} and
        warehouse_id=#{warehouseId} and status = 1
    </select>

    <select id="addLock" resultType="java.lang.Long">
        select id from credential_header where  exe_order_source = #{exeOrderSource} and exe_order_no = #{exeOrderNo} limit 1 for update
    </select>

    <select id="selectMinIdByWarehouseCompleteHeaderByBusinessTime" resultType="java.lang.Long">
        select min(id)
        from credential_header
        where business_time <![CDATA[>=]]> #{minBusinessTime} and business_time <![CDATA[<]]> #{maxBusinessTime} and
        warehouse_id=#{warehouseId} and status = 1
    </select>

    <select id="selectMaxIdByWarehouseCompleteHeaderByBusinessTime" resultType="java.lang.Long">
        select max(id)
        from credential_header
        where business_time <![CDATA[>=]]> #{minBusinessTime} and business_time <![CDATA[<]]> #{maxBusinessTime} and
        warehouse_id=#{warehouseId} and status = 1
    </select>

    <select id="selectByWarehouseIdAndIdBetween" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from credential_header
        where warehouse_id=#{warehouseId} and id <![CDATA[>]]> #{minId} and id <![CDATA[<=]]> #{maxId}
        order by warehouse_id,id+1 asc limit 50
    </select>

    <select id="selectByWarehouseIdAndIdAndBusinessBetween" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from credential_header
        where warehouse_id=#{warehouseId} and id <![CDATA[>]]> #{minId} and id <![CDATA[<=]]> #{maxId}
        and business_time <![CDATA[>=]]> #{minBusinessTime} and business_time <![CDATA[<]]> #{maxBusinessTime}
        and status = 1
        order by warehouse_id,id+1 asc limit 50
    </select>

    <select id="selectBySeqNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from credential_header
        where seq_no=#{seqNo}  for update
    </select>

    <select id="selectIdByMaxIdLimit100" resultType="java.lang.Long">
        select id from credential_header
        where id <![CDATA[<]]> #{maxId} order by id desc  limit 40
    </select>


    <select id="selectIdByMinIdLimit200" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from credential_header
        where id <![CDATA[>]]> #{minId} order by id   limit 200
    </select>



    <select id="selectSeqNoBySeqNoIn" resultType="java.lang.String">
        select seq_no
        from credential_header
        where seq_no in
        <foreach item="item" index="index" collection="seqNoCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="listByWarehouseIdAndOrderOperateTypeAndEndDateTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from credential_header
        where warehouse_id = #{warehouseId}
        and order_type = #{orderType}
        and end_date_time = #{endDateTime}
        and business_time <![CDATA[>=]]> #{minBusinessTime}
    </select>



    <select id="selectMaxIdByCreateTime" resultType="java.lang.Long">
        select max(id)
        from credential_header
        where create_time between #{startCreateTime} and #{endCreateTime}
    </select>


    <select id="selectByOrderNoAndSource" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from credential_header
        where order_operate_type = #{orderOperateType}
        and status = 1
        and (order_no, order_source) in
        <foreach collection="orderNoAndSourceList" item="item" open="(" separator="," close=")">
            (#{item.orderNo}, #{item.orderSource})
        </foreach>
    </select>




    <select id="selectByOrderNoAndExeOrderNoAndOrderTypeAndOrderOperateType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from credential_header
        where order_no = #{orderNo} and exe_order_no = #{exeOrderNo} and order_type = #{orderType}
        and order_operate_type = #{orderOperateType} limit 1
    </select>

    <select id="selectByOrderNosAndBusinessBetween" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from credential_header
        where order_no in
        <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
        and order_operate_type in
        <foreach collection="orderOperateTypes" item="orderOperateType" open="(" separator="," close=")">
            #{orderOperateType}
        </foreach>
        and business_time <![CDATA[>=]]> #{minBusinessTime} and business_time <![CDATA[<]]> #{maxBusinessTime}
        and status = 1
    </select>

    <select id="selectOrderNoByWarehouseIdAndTime" resultType="java.lang.String">
        select distinct order_no from credential_header where
        warehouse_id = #{warehouseId}
        and order_operate_type in
        <foreach collection="orderOperateTypes" item="orderOperateType" open="(" separator="," close=")">
            #{orderOperateType}
        </foreach>
        and business_time <![CDATA[>=]]> #{minBusinessTime} and business_time <![CDATA[<]]> #{maxBusinessTime}
        and status = 1
    </select>

</mapper>
