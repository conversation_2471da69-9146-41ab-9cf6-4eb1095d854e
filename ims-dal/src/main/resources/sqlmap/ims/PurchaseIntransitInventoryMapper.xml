<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory">
        <id column="id" property="id" />
        <result column="logic_inventory_location_code" property="logicInventoryLocationCode" />
        <result column="sku_id" property="skuId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="cargo_owner_id" property="cargoOwnerId" />
        <result column="order_source" property="orderSource" />
        <result column="order_no" property="orderNo" />
        <result column="expect_arrive_time" property="expectArriveTime" />
        <result column="plan_qty" property="planQty" />
        <result column="intransit_qty" property="intransitQty" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="version" property="version" />
        <result column="usage_code" property="usageCode" />
        <result column="order_tag" property="orderTag" />
        <result column="booked_intransit_qty" property="bookedIntransitQty" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, logic_inventory_location_code, sku_id, warehouse_id, cargo_owner_id, order_source, order_no, expect_arrive_time, plan_qty, intransit_qty, create_time, update_time, version,
        usage_code, order_tag,booked_intransit_qty
    </sql>

    <select id="selectOneIdByWarehouseIdsAndCargoOwnerId" resultType="java.lang.Long">
        select id from
        purchase_intransit_inventory
        where cargo_owner_id = #{cargoOwnerId}
        and logic_inventory_location_code = #{logicInventoryCode}
        and warehouse_id in
        <foreach collection="warehouseIds" item="warehouseId" separator="," open="(" close=")">
            #{warehouseId}
        </foreach>
        limit 1
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into purchase_intransit_inventory
        (
        logic_inventory_location_code, sku_id, warehouse_id, cargo_owner_id, order_source, order_no, expect_arrive_time,
        plan_qty, intransit_qty, version, usage_code, order_tag,booked_intransit_qty
        )
        values
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.logicInventoryLocationCode},
            #{item.skuId},
            #{item.warehouseId},
            #{item.cargoOwnerId},
            #{item.orderSource},
            #{item.orderNo},
            #{item.expectArriveTime},
            #{item.planQty},
            #{item.intransitQty},
            #{item.version},
            #{item.usageCode},
            #{item.orderTag},
            #{item.bookedIntransitQty}
            )
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="items" item="item" separator=";">
            update purchase_intransit_inventory set expect_arrive_time = #{item.expectArriveTime},
            plan_qty = #{item.planQty},
            intransit_qty = #{item.intransitQty},
            booked_intransit_qty = #{item.bookedIntransitQty},
            version = version + 1
            where order_source = #{item.orderSource}
            and order_no = #{item.orderNo}
            and sku_id = #{item.skuId}
            and usage_code = #{item.usageCode}
        </foreach>
    </update>


    <select id="listByOrderSourceAndPurchaseNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from purchase_intransit_inventory
        where order_source = #{orderSource}
        and order_no = #{purchaseNo}
    </select>


    <update id="updateExpectArriveTimeByOrder">
        update purchase_intransit_inventory set expect_arrive_time = #{expectArriveTime},
        version = version + 1
        where order_source = #{orderSource}
        and order_no = #{orderNo}
    </update>

    <select id="selectByWarehouseSkuOwnerInfo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from purchase_intransit_inventory
        where (sku_id, warehouse_id, cargo_owner_id) in
        <foreach collection="conditionList" item="item" open="(" separator="," close=")">
            (#{item.skuId}, #{item.warehouseId}, #{item.cargoOwnerId})
        </foreach>
    </select>

    <select id="selectByWarehouseSkuOwnerLocationArriveTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from purchase_intransit_inventory
        where cargo_owner_id = #{request.cargoOwnerId}
        and warehouse_id = #{request.warehouseId}
        and sku_id = #{request.skuId}
        and logic_inventory_location_code = #{request.logicInventoryLocationCode}
        <if test="expectArriveStartTime != null and expectArriveEndTime != null">
            and expect_arrive_time <![CDATA[ >= ]]> #{expectArriveStartTime}
            and expect_arrive_time <![CDATA[ <= ]]> #{expectArriveEndTime}
        </if>
    </select>

    <select id="selectByOrderSourceAndOrderNoAndSkuIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from purchase_intransit_inventory
        <where>order_source=#{orderSource} and order_no=#{orderNo}
            <if test="skuIdCollection != null and skuIdCollection.size() > 0">
                and sku_id in
                <foreach item="item" index="index" collection="skuIdCollection"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateBookedIntransitQtyByIdIn">
        update purchase_intransit_inventory
        set booked_intransit_qty=#{updatedBookedIntransitQty},
        version = version + 1
        where id in
        <foreach item="item" index="index" collection="idCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


</mapper>
