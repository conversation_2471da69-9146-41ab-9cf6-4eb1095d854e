<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryAllocDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail">
        <id column="id" property="id" />
        <result column="logic_inventory_location_code" property="logicInventoryLocationCode" />
        <result column="sku_id" property="skuId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="cargo_owner_id" property="cargoOwnerId" />
        <result column="alloc_qty" property="allocQty" />
        <result column="demand_time" property="demandTime" />
        <result column="alloc_dimension" property="allocDimension" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="version" property="version" />
        <result column="order_source" property="orderSource" />
        <result column="order_no" property="orderNo" />
        <result column="order_type" property="orderType" />
        <result column="usage_code" property="usageCode" />
        <result column="exe_order_source" property="exeOrderSource" />
        <result column="exe_order_no" property="exeOrderNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, logic_inventory_location_code, sku_id, warehouse_id, cargo_owner_id, alloc_qty, demand_time, alloc_dimension, create_time, update_time, version, order_source, order_no, order_type, usage_code,exe_order_source, exe_order_no
    </sql>

    <select id="selectByOrderNoAndOrderSourceAndSkuIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_sku_inventory_alloc_detail
        where order_no=#{orderNo} and order_source=#{orderSource} and sku_id in
        <foreach item="item" index="index" collection="skuIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByExeOrderNoAndExeOrderSourceAndSkuIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_sku_inventory_alloc_detail
        where exe_order_no=#{exeOrderNo}  and sku_id in
        <foreach item="item" index="index" collection="skuIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insertList">
        INSERT INTO warehouse_sku_inventory_alloc_detail(
        logic_inventory_location_code,
        sku_id,
        warehouse_id,
        cargo_owner_id,
        alloc_qty,
        demand_time,
        alloc_dimension,
        version,
        order_source,
        order_no,
        order_type,
        usage_code,
        exe_order_source,
        exe_order_no
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.logicInventoryLocationCode},
            #{element.skuId},
            #{element.warehouseId},
            #{element.cargoOwnerId},
            #{element.allocQty},
            #{element.demandTime},
            #{element.allocDimension},
            #{element.version},
            #{element.orderSource},
            #{element.orderNo},
            #{element.orderType},
            #{element.usageCode},
            #{element.exeOrderSource},
            #{element.exeOrderNo}
            )
        </foreach>
    </insert>




    <delete id="deleteByExeOrderNo">
        delete from warehouse_sku_inventory_alloc_detail
        where exe_order_no=#{exeOrderNo}
    </delete>

    <delete id="deleteByOrderNoAndOrderSource">
        delete from warehouse_sku_inventory_alloc_detail
        where order_no=#{orderNo} and order_source=#{orderSource}
    </delete>

    <delete id="deleteByOrderNoAndOrderSourceAndSkuIdIn">
        delete from warehouse_sku_inventory_alloc_detail
        <where>order_no=#{orderNo} and order_source=#{orderSource}
            <if test="skuIdCollection != null and skuIdCollection.size() > 0">
                and sku_id in
                <foreach item="item" index="index" collection="skuIdCollection"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </delete>

</mapper>
