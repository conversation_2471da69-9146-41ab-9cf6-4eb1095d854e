<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.WarehouseSkuLotInventoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory">
        <id column="id" property="id" />
        <result column="logic_inventory_location_code" property="logicInventoryLocationCode" />
        <result column="sku_id" property="skuId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="cargo_owner_id" property="cargoOwnerId" />
        <result column="lot_id" property="lotId" />
        <result column="free_qty" property="freeQty" />
        <result column="frozen_qty" property="frozenQty" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, logic_inventory_location_code, sku_id, warehouse_id, cargo_owner_id, lot_id, free_qty, frozen_qty, create_time, update_time, version
    </sql>


    <select id="selectBySkuIdInAndLogicInventoryLocation" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_sku_lot_inventory
        where sku_id in
        <foreach item="item" index="index" collection="skuIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
        and cargo_owner_id = #{logicInventoryLocation.cargoOwnerId}
        and warehouse_id = #{logicInventoryLocation.warehouseId}
        and logic_inventory_location_code = #{logicInventoryLocation.logicInventoryLocationCode}
    </select>

    <select id="selectBySkuIdAndLotIdPairAndLogicInventoryLocation" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_sku_lot_inventory
        where (sku_id,lot_id) in
        <foreach item="item" index="index" collection="items"
            open="(" separator="," close=")">
            (#{item.skuId},#{item.lotId})
        </foreach>
        and cargo_owner_id = #{logicInventoryLocation.cargoOwnerId}
        and warehouse_id = #{logicInventoryLocation.warehouseId}
        and logic_inventory_location_code = #{logicInventoryLocation.logicInventoryLocationCode}
    </select>

    <select id="getByLogicInventoryLocationAndLotIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_sku_lot_inventory
        where
        cargo_owner_id = #{logicInventoryLocation.cargoOwnerId}
        and warehouse_id = #{logicInventoryLocation.warehouseId}
        and logic_inventory_location_code = #{logicInventoryLocation.logicInventoryLocationCode}
        and (sku_id, lot_id) in
        <foreach item="item" collection="skuAndLotIdPairs" open="(" separator="," close=")">
            (#{item.skuId}, #{item.lotId})
        </foreach>
    </select>

    <update id="batchUpdate">
        update warehouse_sku_lot_inventory set
        free_qty =
        <foreach collection="items" item="item" open="case id" separator=" " close=" end">
            when #{item.id} then #{item.freeQty}
        </foreach>
        ,frozen_qty =
        <foreach collection="items" item="item" open="case id" separator=" " close=" end">
            when #{item.id} then #{item.frozenQty}
        </foreach>
        ,version = version + 1
        where cargo_owner_id = #{location.cargoOwnerId}
        and warehouse_id = #{location.warehouseId}
        and logic_inventory_location_code = #{location.logicInventoryLocationCode}
        and id in
        <foreach collection="items" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into warehouse_sku_lot_inventory
        (
        logic_inventory_location_code, sku_id, lot_id, warehouse_id, cargo_owner_id, free_qty, frozen_qty, version
        )
        values
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.logicInventoryLocationCode},
            #{item.skuId},
            #{item.lotId},
            #{item.warehouseId},
            #{item.cargoOwnerId},
            #{item.freeQty},
            #{item.frozenQty},
            #{item.version}
            )
        </foreach>
    </insert>

    <delete id="batchDelete">
        delete from warehouse_sku_lot_inventory
        where cargo_owner_id = #{location.cargoOwnerId}
        and warehouse_id = #{location.warehouseId}
        and logic_inventory_location_code = #{location.logicInventoryLocationCode}
        and id in
        <foreach collection="items" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectDistinctSkuIdByWarehouseId" resultType="java.lang.Long">
        select distinct(sku_id)
        from warehouse_sku_lot_inventory
        where cargo_owner_id = #{logicInventoryLocation.cargoOwnerId}
        and warehouse_id = #{logicInventoryLocation.warehouseId}
        and logic_inventory_location_code = #{logicInventoryLocation.logicInventoryLocationCode}
    </select>

    <select id="listSkuLotInventory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_sku_lot_inventory
        where cargo_owner_id = #{request.cargoOwnerId}
        and warehouse_id = #{request.warehouseId}
        and logic_inventory_location_code = #{request.logicInventoryLocationCode}
        and sku_id = #{request.skuId}
        order by `sku_id`,`warehouse_id`,`lot_id`
    </select>

    <select id="getByWarehouseIdList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from warehouse_sku_lot_inventory
        where warehouse_id = #{warehouseId}
        and sku_id in
        <foreach collection="skuIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and logic_inventory_location_code in
        <foreach collection="codes" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getLotIdByWarehouseIdAndSkuIdAndLogicCode" resultType="java.lang.String">
        select lot_id from warehouse_sku_lot_inventory
        where  warehouse_id = #{warehouseId} and sku_id = #{skuId}
        and logic_inventory_location_code in
        <foreach collection="codes" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>
