<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.WarehouseModLockMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.WarehouseModLock">
        <id column="id" property="id" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="mod_num" property="modNum" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, warehouse_id, mod_num, create_time
    </sql>


    <select id="addLock" resultType="java.lang.Integer">
        select count(1) from warehouse_mod_Lock where
        warehouse_id = #{warehouseId}
        and mod_num in
        <foreach collection="modNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        for update
    </select>

    <select id="selectByWarehouseId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from warehouse_mod_Lock
        where   warehouse_id = #{warehouseId}
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into warehouse_mod_Lock
        (
        warehouse_id, mod_num
        )
        values
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.warehouseId},
            #{item.modNum}
            )
        </foreach>
    </insert>

</mapper>
