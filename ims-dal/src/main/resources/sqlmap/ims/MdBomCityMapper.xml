<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.MdBomCityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.MdBomCity">
        <id column="id" property="id" />
        <result column="bom_id" property="bomId" />
        <result column="city_id" property="cityId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, bom_id, city_id, version, create_time, update_time
    </sql>

    <insert id="insertList">
        INSERT INTO md_bom_city(
        bom_id,
        city_id,
        version
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.bomId},
            #{element.cityId},
            #{element.version}
            )
        </foreach>
    </insert>

    <select id="selectByBomIdAndVersionAndCity" resultType="com.ddmc.ims.dal.model.ims.MdBomCity">
        select <include refid="Base_Column_List"/>
        from md_bom_city
        where bom_id = #{bomId}
        and version = #{version}
        and city_id = #{cityId}
    </select>

</mapper>
