<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.WarehouseSkuInventory">
        <id column="id" property="id" />
        <result column="logic_inventory_location_code" property="logicInventoryLocationCode" />
        <result column="sku_id" property="skuId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="cargo_owner_id" property="cargoOwnerId" />
        <result column="usage_code" property="usageCode" />
        <result column="free_qty" property="freeQty" />
        <result column="frozen_qty" property="frozenQty" />
        <result column="alloc_qty" property="allocQty" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, logic_inventory_location_code, sku_id, warehouse_id, usage_code, cargo_owner_id, free_qty, frozen_qty, alloc_qty, create_time, update_time, version
    </sql>

    <select id="selectOneIdByWarehouseIdsAndCargoOwnerId" resultType="java.lang.Long">
        select id from
        warehouse_sku_inventory
        where cargo_owner_id = #{cargoOwnerId}
        and logic_inventory_location_code = #{logicInventoryCode}
        and warehouse_id in
        <foreach collection="warehouseIds" item="warehouseId" separator="," open="(" close=")">
            #{warehouseId}
        </foreach>
        limit 1
    </select>

    <select id="getByLogicInventoryLocationAndSkuIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from warehouse_sku_inventory
        where cargo_owner_id = #{logicInventoryLocation.cargoOwnerId}
        and warehouse_id = #{logicInventoryLocation.warehouseId}
        and logic_inventory_location_code = #{logicInventoryLocation.logicInventoryLocationCode}
        and sku_id in
        <foreach collection="skuIds" item="skuId" separator="," open="(" close=")">
            #{skuId}
        </foreach>
    </select>

    <update id="batchUpdate">
        update warehouse_sku_inventory set
        free_qty =
        <foreach collection="items" item="item" open="case id" separator=" " close=" end">
            when #{item.id} then #{item.freeQty}
        </foreach>
        ,frozen_qty =
        <foreach collection="items" item="item" open="case id" separator=" " close=" end">
            when #{item.id} then #{item.frozenQty}
        </foreach>
        ,alloc_qty =
        <foreach collection="items" item="item" open="case id" separator=" " close=" end">
            when #{item.id} then #{item.allocQty}
        </foreach>
        ,version = version + 1
        where cargo_owner_id = #{location.cargoOwnerId}
        and warehouse_id = #{location.warehouseId}
        and logic_inventory_location_code = #{location.logicInventoryLocationCode}
        and id in
            <foreach collection="items" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
    </update>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into warehouse_sku_inventory
        (
        logic_inventory_location_code, sku_id, warehouse_id,usage_code, cargo_owner_id, free_qty, frozen_qty, alloc_qty, version
        )
        values
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.logicInventoryLocationCode},
            #{item.skuId},
            #{item.warehouseId},
            #{item.usageCode},
            #{item.cargoOwnerId},
            #{item.freeQty},
            #{item.frozenQty},
            #{item.allocQty},
            #{item.version}
            )
        </foreach>
    </insert>

    <delete id="batchDelete">
        delete from warehouse_sku_inventory
        where cargo_owner_id = #{location.cargoOwnerId}
        and warehouse_id = #{location.warehouseId}
        and logic_inventory_location_code = #{location.logicInventoryLocationCode}
        and id in
        <foreach collection="items" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectWarehouseIdAndSkuIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from warehouse_sku_inventory
        where warehouse_id = #{warehouseId}
        and sku_id in
        <foreach collection="skuIds" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and logic_inventory_location_code in
        <foreach collection="codes" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSkuIdByWarehouseId" resultType="java.lang.Long">
        select distinct sku_id from warehouse_sku_inventory
        where warehouse_id = #{warehouseId}
    </select>

    <select id="addLock" resultType="java.lang.Integer">
        select count(1) from warehouse_sku_inventory where
            warehouse_id = #{warehouseId}
            and sku_id in
                <foreach collection="skuIds" item="item" separator="," open="(" close=")">
                        #{item}
                </foreach>
            and logic_inventory_location_code in
            <foreach collection="codes" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>

            for update
    </select>

    <select id="getProcessingWarehouseSkuInventory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from warehouse_sku_inventory_0 where id > #{minId} order by id asc limit 1000
    </select>

    <delete id="deleteByWarehouseSkuInventoryId">
        delete from warehouse_sku_inventory_0 where id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectBySkuIdInAndLogicInventoryLocation" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from warehouse_sku_inventory
        where sku_id in
        <foreach item="item" index="index" collection="skuIdCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
        and cargo_owner_id = #{logicInventoryLocation.cargoOwnerId}
        and warehouse_id = #{logicInventoryLocation.warehouseId}
        and logic_inventory_location_code = #{logicInventoryLocation.logicInventoryLocationCode}
    </select>


    <select id="selectWarehouseSkuOwnerInfo" resultType="com.ddmc.ims.common.bo.WarehouseSkuCargoOwnerInfo">
        select sku_id as skuId, warehouse_id as warehouseId, cargo_owner_id as cargoOwnerId
        from warehouse_sku_inventory
        where sku_id in
        <foreach item="skuId" collection="skuIds" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and warehouse_id in
        <foreach item="warehouseId" collection="warehouseIds" open="(" separator="," close=")">
            #{warehouseId}
        </foreach>
        <if test ="cargoOwnerIds != null and cargoOwnerIds.size() != 0">
            and cargo_owner_id in
            <foreach item="cargoOwnerId" collection="cargoOwnerIds" open="(" separator="," close=")">
                #{cargoOwnerId}
            </foreach>
        </if>
        and logic_inventory_location_code = #{logicInventoryLocationCode}
        <if test="isExcludeZeroStock">
            and free_qty <![CDATA[ <> ]]> 0
        </if>
        group by sku_id, warehouse_id, cargo_owner_id
    </select>



    <select id="selectByWarehouseSkuOwnerInfo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from warehouse_sku_inventory
        where (sku_id, warehouse_id, cargo_owner_id) in
        <foreach collection="conditionList" item="item" open="(" separator="," close=")">
            (#{item.skuId}, #{item.warehouseId}, #{item.cargoOwnerId})
        </foreach>
    </select>


    <select id="selectByFromWarehouseSkuOwnerLocation" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from warehouse_sku_inventory
        where cargo_owner_id = #{request.cargoOwnerId}
        and warehouse_id = #{request.warehouseId}
        and sku_id = #{request.skuId}
        and logic_inventory_location_code = #{request.logicInventoryLocationCode}
    </select>

    <select id="getByWarehouseIdList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from warehouse_sku_inventory
        where warehouse_id = #{warehouseId}
        and sku_id in
        <foreach collection="skuIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and logic_inventory_location_code in
        <foreach collection="codes" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>



</mapper>
