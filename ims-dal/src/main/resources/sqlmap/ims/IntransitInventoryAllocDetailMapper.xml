<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.IntransitInventoryAllocDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.IntransitInventoryAllocDetail">
        <id column="id" property="id" />
        <result column="logic_inventory_location_code" property="logicInventoryLocationCode" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="cargo_owner_id" property="cargoOwnerId" />
        <result column="sku_id" property="skuId" />
        <result column="usage_code" property="usageCode" />
        <result column="to_alloc_qty" property="toAllocQty" />
        <result column="delivery_date" property="deliveryDate" />
        <result column="today_sale" property="todaySale" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="ori_delivery_date" property="oriDeliveryDate"/>
        <result column="transfer_in_not_alloc_qty" property="transferInNotAllocQty"/>
        <result column="to_transfer_in_qty" property="toTransferInQty"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, logic_inventory_location_code, warehouse_id, cargo_owner_id, sku_id, usage_code, to_alloc_qty, delivery_date, today_sale, create_time, update_time,ori_delivery_date,transfer_in_not_alloc_qty,to_transfer_in_qty
    </sql>

    <select id="selectSkuIdByWarehouseIdAndDeliveryDateAndAllocMoreZero" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from intransit_inventory_alloc_detail
        where warehouse_id = #{warehouseId}
        and delivery_date <![CDATA[<=]]> #{deliveryDate}
        and to_alloc_qty > 0
    </select>


    <select id="selectByLocationAndOriDeliveryDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from intransit_inventory_alloc_detail
        where warehouse_id = #{location.warehouseId}
        and cargo_owner_id = #{location.cargoOwnerId}
        and logic_inventory_location_code = #{location.logicInventoryLocationCode}
        and ori_delivery_date = #{oriDeliveryDate}
        and sku_id in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>



    <select id="selectLessDeliveryDateByWarehouseIdAndSkuIdsAndAllocMoreZero" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from intransit_inventory_alloc_detail
        where warehouse_id = #{warehouseId}
        and delivery_date <![CDATA[<=]]> #{deliveryDate}
        and sku_id in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        and to_alloc_qty > 0
    </select>

    <select id="selectByWarehouseIdAndSkuIdsAndOriDeliveryDate" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from intransit_inventory_alloc_detail
        where warehouse_id = #{warehouseId}
        and delivery_date = #{deliveryDate}
        and ori_delivery_date = #{oriDeliveryDate}
        and sku_id in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>

    <select id="selectByLogicInventoryLocationAndDateAndSkuIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from intransit_inventory_alloc_detail where
        warehouse_id = #{location.warehouseId} and logic_inventory_location_code = #{location.logicInventoryLocationCode}
        and cargo_owner_id = #{location.cargoOwnerId} and delivery_date = #{deliveryDate}
        and ori_delivery_date = #{oriDeliveryDate}
        and sku_id in
        <foreach collection="skuIds" item="skuId" separator="," open="(" close=")">
            #{skuId}
        </foreach>
    </select>

    <update id="batchUpdate">
        update intransit_inventory_alloc_detail set
        to_alloc_qty =
        <foreach collection="items" item="item" open="case id" separator=" " close=" end">
            when #{item.id} then #{item.toAllocQty}
        </foreach>
        where  id in
        <foreach collection="items" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateToTransferInQtyAndAllocQty">
        <foreach collection="items" item="item" separator=";">
            update intransit_inventory_alloc_detail set
            to_transfer_in_qty = #{item.toTransferInQty},
            to_alloc_qty = #{item.toAllocQty}
            where id = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateToTransferInQtyAndTransferInNotAllocQty">
        <foreach collection="items" item="item" separator=";">
            update intransit_inventory_alloc_detail set
            transfer_in_not_alloc_qty = #{item.transferInNotAllocQty},
            to_transfer_in_qty = #{item.toTransferInQty}
            where id = #{item.id}
        </foreach>
    </update>


    <update id="batchUpdateToTransferInQtyAndToAllocQty">
        <foreach collection="items" item="item" separator=";">
            update intransit_inventory_alloc_detail set
            transfer_in_not_alloc_qty = #{item.transferInNotAllocQty},
            to_alloc_qty = #{item.toAllocQty}
            where id = #{item.id}
        </foreach>
    </update>


    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO intransit_inventory_alloc_detail(
        logic_inventory_location_code,
        sku_id,
        warehouse_id,
        cargo_owner_id,
        usage_code,
        to_alloc_qty,
        delivery_date,
        today_sale,
        ori_delivery_date,
        transfer_in_not_alloc_qty,
        to_transfer_in_qty
        )VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.logicInventoryLocationCode},
            #{item.skuId},
            #{item.warehouseId},
            #{item.cargoOwnerId},
            #{item.usageCode},
            #{item.toAllocQty},
            #{item.deliveryDate},
            #{item.todaySale},
            #{item.oriDeliveryDate},
            #{item.transferInNotAllocQty},
            #{item.toTransferInQty}
            )
        </foreach>
    </insert>





    <select id="selectByConditions" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from intransit_inventory_alloc_detail
        where (sku_id, warehouse_id, ori_delivery_date, cargo_owner_id, logic_inventory_location_code) in
        <foreach collection="conditions" item="item" open="(" separator="," close=")">
            (#{item.skuId}, #{item.warehouseId}, #{item.oriDeliveryDate}, #{item.cargoOwnerId}, #{item.logicInventoryLocationCode})
        </foreach>
    </select>


    <select id="selectOneIdByCreateTimeBefore" resultType="java.lang.Long">
        select max(id)
        from intransit_inventory_alloc_detail
        where create_time <![CDATA[<]]> #{maxCreateTime}
    </select>

    <select id="selectByMaxIdLimit200" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from intransit_inventory_alloc_detail
        where id <![CDATA[<]]> #{maxId} order by id desc  limit 200
    </select>

</mapper>
