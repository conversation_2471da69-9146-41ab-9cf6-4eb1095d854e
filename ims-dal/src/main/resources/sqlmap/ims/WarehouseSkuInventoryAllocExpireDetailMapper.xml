<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryAllocExpireDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocExpireDetail">
        <id column="id" property="id"/>
        <result column="logic_inventory_location_code" property="logicInventoryLocationCode"/>
        <result column="sku_id" property="skuId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="cargo_owner_id" property="cargoOwnerId"/>
        <result column="alloc_qty" property="allocQty"/>
        <result column="manufacture_date" property="manufactureDate"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
        <result column="usage_code" property="usageCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, logic_inventory_location_code, sku_id, warehouse_id, cargo_owner_id, alloc_qty, manufacture_date,
         create_time, update_time, version, usage_code
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO warehouse_sku_inventory_alloc_expire_detail(
        logic_inventory_location_code,
        sku_id,
        warehouse_id,
        cargo_owner_id,
        alloc_qty,
        manufacture_date,
        version,
        usage_code
        )VALUES
        <foreach collection="items" item="item" index="index" separator=",">
            (
            #{item.logicInventoryLocationCode},
            #{item.skuId},
            #{item.warehouseId},
            #{item.cargoOwnerId},
            #{item.allocQty},
            #{item.manufactureDate},
            #{item.version},
            #{item.usageCode}
            )
        </foreach>
    </insert>

    <select id="selectByWarehouseIdAndDemandTimeAndSkuIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_sku_inventory_alloc_expire_detail where
        warehouse_id = #{warehouseId} and manufacture_date = #{manufactureDate}
        and sku_id in
        <foreach collection="skuIds" item="skuId" separator="," open="(" close=")">
            #{skuId}
        </foreach>
    </select>

    <select id="selectByLogicInventoryLocationAndDateAndSkuIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from warehouse_sku_inventory_alloc_expire_detail where
        warehouse_id = #{location.warehouseId} and logic_inventory_location_code = #{location.logicInventoryLocationCode}
        and cargo_owner_id = #{location.cargoOwnerId} and manufacture_date = #{manufactureDate}
        and sku_id in
        <foreach collection="skuIds" item="skuId" separator="," open="(" close=")">
            #{skuId}
        </foreach>
    </select>


    <update id="batchUpdate">
        <foreach collection="items" item="item" separator=";">
            update warehouse_sku_inventory_alloc_expire_detail set alloc_qty = #{item.allocQty}, version = version + 1
            where id = #{item.id}
        </foreach>
    </update>


</mapper>
