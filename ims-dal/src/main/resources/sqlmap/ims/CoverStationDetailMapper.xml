<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.CoverStationDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.CoverStationDetail">
        <id column="id" property="id" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="station_id" property="stationId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, warehouse_id, station_id, create_time
    </sql>

    <select id="getByWarehouseId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from cover_station_detail
        where warehouse_id = #{warehouseId}
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into cover_station_detail (
            warehouse_id, station_id
        ) values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.warehouseId},
            #{item.stationId}
            )
        </foreach>
    </insert>
</mapper>
