<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.ReceivedMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.ReceivedMsg">
        <id column="id" property="id" />
        <result column="request_source" property="requestSource" />
        <result column="request_no" property="requestNo" />
        <result column="business_type" property="businessType" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, request_source, request_no, business_type, content, create_time, update_time, status
    </sql>

    <select id="getWaitingHandleMsg" resultType="java.lang.Long">
        select id
        from received_msg where status = 0
        and business_type = #{businessType}
        and id > #{lastId} order by id limit 1000
    </select>

    <update id="completeReceivedMsg">
        update received_msg set status = 1, update_time = now() where id = #{id}
    </update>
</mapper>
