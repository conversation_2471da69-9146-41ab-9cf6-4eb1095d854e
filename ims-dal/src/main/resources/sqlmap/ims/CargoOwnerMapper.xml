<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.CargoOwnerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.CargoOwner">
        <id column="id" property="id" />
        <result column="cargo_owner_code" property="cargoOwnerCode" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cargo_owner_code, name, description, create_time, create_user_id
    </sql>

    <select id="listAllByIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from cargo_owner where id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listAllCargoOwner" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from cargo_owner order by id limit 1000
    </select>

    <select id="selectByCorporationCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from cargo_owner where cargo_owner_code = #{corporationCode} limit 1
    </select>

</mapper>
