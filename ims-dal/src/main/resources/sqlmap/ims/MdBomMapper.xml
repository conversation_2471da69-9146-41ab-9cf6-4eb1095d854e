<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.MdBomMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.MdBom">
        <id column="id" property="id"/>
        <result column="bom_id" property="bomId"/>
        <result column="bom_status" property="bomStatus"/>
        <result column="effect_start_time" property="effectStartTime"/>
        <result column="effect_end_time" property="effectEndTime"/>
        <result column="manufacture_type" property="manufactureType"/>
        <result column="corporation_code" property="corporationCode"/>
        <result column="version" property="version"/>
        <result column="sku_id" property="skuId"/>
        <result column="sku_unit" property="skuUnit"/>
        <result column="sku_type" property="skuType"/>
        <result column="proportion" property="proportion"/>
        <result column="apportion" property="apportion"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , bom_id, bom_status, effect_start_time, effect_end_time, manufacture_type, corporation_code, version, sku_id,
        sku_unit, sku_type, proportion, apportion, create_time, update_time
    </sql>

    <select id="getByBomIdAndVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from md_bom
        where bom_id = #{bomId}
        and version = #{version}
    </select>

    <select id="batchGetByBomId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from md_bom
        where bom_id in
        <foreach collection="pair" item="item" open="(" separator="," close=")">
            #{item.bomNo}
        </foreach>
    </select>

    <select id="countByBomIdAndVersion" resultType="java.lang.Long">
        select count(1)
        from md_bom
        where bom_id = #{bomId}
        and version = #{version} lock in share mode
    </select>

    <insert id="insertList">
        INSERT INTO md_bom(
        bom_id,
        bom_status,
        effect_start_time,
        effect_end_time,
        manufacture_type,
        corporation_code,
        version,
        sku_id,
        sku_unit,
        sku_type,
        proportion,
        apportion
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.bomId},
            #{element.bomStatus},
            #{element.effectStartTime},
            #{element.effectEndTime},
            #{element.manufactureType},
            #{element.corporationCode},
            #{element.version},
            #{element.skuId},
            #{element.skuUnit},
            #{element.skuType},
            #{element.proportion},
            #{element.apportion}
            )
        </foreach>
    </insert>


    <select id="selectLastEffectiveSkuBom" resultType="com.ddmc.ims.dal.model.ims.MdBom">
        select
        <include refid="Base_Column_List"/>
        from md_bom
        where sku_id = #{skuId}
        and corporation_code = #{corporationCode}
        and bom_status = #{bomStatus}
        and sku_type = #{skuType}
        and effect_start_time <![CDATA[ <= ]]> #{effectiveTime}
        and effect_end_time <![CDATA[ > ]]> #{effectiveTime}
        order by version desc
        limit 1
    </select>

    <select id="selectLastEffectiveSkuBomBySku" resultType="com.ddmc.ims.dal.model.ims.MdBom">
        select
        <include refid="Base_Column_List"/>
        from md_bom
        where sku_id = #{skuId}
        and bom_status = #{bomStatus}
        and sku_type = #{skuType}
        and effect_start_time &lt;= now()
        and effect_end_time > now()
        order by version desc
        limit 1
    </select>

    <select id="selectLastEffectiveSkuBomBySkuIds" resultType="com.ddmc.ims.dal.model.ims.MdBom">
        select
        sku_id, bom_id, max(version) version
        from md_bom
        where sku_id in
        <foreach collection="skuIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and bom_status = #{bomStatus}
        and corporation_code = #{corporationCode}
        and sku_type = #{skuType}
        and effect_start_time &lt;= now()
        and effect_end_time > now()
        group by sku_id, bom_id
    </select>

    <update id="updateBomStatusByIdIn">
        update md_bom
        set bom_status=#{updatedBomStatus}
        where id in
        <foreach item="item" index="index" collection="idCollection"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
