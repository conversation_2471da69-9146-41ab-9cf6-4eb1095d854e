<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.CredentialDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.CredentialDetail">
        <id column="id" property="id" />
        <result column="credential_header_id" property="credentialHeaderId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="from_logic_location_code" property="fromLogicLocationCode" />
        <result column="from_cargo_owner_id" property="fromCargoOwnerId" />
        <result column="from_warehouse_id" property="fromWarehouseId" />
        <result column="to_logic_location_code" property="toLogicLocationCode" />
        <result column="to_cargo_owner_id" property="toCargoOwnerId" />
        <result column="to_warehouse_id" property="toWarehouseId" />
        <result column="sku_id" property="skuId" />
        <result column="lot_id" property="lotId" />
        <result column="qty" property="qty" />
        <result column="demand" property="demand" />
        <result column="inventory_status" property="inventoryStatus"/>
        <result column="sku_type" property="skuType"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="to_inventory_status" property="toInventoryStatus" />
        <result column="usage_code" property="usageCode" />
        <result column="order_tag" property="orderTag"/>
        <result column="to_usage_code" property="toUsageCode"/>
        <result column="command_type" property="commandType"/>
        <result column="today_sale" property="todaySale"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, credential_header_id, warehouse_id, from_logic_location_code, from_cargo_owner_id, from_warehouse_id, to_logic_location_code, to_cargo_owner_id, to_warehouse_id, sku_id, lot_id, qty, demand,inventory_status,sku_type, create_time, update_time,to_inventory_status,usage_code,order_tag,to_usage_code,command_type,today_sale
    </sql>


    <select id="selectByCredentialHeaderId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from credential_detail
        where credential_header_id = #{credentialHeaderId}
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into credential_detail (
        credential_header_id, warehouse_id, from_logic_location_code, from_cargo_owner_id, from_warehouse_id, to_logic_location_code, to_cargo_owner_id, to_warehouse_id, sku_id, lot_id, qty, demand,inventory_status,sku_type,to_inventory_status,usage_code,order_tag,to_usage_code,command_type,today_sale
        ) values
        <foreach collection="items" item="item" separator=",">
            (
            #{item.credentialHeaderId},
            #{item.warehouseId},
            #{item.fromLogicLocationCode},
            #{item.fromCargoOwnerId},
            #{item.fromWarehouseId},
            #{item.toLogicLocationCode},
            #{item.toCargoOwnerId},
            #{item.toWarehouseId},
            #{item.skuId},
            #{item.lotId},
            #{item.qty},
            #{item.demand},
            #{item.inventoryStatus},
            #{item.skuType},
            #{item.toInventoryStatus},
            #{item.usageCode},
            #{item.orderTag},
            #{item.toUsageCode},
            #{item.commandType},
            #{item.todaySale}
            )
        </foreach>
    </insert>

    <delete id="deleteByCredentialHeaderId">
        delete from credential_detail where credential_header_id = #{credentialHeaderId}
    </delete>

    <delete id="deleteByHeaderIds">
        delete from credential_detail where credential_header_id in
        <foreach collection="credentialHeaderIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <select id="selectByCredentialHeaderIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from credential_detail
        where credential_header_id in
        <foreach collection="headIds" item="headId" separator="," open="(" close=")">
            #{headId}
        </foreach>
    </select>

    <select id="getByMinId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from credential_detail where id > #{minId} order by id asc limit 1000
    </select>

</mapper>
