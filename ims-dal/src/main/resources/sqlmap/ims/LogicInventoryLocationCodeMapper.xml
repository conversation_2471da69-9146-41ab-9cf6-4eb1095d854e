<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ddmc.ims.dal.mapper.ims.LogicInventoryLocationCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ddmc.ims.dal.model.ims.LogicInventoryLocationCode">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, name, create_time, create_user_id
    </sql>

    <select id="listAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from logic_inventory_location_code
    </select>

    <select id="listLogicInventoryCodeByCodes" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from logic_inventory_location_code
        where code in
        <foreach collection="codes" item="code" separator="," open="(" close=")">
            #{code}
        </foreach>
    </select>

</mapper>
