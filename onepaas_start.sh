#!/bin/sh

#显示一些基本信息和目录结构
echo "[onepaas]start begin(app=${APPRUNTIME_APPNAME})......"

#读取环境变量文件部分
curr_envfile=$(cd "$(dirname "$0")";pwd)/onepaas-env-info.txt
if [  -f "$curr_envfile" ]; then
 echo "[onepaas vmdeploy] [$(date +'%Y-%m-%d %H:%M:%S.%3N')] 环境变量文件 ${curr_envfile} 内容如下"
 cat $curr_envfile
 set -a
 . $curr_envfile
 set +a
 echo "[onepaas vmdeploy] [$(date +'%Y-%m-%d %H:%M:%S.%3N')] 变量文件装载结束 "
 env
 echo "[onepaas vmdeploy] [$(date +'%Y-%m-%d %H:%M:%S.%3N')] 以上是变量文件装载后当前进程持有的全部环境变量  "
fi

echo "[onepaas]${APPRUNTIME_APPNAME} soacluster=${APPRUNTIME_ENV_CLUSTER}"
echo "[onepaas]${APPRUNTIME_APPNAME} /data directory"
tree -L 2 /data
echo "[onepaas]${APPRUNTIME_APPNAME} cat /data/${APPRUNTIME_APPNAME}/appspec.yaml"
cat /data/${APPRUNTIME_APPNAME}/appspec.yaml
echo "[onepaas]${APPRUNTIME_APPNAME} APPRUNTIME environment variable"
env| grep APPRUNTIME

#给cpu数量一个默认值
[ -z $APPRUNTIME_CPU_CORE ] && APPRUNTIME_CPU_CORE=2

# 测试流量回放组件
if [ "x${with_repeater}" == "xtrue" ];then
  repeater_agent="/usr/src/agent/repeater-agent.jar"
  mkdir -p "/usr/src/agent/"
  curl ${repeater_url}:4567/repeater-agent.jar -o ${repeater_agent}

  if [ -f "${repeater_agent}" ];then
   java -jar ${repeater_agent} > /dev/null &
  fi
fi

#设置在容器环境中jvm xmx,xmn,mete占系统内存的百分比,后面脚本根据此比例计算xmx值
JVM_XMX_MEM_RATIO=65
JVM_XMN_MEM_RATIO=20
JVM_DIRECT_MEM_RATIO=10
JVM_META_MEM_RATIO=10
#JVM_META_MEM_LIMT=384
[ -z $JVM_META_MEM_LIMT ] && JVM_META_MEM_LIMT=512

jar_file=`ls /data/${APPRUNTIME_APPNAME}/*.jar`
jar_name=`basename $jar_file`
env=$APPRUNTIME_ENV_CLUSTER;
dump_dir=/data/share/${APPRUNTIME_APPNAME}_dump.log

echo "[onepaas]${APPRUNTIME_APPNAME} jar_file=${jar_file}"
echo "[onepaas]${APPRUNTIME_APPNAME} jar_name=${jar_name}"

calc() {
  local formula="$1"
  shift
  echo "$@" | awk '
    function ceil(x) {
      return x % 1 ? int(x) + 1 : x
    }
    function log2(x) {
      return log(x)/log(2)
    }
    function max2(x, y) {
      return x > y ? x : y
    }
    function round(x) {
      return int(x + 0.5)
    }
    {print '"int(${formula})"'}
  '
}
container_core_limit() {
  local cpu_period_file="/sys/fs/cgroup/cpu/cpu.cfs_period_us"
  local cpu_quota_file="/sys/fs/cgroup/cpu/cpu.cfs_quota_us"
  if [ -r "${cpu_period_file}" ]; then
    local cpu_period="$(cat ${cpu_period_file})"
    if [ -r "${cpu_quota_file}" ]; then
      local cpu_quota="$(cat ${cpu_quota_file})"
      if [ ${cpu_quota:-0} -ne -1 ]; then
        echo $(calc 'ceil($1/$2)' "${cpu_quota}" "${cpu_period}")
      fi
    fi
  fi
}
container_max_memory() {
  local mem_file="/sys/fs/cgroup/memory/memory.limit_in_bytes"
  if [ -r "${mem_file}" ]; then
    local max_mem_cgroup="$(cat ${mem_file})"
    local max_mem_meminfo_kb="$(cat /proc/meminfo | awk '/MemTotal/ {print $2}')"
    local max_mem_meminfo="$(expr $max_mem_meminfo_kb \* 1024)"
    if [ ${max_mem_cgroup:-0} != -1 ]
    then
      echo "${max_mem_cgroup}"
    fi
  fi
}
init_java_major_version() {
    if [ -z "${JAVA_MAJOR_VERSION:-}" ]; then
        local full_version=""

        if [ -n "${JAVA_VERSION:-}" ]; then
            full_version="$JAVA_VERSION"
        elif [ -n "${JAVA_HOME:-}" ] && [ -r "${JAVA_HOME}/release" ]; then
            full_version="$(grep -e '^JAVA_VERSION=' ${JAVA_HOME}/release | sed -e 's/.*\"\([0-9.]\{1,\}\).*/\1/')"
        else
            full_version=$(java -version 2>&1 | head -1 | sed -e 's/.*\"\([0-9.]\{1,\}\).*/\1/')
        fi
        export JAVA_MAJOR_VERSION=$(echo $full_version | sed -e 's/[^0-9]*\(1\.\)\{0,1\}\([0-9]\{1,\}\).*/\2/')
    fi
}
init_container_vars() {
  local core_limit="$(container_core_limit)"
  if [ -n "${core_limit}" ]; then
    export CONTAINER_CORE_LIMIT="${core_limit}"
  fi

  local mem_limit="$(container_max_memory)"
  if [ -n "${mem_limit}" ]; then
    export CONTAINER_MAX_MEMORY="${mem_limit}"
  fi
}
ci_compiler_count() {
  local core_limit="$1"
  local log_cpu=$(calc 'log2($1)' "$core_limit")
  local loglog_cpu=$(calc 'log2(max2($1,1))' "$log_cpu")
  local count=$(calc 'max2($1*$2,1)*3/2' "$log_cpu" "$loglog_cpu")
  local c1_count=$(calc 'max2($1/3,1)' "$count")
  local c2_count=$(calc 'max2($1-$2,1)' "$count" "$c1_count")
  [ $(c2_disabled) = true ] && echo "$c1_count" || echo $(calc '$1+$2' "$c1_count" "$c2_count")
}
c2_disabled() {
  if [ -n "${CONTAINER_MAX_MEMORY:-}" ]; then
    # 300m
    if [ "${CONTAINER_MAX_MEMORY}" -le 314572800 ]; then
      echo true
      return
    fi
  fi
  echo false
}
jvm_calc_mem() {
  local max_mem="$1"
  local fraction="$2"

  local val=$(calc 'round($1*$2/100/1048576)' "${max_mem}" "${fraction}")
  echo "${val}"
}
jvm_cpu_options() {
  if [ "${JAVA_MAJOR_VERSION:-0}" -ge "10" ]; then
    return
  fi

  if [ -n "${CONTAINER_CORE_LIMIT:-}" ]; then
    if [ -z ${core_limit} ]; then
      core_limit="${CONTAINER_CORE_LIMIT}"
    fi
    echo "-XX:ParallelGCThreads=${APPRUNTIME_CPU_CORE} " \
         "-XX:ConcGCThreads=$(calc 'ceil($1/$2)' "${APPRUNTIME_CPU_CORE}" "4") " \
         "-Djava.util.concurrent.ForkJoinPool.common.parallelism=${APPRUNTIME_CPU_CORE} " \
         "-XX:CICompilerCount=$(ci_compiler_count $APPRUNTIME_CPU_CORE)"
  fi
}


#给一组默认值
[ -z $mx_mem ] && mx_mem=1024
[ -z $ms_mem ] && ms_mem=1024
[ -z $mn_mem ] && mn_mem=256
[ -z $max_direct_mem ] && max_direct_mem=512
[ -z $max_meta_mem ] && max_meta_mem=256

#识别容器特定环境变量识别是否在容器环境或者是在vm环境
if [ -n "${APPRUNTIME_CONTAINER_POD_IP}" ]; then
    init_container_vars
    init_java_major_version
    echo "[onepaas]${APPRUNTIME_APPNAME} container system info javaversion=${JAVA_MAJOR_VERSION},memory=${CONTAINER_MAX_MEMORY},cpu=${CONTAINER_CORE_LIMIT}"
    cpu_opt=$(jvm_cpu_options)
    JVM=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_XMX_MEM_RATIO}")
    mx_mem=$JVM
    ms_mem=$JVM
    mn_mem=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_XMN_MEM_RATIO}")
    max_direct_mem=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_DIRECT_MEM_RATIO}")
    max_meta_mem=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_META_MEM_RATIO}")
    if [ "${max_meta_mem}" -ge ${JVM_META_MEM_LIMT} ]; then
      max_meta_mem=${JVM_META_MEM_LIMT}
    fi
    if [ ${JVM_META_MEM_LIMT} -ne 512]; then
      max_meta_mem=${JVM_META_MEM_LIMT}
    fi
else
    init_container_vars
    init_java_major_version
    vm_max_mem_meminfo_kb="$(cat /proc/meminfo | awk '/MemTotal/ {print $2}')"
    vm_max_mem_meminfo="$(expr $vm_max_mem_meminfo_kb \* 1024)"
    CONTAINER_MAX_MEMORY=$vm_max_mem_meminfo
    echo "[onepaas]${APPRUNTIME_APPNAME} vm system info javaversion=${JAVA_MAJOR_VERSION},memory=${CONTAINER_MAX_MEMORY}"
    JVM=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_XMX_MEM_RATIO}")
    mx_mem=$JVM
    ms_mem=$JVM
    mn_mem=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_XMN_MEM_RATIO}")
    max_direct_mem=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_DIRECT_MEM_RATIO}")
    max_meta_mem=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_META_MEM_RATIO}")
    if [ "${max_meta_mem}" -ge ${JVM_META_MEM_LIMT} ]; then
      max_meta_mem=${JVM_META_MEM_LIMT}
    fi
    if [ ${JVM_META_MEM_LIMT} -ne 512]; then
      max_meta_mem=${JVM_META_MEM_LIMT}
    fi
fi
# 默认jdk11
JDK_PATH=" /usr/java/jdk-11.0.14/bin/java "
if [ "${APPBUILD_LANGUAGE_VERSION}" == "1.8" ]; then
    JDK_PATH="java"
fi
jvm_gc_args() {
  local jvm_max_mem=$(jvm_calc_mem "${CONTAINER_MAX_MEMORY}" "${JVM_XMX_MEM_RATIO}")
  local gc_allocate_args="-Xms${ms_mem}M -Xmx${mx_mem}M -Xss1M -XX:MaxDirectMemorySize=${max_direct_mem}M -XX:MetaspaceSize=${max_meta_mem}M -XX:MaxMetaspaceSize=${max_meta_mem}M"
#  local gc_log_args=" -Xlog:gc*=info:file=/dev/shm/gc-%t.log:time,tid,tags:filecount=2,filesize=10m "
  gc_log_args=" -Xlog:gc*:file=/data/share/gc-%t.log:time,tid,tags:filecount=20,filesize=50m "
  if [ "${APPBUILD_LANGUAGE_VERSION}" == "1.8" ]; then
      gc_log_args=" -XX:+PrintGC -XX:+PrintGCDateStamps -XX:+PrintReferenceGC -XX:+PrintAdaptiveSizePolicy -XX:NumberOfGCLogFiles=20 -XX:GCLogFileSize=50M -Xloggc:/data/share/gc-%t.log"
  fi
  if [ "${USE_ZGC}" == "true" ]; then
    # ZGC
    echo "${gc_allocate_args} \
    ${gc_log_args} \
    -XX:+UseZGC \
    -XX:ZCollectionInterval=120 \
    -XX:ZAllocationSpikeTolerance=5 \
    -XX:+UnlockDiagnosticVMOptions \
    -XX:-ZProactive"
  elif [ "${USE_CMS}" == "true" ]; then
    #    CMS
    echo "${gc_allocate_args} \
    ${gc_log_args} \
    -Xmn${mn_mem}M \
    -XX:+UseConcMarkSweepGC \
    -XX:+CMSParallelInitialMarkEnabled \
    -XX:+CMSParallelRemarkEnabled \
    -XX:+UseCMSInitiatingOccupancyOnly \
    -XX:CMSInitiatingOccupancyFraction=70"
  else
    #  G1
    # G1默认值
    [ -z "$g1_max_gc_pause_millis" ] && g1_max_gc_pause_millis=100
    [ -z "$g1_new_size_percent" ] && g1_new_size_percent=35
    [ -z "$g1_max_new_size_percent" ] && g1_max_new_size_percent=60
    echo "${gc_allocate_args} \
    ${gc_log_args} \
    -XX:+UseG1GC \
    -XX:MaxGCPauseMillis=${g1_max_gc_pause_millis} \
    -XX:+ParallelRefProcEnabled \
    -XX:G1NewSizePercent=${g1_new_size_percent} \
    -XX:G1MaxNewSizePercent=${g1_max_new_size_percent}"
  fi
}

#组合jvm参数
java_opts=" -server \
${cpu_opt} \
-XX:+UnlockExperimentalVMOptions \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=${dump_dir} \
-XX:ErrorFile=/data/share/hs_err_pid%p.log
-Duser.timezone=GMT+8 \
-Djava.security.egd=/dev/urandom \
-Dfile.encoding=UTF-8 \
-Djava.net.preferIPv4Stack=true \
-Djava.net.preferIPv4Addresses \
$(jvm_gc_args) \
${opt}"

#启动进程的一些参数项展示
echo "[onepaas]${APPRUNTIME_APPNAME} java_opts=${java_opts}"
echo "[onepaas]${APPRUNTIME_APPNAME} onepass_env_opts=${ONEPAAS_ENV_OPTS}"
echo "[onepaas]${APPRUNTIME_APPNAME} start runing......"
echo "[onepaas]${APPRUNTIME_APPNAME} run command: ${JDK_PATH} -Dappname=${APPRUNTIME_APPNAME} ${java_opts} ${ONEPAAS_ENV_OPTS} -jar ${jar_file} --spring.profiles.active=${env}"

#添加jacoco，统计覆盖率
if [ "x${with_jacoco}" == "xtrue" ] ;then
  jacoco_agent="/usr/src/jacocoagent.jar"
  [ -z $jacoco_port ] && jacoco_port=20001
  curl https://t.bsapi.dingdongxiaoqu.com/quality-center/download/jacocoagent.jar -o ${jacoco_agent}
  if [ -f "$jacoco_agent" ] ;then
      echo "download jacoco agent success then will adding agent to service"
      jacoco_opts="-javaagent:${jacoco_agent}=includes=*,output=tcpserver,append=false,address=*,port=${jacoco_port}"
      java_opts="${java_opts} ${jacoco_opts}"
  fi
fi

#java地址仅供参考，容器目录变更自行修改
exec ${JDK_PATH} -Dappname=${APPRUNTIME_APPNAME} ${java_opts} ${ONEPAAS_ENV_OPTS} -jar ${jar_file} --spring.profiles.active=${env} 2>&1