apply plugin: 'java'
apply plugin: "jacoco"
apply plugin: 'org.sonarqube'

jacoco {
    toolVersion = "0.8.5"
    // reportsDir = file(jacocoReportPaths)
}

jacocoTestReport {
    dependsOn test
    reports {
        xml.required
        html.required
        csv.required
    }
}

sonarqube {
    properties {
        property 'sonar.sourceEncoding', 'UTF-8'
        // sonar中服务名
        property 'sonar.projectName', 'scm-ims-service'
        // sonar中服务对应的key，建议和服务名一致
        property 'sonar.projectKey', 'scm-ims-service'
        // sonar后端服务地址
        property 'sonar.host.url', 'https://sonar.corp.100.me'
        // 在sonar后端生成替换
        property 'sonar.login', '****************************************'
    }
}