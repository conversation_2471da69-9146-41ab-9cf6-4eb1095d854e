package com.ddmc.ims.calculator;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import java.math.BigDecimal;

/**
 * 调拨计划已出未收量拆分用途,用于清理
 *
 * <AUTHOR>
 */
public class TransferCleanInTransitSplitUsageCalculator extends TransferCleanCalculator {


    @Override
    protected BigDecimal getQtyToDeduct(TransferIntransitInventoryBo inventory) {
        return inventory.getIntransitQty();
    }

    @Override
    protected void updateInventoryQty(TransferIntransitInventoryBo inventory, BigDecimal deductedQty) {
        inventory.setIntransitQty(inventory.getIntransitQty().subtract(deductedQty).max(BigDecimal.ZERO));
    }
}
