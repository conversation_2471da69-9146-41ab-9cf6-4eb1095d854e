package com.ddmc.ims.calculator;


import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.credential.UsageDetailBo;
import com.ddmc.ims.config.param.LocalParamConfig.TransferUsagePriority;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 调拨通用计算类,按用途拆分至计划未出量、已出未发量
 *
 * <AUTHOR>
 */
public abstract class TransferAllocationCalculator {

    /**
     * 按用途拆分数量
     *
     * @param deductionDetails deductionDetails
     * @param inventories inventories
     */
    public List<UsageDetailBo> deductSkuTransferInventory(List<TransferUsagePriority> transferUsagePriority,
                                                          List<CredentialDetail> deductionDetails,
                                                          List<TransferIntransitInventoryBo> inventories
                                                          ) {

        if (CollectionUtils.isEmpty(deductionDetails)) {
            return Collections.emptyList();
        }


        List<UsageDetailBo> result = Lists.newArrayListWithExpectedSize(inventories.size());
        //每个sku的多条凭证循环扣减
        for (CredentialDetail deductionDetail : deductionDetails) {
            List<UsageDetailBo> details = Lists.newArrayListWithExpectedSize(inventories.size());
            //待扣总量
            BigDecimal deductionQty = deductionDetail.getQty();
            //按优先级循环
            for (TransferIntransitInventoryBo inventory : inventories) {
                if (deductionQty.compareTo(BigDecimal.ZERO) <= 0) {
                    // 扣减数量用尽，退出循环
                    break;
                }
                //获取调拨上的待扣量，出库为计划待出量，入库为已出未收量
                BigDecimal qtyToDeduct = getQtyToDeduct(inventory);
                //待扣总量、待扣量
                BigDecimal deductedQty = deductionQty.min(qtyToDeduct);
                details.addAll(getUsageDetailBos(inventory, deductedQty, deductionDetail));
                //更新调拨待扣量
                updateInventoryQty(inventory, deductedQty);
                deductionQty = deductionQty.subtract(deductedQty);
            }
            //扫尾处理
            cleanUpDeductionQty(transferUsagePriority, deductionDetail, details, deductionQty, inventories);
            result.addAll(details);
        }

        return result;
    }

    /**
     * 对剩余的待扣量进行扫尾处理
     *
     * @param deductionDetail deductionDetail
     * @param details details
     * @param deductionQty deductionQty
     */
    protected void cleanUpDeductionQty(List<TransferUsagePriority> transferUsagePriority,
                                       CredentialDetail deductionDetail, List<UsageDetailBo> details,
                                       BigDecimal deductionQty, List<TransferIntransitInventoryBo> inventories) {

    }


    /**
     * 获取调拨在途上的待扣量，出库为计划待出量，入库为已出未收量
     *
     * @param inventory inventory
     * @return result
     */
    protected abstract BigDecimal getQtyToDeduct(TransferIntransitInventoryBo inventory);

    /**
     * 更新调拨在途的待扣量，出库为计划待出量，入库为已出未收量
     *
     * @param inventory inventory
     * @param deductedQty deductedQty
     */
    protected abstract void updateInventoryQty(TransferIntransitInventoryBo inventory, BigDecimal deductedQty);


    /**
     * 构建用途明细凭证
     *
     * @param inventory inventory
     * @param deductedQty deductedQty
     * @param deductionDetail deductionDetail
     * @return result
     */
    protected abstract List<UsageDetailBo> getUsageDetailBos(TransferIntransitInventoryBo inventory,
                                                             BigDecimal deductedQty, CredentialDetail deductionDetail);

    /**
     * 获取实际用途，出库为目标用途，入库为来源用途
     *
     * @param usageDetailBo usageDetailBo
     * @return result
     */
    protected abstract String getUsageCode(UsageDetailBo usageDetailBo);



}

