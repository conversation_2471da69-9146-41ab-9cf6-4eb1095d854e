package com.ddmc.ims.calculator;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 调拨通用计算类,按用途拆分清理计划未出量或已出未发量
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class TransferCleanCalculator {


    /**
     * 调拨在途的变化量
     *
     * @param transits transits
     * @param deductionQty deductionQty
     */
    public Map<String, BigDecimal> getTransitChangeQty(List<TransferIntransitInventoryBo> transits,
        BigDecimal deductionQty) {

        //调拨在途id,变更量Map,后续作为结果转换为UsageDetail
        Map<String, BigDecimal> changeQty = Maps.newHashMapWithExpectedSize(transits.size());

        if (deductionQty.compareTo(BigDecimal.ZERO) <= 0) {
            // 待清除量为0,直接返回
            return changeQty;
        }

        //优先级逆序循环将调拨待扣量扣减未售量
        for (TransferIntransitInventoryBo inventory : transits) {
            if (deductionQty.compareTo(BigDecimal.ZERO) <= 0) {
                // 扣减数量用尽，退出循环
                break;
            }
            //调拨在途待扣量
            BigDecimal qtyToDeduct = getQtyToDeduct(inventory);
            //待扣总量、调拨在途待扣量
            BigDecimal deductedQty = deductionQty.min(qtyToDeduct);
            updateInventoryQty(inventory, deductedQty);
            deductionQty = deductionQty.subtract(deductedQty);
            changeQty.merge(inventory.getUuid(), deductedQty, BigDecimal::add);
        }

        return changeQty;
    }



    /**
     * 获取调拨在途上的待扣量，出库为计划待出量，入库为已出未收量
     *
     * @param inventory inventory
     * @return result
     */
    protected abstract BigDecimal getQtyToDeduct(TransferIntransitInventoryBo inventory);


    /**
     * 更新调拨在途的待扣量，出库为计划待出量，入库为已出未收量
     *
     * @param inventory inventory
     * @param deductedQty deductedQty
     */
    protected abstract void updateInventoryQty(TransferIntransitInventoryBo inventory, BigDecimal deductedQty);


}
