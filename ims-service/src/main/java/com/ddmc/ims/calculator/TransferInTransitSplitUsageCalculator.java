package com.ddmc.ims.calculator;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.credential.UsageDetailBo;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.config.param.LocalParamConfig.TransferUsagePriority;
import com.ddmc.ims.converter.usage.UsageDetailBoConverter;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 调拨计划已出未收量拆分用途
 *
 * <AUTHOR>
 */
@Slf4j
public class TransferInTransitSplitUsageCalculator extends TransferAllocationCalculator {

    private final LocalParamConfig localParamConfig;

    public TransferInTransitSplitUsageCalculator(LocalParamConfig localParamConfig) {
        this.localParamConfig = localParamConfig;
    }

    @Override
    protected BigDecimal getQtyToDeduct(TransferIntransitInventoryBo inventory) {
        return inventory.getIntransitQty();
    }

    @Override
    protected void updateInventoryQty(TransferIntransitInventoryBo inventory, BigDecimal deductedQty) {
        inventory.setIntransitQty(inventory.getIntransitQty().subtract(deductedQty).max(BigDecimal.ZERO));
    }

    @Override
    protected List<UsageDetailBo> getUsageDetailBos(TransferIntransitInventoryBo inventory,
        BigDecimal deductedQty, CredentialDetail deductionDetail) {

        List<UsageDetailBo> usageDetailBos = Lists.newArrayListWithCapacity(2);
        String warehouseSkuInventoryUsageCode = getWarehouseSkuInventoryUsageCode(deductionDetail, inventory);

        usageDetailBos.add(UsageDetailBoConverter.getUsageDetailBo(deductionDetail, inventory, deductedQty,
            inventory.getUsageCode(), inventory.getToUsageCode(), CommandTypeEnum.TRANSFER_IN_TRANSIT_IN));
        //调拨入实际入库的渠道使用
        usageDetailBos.add(UsageDetailBoConverter.getModifyInventoryAvailableCredentialUsageDetail(deductionDetail,  warehouseSkuInventoryUsageCode,
            deductedQty, CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));
        return usageDetailBos;

    }

    @Override
    protected void cleanUpDeductionQty(List<TransferUsagePriority> transferUsagePriority,
        CredentialDetail deductionDetail, List<UsageDetailBo> details, BigDecimal deductionQty, List<TransferIntransitInventoryBo> inventories) {
        String modifyUsageCode = getWarehouseSkuInventoryUsageCode(deductionDetail, null);
        //找到修改在库的
        List<UsageDetailBo> sortedEcBos = details.stream()
            .filter(t -> Objects.equals(modifyUsageCode, t.getUsageCode())
                && CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode().equals(t.getCommandType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sortedEcBos)) {
            UsageDetailBo usageDetailBo = sortedEcBos.get(0);
            usageDetailBo.setQty(usageDetailBo.getQty().add(deductionQty));
            return;
        }
        UsageDetailBo usageDetailBo = UsageDetailBoConverter.getModifyInventoryAvailableCredentialUsageDetail(deductionDetail, modifyUsageCode
            , deductionQty, CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE);
        details.add(usageDetailBo);
    }

    //EC,TB->EC,TB
    private String getWarehouseSkuInventoryUsageCode(CredentialDetail deductionDetail,
                                  TransferIntransitInventoryBo inventory) {
        List<String> toUsages = deductionDetail.getToUsageList();
        if (toUsages.size() == 1) {
            return deductionDetail.getToUsageCode();
        }
        //传入多个用途时
        if (Objects.nonNull(inventory)) {
            if (toUsages.contains(inventory.getToUsageCode())) {
                return inventory.getToUsageCode();
            }
        }

        List<String> defaultUsageForInbound = localParamConfig.getDefaultUsageForInbound(deductionDetail.getToLogicLocationCode());
        if (CollectionUtils.isEmpty(defaultUsageForInbound)) {
            log.error("[getWarehouseSkuInventoryUsageCode]调拨入库存在多个用途，没有在途数据也没有默认用途->{}", JsonUtil.toJson(deductionDetail));
            throw new ImsBusinessException("[getWarehouseSkuInventoryUsageCode]调拨入库存在多个用途，没有在途数据也没有默认用途");
        }
        List<String> existsUsage = defaultUsageForInbound.stream().filter(toUsages::contains).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existsUsage)) {
            return existsUsage.get(existsUsage.size() - 1);
        }
        return defaultUsageForInbound.get(defaultUsageForInbound.size() - 1);
    }



    @Override
    protected String getUsageCode(UsageDetailBo usageDetailBo) {
        return usageDetailBo.getUsageCode();
    }
}
