package com.ddmc.ims.calculator;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.credential.UsageDetailBo;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.config.param.LocalParamConfig.TransferUsagePriority;
import com.ddmc.ims.converter.usage.UsageDetailBoConverter;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.google.common.collect.Lists;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 调拨计划未发量拆分用途
 *
 * <AUTHOR>
 */
@Slf4j
public class TransferWaitAllocSplitUsageCalculator extends TransferAllocationCalculator {


    private final LocalParamConfig localParamConfig;


    private final Map<String, Integer> transferSceneResponseMap;

    public TransferWaitAllocSplitUsageCalculator(LocalParamConfig localParamConfig,
        Map<String, Integer> transferSceneResponseMap) {
        this.localParamConfig = localParamConfig;
        this.transferSceneResponseMap = transferSceneResponseMap;
    }

    @Override
    protected BigDecimal getQtyToDeduct(TransferIntransitInventoryBo inventory) {
        if (Objects.isNull(inventory)) {
            return BigDecimal.ZERO;
        }
        return inventory.getWaitAllocQty();
    }

    @Override
    protected void updateInventoryQty(TransferIntransitInventoryBo inventory, BigDecimal deductedQty) {
        inventory.setWaitAllocQty(inventory.getWaitAllocQty().subtract(deductedQty).max(BigDecimal.ZERO));
    }

    @Override
    protected void cleanUpDeductionQty(List<TransferUsagePriority> transferUsagePriority,
        CredentialDetail deductionDetail, List<UsageDetailBo> details, BigDecimal deductionQty,
                                       List<TransferIntransitInventoryBo> inventories) {
        ImmutablePair<String, String> usageCodePair = getOutboundUsageCode(deductionDetail);
        String transferFromUsageCode = usageCodePair.getRight();
        String transferToUsageCode = usageCodePair.getRight();
        if (CollectionUtils.isNotEmpty(inventories)) {
            if (!transferSceneResponseMap.containsKey(deductionDetail.getUsageCode())
                && deductionDetail.getUsageList().size() == 1) {
                TransferIntransitInventoryBo lastPriority = inventories.get(inventories.size() - 1);
                transferToUsageCode = lastPriority.getToUsageCode();
                transferFromUsageCode = lastPriority.getUsageCode();
            }
        }

        String finalTransferToUsageCode = transferToUsageCode;
        String finalTransferFromUsageCode = transferFromUsageCode;
        List<UsageDetailBo> detailBos = details.stream()
            .filter(t -> Objects.equals(finalTransferToUsageCode, t.getToUsageCode()) && Objects
                .equals(finalTransferFromUsageCode, t.getUsageCode()) && Objects
                .equals(t.getCommandType(), CommandTypeEnum.TRANSFER_IN_TRANSIT_OUT.getCode()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(detailBos)) {
            UsageDetailBo usageDetailBo = detailBos.get(0);
            usageDetailBo.setQty(usageDetailBo.getQty().add(deductionQty));
        } else {
            log.info("[cleanUpDeductionQty] 在途存在但是命令中没有匹配的usageDetails");
            UsageDetailBo usageDetailBoOut = UsageDetailBoConverter
                .getDefaultOutStoreCredentialUsageDetail(deductionDetail, deductionQty, usageCodePair.getRight()
                    , usageCodePair.getRight(), CommandTypeEnum.TRANSFER_IN_TRANSIT_OUT);
            details.add(usageDetailBoOut);
        }

        String modifyOutUsageCode = usageCodePair.getLeft();
        List<UsageDetailBo> outDetailBos = details.stream()
            .filter(t -> Objects.equals(modifyOutUsageCode, t.getUsageCode()) && Objects
                .equals(t.getCommandType(), CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outDetailBos)) {
            UsageDetailBo usageDetailBo = outDetailBos.get(0);
            usageDetailBo.setQty(usageDetailBo.getQty().add(deductionQty));
        } else {
            UsageDetailBo usageDetailBoOut = UsageDetailBoConverter
                .getModifyOutInventoryAvailableCredentialUsageDetail(deductionDetail, modifyOutUsageCode, deductionQty,
                    CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE);
            details.add(usageDetailBoOut);
        }
    }


    public ImmutablePair<String,String> getOutboundUsageCode(CredentialDetail deductionDetail) {
        List<String> outUsages = deductionDetail.getUsageList();
        if (outUsages.size() == 1) {
            if (transferSceneResponseMap.containsKey(deductionDetail.getUsageCode())) {
                List<TransferUsagePriority> transferUsagePriorityList = localParamConfig
                    .getTransferOutUsagePriority(deductionDetail.getToUsageCode());
                TransferUsagePriority priority = transferUsagePriorityList.get(transferUsagePriorityList.size() - 1);
                return new ImmutablePair<>(priority.getFromUsageCode(), priority.getToUsageCode());
            } else {
                return new ImmutablePair<>(deductionDetail.getUsageCode(), deductionDetail.getToUsageCode());
            }
        }

        List<String> defaultUsageForOutbound = localParamConfig.getDefaultUsageForOutbound(deductionDetail.getToLogicLocationCode());
        if (CollectionUtils.isEmpty(defaultUsageForOutbound)) {
            log.error("[getWarehouseSkuInventoryUsageCode]调拨入库存在多个用途，没有在途数据也没有默认用途->{}", JsonUtil.toJson(deductionDetail));
            throw new ImsBusinessException("[getWarehouseSkuInventoryUsageCode]调拨入库存在多个用途，没有在途数据也没有默认用途");
        }
        List<String> existsUsage = defaultUsageForOutbound.stream().filter(outUsages::contains).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existsUsage)) {
            String usageCode = existsUsage.get(existsUsage.size() - 1);
            return new ImmutablePair<>(usageCode, usageCode);
        }
        String usageCode = defaultUsageForOutbound.get(defaultUsageForOutbound.size() - 1);
        return new ImmutablePair<>(usageCode, usageCode);
    }


    @Override
    protected List<UsageDetailBo> getUsageDetailBos(TransferIntransitInventoryBo inventory,
        BigDecimal deductedQty, CredentialDetail deductionDetail) {

        List<UsageDetailBo> usageDetailBos = Lists.newArrayListWithCapacity(2);
        usageDetailBos.add(UsageDetailBoConverter.getUsageDetailBo(deductionDetail, inventory, deductedQty,
            inventory.getUsageCode(), inventory.getToUsageCode(), CommandTypeEnum.TRANSFER_IN_TRANSIT_OUT));

        if (deductionDetail.getFromLogicLocationCode().equals(deductionDetail.getToLogicLocationCode())) {
            usageDetailBos.add(UsageDetailBoConverter
                .getModifyOutInventoryAvailableCredentialUsageDetail(deductionDetail, inventory.getUsageCode(),
                    deductedQty, CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));
        } else {
            usageDetailBos.add(UsageDetailBoConverter
                .getModifyOutInventoryAvailableCredentialUsageDetail(deductionDetail, deductionDetail.getUsageCode(),
                    deductedQty, CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));
        }

        return usageDetailBos;

    }

    @Override
    protected String getUsageCode(UsageDetailBo usageDetailBo) {
        return usageDetailBo.getToUsageCode();
    }


}


