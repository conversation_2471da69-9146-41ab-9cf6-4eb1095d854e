package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.CredentialHeaderExtEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.RealChangeRowNumberChecker;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.dal.mapper.ims.CredentialDetailMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderExtMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialUseageDetailMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialHeaderExt;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xxl.job.core.util.DateUtil;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CredentialWrapperManager {


    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;
    @Resource
    private CredentialDetailMapper credentialDetailMapper;
    @Resource
    private CredentialUseageDetailMapper credentialUsageDetailMapper;

    @Resource
    private AlertService alertService;

    @Resource
    private LocalParamConfig localParamConfig;
    @Resource
    private CredentialHeaderExtMapper credentialHeaderExtMapper;

    @Value("${ims.business.delay.alert.minutes:30}")
    private int businessDelayAlertMinutes;

    protected static final Set<Integer> TRANSFER_CHANGE_OPERATE_TYPE_LIST = Sets.newHashSet(
        OrderOperateTypeEnum.OUT_OF_STOCK.getCode(),
        OrderOperateTypeEnum.FINISH_OUT_OF_STOCK.getCode(), OrderOperateTypeEnum.IN_STOCK.getCode(),
        OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode(),
        OrderOperateTypeEnum.TRANSFER_REJECT.getCode());

    /**
     * 根据幂等id取消凭证
     *
     * @param idempotentId 幂等id
     */
    public void cancel(String idempotentId) {
        CredentialHeader credentialHeader = credentialHeaderMapper.selectByIdempotentId(idempotentId);
        if (Objects.isNull(credentialHeader)) {
            return;
        }
        int count = credentialHeaderMapper.deleteById(credentialHeader.getId());
        RealChangeRowNumberChecker.checkAndException(1, count, "取消凭证时，删除数据异常");
        credentialDetailMapper.deleteByCredentialHeaderId(credentialHeader.getId());
    }

    /**
     * 根据幂等id获取凭证及凭证详情
     *
     * @param idempotentId 幂等id
     * @return 获取凭证及凭证详情
     */
    public CredentialHeader getCredentialHeaderAndDetail(String idempotentId) {
        CredentialHeader credentialHeader = credentialHeaderMapper.selectByIdempotentId(idempotentId);
        if (Objects.isNull(credentialHeader)) {
            return null;
        }
        appendCredentialHeaderExt(credentialHeader);
        List<CredentialDetail> credentialDetails = credentialDetailMapper
            .selectByCredentialHeaderId(credentialHeader.getId());

        Map<String, String> defaultUsageMap = localParamConfig.getDefaultLocationUsageCodeMap();

        credentialDetails.forEach(t -> {
            if (StringUtils.isEmpty(t.getUsageCode())) {
                String defaultUsage = defaultUsageMap.getOrDefault(t.getFromLogicLocationCode(), Strings.EMPTY);
                t.setUsageCode(defaultUsage);
            }

            if (StringUtils.isEmpty(t.getToUsageCode())) {
                String defaultUsage = defaultUsageMap.getOrDefault(t.getToLogicLocationCode(), Strings.EMPTY);
                t.setToUsageCode(defaultUsage);
            }
        });
        credentialHeader.setCredentialDetailList(credentialDetails);
        return credentialHeader;
    }

    private void appendCredentialHeaderExt(CredentialHeader credentialHeader) {
        CredentialHeaderExt credentialHeaderExt = credentialHeaderExtMapper
            .selectByCredentialHeaderIdAndCode(credentialHeader.getId(), CredentialHeaderExtEnum.ORI_DELIVERY_DATE
                .getCode());
        if (Objects.nonNull(credentialHeaderExt)) {
            Date oriDeliveryDate = ThreadLocalDateUtils.parseYmd(credentialHeaderExt.getExtValue());
            credentialHeader.setOriDeliveryDate(oriDeliveryDate);
        }
    }

    public List<CredentialHeader> getCredentialHeaderAndDetailList(List<Long> headIds) {
        return Lists.partition(headIds, CommonConstants.BATCH_SELECT_STORE_100).stream()
            .map(this::listCredentialHeaderAndDetail).flatMap(List::stream).collect(
                Collectors.toList());
    }

    public List<CredentialHeader> listCredentialHeaderAndDetail(List<Long> headIds) {
        List<CredentialHeader> credentialHeaders = credentialHeaderMapper.selectByIds(headIds);
        return appendCredentialDetail(credentialHeaders);
    }


    public Long getMinCredentialHeader(Long warehouseId, Date startDate, Date endDate) {
        return credentialHeaderMapper
            .selectMinIdByWarehouseCompleteHeaderByBusinessTime(warehouseId, startDate, endDate);
    }

    public Long getMaxCredentialHeader(Long warehouseId, Date startDate, Date endDate) {
        return credentialHeaderMapper
            .selectMaxIdByWarehouseCompleteHeaderByBusinessTime(warehouseId, startDate, endDate);
    }


    public List<CredentialHeader> getCredentialHeaders(Long warehouseId, Date startDate, Date endDate) {
        //凭证头最小值
        Long minId = getMinCredentialHeader(warehouseId, startDate, endDate);
        //凭证头最大值
        Long maxId = getMaxCredentialHeader(warehouseId, startDate, endDate);
        log.info("[CredentialWrapperManager] getCredentialHeaders start warehouseId:{} minId:{}, maxId:{}", warehouseId,
            minId, maxId);

        minId = Objects.isNull(minId) ? 0 : minId - 1;
        List<CredentialHeader> credentialHeaderAndDetails = new ArrayList<>();
        List<CredentialHeader> subList;
        while (CollectionUtils.isNotEmpty(
            subList = getCredentialHeaderAndDetailList(warehouseId, minId, maxId, startDate, endDate))) {
            minId = subList.get(subList.size() - 1).getId();
            log.info("[CredentialWrapperManager] getCredentialHeaders warehouseId:{} current minId:{}", warehouseId,
                minId);
            subList = subList.stream().sorted(Comparator.comparing(CredentialHeader::getBusinessTime))
                .collect(Collectors.toList());
            List<CredentialHeader> filterList = subList.stream()
                .filter(t -> t.getBusinessTime().compareTo(startDate) >= 0 && t.getBusinessTime().before(endDate))
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterList)) {
                credentialHeaderAndDetails.addAll(filterList);
            }
            sleep();
        }
        log.info("[CredentialWrapperManager] getCredentialHeaders warehouseId:{} end", warehouseId);
        return credentialHeaderAndDetails;
    }

    @SneakyThrows
    private void sleep() {
        Thread.sleep(localParamConfig.getSnapshotPerHourSleepInterval());
    }


    public List<CredentialHeader> getCredentialHeaderAndDetailList(Long warehouseId, Long minId, Long maxId,
        Date startDate, Date endDate) {
        List<CredentialHeader> credentialHeaders = credentialHeaderMapper
            .selectByWarehouseIdAndIdAndBusinessBetween(warehouseId, minId, maxId, startDate, endDate);
        return appendCredentialDetail(credentialHeaders);

    }

    private List<CredentialHeader> appendCredentialDetail(List<CredentialHeader> credentialHeaders) {
        if (CollectionUtils.isEmpty(credentialHeaders)) {
            return Collections.emptyList();
        }
        List<Long> headIds = credentialHeaders.stream().map(CredentialHeader::getId).collect(Collectors.toList());
        List<CredentialDetail> credentialDetailsResult = Lists.newArrayList();
        Lists.partition(headIds, CommonConstants.BATCH_SELECT_DB_50).forEach(subList -> {
            List<CredentialDetail> credentialDetails = credentialDetailMapper.selectByCredentialHeaderIds(subList);
            if (CollectionUtils.isNotEmpty(credentialDetails)) {
                credentialDetailsResult.addAll(credentialDetails);
            }
        });
        //查询用途维度detail
        List<CredentialUseageDetail> credentialUsageDetailsResult = Lists.newArrayList();
        Lists.partition(headIds, CommonConstants.BATCH_SELECT_DB_50).forEach(subList -> {
            List<CredentialUseageDetail> credentialDetails = credentialUsageDetailMapper
                .selectByCredentialHeaderIds(subList);
            if (CollectionUtils.isNotEmpty(credentialDetails)) {
                credentialUsageDetailsResult.addAll(credentialDetails);
            }
        });

        Map<Long, List<CredentialDetail>> headDetailMap = credentialDetailsResult.stream()
            .collect(Collectors.groupingBy(CredentialDetail::getCredentialHeaderId));
        Map<Long, List<CredentialUseageDetail>> headUsageDetailMap = credentialUsageDetailsResult.stream()
            .collect(Collectors.groupingBy(CredentialUseageDetail::getCredentialHeaderId));
        credentialHeaders
            .forEach(t -> {
                List<CredentialDetail> credentialDetails = headDetailMap
                    .getOrDefault(t.getId(), Collections.emptyList());
                t.setCredentialDetailList(credentialDetails);
                List<CredentialUseageDetail> credentialUseageDetails = headUsageDetailMap.get(t.getId());
                if (CollectionUtils.isEmpty(credentialUseageDetails)) {
                    credentialUseageDetails = credentialDetails.stream()
                        .map(CredentialUsageDetailConverter::getSingleCredentialUsageDetail)
                        .collect(Collectors.toList());
                }
                t.setCredentialUseageDetailList(credentialUseageDetails);
            });
        return credentialHeaders;
    }


    /**
     * 保存入库凭证
     *
     * @param header 凭证请求
     */
    public void saveCredential(CredentialHeader header) {
        handleBusinessTime(header);
        saveCredentialHeaderAndDetail(header);
    }

    public void saveCredentialHeaderAndDetail(CredentialHeader header) {
        credentialHeaderMapper.insert(header);
        header.getCredentialDetailList().forEach(d -> d.setCredentialHeaderId(header.getId()));
        Lists.partition(header.getCredentialDetailList(), CommonConstants.BATCH_UPDATE_DB_100)
            .forEach(credentialDetailMapper::batchInsert);
        if (CollectionUtils.isNotEmpty(header.getCredentialHeaderExtList())) {
            header.getCredentialHeaderExtList().forEach(d -> d.setCredentialHeaderId(header.getId()));
            credentialHeaderExtMapper.batchInsert(header.getCredentialHeaderExtList());
        }
    }

    /**
     * 检验业务时间，如果超过10分钟则告警，超过1小时则修改业务时间为当前时间
     *
     * @param header header
     */
    private void handleBusinessTime(CredentialHeader header) {
        if (Objects.isNull(header.getBusinessTime())) {
            throw new ImsBusinessException("业务操作时间为空");
        }

        LocalDateTime businessLocalDateTime = header.getBusinessTime().toInstant().atZone(ZoneId.systemDefault())
            .toLocalDateTime();
        LocalDateTime now = CurrentDateUtil.newLocalDate();
        Duration duration = Duration.between(businessLocalDateTime, now);

        if (duration.toMinutes() >= businessDelayAlertMinutes && !header.getOrderNo().startsWith(CommonConstants.RT)) {
            log.warn("[业务操作时间滞后] header:{}", JsonUtil.toJson(header));
            String message = String.format("orderNo:%s,businessTime:%s,当前时间:%s, traceId:%s", header.getOrderNo(),
                ThreadLocalDateUtils.formatYmdhms(header.getBusinessTime()),
                ThreadLocalDateUtils.formatYmdhms(CurrentDateUtil.newDate()), MDC.get("traceId"));
            alertService.alert("业务操作时间滞后告警", message, AlertLevel.WARNING);
        }

        if (duration.toMinutes() >= getCredentialBusinessTimeDelay()) {
            log.warn("[业务操作时间滞后] 修改businessTime为当前时间 header:{},", JsonUtil.toJson(header));
            header.setBusinessTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
        }
    }


    public List<CredentialHeader> listByWarehouseIdAndOrderOperateTypeAndEndDateTime(Long warehouseId,
        OrderTypeEnum orderTypeEnum, Date endDateTime) {
        Integer aheadDate = localParamConfig.getBusinessDateAheadEndDate();
        Date minBusinessTime = DateUtil.addDays(endDateTime, -aheadDate);

        List<CredentialHeader> credentialHeaders = credentialHeaderMapper
            .listByWarehouseIdAndOrderOperateTypeAndEndDateTime(warehouseId, orderTypeEnum.getCode(), minBusinessTime,
                endDateTime);
        if (CollectionUtils.isEmpty(credentialHeaders)) {
            return Collections.emptyList();
        }
        List<Long> headerIds = credentialHeaders.stream().map(CredentialHeader::getId).collect(Collectors.toList());
        List<CredentialDetail> credentialDetails = Lists.partition(headerIds, CommonConstants.BATCH_SELECT_DB_50)
            .stream().map(t -> credentialDetailMapper.selectByCredentialHeaderIds(t)).flatMap(List::stream).collect(
                Collectors.toList());
        Map<Long, List<CredentialDetail>> detailMap = credentialDetails.stream()
            .collect(Collectors.groupingBy(CredentialDetail::getCredentialHeaderId));

        credentialHeaders.forEach(t ->
            t.setCredentialDetailList(detailMap.getOrDefault(t.getId(), Collections.emptyList())));
        return credentialHeaders;
    }

    private Integer getCredentialBusinessTimeDelay() {
        return localParamConfig.getCredentialBusinessTimeDelay();

    }

    public List<CredentialHeader> getCredentialHeaderByOrderNoAndBusinessTime(Set<String> allOrderNos, Date startDate,
        Date endDate) {
        return Lists.partition(Lists.newArrayList(allOrderNos), CommonConstants.BATCH_INSERT_DB_100)
            .stream().map(t -> {
                List<CredentialHeader> credentialHeaders = credentialHeaderMapper
                    .selectByOrderNosAndBusinessBetween(t, startDate, endDate,
                        TRANSFER_CHANGE_OPERATE_TYPE_LIST);
                return appendCredentialDetail(credentialHeaders);
            }).flatMap(List::stream).collect(Collectors.toList());
    }
}
