package com.ddmc.ims.manager;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsRemoteInvocationException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.rpc.common.CommonDictionaryClient;
import com.ddmc.ims.rpc.sso.dto.Zone;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.pms.common.client.response.dictionary.SystemDictionaryVO;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 调用pms-common
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PmsCommonRemoteManager {

    /**
     * 区域
     */
    private static final String SCM_ZONE = "SCM-Zone";

    @Resource
    private CommonDictionaryClient dictionaryClient;
    @Resource
    private AlertService alertService;

    /**
     * 从common服务获取字典信息
     *
     * @param sysDictTypeCode code
     * @return 返回数据
     */
    private List<SystemDictionaryVO> selectFromClient(String sysDictTypeCode) {
        ResponseBaseVo<List<SystemDictionaryVO>> responseBaseVo = dictionaryClient
            .getSystemDictionaryList(sysDictTypeCode);
        if (!responseBaseVo.isSuccess()) {
            log.error("【请求pms-com服务错误】获取字典信息, 请求数据：{}, 返回数据:{}", sysDictTypeCode, responseBaseVo);
            alertService.alert("【请求pms-com服务错误】获取调拨模板",
                "请求数据:" + JsonUtil.toJson(sysDictTypeCode) + "|返回数据:" + JsonUtil.toJson(responseBaseVo),
                AlertLevel.CRITICAL);
            throw new ImsRemoteInvocationException(CommonErrorCode.PMS_COMMON_SERVER_ERROR, responseBaseVo);
        }
        return responseBaseVo.getData();
    }


    public List<SystemDictionaryVO> getAllByCodeCached(String sysDictTypeCode) {
        List<SystemDictionaryVO> dictionaryVOList = CacheConfig.COMMON_DICTIONARY_CACHE.getIfPresent(sysDictTypeCode);
        if (CollectionUtils.isNotEmpty(dictionaryVOList)) {
            return dictionaryVOList;
        }
        dictionaryVOList = selectFromClient(sysDictTypeCode);
        if (CollectionUtils.isNotEmpty(dictionaryVOList)) {
            CacheConfig.COMMON_DICTIONARY_CACHE.put(sysDictTypeCode, dictionaryVOList);
        }
        return dictionaryVOList;
    }

    /**
     * 获取所有大区数据
     *
     * @return 返回数据
     */
    public List<Zone> allZoneList() {
        List<SystemDictionaryVO> dictionaryVOList = getAllByCodeCached(SCM_ZONE);
        if (CollectionUtils.isEmpty(dictionaryVOList)) {
            return Collections.emptyList();
        }
        return dictionaryVOList.stream().map(t -> {
            Zone zone = new Zone();
            zone.setZoneId(t.getValue());
            zone.setZoneName(t.getName());
            return zone;
        }).collect(Collectors.toList());
    }

    /**
     * 获取大区id与名称map
     *
     * @return 大区id对应名称map
     */
    public Map<String, String> zoneIdAndNameMap() {
        List<Zone> zoneList = allZoneList();
        return zoneList.stream().collect(Collectors.toMap(Zone::getZoneId, Zone::getZoneName, (k1, k2) -> k1));
    }

    /**
     * 获取所有区域信息
     *
     * @return 返回数据
     */
    public Map<String, Zone> listZoneMap() {
        List<Zone> zoneList = allZoneList();
        return zoneList.stream().collect(Collectors.toMap(Zone::getZoneId, p -> p));
    }


}
