package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.common.bo.TransferOrderNoAndSource;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryInDetailMapper;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryOutDetailMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TransferInTransitManager {

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private TransferIntransitInventoryOutDetailMapper transferIntransitInventoryOutDetailMapper;

    @Resource
    private TransferIntransitInventoryInDetailMapper transferIntransitInventoryInDetailMapper;

    @Resource
    private LocalParamService localParamService;


    /**
     * 插入或增加调拨在途计划未发量
     *
     * @param needInsert needInsert
     * @param needUpdate needUpdate
     */
    public void insertOrAddPlanOutWaitAllocQty(List<TransferIntransitInventory> needInsert,
        List<TransferIntransitInventory> needUpdate) {
        if (CollectionUtils.isNotEmpty(needInsert)) {
            Lists.partition(needInsert, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(transferIntransitInventoryMapper::batchInsert);
        }

        if (CollectionUtils.isNotEmpty(needUpdate)) {
            needUpdate.sort(Comparator.comparing(TransferIntransitInventory::getId));
            Lists.partition(needUpdate, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(transferIntransitInventoryMapper::batchAddPlanQtyWaitAllocQty);
        }
    }

    /**
     * 批量更新已出未收量
     *
     * @param needUpdate needUpdate
     */
    public void updateInTransitQty(List<TransferIntransitInventory> needUpdate, List<TransferIntransitInventory> needInsert) {
        if (CollectionUtils.isNotEmpty(needUpdate)) {
            needUpdate.sort(Comparator.comparing(TransferIntransitInventory::getId));
            Lists.partition(needUpdate, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(transferIntransitInventoryMapper::batchUpdateInTransitQty);
        }
        if (CollectionUtils.isNotEmpty(needInsert)) {
            Lists.partition(needInsert, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(transferIntransitInventoryMapper::batchInsert);
        }
    }



    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void deleteTransferInventory(TransferOrderNoAndSource transferOrderNoAndSource, Date endTime) {
        Date maxUpdateTime = DateUtils.addDays(CurrentDateUtil.newDate(), -2);
        List<TransferIntransitInventory> transferIntransitInventories = transferIntransitInventoryMapper.selectTransferIntransitInventory(
            transferOrderNoAndSource);
        boolean allMatch = transferIntransitInventories.stream().allMatch(
            h -> h.getIntransitQty().compareTo(BigDecimal.ZERO) == 0
                && h.getWaitAllocQty().compareTo(BigDecimal.ZERO) == 0 && h.getExpectOutTime().before(endTime)
                && h.getCreateTime().before(endTime)
                && h.getUpdateTime().before(maxUpdateTime));
        if (allMatch) {
            log.info("[deleteTransferIntransitInventoryJob] 删除的单据 {}", JsonUtil.toJson(transferOrderNoAndSource));
            List<Long> headerIds = transferIntransitInventories.stream().map(TransferIntransitInventory::getId)
                .collect(Collectors.toList());
            transferIntransitInventoryMapper.deleteBatchIds(headerIds);
            transferIntransitInventoryOutDetailMapper.deleteByRefOrderNoAndSource(transferOrderNoAndSource.getOrderNo(),
                transferOrderNoAndSource.getOrderSource());
            transferIntransitInventoryInDetailMapper.deleteByRefOrderNoAndSource(transferOrderNoAndSource.getOrderNo(),
                transferOrderNoAndSource.getOrderSource());
            delay();
        }
    }

    private void delay() {
        try {
            Thread.sleep(delayOfMillis());
        } catch (InterruptedException interruptedException) {
            log.info("[延时失败]", interruptedException);
            Thread.currentThread().interrupt();
        }
    }

    private int delayOfMillis() {
        return localParamService.getIntValue("ims.deleteTransferIntransitInventoryJob.delayOfMillis", 50);
    }


}
