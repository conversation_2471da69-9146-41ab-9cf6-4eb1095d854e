package com.ddmc.ims.manager;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsRemoteInvocationException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.rpc.imsconfig.ImsConfigInventoryUsageClient;
import com.ddmc.ims.service.common.AlertService;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

/**
 * 调用ims-config
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ImsConfigRemoteManager {

    @Resource
    private AlertService alertService;

    @Resource
    private ImsConfigInventoryUsageClient inventoryUsageClient;


    @Resource
    private LocalParamConfig localParamConfig;


    @Resource(name = "autoRefreshTransferSceneScheduledExecutor")
    private ScheduledExecutorService scheduledExecutor;

    public void scheduleRefreshCache() {
        scheduledExecutor.scheduleAtFixedRate(() -> {
            log.info("开始加载调拨场景数据");
            try {
                getTransferSceneResponseMap();
            } catch (Exception e) {
                log.warn("加载调拨场景失败->", e);
                alertService.alert("加载调拨场景缓存失败", "加载调拨场景缓存失败:" + e.getMessage(),
                    AlertLevel.CRITICAL);
            }
        }, 0, 4, TimeUnit.MINUTES);
    }



    public Map<String, Integer> getTransferSceneResponseMap() {

        if (!localParamConfig.getEnableTransferScene()) {
            Map<String, Integer> result = Maps.newHashMapWithExpectedSize(2);
            result.put("TB",0);
            result.put("EC",1);
            return result;
        }

        Map<String, Integer> result = CacheConfig.TRANSFER_SCENE.getIfPresent(CacheConfig.KEY_ALL);
        if (MapUtils.isNotEmpty(result)) {
            return result;
        }
        refreshTransferSceneResponseMap();
        return CacheConfig.TRANSFER_SCENE.getIfPresent(CacheConfig.KEY_ALL);
    }


    private void refreshTransferSceneResponseMap() {
        ResponseBaseVo<List<String>> responseBaseVo = inventoryUsageClient.transferScene();
        List<String> usageCodes = responseBaseVo.getData();
        if (!responseBaseVo.isSuccess() || CollectionUtils.isEmpty(usageCodes)) {
            log.error("【请求ims-config服务错误】调拨场景用途查询, 返回数据:{}", responseBaseVo);
            alertService
                .alert("【请求ims-config服务错误】调拨场景用途查询", "返回数据: " + JsonUtil.toJson(responseBaseVo), AlertLevel.CRITICAL);
            throw new ImsRemoteInvocationException(CommonErrorCode.IMS_CONFIG_SERVER_ERROR,
                JsonUtil.toJson(responseBaseVo));
        }

        Map<String, Integer> result = Maps.newHashMapWithExpectedSize(usageCodes.size());
        for (int i = 0; i < usageCodes.size(); i++) {
            result.put(usageCodes.get(i), i);
        }

        CacheConfig.TRANSFER_SCENE.put(CacheConfig.KEY_ALL, result);
    }







    public void warmUp() {
        try {
            refreshTransferSceneResponseMap();
        } catch (Exception e) {
            log.warn("加载调拨场景失败->", e);
            alertService.alert("加载调拨场景缓存失败", "加载调拨场景缓存失败:" + e.getMessage(),
                AlertLevel.CRITICAL);
        }

        scheduleRefreshCache();
    }

}
