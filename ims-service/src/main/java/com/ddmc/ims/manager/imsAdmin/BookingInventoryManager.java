package com.ddmc.ims.manager.imsAdmin;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.exception.ImsRemoteInvocationException;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.rpc.imsadmin.ImsAdminBookingInventoryClient;
import com.ddmc.ims.sale.request.TransferOutChangeEventRequest;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BookingInventoryManager {

    @Resource
    private ImsAdminBookingInventoryClient imsAdminBookingInventoryClient;

    public void notifyTransferOutEvent(LogicInventoryLocation fromLocation,
                                       LogicInventoryLocation toLocation,
                                       Date shipExpectTime,
                                       Integer shipIndex,
                                       List<Long> skuIds,
                                       String bookingInventoryChangeTriggerType) {
        TransferOutChangeEventRequest request = new TransferOutChangeEventRequest();
        request.setFromWarehouseId(fromLocation.getWarehouseId());
        request.setFromCargoOwnerId(fromLocation.getCargoOwnerId());
        request.setFromLogicInventoryLocationCode(fromLocation.getLogicInventoryLocationCode());
        request.setToWarehouseId(toLocation.getWarehouseId());
        request.setToCargoOwnerId(toLocation.getCargoOwnerId());
        request.setToLogicInventoryLocationCode(toLocation.getLogicInventoryLocationCode());
        request.setExpectShipDate(shipExpectTime);
        request.setShipIndex(shipIndex);
        request.setSkuIds(skuIds);
        request.setBookingChangeTriggerType(Optional.ofNullable(bookingInventoryChangeTriggerType).orElse(CommonConstants.DEFAULT_BOOKING_INVENTORY_CHANGE_TYPE));
        ResponseBaseVo<Boolean> booleanResponseBaseVo = imsAdminBookingInventoryClient.notifyTransferOutEvent(request);
        if (Objects.isNull(booleanResponseBaseVo) || !booleanResponseBaseVo.isSuccess()
            || !Objects.equals(Boolean.TRUE, booleanResponseBaseVo.getData())) {
            log.warn("[notifyTransferOutEvent] 调拨在途发送变更，通知ims-admin失败, param -> {}, response -> {}",
                JsonUtils.toJson(request), JsonUtils.toJson(booleanResponseBaseVo));
            throw new ImsRemoteInvocationException("调拨在途发送变更，通知ims-admin失败");
        }
    }
}
