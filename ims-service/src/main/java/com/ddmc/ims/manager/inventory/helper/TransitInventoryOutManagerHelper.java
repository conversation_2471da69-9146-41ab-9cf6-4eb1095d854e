package com.ddmc.ims.manager.inventory.helper;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.converter.usage.TransferIntransitInventoryBoConverter;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 调拨在途出库命令数据处理帮助类
 *
 * <AUTHOR>
 */

public class TransitInventoryOutManagerHelper {

    private TransitInventoryOutManagerHelper() {

    }


    /**
     * 将凭证依次处理，不存在则构建数据塞入insertMap,存在则更新数据塞入existMap
     *
     * @param header header
     * @param usageDetails usageDetails
     * @param insertMap insertMap
     * @param existMap existMap
     */
    public static void processTransferInTransitInventory(CredentialHeader header,List<CredentialUseageDetail> usageDetails,
        Map<TransferInTransitItem, List<TransferIntransitInventoryBo>> insertMap,
        Map<TransferInTransitItem, List<TransferIntransitInventoryBo>> existMap) {
        usageDetails.forEach(t -> {
            TransferInTransitItem condition = TransferInTransitItemConverter
                .convertByCredentialUsageDetail(t, true);
            //获取同四要素+品+出库仓+出库货主+入库仓+入库货主+入库逻辑库位编码+目标用途的在途数据
            List<TransferIntransitInventoryBo> inTransitInventoryList = existMap
                .getOrDefault(condition, insertMap.get(condition));
            if (CollectionUtils.isEmpty(inTransitInventoryList)) {
                TransferIntransitInventoryBo inTransit = TransferIntransitInventoryBoConverter
                    .convertToOutTransferInTransitInventory(header,t);
                List<TransferIntransitInventoryBo> newList = new ArrayList<>();
                newList.add(inTransit);
                insertMap.put(condition, newList);
            } else {
                changeTransferSkuInventory(inTransitInventoryList, t.getQty(), t.getFromLogicLocationCode());
            }
        });
    }


    /**
     * 扣减计划未发量，增加出库量，增加调拨在途量
     *
     * @param inventoryList inventoryList
     * @param qty qty
     * @param fromLogicLocationCode 来源逻辑库位
     */
    private static void changeTransferSkuInventory(List<TransferIntransitInventoryBo> inventoryList, BigDecimal qty,
        String fromLogicLocationCode) {
        //特殊情况下会有多条记录，优先匹配同来源逻辑库位的记录，剩下的再按顺序分配，只能保证总数一致
        Optional<TransferIntransitInventoryBo> matchOptional = inventoryList.stream()
            .filter(t -> t.getFromLogicInventoryLocationCode().equals(fromLogicLocationCode)).findFirst();
        //存在则是完全匹配，按登记的调拨在途处理
        if (matchOptional.isPresent()) {
            TransferIntransitInventoryBo inventory = matchOptional.get();
            changeInventoryQty(qty, inventory);
            return;
        }
        //不存在认为是登记了调拨在途的出库逻辑库位编码与实际出库的逻辑库位编码不一致。则调拨在途的相应数据按登记的出库逻辑库位变更
        for (TransferIntransitInventoryBo inventory : inventoryList) {
            if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                // 扣减数量用尽，退出循环
                break;
            }
            //待扣量与计划未出量相比取小值
            BigDecimal deductQty = qty.min(inventory.getWaitAllocQty());
            changeInventoryQty(deductQty, inventory);
            qty = qty.subtract(deductQty);
        }
        //最后还有剩则为实出量大于所以的计划待出量,则随便取第一个给到
        if (qty.signum() > 0) {
            TransferIntransitInventoryBo inventory = inventoryList.get(0);
            changeInventoryQty(qty, inventory);
        }
    }

    /**
     * 增加调拨在途量，增加出库量，扣减计划待出量
     *
     * @param qty qty
     * @param inventory inventory
     */
    private static void changeInventoryQty(BigDecimal qty, TransferIntransitInventoryBo inventory) {
        BigDecimal allocQty = inventory.getAllocQty().add(qty);
        BigDecimal inTransitQty = inventory.getIntransitQty().add(qty);
        BigDecimal waitAllocQty = inventory.getWaitAllocQty().subtract(qty).max(BigDecimal.ZERO);
        inventory.setIntransitQty(inTransitQty);
        inventory.setWaitAllocQty(waitAllocQty);
        inventory.setAllocQty(allocQty);
    }




}
