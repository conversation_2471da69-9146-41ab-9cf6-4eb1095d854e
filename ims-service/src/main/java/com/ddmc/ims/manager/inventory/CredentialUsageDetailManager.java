package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.errorcode.Asserts;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.converter.usage.UsageDetailConverter;
import com.ddmc.ims.dal.mapper.ims.CredentialUseageDetailMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CredentialUsageDetailManager {

    @Resource
    private CredentialUseageDetailMapper credentialUseageDetailMapper;

    @Resource
    private List<UsageDetailConverter> usageDetailConverters;
    @Resource
    private LocalParamConfig localParamConfig;

    @Resource(name = "commonUsageDetailConverter")
    private UsageDetailConverter commonUsageDetailConverter;

    public void saveCredentialUsageDetail(ConfirmContext confirmContext) {
        CredentialHeader credentialHeaderAndDetail = confirmContext.getCredentialHeader();
        if (CollectionUtils.isEmpty(credentialHeaderAndDetail.getCredentialDetailList())) {
            credentialHeaderAndDetail.setCredentialUseageDetailList(Collections.emptyList());
            return;
        }

        UsageDetailConverter usageConvert = getUsageConvert(credentialHeaderAndDetail.getOrderType(),
            credentialHeaderAndDetail.getOrderOperateType());
        List<CredentialUseageDetail> credentialUseageDetailList = usageConvert.convert(confirmContext);

        Optional<CredentialUseageDetail> commandTypeOptional = credentialUseageDetailList.stream()
            .filter(t -> StringUtils.isBlank(t.getCommandType())).findAny();
        if (commandTypeOptional.isPresent()) {
            Optional<CredentialUseageDetail> any = credentialUseageDetailList.stream()
                .filter(t -> StringUtils.isNotBlank(t.getCommandType())).findAny();
            any.ifPresent(t -> {
                log.error("[同一个凭证不允许CommandType同时存在有值与空值] {}", JsonUtil.toJson(credentialHeaderAndDetail));
                throw new ImsBusinessException("同一个凭证不允许CommandType同时存在有值与空值");
            });
            List<CommandTypeEnum> commandTypeEnumList = localParamConfig
                .getCommandTypeEnumList(credentialHeaderAndDetail);
            credentialUseageDetailList = buildDefaultCommandType(credentialUseageDetailList, commandTypeEnumList);
        }

        credentialUseageDetailList.forEach(t -> {
            t.setCredentialHeaderId(credentialHeaderAndDetail.getId());
            if (Objects.isNull(t.getTodaySale())) {
                t.setTodaySale(Boolean.FALSE);
            }
        });
        Lists.partition(credentialUseageDetailList, CommonConstants.BATCH_INSERT_DB_300)
            .forEach(credentialUseageDetailMapper::batchInsert);
        credentialHeaderAndDetail.setCredentialUseageDetailList(credentialUseageDetailList);
    }

    private List<CredentialUseageDetail> buildDefaultCommandType(List<CredentialUseageDetail> usageDetailList,
        List<CommandTypeEnum> commandTypeEnumList) {
        if (CollectionUtils.isEmpty(commandTypeEnumList)) {
            return usageDetailList;
        }
        if (commandTypeEnumList.size() == 1) {
            String commandType = commandTypeEnumList.get(0).getCode();
            usageDetailList.forEach(t -> t.setCommandType(commandType));
        }
        List<CredentialUseageDetail> resultList = Lists.newArrayListWithExpectedSize(usageDetailList.size());
        for (CredentialUseageDetail credentialUseageDetail : usageDetailList) {
            for (CommandTypeEnum commandTypeEnum : commandTypeEnumList) {
                CredentialUseageDetail result = CredentialUsageDetailConverter
                    .copyCredentialUseageDetail(credentialUseageDetail);
                result.setCommandType(commandTypeEnum.getCode());
                resultList.add(result);
            }
        }
        return resultList;
    }


    /**
     * @return 解析器
     */
    private UsageDetailConverter getUsageConvert(String orderType, Integer orderOperateType) {
        List<UsageDetailConverter> usageDetailConverterList = this.usageDetailConverters.stream()
            .filter(e -> e.isSupport(orderType, orderOperateType)).collect(Collectors.toList());
        Asserts.isTrue(usageDetailConverterList.size() <= 1, CommonErrorCode.USAGE_DETAIL_CONVERT_MORE_ONE);
        if (CollectionUtils.isEmpty(usageDetailConverterList)) {
            log.warn("UsageDetailConverter orderType:{},orderOperateType:{}, not found Converter", orderType,
                orderOperateType);
            return commonUsageDetailConverter;
        }
        return usageDetailConverterList.get(0);
    }


}
