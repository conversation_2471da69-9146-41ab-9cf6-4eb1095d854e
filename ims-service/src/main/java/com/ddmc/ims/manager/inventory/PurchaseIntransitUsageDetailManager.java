package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.ims.service.common.AlertService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购在途用途处理
 */
@Slf4j
@Component
public class PurchaseIntransitUsageDetailManager {

    @Resource
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;
    @Resource
    private AlertService alertService;
    @Resource
    private LocalParamConfig localParamConfig;

    /**
     * 获取采购在途用途详情
     */
    public List<CredentialUseageDetail> getUseageDetail(CredentialHeader credentialHeaderAndDetail) {
        boolean isClean =
            OrderOperateTypeEnum.CLEAR_PLAN_IN.getCode().equals(credentialHeaderAndDetail.getOrderOperateType())
                || OrderOperateTypeEnum.CLEAR_BOOKED_PLAN_IN.getCode()
                .equals(credentialHeaderAndDetail.getOrderOperateType());
        if (isClean && CollectionUtils.isNotEmpty(credentialHeaderAndDetail.getCredentialDetailList())) {
            return credentialHeaderAndDetail.getCredentialDetailList().stream().map(CredentialUsageDetailConverter
                ::getSingleCredentialUsageDetail).collect(Collectors.toList());
        }

        if (isClean || CollectionUtils.isEmpty(credentialHeaderAndDetail.getCredentialDetailList())){
            return Collections.emptyList();
        }
        //申请入库
        if (OrderOperateTypeEnum.APPLY_IN_OF_STOCK.getCode().equals(credentialHeaderAndDetail.getOrderOperateType())){
            return credentialHeaderAndDetail.getCredentialDetailList().stream().map(CredentialUsageDetailConverter
                    ::getSingleCredentialUsageDetail).collect(Collectors.toList());
        }

        //获取采购在途信息
        List<PurchaseIntransitInventory> purchaseIntransitInventories = purchaseIntransitInventoryMapper.listByOrderSourceAndPurchaseNo(
            credentialHeaderAndDetail.getOrderSource(), credentialHeaderAndDetail.getOrderNo());
        if (CollectionUtils.isEmpty(purchaseIntransitInventories)){
            log.warn("[获取采购在途用途] 未获取到在途信息, 采购单号:{}, 订单来源:{}, 单据操作类型:{}", credentialHeaderAndDetail.getOrderNo(),
                credentialHeaderAndDetail.getOrderSource(), credentialHeaderAndDetail.getOrderOperateType());
            //根据详情转到默认用途
            return credentialHeaderAndDetail.getCredentialDetailList().stream()
                .map(this::defaultUsageHandle).flatMap(Collection::stream)
                .collect(Collectors.toList());
        }

        //采购在途根据skuId和用途分组
        Map<ImmutablePair<Long, String>, BigDecimal> skuIdUsageCodeMap = purchaseIntransitInventories.stream()
            .collect(Collectors.toMap(intransit -> ImmutablePair.of(intransit.getSkuId(), intransit.getUsageCode()),
                PurchaseIntransitInventory::getIntransitQty, (n1, n2) -> n2));
        //根据凭证详情中的用途 匹配采购在途中的用途
        return credentialHeaderAndDetail.getCredentialDetailList().stream()
            .map(credentialDetail -> matchHandle(credentialDetail, skuIdUsageCodeMap))
            .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 匹配处理
     * @param detail 凭证详情信息
     * @param skuIdUsageCodeMap <<skuId, usageCode>, 采购在途>
     * @return 凭证用途详情
     */
    private List<CredentialUseageDetail> matchHandle(CredentialDetail detail,
        Map<ImmutablePair<Long, String>, BigDecimal> skuIdUsageCodeMap) {
        List<CredentialUseageDetail> credentialUseageDetails = detail.getUsageList().stream().map(
                usageCode -> converterTo(detail, skuIdUsageCodeMap, usageCode))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        //多收处理
        overchargeHandle(detail, credentialUseageDetails);
        return credentialUseageDetails;
    }

    /**
     * 根据采购用途登记凭证用途详情信息
     * @param credentialDetail 凭证详情信息
     * @param skuIdUsageCodeMap 采购在途库存map
     * @param usageCode 用途
     * @return 凭证用途详情
     */
    private CredentialUseageDetail converterTo(CredentialDetail credentialDetail,
                                               Map<ImmutablePair<Long, String>, BigDecimal> skuIdUsageCodeMap, String usageCode) {
        ImmutablePair<Long, String> skuUsagePair = ImmutablePair.of(credentialDetail.getSkuId(), usageCode);
        //在途数量
        BigDecimal intransitQty = skuIdUsageCodeMap.get(skuUsagePair);
        if (Objects.isNull(intransitQty) || BigDecimal.ZERO.compareTo(intransitQty) >= 0) {
            return null;
        }
        CredentialUseageDetail useageDetail = new CredentialUseageDetail();
        useageDetail.setCredentialHeaderId(credentialDetail.getCredentialHeaderId());
        useageDetail.setFromLogicLocationCode(credentialDetail.getFromLogicLocationCode());
        useageDetail.setFromCargoOwnerId(credentialDetail.getFromCargoOwnerId());
        useageDetail.setFromWarehouseId(credentialDetail.getFromWarehouseId());
        useageDetail.setToLogicLocationCode(credentialDetail.getToLogicLocationCode());
        useageDetail.setToCargoOwnerId(credentialDetail.getToCargoOwnerId());
        useageDetail.setToWarehouseId(credentialDetail.getToWarehouseId());
        useageDetail.setLotId(credentialDetail.getLotId());
        useageDetail.setSkuId(credentialDetail.getSkuId());

        //凭证剩余数量
        BigDecimal qty = credentialDetail.getQty();

        //用途数量 = min（凭证剩余数量, 在途数量）
        useageDetail.setQty(qty.min(intransitQty));
        //凭证剩余数量 = 凭证剩余数量 - 用途数量
        skuIdUsageCodeMap.put(skuUsagePair, intransitQty.subtract(useageDetail.getQty()));
        credentialDetail.setQty(qty.subtract(useageDetail.getQty()));

        useageDetail.setDemand(credentialDetail.getDemand());
        useageDetail.setInventoryStatus(credentialDetail.getInventoryStatus());
        useageDetail.setToInventoryStatus(credentialDetail.getToInventoryStatus());
        useageDetail.setUsageCode(usageCode);
        useageDetail.setOrderTag(credentialDetail.getOrderTag());
        useageDetail.setToUsageCode(StringUtils.EMPTY);
        return useageDetail;
    }

    private CredentialUseageDetail converterDefault(CredentialDetail credentialDetail,String usageCode) {
        CredentialUseageDetail useageDetail = new CredentialUseageDetail();
        useageDetail.setCredentialHeaderId(credentialDetail.getCredentialHeaderId());
        useageDetail.setFromLogicLocationCode(credentialDetail.getFromLogicLocationCode());
        useageDetail.setFromCargoOwnerId(credentialDetail.getFromCargoOwnerId());
        useageDetail.setFromWarehouseId(credentialDetail.getFromWarehouseId());
        useageDetail.setToLogicLocationCode(credentialDetail.getToLogicLocationCode());
        useageDetail.setToCargoOwnerId(credentialDetail.getToCargoOwnerId());
        useageDetail.setToWarehouseId(credentialDetail.getToWarehouseId());
        useageDetail.setLotId(credentialDetail.getLotId());
        useageDetail.setSkuId(credentialDetail.getSkuId());

        useageDetail.setQty(credentialDetail.getQty());
        //凭证剩余数量 = 凭证剩余数量 - 用途数量
        useageDetail.setDemand(credentialDetail.getDemand());
        useageDetail.setInventoryStatus(credentialDetail.getInventoryStatus());
        useageDetail.setToInventoryStatus(credentialDetail.getToInventoryStatus());
        useageDetail.setUsageCode(usageCode);
        useageDetail.setOrderTag(credentialDetail.getOrderTag());
        useageDetail.setToUsageCode(StringUtils.EMPTY);
        return useageDetail;
    }

    /**
     * 多收处理
     */
    private void overchargeHandle(CredentialDetail credentialDetail, List<CredentialUseageDetail> credentialUseageDetails){
        //凭证用途详情为空 或 凭证剩余数量为0 不处理
        // 条件不加小于0，兼容采购入负数的场景(采购撤销)
        if (BigDecimal.ZERO.compareTo(credentialDetail.getQty()) == 0) {
            return;
        }
        if (CollectionUtils.isEmpty(credentialUseageDetails)) {
            credentialUseageDetails.addAll(this.defaultUsageHandle(credentialDetail));
            return;
        }
        //剩余数量加到优先级最低的用途上
        BigDecimal qty = credentialDetail.getQty();
        CredentialUseageDetail credentialUseageDetail = credentialUseageDetails.get(credentialUseageDetails.size() - 1);
        credentialUseageDetail.setQty(credentialUseageDetail.getQty().add(qty));
    }


    /**
     * 兼容新老流程处理
     * 无采购在途信息，用配置的默认用途
     * @return 凭证用途详情
     */
    private List<CredentialUseageDetail> defaultUsageHandle(CredentialDetail detail){
        //凭证详情有用途，则用凭证详情上的用途处理
        if (CollectionUtils.isNotEmpty(detail.getUsageList())){
            return Lists.newArrayList(converterDefault(detail, detail.getUsageList().get(detail.getUsageList().size() - 1)));
        }
        //使用默认用途
        Map<String, String> defaultLocationUsageCodeMap = localParamConfig.getDefaultLocationUsageCodeMap();
        return Collections.singletonList(converterDefault(detail,
            defaultLocationUsageCodeMap.getOrDefault(detail.getFromLogicLocationCode(), StringUtils.EMPTY)));
    }



}
