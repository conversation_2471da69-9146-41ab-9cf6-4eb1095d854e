package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.common.bo.LogicInventoryLocationWithSku;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.SkuIdAndLotIdPair;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.dal.mapper.ims.InventoryLotInfoMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuLotInventoryMapper;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.ddmc.ims.dto.MultiSkuWarehouseInventoryWrapper;
import com.ddmc.ims.dto.SingleSkuWarehouseInventoryWrapper;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class WarehouseInventoryWrapperManager {

    @Resource
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;

    @Resource
    private WarehouseSkuLotInventoryMapper warehouseSkuLotInventoryMapper;

    @Resource
    private InventoryLotInfoMapper inventoryLotInfoMapper;


    public MultiSkuWarehouseInventoryWrapper loadSkuAndLotInventory(LogicInventoryLocation logicInventoryLocation,
        List<SkuIdAndLotIdPair> skuIdAndLotIdPairs,
        YesNoEnum refRealInventory) {
        if (YesNoEnum.YES.equals(refRealInventory)) {
            return loadSkuAndLotInventory(logicInventoryLocation, skuIdAndLotIdPairs);
        }
        Set<Long> skuIds = skuIdAndLotIdPairs.stream().map(SkuIdAndLotIdPair::getSkuId).collect(Collectors.toSet());
        return loadSkuInventory(logicInventoryLocation, skuIds);
    }


    public MultiSkuWarehouseInventoryWrapper loadSkuInventory(LogicInventoryLocation logicInventoryLocation,
        Set<Long> skuIds) {
        List<SingleSkuWarehouseInventoryWrapper> warehouseInventoryWrappers = mapWarehouseSkuInventory(
            logicInventoryLocation, skuIds).values().stream()
            .map(wsi -> new SingleSkuWarehouseInventoryWrapper(
                new LogicInventoryLocationWithSku(logicInventoryLocation, wsi.get(0).getSkuId()), wsi,
                Collections.emptyList(), Lists.newArrayList())).collect(Collectors.toList());
        return new MultiSkuWarehouseInventoryWrapper(warehouseInventoryWrappers);
    }

    /**
     * 加载货品库存
     *
     * @param logicInventoryLocation 逻辑库位
     * @param skuIds 货品id
     * @param loadLotInventory 是否加载批次库存
     * @param loadLotInfo 是否加载批次信息
     * @return 库存包装器
     */
    public MultiSkuWarehouseInventoryWrapper loadSkuInventory(LogicInventoryLocation logicInventoryLocation,
        Set<Long> skuIds,
        boolean loadLotInventory, boolean loadLotInfo) {
        Map<Long, List<WarehouseSkuInventory>> warehouseSkuInventoryMap = mapWarehouseSkuInventory(
            logicInventoryLocation, skuIds);

        //处理批次库存
        Map<Long, List<InventoryLotInfo>> inventoryLotInfoMap = Collections.emptyMap();
        Map<Long, List<WarehouseSkuLotInventory>> warehouseSkuLotInventoryMap = Collections.emptyMap();
        if (loadLotInventory) {
            warehouseSkuLotInventoryMap = mapWarehouseSkuLotInventoryBySkuIds(logicInventoryLocation, skuIds);
            List<String> lotIds = warehouseSkuLotInventoryMap.values().stream().flatMap(List::stream)
                .map(WarehouseSkuLotInventory::getLotId)
                .distinct().collect(Collectors.toList());

            if (loadLotInfo) {
                inventoryLotInfoMap = mapInventoryLotInfo(logicInventoryLocation.getWarehouseId(), lotIds);
            }
        }

        Map<Long, List<WarehouseSkuLotInventory>> finalWarehouseSkuLotInventoryMap = warehouseSkuLotInventoryMap;
        Map<Long, List<InventoryLotInfo>> finalInventoryLotInfoMap = inventoryLotInfoMap;
        List<SingleSkuWarehouseInventoryWrapper> warehouseInventoryWrappers = Lists
            .newArrayListWithExpectedSize(warehouseSkuInventoryMap.size());
        warehouseSkuInventoryMap.forEach((skuId, v) -> {
            List<WarehouseSkuLotInventory> warehouseSkuLotInventories = finalWarehouseSkuLotInventoryMap
                .getOrDefault(skuId, Lists.newArrayList());
            List<InventoryLotInfo> inventoryLotInfos = finalInventoryLotInfoMap
                .getOrDefault(skuId, Lists.newArrayList());
            SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = new SingleSkuWarehouseInventoryWrapper(
                new LogicInventoryLocationWithSku(logicInventoryLocation, skuId), v, warehouseSkuLotInventories,
                inventoryLotInfos);
            warehouseInventoryWrappers.add(singleSkuWarehouseInventoryWrapper);
        });
        return new MultiSkuWarehouseInventoryWrapper(warehouseInventoryWrappers);
    }

    private Map<Long, List<InventoryLotInfo>> mapInventoryLotInfo(Long warehouseId, List<String> lotIds) {
        return Lists.partition(lotIds, CommonConstants.BATCH_SELECT_DB_200).stream()
            .map(lis -> inventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(warehouseId, lotIds))
            .flatMap(List::stream).collect(Collectors.groupingBy(InventoryLotInfo::getSkuId));
    }

    private Map<Long, List<WarehouseSkuInventory>> mapWarehouseSkuInventory(
        LogicInventoryLocation logicInventoryLocation, Set<Long> skuIds) {
        return Lists.partition(Lists.newArrayList(skuIds),
            CommonConstants.BATCH_SELECT_DB_50)
            .stream()
            .map(sis -> warehouseSkuInventoryMapper.getByLogicInventoryLocationAndSkuIds(logicInventoryLocation, sis))
            .flatMap(List::stream).collect(Collectors.groupingBy(WarehouseSkuInventory::getSkuId));
    }

    private Map<Long, List<WarehouseSkuLotInventory>> mapWarehouseSkuLotInventory(
        LogicInventoryLocation logicInventoryLocation, List<SkuIdAndLotIdPair> skuIdAndLotIdPairs) {
        return Lists.partition(Lists.newArrayList(skuIdAndLotIdPairs),
            CommonConstants.BATCH_SELECT_DB_50)
            .stream().map(
                sis -> warehouseSkuLotInventoryMapper.getByLogicInventoryLocationAndLotIds(logicInventoryLocation, sis))
            .flatMap(List::stream).collect(Collectors.groupingBy(WarehouseSkuLotInventory::getSkuId));
    }

    private Map<Long, List<WarehouseSkuLotInventory>> mapWarehouseSkuLotInventoryBySkuIds(
        LogicInventoryLocation logicInventoryLocation, Set<Long> skuIds) {
        return Lists.partition(Lists.newArrayList(skuIds),
            CommonConstants.BATCH_SELECT_DB_50)
            .stream().map(sis -> warehouseSkuLotInventoryMapper
                .selectBySkuIdInAndLogicInventoryLocation(sis, logicInventoryLocation))
            .flatMap(List::stream).collect(Collectors.groupingBy(WarehouseSkuLotInventory::getSkuId));
    }

    private MultiSkuWarehouseInventoryWrapper loadSkuAndLotInventory(LogicInventoryLocation logicInventoryLocation,
        List<SkuIdAndLotIdPair> skuIdAndLotIdPairs) {
        //查询货品库存
        Set<Long> skuIds = skuIdAndLotIdPairs.stream().map(SkuIdAndLotIdPair::getSkuId).collect(Collectors.toSet());
        Map<Long, List<WarehouseSkuInventory>> warehouseSkuInventoryMap = mapWarehouseSkuInventory(
            logicInventoryLocation, skuIds);
        //处理批次库存
        Map<Long, List<WarehouseSkuLotInventory>> warehouseSkuLotInventoryMap = mapWarehouseSkuLotInventory(
            logicInventoryLocation, skuIdAndLotIdPairs);
        List<SingleSkuWarehouseInventoryWrapper> warehouseInventoryWrappers = Lists
            .newArrayListWithExpectedSize(warehouseSkuInventoryMap.size());
        warehouseSkuInventoryMap.forEach((skuId, v) -> {
            List<WarehouseSkuLotInventory> warehouseSkuLotInventories = warehouseSkuLotInventoryMap
                .getOrDefault(skuId, Lists.newArrayList());
            SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = new SingleSkuWarehouseInventoryWrapper(
                new LogicInventoryLocationWithSku(logicInventoryLocation, skuId), v, warehouseSkuLotInventories,
                Lists.newArrayList());
            warehouseInventoryWrappers.add(singleSkuWarehouseInventoryWrapper);
        });
        return new MultiSkuWarehouseInventoryWrapper(warehouseInventoryWrappers);
    }

    /**
     * 更新在库库存
     *
     * @param warehouseInventoryWrappers 库存包装器
     */
    public void saveInventory(MultiSkuWarehouseInventoryWrapper warehouseInventoryWrappers) {

        saveOrUpdateWarehouseSkuInventories(warehouseInventoryWrappers);

        saveOrUpdateWarehouseSkuLotInventories(warehouseInventoryWrappers);
    }

    private void saveOrUpdateWarehouseSkuLotInventories(MultiSkuWarehouseInventoryWrapper warehouseInventoryWrappers) {
        List<WarehouseSkuLotInventory> needUpdateWarehouseSkuLotInventories = warehouseInventoryWrappers
            .getChangedWarehouseSkuLotInventories();

        needUpdateWarehouseSkuLotInventories.sort(Comparator.comparing(WarehouseSkuLotInventory::getId));

        if (CollectionUtils.isNotEmpty(needUpdateWarehouseSkuLotInventories)) {
            WarehouseSkuLotInventory warehouseSkuLotInventory = needUpdateWarehouseSkuLotInventories.get(0);
            LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(
                warehouseSkuLotInventory.getWarehouseId(),
                warehouseSkuLotInventory.getCargoOwnerId(), warehouseSkuLotInventory.getLogicInventoryLocationCode());

            Lists.partition(needUpdateWarehouseSkuLotInventories, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(t -> warehouseSkuLotInventoryMapper.batchUpdate(logicInventoryLocation, t));
        }
        List<WarehouseSkuLotInventory> warehouseSkuLotInventoryList = warehouseInventoryWrappers
            .getAddWarehouseSkuLotInventories().stream()
            .filter(t -> !(StringUtils.isBlank(t.getLotId()) && t.getFreeQty().compareTo(
                BigDecimal.ZERO) == 0 && t.getFrozenQty().compareTo(BigDecimal.ZERO) == 0))
            .collect(Collectors.toList());
        Lists.partition(warehouseSkuLotInventoryList,
            CommonConstants.BATCH_UPDATE_DB_100).forEach(warehouseSkuLotInventoryMapper::batchInsert);
    }

    private void saveOrUpdateWarehouseSkuInventories(MultiSkuWarehouseInventoryWrapper warehouseInventoryWrappers) {

        List<WarehouseSkuInventory> needUpdateWarehouseSkuInventories = warehouseInventoryWrappers
            .getChangedWarehouseSkuInventories();
        //排序
        needUpdateWarehouseSkuInventories.sort(Comparator.comparing(WarehouseSkuInventory::getId));

        if (CollectionUtils.isNotEmpty(needUpdateWarehouseSkuInventories)) {
            WarehouseSkuInventory warehouseSkuInventory = needUpdateWarehouseSkuInventories.get(0);
            LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(
                warehouseSkuInventory.getWarehouseId(),
                warehouseSkuInventory.getCargoOwnerId(), warehouseSkuInventory.getLogicInventoryLocationCode());
            //更新
            Lists.partition(needUpdateWarehouseSkuInventories, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(t -> warehouseSkuInventoryMapper.batchUpdate(logicInventoryLocation, t));
        }

        //插入
        Lists.partition(warehouseInventoryWrappers.getAddWarehouseSkuInventories(), CommonConstants.BATCH_UPDATE_DB_100)
            .forEach(warehouseSkuInventoryMapper::batchInsert);
    }

    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER)
    public void deleteProcessingInventory(List<WarehouseSkuInventory> needDelete) {
        List<Long> ids = needDelete.stream().map(WarehouseSkuInventory::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        warehouseSkuInventoryMapper.deleteByWarehouseSkuInventoryId(ids);
        //处理加工中批次
        needDelete.forEach(w -> {
            LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(w.getWarehouseId(),
                w.getCargoOwnerId(), w.getLogicInventoryLocationCode());
            List<WarehouseSkuLotInventory> warehouseSkuLotInventories = warehouseSkuLotInventoryMapper
                .selectBySkuIdInAndLogicInventoryLocation(Lists.newArrayList(w.getSkuId()),
                    logicInventoryLocation);
            if (CollectionUtils.isEmpty(warehouseSkuLotInventories)) {
                return;
            }
            List<Long> warehouseSkuLotInventoriesId = warehouseSkuLotInventories.stream()
                .map(WarehouseSkuLotInventory::getId).collect(Collectors.toList());
            warehouseSkuLotInventoryMapper.batchDelete(logicInventoryLocation, warehouseSkuLotInventoriesId);
        });
    }
}
