package com.ddmc.ims.manager;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.sku.api.client.SkuClient;
import com.ddmc.sku.api.client.enums.OptionalSkuPropertyEnum;
import com.ddmc.sku.api.client.request.BatchQuerySkuOptionalPropertiesRequest;
import com.ddmc.sku.api.client.response.SkuBaseResponse;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SkuQueryManager {

    @Resource
    private SkuClient skuClient;

    @Resource
    private AlertService alertService;




    /**
     * 根据货品id查询商品属性
     * @param skuIds 货品id集合
     * @return <skuId, 属性>
     */
    public Map<Long, SkuBaseResponse> batchGetSkuBySkuIds(List<Long> skuIds){
        if (CollectionUtils.isEmpty(skuIds)){
            return Collections.emptyMap();
        }
        return this.batchGetOptionalSkuPropertiesBySkuIds(skuIds).stream()
            .collect(Collectors.toMap(SkuBaseResponse::getSkuId, Function.identity(), (v1, v2) -> v2));
    }


    /**
     * 根据货品id查询商品属性
     * @param skuIds 货品id集合
     * @return 属性
     */
    private List<SkuBaseResponse> batchGetOptionalSkuPropertiesBySkuIds(List<Long> skuIds){
        if (CollectionUtils.isEmpty(skuIds)){
            return Collections.emptyList();
        }
        Set<String> expectedSkuProperties = new HashSet<>();
        expectedSkuProperties.add(OptionalSkuPropertyEnum.SKU_NAME.getCode());
        expectedSkuProperties.add(OptionalSkuPropertyEnum.MANAGE_CATEGORY_ID.getCode());
        expectedSkuProperties.add(OptionalSkuPropertyEnum.MANAGE_CATEGORY_PATH.getCode());
        expectedSkuProperties.add(OptionalSkuPropertyEnum.RESPONSIBLE_GROUP_ID.getCode());
        expectedSkuProperties.add(OptionalSkuPropertyEnum.IS_MATERIAL.getCode());
        List<SkuBaseResponse> skuBaseResponseList = Lists.newArrayList();
        Lists.partition(skuIds, CommonConstants.BATCH_SELECT_500).forEach(list ->{
            BatchQuerySkuOptionalPropertiesRequest request = new BatchQuerySkuOptionalPropertiesRequest();
            request.setSkuIds(list);
            request.setExpectedSkuProperties(expectedSkuProperties);
            ResponseBaseVo<List<SkuBaseResponse>> listResponseBaseVo = skuClient.batchGetOptionalSkuPropertiesBySkuIds(
                request);
            if (!listResponseBaseVo.isSuccess() || CollectionUtils.isEmpty(listResponseBaseVo.getData())){
                log.error("【请求货品服务错误】批量查询货品属性（指定属性名称），请求数据:{}, 返回数据:{}", JsonUtils.toJson(list),
                    JsonUtils.toJson(listResponseBaseVo));
                alertService.alertWarning("【请求货品服务错误】批量查询货品属性（指定属性名称）异常",
                    "skuIdList:" + JsonUtils.toJson(list) + ", skuServiceRep:" + JsonUtils.toJson(listResponseBaseVo));
            }else {
                skuBaseResponseList.addAll(listResponseBaseVo.getData());
            }
        });
        return skuBaseResponseList;
    }




}
