package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.inventory.UsageCodeSkuId;
import com.ddmc.ims.common.bo.TransferOrderNoAndSource;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.config.param.LocalParamConfig.TransferUsagePriority;
import com.ddmc.ims.converter.usage.TransferIntransitInventoryBoConverter;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.manager.ImsConfigRemoteManager;
import com.ddmc.ims.service.lot.InventoryLotInfoService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TransferTransitUsageDetailManager {

    @Resource
    protected ImsConfigRemoteManager imsConfigRemoteManager;

    @Resource
    protected TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    protected InventoryLotInfoService inventoryLotInfoService;

    @Resource
    protected LocalParamConfig localParamConfig;

    /**
     * 获取sku,销售库存量。后续进行实现
     *
     * @param skuIds skuIds
     * @return result
     */
    public Map<UsageCodeSkuId, BigDecimal> getSkuToTransferInQtyMap(Set<Long> skuIds,
        Map<String, Integer> transferScenes) {
        Map<UsageCodeSkuId, BigDecimal> skuSaleQtyMap = Maps.newHashMapWithExpectedSize(skuIds.size() * 3);
        transferScenes.keySet().forEach(usageCode -> skuIds
            .forEach(skuId -> skuSaleQtyMap.put(new UsageCodeSkuId(usageCode, skuId), BigDecimal.ZERO)));
        return skuSaleQtyMap;
    }

    /**
     * 获取sku,销售库存量。后续进行实现
     *
     * @param credentialDetails credentialDetails
     * @return result
     */
    public Map<UsageCodeSkuId, BigDecimal> getSkuToTransferInQtyMap(List<CredentialDetail> credentialDetails,
        Map<String, Integer> transferScenes) {
        Set<Long> skuIds = credentialDetails.stream().map(CredentialDetail::getSkuId).collect(Collectors.toSet());
        Map<UsageCodeSkuId, BigDecimal> skuSaleQtyMap = Maps.newHashMapWithExpectedSize(skuIds.size() * 3);
        transferScenes.keySet().forEach(usageCode -> skuIds
            .forEach(skuId -> skuSaleQtyMap.put(new UsageCodeSkuId(usageCode, skuId), BigDecimal.ZERO)));
        return skuSaleQtyMap;
    }



    /**
     * 获取批次信息明细
     *
     * @param commands commands
     * @return result
     */
    public Map<String, InventoryLotInfo> getInventoryLotInfoMap(
        List<TransferIntransitInventoryCommand> commands) {
        Set<String> lotIds = commands.stream().map(TransferIntransitInventoryCommand::getLotId)
            .collect(Collectors.toSet());
        //该方法将检查批次是否存在，不存在则抛出异常
        return inventoryLotInfoService.queryInventoryLotInfo(lotIds);
    }









    /**
     * 通过命令查询调拨在途
     *
     * @param commands commands
     * @return result
     */
    public List<TransferIntransitInventory> getTransferInTransitInventoryList(
        List<TransferIntransitInventoryCommand> commands) {
        List<Long> skuIds = commands.stream().map(SkuInventoryCommand::getSkuId).collect(Collectors.toList());
        String orderNo = commands.get(0).getOrderNo();
        String orderSource = commands.get(0).getOrderSource();
        TransferOrderNoAndSource orderNoAndSource = new TransferOrderNoAndSource(orderSource, orderNo);
        return getTransferInTransitInventories(skuIds, orderNoAndSource);
    }


    /**
     * 通过凭证查询调拨在途
     *
     * @param headerAndDetail headerAndDetail
     * @return result
     */
    protected List<TransferIntransitInventoryBo> getTransferInTransitInventoryList(CredentialHeader headerAndDetail) {
        List<Long> skuIds = headerAndDetail.getCredentialDetailList().stream()
            .map(CredentialDetail::getSkuId).distinct().collect(Collectors.toList());
        String orderNo = headerAndDetail.getOrderNo();
        String orderSource = headerAndDetail.getOrderSource();
        TransferOrderNoAndSource orderNoAndSource = new TransferOrderNoAndSource(orderSource, orderNo);
        return getTransferInTransitInventoryBos(skuIds, orderNoAndSource);
    }

    /**
     * 查询调拨在途
     *
     * @param skuIds skuIds
     * @param orderNoAndSource orderNoAndSource
     * @return result
     */
    private List<TransferIntransitInventoryBo> getTransferInTransitInventoryBos(List<Long> skuIds,
        TransferOrderNoAndSource orderNoAndSource) {
        List<TransferIntransitInventory> result = getTransferInTransitInventories(skuIds, orderNoAndSource);
        return TransferIntransitInventoryBoConverter.convert(result);
    }

    /**
     * 查询调拨在途
     *
     * @param skuIds skuIds
     * @param orderNoAndSource orderNoAndSource
     * @return result
     */
    private List<TransferIntransitInventory> getTransferInTransitInventories(List<Long> skuIds,
        TransferOrderNoAndSource orderNoAndSource) {
        skuIds = skuIds.stream().distinct().collect(Collectors.toList());
        List<TransferIntransitInventory> result = Lists.newArrayListWithCapacity(skuIds.size());
        Lists.partition(skuIds, CommonConstants.BATCH_SELECT_DB_200).forEach(subList -> {
            List<TransferIntransitInventory> existInventory = transferIntransitInventoryMapper
                .listByOrderSourceAndNoAndSkuIds(orderNoAndSource, subList);
            if (CollectionUtils.isNotEmpty(existInventory)) {
                result.addAll(existInventory);
            }
        });
        return result;
    }





    /**
     * 将changeQty转换为UsageDetail输出
     *
     * @param transits transits
     * @param changeQtyMap changeQtyMap
     * @return result
     */
    protected List<CredentialUseageDetail> convertToUsageDetail(List<TransferIntransitInventoryBo> transits,
        Map<String, BigDecimal> changeQtyMap, Long headerId, String lotId) {
        List<CredentialUseageDetail> result = Lists.newArrayListWithCapacity(transits.size());
        Map<String, TransferIntransitInventoryBo> transitMap = transits.stream()
            .collect(Collectors.toMap(TransferIntransitInventoryBo::getUuid, Function.identity()));

        transitMap.forEach((id, transit) -> {
            BigDecimal changeQty = changeQtyMap.getOrDefault(id, BigDecimal.ZERO);
            CredentialUseageDetail detail = new CredentialUseageDetail();
            detail.setCredentialHeaderId(headerId);
            detail.setFromLogicLocationCode(transit.getFromLogicInventoryLocationCode());
            detail.setFromCargoOwnerId(transit.getFromCargoOwnerId());
            detail.setFromWarehouseId(transit.getFromWarehouseId());
            detail.setToLogicLocationCode(transit.getToLogicInventoryLocationCode());
            detail.setToCargoOwnerId(transit.getToCargoOwnerId());
            detail.setToWarehouseId(transit.getToWarehouseId());
            detail.setSkuId(transit.getSkuId());
            detail.setQty(changeQty);
            detail.setUsageCode(transit.getUsageCode());
            detail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
            detail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
            detail.setLotId(lotId);
            detail.setOrderTag("");
            detail.setDemand(CurrentDateUtil.newDate());
            detail.setToUsageCode(transit.getToUsageCode());
            result.add(detail);
        });
        return result;
    }

    public String getInboundUsageCode(CredentialDetail deductionDetail,
                                      TransferIntransitInventoryBo inventory) {
        List<String> toUsages = deductionDetail.getToUsageList();
        if (toUsages.size() == 1) {
            return deductionDetail.getToUsageCode();
        }
        //传入多个用途时
        if (Objects.nonNull(inventory)) {
            if (toUsages.contains(inventory.getToUsageCode())) {
                return inventory.getToUsageCode();
            }
        }

        List<String> defaultUsageForInbound = localParamConfig.getDefaultUsageForInbound(deductionDetail.getToLogicLocationCode());
        if (CollectionUtils.isEmpty(defaultUsageForInbound)) {
            log.error("[getWarehouseSkuInventoryUsageCode]调拨入库存在多个用途，没有在途数据也没有默认用途->{}", JsonUtil.toJson(deductionDetail));
            throw new ImsBusinessException("[getWarehouseSkuInventoryUsageCode]调拨入库存在多个用途，没有在途数据也没有默认用途");
        }
        List<String> existsUsage = defaultUsageForInbound.stream().filter(toUsages::contains).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existsUsage)) {
            return existsUsage.get(existsUsage.size() - 1);
        }
        return defaultUsageForInbound.get(defaultUsageForInbound.size() - 1);
    }


    public ImmutablePair<String, String> getOutboundUsageCode(CredentialDetail deductionDetail,
        Map<String, Integer> transferSceneResponseMap) {
        List<String> outUsages = deductionDetail.getUsageList();
        if (outUsages.size() == 1) {
            if (transferSceneResponseMap.containsKey(deductionDetail.getUsageCode())) {
                List<TransferUsagePriority> transferUsagePriorityList = localParamConfig
                    .getTransferOutUsagePriority(deductionDetail.getToUsageCode());
                TransferUsagePriority priority = transferUsagePriorityList.get(transferUsagePriorityList.size() - 1);
                return new ImmutablePair<>(priority.getFromUsageCode(), priority.getToUsageCode());
            } else {
                return new ImmutablePair<>(deductionDetail.getUsageCode(), deductionDetail.getToUsageCode());
            }
        }

        List<String> defaultUsageForOutbound = localParamConfig.getDefaultUsageForOutbound(deductionDetail.getToLogicLocationCode());
        if (CollectionUtils.isEmpty(defaultUsageForOutbound)) {
            log.error("[getWarehouseSkuInventoryUsageCode]调拨入库存在多个用途，没有在途数据也没有默认用途->{}", JsonUtil.toJson(deductionDetail));
            throw new ImsBusinessException("[getWarehouseSkuInventoryUsageCode]调拨入库存在多个用途，没有在途数据也没有默认用途");
        }
        List<String> existsUsage = defaultUsageForOutbound.stream().filter(outUsages::contains).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existsUsage)) {
            String usageCode = existsUsage.get(existsUsage.size() - 1);
            return new ImmutablePair<>(usageCode, usageCode);
        }
        String usageCode = defaultUsageForOutbound.get(defaultUsageForOutbound.size() - 1);
        return new ImmutablePair<>(usageCode, usageCode);
    }
}
