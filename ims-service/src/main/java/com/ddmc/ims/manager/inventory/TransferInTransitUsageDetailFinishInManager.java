package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.bo.inventory.TransferInTransitItem.TransferInTransitItemBuilder;
import com.ddmc.ims.calculator.TransferCleanInTransitSplitUsageCalculator;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig.TransferUsagePriority;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TransferInTransitUsageDetailFinishInManager extends TransferTransitUsageDetailManager {

    /**
     * 调拨在途用途入库完成明细拆分
     *
     * @param headerAndDetail headerAndDetail
     */
    public List<CredentialUseageDetail> handleTransferFinishInBound(CredentialHeader headerAndDetail) {

        //入库按 四要素+sku+出库仓+出库逻辑库位+出库货主+入库仓+入库货主 分组
        Map<TransferInTransitItem, List<TransferIntransitInventoryBo>> skuInventoryMap = getConditionInventoryMap(
            headerAndDetail);

        List<CredentialDetail> credentialDetailList = headerAndDetail.getCredentialDetailList();
        //出库仓+出库逻辑库位+出库货主+入库仓+入库货主+sku,凭证map
        Map<TransferInTransitItem, List<CredentialDetail>> skuCredentialDetailMap = credentialDetailList.stream()
            .collect(Collectors.groupingBy(t -> TransferInTransitItemConverter.convertByCredential(t, false)));
        List<CredentialUseageDetail> result = Lists.newArrayListWithExpectedSize(skuInventoryMap.size());
        skuCredentialDetailMap.forEach((condition, deductionDetails) -> {

            List<TransferUsagePriority> transferUsagePriority = localParamConfig
                .getTransferInUsagePriority(deductionDetails.get(0).getUsageCode());
            if (CollectionUtils.isEmpty(transferUsagePriority) && deductionDetails.get(0).getUsageList().size() == 1) {
                TransferUsagePriority priority = new TransferUsagePriority();
                priority.setFromUsageCode(deductionDetails.get(0).getUsageCode());
                priority.setToUsageCode(deductionDetails.get(0).getUsageCode());
                transferUsagePriority = Lists.newArrayList(priority);
            } else if (CollectionUtils.isEmpty(transferUsagePriority)) {
                log.error("[handleTransferFinishInBound]存在多个来源用途，没有优先级配置->{}", JsonUtil.toJson(deductionDetails));
                throw new ImsBusinessException("[handleTransferFinishInBound]存在多个来源用途，没有优先级配");
            }

            Map<String, Integer> usagePriority = Maps.newHashMapWithExpectedSize(transferUsagePriority.size());
            for (int i = 0; i < transferUsagePriority.size(); i++) {
                TransferUsagePriority priority = transferUsagePriority.get(i);
                usagePriority.put(priority.getFromUsageCode() + "," + priority.getToUsageCode(), i);
            }

            List<TransferIntransitInventoryBo> transits = skuInventoryMap.get(condition);
            //如果为空，按默认处理
            if (CollectionUtils.isEmpty(transits)) {
                return;
            }

            for (CredentialDetail detail : deductionDetails) {
                BigDecimal deductionQty = detail.getQty();

                //先按出库用途排序，再按入库用途排序
                List<TransferIntransitInventoryBo> intransitInventoryBos = transits.stream()
                    .filter(t -> usagePriority.containsKey(t.getUsageCode() + "," + t.getToUsageCode()))
                    .sorted(Comparator.comparing((TransferIntransitInventoryBo t) -> usagePriority
                        .get(t.getUsageCode() + "," + t.getToUsageCode())).reversed())
                    .collect(Collectors.toList());

                TransferCleanInTransitSplitUsageCalculator calculator = new TransferCleanInTransitSplitUsageCalculator();
                Map<String, BigDecimal> transitChangeQty = calculator
                    .getTransitChangeQty(intransitInventoryBos, deductionQty);

                //将changeQty转换为UsageDetail输出
                List<CredentialUseageDetail> usageDetails = convertToUsageDetail(intransitInventoryBos,
                    transitChangeQty,
                    headerAndDetail.getId(), detail.getLotId());
                result.addAll(usageDetails);

            }
        });

        return result;
    }


    /**
     * 调拨在途
     *
     * @param headerAndDetail headerAndDetail
     * @return result
     */
    private Map<TransferInTransitItem, List<TransferIntransitInventoryBo>> getConditionInventoryMap(
        CredentialHeader headerAndDetail) {
        List<TransferIntransitInventoryBo> inventories = getTransferInTransitInventoryList(headerAndDetail);
        return inventories.stream().collect(Collectors.groupingBy(this::convertToCondition));
    }


    private TransferInTransitItem convertToCondition(TransferIntransitInventoryBo inventory) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(inventory.getFromCargoOwnerId());
        condition.fromWarehouseId(inventory.getFromWarehouseId());
        condition.fromLogicInventoryLocationCode(inventory.getFromLogicInventoryLocationCode());
        condition.toCargoOwnerId(inventory.getToCargoOwnerId());
        condition.toWarehouseId(inventory.getToWarehouseId());
        condition.skuId(inventory.getSkuId());
        return condition.build();
    }


}
