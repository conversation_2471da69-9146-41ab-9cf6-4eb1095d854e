package com.ddmc.ims.manager.ocs;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ocs.inventorycredential.OcsInventoryCredentialClient;
import com.ddmc.ocs.inventorycredential.request.GetInventoryCredentialStatusRequest;
import com.ddmc.ocs.inventorycredential.request.QueryManufactureDemandDetailRequest;
import com.ddmc.ocs.inventorycredential.response.ManufactureDemandDetailResponse;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class InventoryCredentialManager {

    @Resource
    private OcsInventoryCredentialClient inventoryCredentialClient;


    public Boolean checkInventoryCredentialExists(String globalTransactionId) {
        GetInventoryCredentialStatusRequest request = new GetInventoryCredentialStatusRequest();
        request.setGlobalTransactionId(globalTransactionId);
        ResponseBaseVo<Boolean> responseResponseBaseVo = inventoryCredentialClient.checkInventoryCredentialExists(request);
        if (Objects.isNull(responseResponseBaseVo) || !responseResponseBaseVo.isSuccess()) {
            throw new ImsBusinessException(CommonErrorCode.CALL_OCS_ERROR);
        }
        return responseResponseBaseVo.getData();
    }

    public List<ManufactureDemandDetailResponse> queryManufactureDemandDetail(String orderNo,String system) {
        QueryManufactureDemandDetailRequest request = new QueryManufactureDemandDetailRequest();
        request.setOriOrderNo(orderNo);
        request.setOriOrderSystem(system);
        ResponseBaseVo<List<ManufactureDemandDetailResponse>> responseResponseBaseVo = inventoryCredentialClient.queryManufactureDemandDetail(request);
        if (Objects.isNull(responseResponseBaseVo) || !responseResponseBaseVo.isSuccess()) {
            throw new ImsBusinessException(CommonErrorCode.CALL_OCS_ERROR);
        }
        return responseResponseBaseVo.getData();
    }
}
