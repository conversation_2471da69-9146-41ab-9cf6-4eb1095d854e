package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.SnapshotCredentialDiff;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.google.common.collect.Lists;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SnapshotCredentialDiffManager {

    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;
    @Resource
    private SnapshotCredentialDiffMapper snapshotCredentialDiffMapper;
    @Resource
    private AlertService alertService;

    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Throwable.class)
    public void doSnapshotInventoryDiff(CredentialHeader credentialHeader) {
        credentialHeaderMapper.addLock(credentialHeader.getExeOrderSource(), credentialHeader.getExeOrderNo());
        //反加工入库，按异动处理
        if (OrderTypeEnum.UN_MANUFACTURE_MATERIAL_INBOUND.getCode().equals(credentialHeader.getOrderType())) {
            unManufactureMaterialInbound(credentialHeader);
            return;
        }
        if (OrderOperateTypeEnum.IN_STOCK.getCode().equals(credentialHeader.getOrderOperateType())) {
            dealIsStockChange(credentialHeader);
            return;
        }
        if (OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode().equals(credentialHeader.getOrderOperateType())
            || OrderTypeEnum.REVERSE_MANUFACTURE.getCode().equals(credentialHeader.getOrderType())) {
            completedCredential(credentialHeader);
            return;
        }
        log.error("异动类型为空, 入参{}", JsonUtil.toJson(credentialHeader));
        alertService.alert("异动类型为空", "入参" + JsonUtil.toJson(credentialHeader), AlertLevel.INFO);

    }

    private void completedCredential(CredentialHeader credentialHeader) {
        List<SnapshotCredentialDiff> snapshotCredentialDiffs = snapshotCredentialDiffMapper
            .listByExeOrderSourceAndNo(credentialHeader.getExeOrderSource(),
                credentialHeader.getExeOrderNo());
        List<Long> deleteDiffIds = Lists.newArrayList();
        List<Long> updateDiffIds = Lists.newArrayList();
        snapshotCredentialDiffs.forEach(t -> {
            if (DateUtil.checkSameDay(credentialHeader.getEndDateTime(), t.getBusinessTime())) {
                deleteDiffIds.add(t.getId());
            } else {
                updateDiffIds.add(t.getId());
            }
        });
        if (CollectionUtils.isNotEmpty(deleteDiffIds)) {
            Lists.partition(deleteDiffIds, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(snapshotCredentialDiffMapper::deleteBatchIds);
        }
        if (CollectionUtils.isNotEmpty(updateDiffIds)) {
            Lists.partition(updateDiffIds, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(t -> snapshotCredentialDiffMapper
                    .updateEndDateTime(t, truncateDate(credentialHeader.getEndDateTime())));
        }
    }

    /**
     * 反加工原料入库，生成差异信息
     *
     * @param credentialHeader 原料入库数据
     */
    private void unManufactureMaterialInbound(CredentialHeader credentialHeader) {
        CredentialHeader completedCredential = credentialHeaderMapper
            .listByExeOrderSourceAndNoAndOrderOperateType(credentialHeader.getExeOrderSource(),
                credentialHeader.getExeOrderNo(), OrderOperateTypeEnum.FINISH_PRODUCTION.getCode());
        commonSaveCredentialDiff(credentialHeader, completedCredential);
    }

    /**
     * 库存异动，生成差异信息
     *
     * @param credentialHeader 原料入库数据
     */
    private void dealIsStockChange(CredentialHeader credentialHeader) {
        CredentialHeader completedCredential = credentialHeaderMapper
            .listByExeOrderSourceAndNoAndOrderOperateType(credentialHeader.getExeOrderSource(),
                credentialHeader.getExeOrderNo(), OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode());
        commonSaveCredentialDiff(credentialHeader, completedCredential);
    }

    private void commonSaveCredentialDiff(CredentialHeader credentialHeader, CredentialHeader completedCredential) {
        if (Objects.isNull(completedCredential)) {
            SnapshotCredentialDiff diff = convertToDiff(credentialHeader);
            diff.setEndDateTime(null);
            saveSnapshotCredentialDiff(diff);
            return;
        }
        if (Objects.isNull(completedCredential.getEndDateTime())) {
            throw new ImsBusinessException("完成入库时间归结日期为空");
        }
        if (DateUtil.checkSameDay(credentialHeader.getBusinessTime(), completedCredential.getEndDateTime())) {
            return;
        }
        SnapshotCredentialDiff diff = convertToDiff(credentialHeader);
        diff.setEndDateTime(DateUtils.truncate(completedCredential.getEndDateTime(), Calendar.DATE));
        saveSnapshotCredentialDiff(diff);
    }

    private SnapshotCredentialDiff convertToDiff(CredentialHeader credentialHeader) {
        SnapshotCredentialDiff diff = new SnapshotCredentialDiff();
        diff.setCredentialId(credentialHeader.getId());
        diff.setExeOrderNo(credentialHeader.getExeOrderNo());
        diff.setExeOrderSource(credentialHeader.getExeOrderSource());
        diff.setBusinessTime(truncateDate(credentialHeader.getBusinessTime()));
        diff.setEndDateTime(truncateDate(credentialHeader.getEndDateTime()));
        diff.setOrderNo(credentialHeader.getOrderNo());
        diff.setOrderSource(credentialHeader.getOrderSource());
        diff.setWarehouseId(credentialHeader.getWarehouseId());
        return diff;
    }

    private Date truncateDate(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return DateUtils.truncate(date, Calendar.DATE);
    }


    public void saveCompletedCredentialDiff(CredentialHeader header) {
        SnapshotCredentialDiff diff = convertToDiff(header);
        saveSnapshotCredentialDiff(diff);
    }


    private void saveSnapshotCredentialDiff(SnapshotCredentialDiff snapshotCredentialDiff) {
        Long id = snapshotCredentialDiffMapper.selectIdByCredentialId(snapshotCredentialDiff.getCredentialId());
        if (Objects.nonNull(id) && id > 0) {
            return;
        }
        snapshotCredentialDiffMapper.insert(snapshotCredentialDiff);
    }

}
