package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.credential.UsageDetailBo;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.calculator.TransferWaitAllocSplitUsageCalculator;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig.TransferUsagePriority;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.converter.usage.TransferIntransitInventoryBoConverter;
import com.ddmc.ims.converter.usage.UsageDetailBoConverter;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TransferInTransitUsageDetailOutManager extends TransferTransitUsageDetailManager {

    /**
     * 调拨在途用途出库明细拆分
     *
     * @param headerAndDetail headerAndDetail
     * @return result
     */
    public List<CredentialUseageDetail> handleTransferOutBound(CredentialHeader headerAndDetail) {
        if (CollectionUtils.isEmpty(headerAndDetail.getCredentialDetailList())) {
            return Collections.emptyList();
        }
        if (Objects.equals(headerAndDetail.getOrderType(), OrderTypeEnum.TRANSFER_OUTBOUND.getCode()) && Objects
            .equals(headerAndDetail.getOrderOperateType(), OrderOperateTypeEnum.FINISH_OUT_OF_STOCK.getCode())
            && CollectionUtils.isNotEmpty(headerAndDetail.getCredentialDetailList())) {
            return headerAndDetail.getCredentialDetailList().stream()
                .map(CredentialUsageDetailConverter::getSingleCredentialUsageDetail).collect(Collectors.toList());
        }

        List<CredentialUseageDetail> result = Lists
            .newArrayListWithCapacity(headerAndDetail.getCredentialDetailList().size());

        //构建出库用途凭证明细
        List<CredentialUseageDetail> credentialUsageDetails = getOutBoundTransferUsageDetail(headerAndDetail);
        result.addAll(credentialUsageDetails);

        //构建出库完成用途凭证明细
        List<CredentialUseageDetail> finishOutBoundUsageDetails = getClearPlanNotOutQtyDetail(headerAndDetail);
        if (CollectionUtils.isNotEmpty(finishOutBoundUsageDetails) && !Objects.equals(headerAndDetail.getOrderType(),
            OrderTypeEnum.TRANSFER_ORDER.getCode())) {
            log.warn("[handleTransferOutBound] 调拨在途清理非do单：{}", headerAndDetail.getId());
        }
        result.addAll(finishOutBoundUsageDetails);
        return result.stream().filter(t -> t.getQty().signum() != 0).collect(Collectors.toList());
    }

    /**
     * 构建出库用途凭证明细
     *
     * @param headerAndDetail headerAndDetail
     * @return result
     */
    private List<CredentialUseageDetail> getOutBoundTransferUsageDetail(CredentialHeader headerAndDetail) {
        //获取调拨出库凭证
        List<CredentialDetail> credentialDetails = getTransferOutCredentialDetails(headerAndDetail);
        //orderNo+orderSource+skuIds,调拨在途
        List<TransferIntransitInventoryBo> inventories = getTransferInTransitInventoryList(headerAndDetail);

        //出库按 四要素+sku+出库仓+出库货主+入库仓+入库逻辑库位+入库货主 分组
        Map<TransferInTransitItem, List<TransferIntransitInventoryBo>> transferMap = inventories.stream()
            .collect(Collectors.groupingBy(TransferInTransitItemConverter::convertToOutStoreCondition));

        //四要素+sku+出库仓+出库货主+入库仓+入库逻辑库位+入库货主,凭证map
        Map<TransferInTransitItem, List<CredentialDetail>> credentialDetailMap = credentialDetails.stream()
            .collect(Collectors.groupingBy(t -> TransferInTransitItemConverter.convertByCredential(t, true)));

        //获取用途明细拆分结果
        List<UsageDetailBo> transferUsageDetails = getOutBoundTransferUsageDetail(transferMap, credentialDetailMap);
        return UsageDetailBoConverter.convertToCredentialUsageDetail(transferUsageDetails);
    }


    /**
     * 按调拨在途场景用途排序后依次扣减计划未发量，直到扣减至0。最后将拆分结果返回
     *
     * @param skuInventoryMap skuInventoryMap
     * @param skuCredentialDetailMap skuCredentialDetailMap
     * @return result
     */
    public List<UsageDetailBo> getOutBoundTransferUsageDetail(
        Map<TransferInTransitItem, List<TransferIntransitInventoryBo>> skuInventoryMap,
        Map<TransferInTransitItem, List<CredentialDetail>> skuCredentialDetailMap) {
        List<UsageDetailBo> usageDetailBos = Lists.newArrayListWithExpectedSize(skuCredentialDetailMap.size());
        Map<String, Integer> transferSceneResponseMap = imsConfigRemoteManager.getTransferSceneResponseMap();
        skuCredentialDetailMap.forEach((condition, deductionDetails) -> {
            List<TransferUsagePriority> transferUsagePriority = localParamConfig
                .getTransferOutUsagePriority(deductionDetails.get(0).getToUsageCode());
            if (CollectionUtils.isNotEmpty(transferUsagePriority) && transferSceneResponseMap
                .containsKey(deductionDetails.get(0).getUsageCode()) && Objects.equals(deductionDetails.get(0).getUsageCode(), "EC")
                && Objects.equals(deductionDetails.get(0).getToUsageCode(), "EC")) {
                TransferUsagePriority priority = new TransferUsagePriority();
                priority.setFromUsageCode(deductionDetails.get(0).getUsageCode());
                priority.setToUsageCode(deductionDetails.get(0).getToUsageCode());
                transferUsagePriority = Lists.newArrayList(priority);
            } else if (CollectionUtils.isEmpty(transferUsagePriority)
                && deductionDetails.get(0).getToUsageList().size() == 1) {
                TransferUsagePriority priority = new TransferUsagePriority();
                priority.setFromUsageCode(deductionDetails.get(0).getToUsageCode());
                priority.setToUsageCode(deductionDetails.get(0).getToUsageCode());
                transferUsagePriority = Lists.newArrayList(priority);
            } else if (CollectionUtils.isEmpty(transferUsagePriority)) {
                log.error("[getOutBoundTransferUsageDetail]存在多个来源用途，没有优先级配置->{}", JsonUtil.toJson(deductionDetails));
                throw new ImsBusinessException("[getOutBoundTransferUsageDetail]存在多个来源用途，没有优先级配");
            }

            Map<String, Integer> usagePriority = Maps.newHashMapWithExpectedSize(transferUsagePriority.size());
            for (int i = 0; i < transferUsagePriority.size(); i++) {
                TransferUsagePriority priority = transferUsagePriority.get(i);
                usagePriority.put(priority.getFromUsageCode() + "," + priority.getToUsageCode(), i);
            }

            List<TransferIntransitInventoryBo> transit = skuInventoryMap.get(condition);
            //如果为空，按默认处理
            if (CollectionUtils.isEmpty(transit)) {
                log.warn("[TransferInTransitUsageDetailOutManager] 调拨在途不存在:{}",
                    JsonUtil.toJson(deductionDetails.get(0).getCredentialHeaderId()));

                for (CredentialDetail detail : deductionDetails) {
                    ImmutablePair<String, String> defaultPair = getOutboundUsageCode(detail, transferSceneResponseMap);
                    String fromUsageCode = defaultPair.getLeft();
                    String toUsageCode = defaultPair.getRight();

                    usageDetailBos.add(UsageDetailBoConverter
                        .getModifyOutInventoryAvailableCredentialUsageDetail(detail, fromUsageCode, detail.getQty(),
                            CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));

                    if (transferSceneResponseMap.containsKey(fromUsageCode) && transferSceneResponseMap.containsKey(toUsageCode)) {
                        usageDetailBos.add(UsageDetailBoConverter
                            .getDefaultOutStoreCredentialUsageDetail(detail, detail.getQty(), fromUsageCode, toUsageCode,
                                CommandTypeEnum.TRANSFER_IN_TRANSIT_OUT));
                    } else {
                        usageDetailBos.add(UsageDetailBoConverter
                            .getDefaultOutStoreCredentialUsageDetail(detail, detail.getQty(), toUsageCode, toUsageCode,
                                CommandTypeEnum.TRANSFER_IN_TRANSIT_OUT));
                    }

                }
                return;
            }

            //先按出库用途排序，再按入库用途排序
            List<TransferIntransitInventoryBo> intransitInventoryBos = transit.stream()
                .filter(t -> usagePriority.containsKey(t.getUsageCode() + "," + t.getToUsageCode()))
                .sorted(Comparator.comparing(t -> usagePriority.get(t.getUsageCode() + "," + t.getToUsageCode())))
                .collect(Collectors.toList());

            TransferWaitAllocSplitUsageCalculator calculator = new TransferWaitAllocSplitUsageCalculator(
                localParamConfig, transferSceneResponseMap);
            //在途用途明细拆分
            List<UsageDetailBo> transferUsageDetails = calculator
                .deductSkuTransferInventory(transferUsagePriority, deductionDetails, intransitInventoryBos);
            usageDetailBos.addAll(transferUsageDetails);
        });

        return usageDetailBos.stream().filter(t -> t.getQty().signum() != 0).collect(Collectors.toList());
    }


    /**
     * 构建出库完成凭证明细
     *
     * @param headerAndDetail headerAndDetail
     * @return result
     */
    public List<CredentialUseageDetail> getClearPlanNotOutQtyDetail(CredentialHeader headerAndDetail) {
        if (CollectionUtils.isEmpty(headerAndDetail.getCredentialDetailList())) {
            return Collections.emptyList();
        }

        //获取调拨出库完成凭证
        List<CredentialDetail> credentialDetails = getTransferFinishOutCredentialDetails(headerAndDetail);
        List<CredentialUseageDetail> result = Lists.newArrayListWithExpectedSize(credentialDetails.size());
        credentialDetails.forEach(t -> result.add(TransferIntransitInventoryBoConverter
            .convertToUsageDetailByCredential(t, t.getQty(), headerAndDetail.getId())));
        result.forEach(t -> t.setCommandType(CommandTypeEnum.CLEAN_TRANSFER_IN_TRANSIT.getCode()));
        return result;
    }


    /**
     * 获取调拨出库凭证
     *
     * @param headerAndDetail headerAndDetail
     * @return result
     */
    private List<CredentialDetail> getTransferOutCredentialDetails(CredentialHeader headerAndDetail) {
        return headerAndDetail.getCredentialDetailList().stream().filter(
            t -> Objects.equals(t.getCommandType(), CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT.getCode()) ||
                Objects.equals(t.getCommandType(), CommandTypeEnum.TRANSFER_IN_TRANSIT_OUT.getCode()))
            .collect(Collectors.toList());
    }


    /**
     * 获取调拨出库完成凭证
     *
     * @param headerAndDetail headerAndDetail
     * @return result
     */
    private List<CredentialDetail> getTransferFinishOutCredentialDetails(CredentialHeader headerAndDetail) {
        return headerAndDetail.getCredentialDetailList().stream().filter(
            t -> Objects.equals(t.getCommandType(), CommandTypeEnum.CLEAN_TRANSFER_IN_TRANSIT.getCode()))
            .collect(Collectors.toList());
    }


}
