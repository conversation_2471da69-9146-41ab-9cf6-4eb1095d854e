package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.bo.credential.WarehouseLotInventoryBo;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuLotInventoryMapper;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WarehouseSkuLotInventoryManager {

    @Resource
    private WarehouseSkuLotInventoryMapper warehouseSkuLotInventoryMapper;
    @Resource
    private LocalParamConfig localParamConfig;

    public String getLotId(Long warehouseId, Long skuId) {
        List<String> codes = localParamConfig.filterProcessingLocationSortCodes();
        List<String> lotIdList = warehouseSkuLotInventoryMapper.getLotIdByWarehouseIdAndSkuIdAndLogicCode(warehouseId,
            skuId, codes);
        if (CollectionUtils.isNotEmpty(lotIdList)) {
            Collections.sort(lotIdList);
            return lotIdList.get(lotIdList.size() - 1);
        }
        log.warn("[根据仓品查询不到批次] warehouseId  {} skuId {}", warehouseId, skuId);
        return StringUtils.EMPTY;
    }

    public List<WarehouseLotInventoryBo> getWarehouseLotInventoryBo(List<Long> skuIds,
        LogicInventoryLocation location) {
        List<WarehouseSkuLotInventory> warehouseSkuLotInventories = warehouseSkuLotInventoryMapper.selectBySkuIdInAndLogicInventoryLocation(
            skuIds, location);
        return warehouseSkuLotInventories.stream()
            .map(t -> new WarehouseLotInventoryBo(t.getSkuId(), t.getLotId(), t.getFreeQty()))
            .collect(Collectors.toList());
    }

}
