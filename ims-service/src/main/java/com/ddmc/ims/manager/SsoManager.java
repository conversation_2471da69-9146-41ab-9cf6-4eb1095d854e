package com.ddmc.ims.manager;


import com.ddmc.duc.vo.BaseResponseVo;
import com.ddmc.duc.vo.user.BaseMeta;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsRemoteInvocationException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.rpc.sso.SsoApiNewClient;
import com.ddmc.ims.rpc.sso.dto.SsoCity;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SsoManager {

    public static final String CACHE_KEY_SSO_CITY = "SSO_CITY";


    @Resource
    private SsoApiNewClient ssoApiNewClient;


    /**
     * 调用SSO-查询城市Map
     *
     * @return 返回数据 城市id -> 城市信息
     */
    public Map<String, SsoCity> ssoCityMap() {
        return listCityList().stream().collect(Collectors.toMap(SsoCity::getId, Function.identity(), (n1, n2) -> n2));
    }

    /**
     * 调用SSO-查询城市Map
     *
     * @return 返回数据 城市id -> 城市名称
     */
    public Map<String, String> cityMap() {
        return listCityList().stream().collect(Collectors.toMap(SsoCity::getId, SsoCity::getName));
    }


    /**
     * 获取新sso城市列表
     *
     * @return 返回数据
     */
    public List<SsoCity> listCityList() {

        List<SsoCity> responseVo;
        responseVo = CacheConfig.SSO_CITY_LIST.getIfPresent(CACHE_KEY_SSO_CITY);
        if (CollectionUtils.isNotEmpty(responseVo)) {
            log.info("SsoManager.listCityList 数据命中缓存:{}", JsonUtil.toJson(responseVo));
            return responseVo;
        }
        responseVo = getAllSsoCityList().stream().map(SsoManager::buildSsoCity).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(responseVo)) {
            log.info("SsoManager.listCityList 设置缓存:{}", JsonUtil.toJson(responseVo));
            CacheConfig.SSO_CITY_LIST.put(CACHE_KEY_SSO_CITY, responseVo);
        }
        return responseVo;
    }

    private static SsoCity buildSsoCity(BaseMeta baseMeta) {
        SsoCity ssoCity = new SsoCity();
        ssoCity.setId(baseMeta.getId());
        ssoCity.setName(baseMeta.getName());
        return ssoCity;
    }


    /**
     * 获取新sso-服务城市列表数据
     *
     * @return 返回数据
     */
    private List<BaseMeta> getAllSsoCityList() {
        BaseResponseVo<List<BaseMeta>> responseVo = ssoApiNewClient.getCity();
        if (!responseVo.isSuccess()) {
            throw new ImsRemoteInvocationException(CommonErrorCode.DUC_SERVER_ERROR, responseVo.getMessage());
        }
        return responseVo.getData();
    }

}
