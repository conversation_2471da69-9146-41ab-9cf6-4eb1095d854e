package com.ddmc.ims.manager;

import com.ddmc.duc.resp.ProductGroupResp;
import com.ddmc.duc.vo.BaseResponseVo;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.rpc.sso.SsoApiNewClient;
import com.ddmc.ims.rpc.sso.dto.CategoryGroupVO;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CategoryGroupRemoteManager {

    @Resource
    private SsoApiNewClient ssoApiNewClient;

    /**
     * 查询负责组
     *
     * @param categoryGroupIds 负责组id集合
     * @return 负责组map, key->负责组id
     */
    public Map<String, CategoryGroupVO> getCategoryGroups(Set<String> categoryGroupIds) {
        Map<String, CategoryGroupVO> categoryGroupMap = getCategoryGroupsFromCache(
            categoryGroupIds);
        if (categoryGroupMap.size() < categoryGroupIds.size()) {
            loadFromRemote();
        }
        return getCategoryGroupsFromCache(categoryGroupIds);
    }

    private Map<String, CategoryGroupVO> getCategoryGroupsFromCache(Set<String> categoryGroupIds) {
        return categoryGroupIds.stream()
            .map(CacheConfig.CATEGORY_GROUP_CACHE::getIfPresent).filter(
                Objects::nonNull)
            .collect(Collectors.toMap(CategoryGroupVO::getId, Function.identity()));
    }

    private void loadFromRemote() {
        BaseResponseVo<List<ProductGroupResp>> productGroupList = ssoApiNewClient.getProductGroupList();
        if (!productGroupList.isSuccess() || CollectionUtils.isEmpty(productGroupList.getData())) {
            return;
        }
        productGroupList.getData().stream().map(x -> new CategoryGroupVO(x.getMongoId(), x.getName()))
            .forEach(t -> CacheConfig.CATEGORY_GROUP_CACHE.put(t.getId(), t));
    }
}
