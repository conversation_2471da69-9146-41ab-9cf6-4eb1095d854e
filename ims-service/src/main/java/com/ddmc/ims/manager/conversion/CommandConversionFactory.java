package com.ddmc.ims.manager.conversion;

import com.ddmc.ims.bo.command.OutBoundInventoryCommand;
import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.command.WarehouseInventoryCommand;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.IntegerEnumInterface;
import com.ddmc.ims.common.enums.ims.DeliveryModeEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
public class CommandConversionFactory {

    private CommandConversionFactory() {

    }


    private static final Map<OrderOperateTypeEnum, List<CommandTypeEnum>> ORDER_OPERATOR_COMMAND_MAP = new ConcurrentHashMap<>();


    static {
        //制造，完成制造单--->变更可用
        ORDER_OPERATOR_COMMAND_MAP
            .put(OrderOperateTypeEnum.FINISH_PRODUCTION,
                Collections.singletonList(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));
    }

    static {
        //入库，完成入库--->变更可用
        ORDER_OPERATOR_COMMAND_MAP
            .put(OrderOperateTypeEnum.FINISH_IN_OF_STOCK,
                Collections.singletonList(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));
    }

    static {
        //出库，申请出库--->等级占用
        ORDER_OPERATOR_COMMAND_MAP
            .put(OrderOperateTypeEnum.APPLY_OUT_OF_STOCK,
                Lists.newArrayList(CommandTypeEnum.EMPTY_OPERATE));
    }

    static {
        //出库，部分出库--->调拨在途
        ORDER_OPERATOR_COMMAND_MAP
            .put(OrderOperateTypeEnum.OUT_OF_STOCK,
                Lists.newArrayList(CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT));
    }


    static {
        //出库，完成出库--->释放占用
        ORDER_OPERATOR_COMMAND_MAP
            .put(OrderOperateTypeEnum.FINISH_OUT_OF_STOCK,
                Lists.newArrayList(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));
    }

    static {
        //调整，执行调整--->变更可用
        ORDER_OPERATOR_COMMAND_MAP.put(OrderOperateTypeEnum.ADJUSTMENT,
            Collections.singletonList(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));
    }


    static {
        //入库，执行调整--->变更可用
        ORDER_OPERATOR_COMMAND_MAP.put(OrderOperateTypeEnum.ADJUSTMENT,
            Collections.singletonList(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));
    }

    static {
        //入库，入库--->变更可用
        ORDER_OPERATOR_COMMAND_MAP.put(OrderOperateTypeEnum.IN_STOCK,
            Collections.singletonList(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE));
    }



    static {
        //取消出库
        ORDER_OPERATOR_COMMAND_MAP.put(OrderOperateTypeEnum.CANCEL_OUT_APPLY,
            Collections.singletonList(CommandTypeEnum.EMPTY_OPERATE));
    }

    static {
        //修改计划出库时间
        ORDER_OPERATOR_COMMAND_MAP.put(OrderOperateTypeEnum.MODIFY_EXPECT_OUT_TIME,
            Collections.singletonList(CommandTypeEnum.EMPTY_OPERATE));
    }



    public static List<? extends SkuInventoryCommand> getSkuInventoryCommand(
        CredentialHeader credentialHeader, List<CommandTypeEnum> commandTypes) {
        List<CredentialUseageDetail> credentialDetails = credentialHeader.getCredentialUseageDetailList();
        return getSkuInventoryCommand(credentialHeader, credentialDetails, commandTypes);
    }


    public static List<? extends SkuInventoryCommand> getSkuInventoryCommand(
        CredentialHeader credentialHeader, List<CredentialUseageDetail> credentialDetails,
        List<CommandTypeEnum> commandTypes) {
        return commandTypes.stream()
            .map(t -> getCommandList(credentialHeader, credentialDetails, t)).flatMap(List::stream)
            .collect(Collectors.toList());
    }



    private static List<? extends SkuInventoryCommand> getCommandList(
        CredentialHeader credentialHeader, List<CredentialUseageDetail> credentialDetails,
        CommandTypeEnum commandTypeEnum) {
        switch (commandTypeEnum) {
            case TRANSFER_INVENTORY:
            case MODIFY_INVENTORY_AVAILABLE:
                return warehouseInventoryCommandFunction(credentialHeader, credentialDetails, commandTypeEnum);
            case MODIFY_PURCHASE_ARRIVAL_TIME:
                return modifyPurchaseArrivalTimeFunction(credentialHeader, commandTypeEnum);
            case CLEAN_PURCHASE_IN_TRANSIT:
            case CLEAN_BOOKED_PURCHASE_IN_TRANSIT:
                return cleanPurchaseCommandFunction(credentialHeader,credentialDetails, commandTypeEnum);
            case PUBLISH_PURCHASE_IN_TRANSIT:
            case PURCHASE_IN_TRANSIT_TO_AVAILABLE:
                return purchaseIntransitInventoryCommandFunction(credentialHeader, credentialDetails, commandTypeEnum);
            case CLEAN_TRANSFER_IN_TRANSIT:
            case PUBLISH_TRANSFER_IN_TRANSIT:
            case AVAILABLE_TO_TRANSFER_IN_TRANSIT:
            case TRANSFER_IN_TRANSIT_TO_AVAILABLE:
            case TRANSFER_REJECT:
            case TRANSFER_IN_TRANSIT_OUT:
            case TRANSFER_IN_TRANSIT_IN:
                return transferIntransitInventoryCommandFunction(credentialHeader, credentialDetails, commandTypeEnum);
            case EMPTY_OPERATE:
                return Collections.emptyList();
            case PUBLISH_ALLOC:
            case OUTBOUND_INVENTORY:
            case CLEAN_ALLOC:
                return outBoundInventoryCommandFunction(credentialHeader, credentialDetails, commandTypeEnum);
            default:
                throw new ImsBusinessException("不支持的操作类型");
        }
    }


    private static List<? extends SkuInventoryCommand> warehouseInventoryCommandFunction(
        CredentialHeader credentialHeader, List<CredentialUseageDetail> credentialDetails,
        CommandTypeEnum commandType) {
        return credentialDetails.stream().map(t -> {
            LogicInventoryLocation fromLocation = new LogicInventoryLocation(t.getFromWarehouseId(),
                t.getFromCargoOwnerId(), t.getFromLogicLocationCode());
            LogicInventoryLocation toLocation = new LogicInventoryLocation(t.getToWarehouseId(), t.getToCargoOwnerId(),
                t.getToLogicLocationCode());
            //在库库存出库变更，数量取反
            boolean isOut = OrderOperateTypeEnum.OUT_OF_STOCK.getCode().equals(credentialHeader.getOrderOperateType())
                || OrderOperateTypeEnum.FINISH_OUT_OF_STOCK.getCode().equals(credentialHeader.getOrderOperateType());
            BigDecimal qty =
                commandType.equals(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE) && isOut ? t.getQty().negate()
                    : t.getQty();
            return WarehouseInventoryCommand
                .builder()
                .fromLocation(fromLocation)
                .toLocation(toLocation)
                .commandType(commandType)
                .skuId(t.getSkuId())
                .lotId(t.getLotId())
                .qty(qty)
                .fromInventoryStatus(t.getInventoryStatus())
                .toInventoryStatus(t.getToInventoryStatus())
                .usageCode(t.getUsageCode())
                .toUsageCode(t.getToUsageCode())
                .build();
        }).collect(Collectors.toList());
    }



    private static List<? extends SkuInventoryCommand> outBoundInventoryCommandFunction(
        CredentialHeader credentialHeader, List<CredentialUseageDetail> credentialDetails,
        CommandTypeEnum commandType) {

        if (CollectionUtils.isEmpty(credentialDetails)) {
            return Collections.singletonList(OutBoundInventoryCommand
                .builder()
                .commandType(commandType)
                .orderSource(credentialHeader.getOrderSource())
                .orderNo(credentialHeader.getOrderNo())
                .expectOutTime(credentialHeader.getExpectOutTime())
                .exeOrderNo(credentialHeader.getExeOrderNo())
                .exeOrderSource(credentialHeader.getExeOrderSource())
                .build());
        }
        return credentialDetails.stream().map(t -> {
            LogicInventoryLocation fromLocation = new LogicInventoryLocation(t.getFromWarehouseId(),
                t.getFromCargoOwnerId(), t.getFromLogicLocationCode());
            LogicInventoryLocation toLocation = new LogicInventoryLocation(t.getToWarehouseId(), t.getToCargoOwnerId(),
                t.getToLogicLocationCode());
            //在库库存出库变更，数量取反
            boolean isOut = OrderOperateTypeEnum.OUT_OF_STOCK.getCode().equals(credentialHeader.getOrderOperateType())
                || OrderOperateTypeEnum.FINISH_OUT_OF_STOCK.getCode().equals(credentialHeader.getOrderOperateType());
            BigDecimal qty =
                commandType.equals(CommandTypeEnum.OUTBOUND_INVENTORY) && isOut ? t.getQty().negate() : t.getQty();
            return OutBoundInventoryCommand
                .builder()
                .fromLocation(fromLocation)
                .toLocation(toLocation)
                .commandType(commandType)
                .skuId(t.getSkuId())
                .lotId(t.getLotId())
                .qty(qty)
                .fromInventoryStatus(t.getInventoryStatus())
                .toInventoryStatus(t.getToInventoryStatus())
                .usageCode(t.getUsageCode())
                .toUsageCode(t.getToUsageCode())
                .orderNo(credentialHeader.getOrderNo())
                .orderSource(credentialHeader.getOrderSource())
                .orderType(credentialHeader.getOrderType())
                .expectOutTime(credentialHeader.getExpectOutTime())
                .exeOrderNo(credentialHeader.getExeOrderNo())
                .exeOrderSource(credentialHeader.getExeOrderSource())
                .build();
        }).collect(Collectors.toList());
    }


    private static List<? extends SkuInventoryCommand> transferIntransitInventoryCommandFunction(
        CredentialHeader credentialHeader, List<CredentialUseageDetail> credentialDetails,
        CommandTypeEnum commandType) {

        if (Objects.equals(credentialHeader.getOrderType(), OrderTypeEnum.TRANSFER_OUTBOUND.getCode()) && Objects
            .equals(credentialHeader.getOrderOperateType(), OrderOperateTypeEnum.FINISH_OUT_OF_STOCK.getCode())
            && CollectionUtils.isEmpty(credentialDetails)) {
            return Collections.singletonList(TransferIntransitInventoryCommand
                .builder()
                .commandType(commandType)
                .orderOperateType(credentialHeader.getOrderOperateType())
                .orderSource(credentialHeader.getOrderSource())
                .orderNo(credentialHeader.getOrderNo())
                .deliveryMode(IntegerEnumInterface.get(credentialHeader.getDeliveryMode(), DeliveryModeEnum.class))
                .expectOutTime(credentialHeader.getExpectOutTime())
                .expectInTime(credentialHeader.getExpectInTime())
                .build());
        }

        return credentialDetails.stream().map(t -> {
            LogicInventoryLocation fromLocation = new LogicInventoryLocation(t.getFromWarehouseId(),
                t.getFromCargoOwnerId(),
                t.getFromLogicLocationCode());
            LogicInventoryLocation toLocation = new LogicInventoryLocation(t.getToWarehouseId(), t.getToCargoOwnerId(),
                t.getToLogicLocationCode());
            return TransferIntransitInventoryCommand
                .builder()
                .fromLocation(fromLocation)
                .fromInventoryStatus(t.getInventoryStatus())
                .toLocation(toLocation)
                .toInventoryStatus(t.getToInventoryStatus())
                .usageCode(t.getUsageCode())
                .toUsageCode(t.getToUsageCode())
                .orderTag(t.getOrderTag())
                .commandType(commandType)
                .skuId(t.getSkuId())
                .orderOperateType(credentialHeader.getOrderOperateType())
                .orderSource(credentialHeader.getOrderSource())
                .orderNo(credentialHeader.getOrderNo())
                .exeOrderNo(credentialHeader.getExeOrderNo())
                .qty(t.getQty())
                .deliveryMode(IntegerEnumInterface.get(credentialHeader.getDeliveryMode(), DeliveryModeEnum.class))
                .expectOutTime(credentialHeader.getExpectOutTime())
                .expectInTime(credentialHeader.getExpectInTime())
                .lotId(t.getLotId())
                .todaySale(t.getTodaySale())
                .exeOrderSource(credentialHeader.getExeOrderSource())
                .businessTime(credentialHeader.getBusinessTime())
                .build();
        }).collect(Collectors.toList());
    }

    private static List<? extends SkuInventoryCommand> cleanPurchaseCommandFunction(CredentialHeader credentialHeader,
        List<CredentialUseageDetail> credentialDetails,CommandTypeEnum commandType) {
        if (CollectionUtils.isNotEmpty(credentialDetails)) {
            return credentialDetails.stream().map(t -> {
                LogicInventoryLocation fromLocation = new LogicInventoryLocation(t.getFromWarehouseId(),
                    t.getFromCargoOwnerId(),
                    t.getFromLogicLocationCode());
                LogicInventoryLocation toLocation = new LogicInventoryLocation(t.getToWarehouseId(), t.getToCargoOwnerId(),
                    t.getToLogicLocationCode());
                return PurchaseIntransitInventoryCommand
                    .builder()
                    .fromLocation(fromLocation)
                    .toLocation(toLocation)
                    .commandType(commandType)
                    .skuId(t.getSkuId())
                    .orderSource(credentialHeader.getOrderSource())
                    .orderNo(credentialHeader.getOrderNo())
                    .qty(t.getQty())
                    .expectArriveTime(credentialHeader.getExpectArriveTime())
                    .lotId(t.getLotId())
                    .usageCode(t.getUsageCode())
                    .orderTag(t.getOrderTag())
                    .build();
            }).collect(Collectors.toList());
        } else {
            if (!Objects.equals(credentialHeader.getOrderOperateType(), OrderOperateTypeEnum.CANCEL_IN_STOCK_APPLY.getCode())) {
                log.warn("采购在途清理无skuId{}", JsonUtil.toJson(credentialHeader));
            }
        }

        return Collections.singletonList(PurchaseIntransitInventoryCommand
            .builder()
            .orderNo(credentialHeader.getOrderNo())
            .orderSource(credentialHeader.getOrderSource())
            .commandType(commandType)
            .build());
    }

    private static List<? extends SkuInventoryCommand> modifyPurchaseArrivalTimeFunction(
        CredentialHeader credentialHeader, CommandTypeEnum commandType) {
        return Collections.singletonList(PurchaseIntransitInventoryCommand
            .builder()
            .orderNo(credentialHeader.getOrderNo())
            .orderSource(credentialHeader.getOrderSource())
            .commandType(commandType)
            .expectArriveTime(credentialHeader.getExpectArriveTime())
            .build());
    }


    private static List<? extends SkuInventoryCommand> purchaseIntransitInventoryCommandFunction(
        CredentialHeader credentialHeader, List<CredentialUseageDetail> credentialDetails,
        CommandTypeEnum commandType) {
        return credentialDetails.stream().map(t -> {
            LogicInventoryLocation fromLocation = new LogicInventoryLocation(t.getFromWarehouseId(),
                t.getFromCargoOwnerId(),
                t.getFromLogicLocationCode());
            LogicInventoryLocation toLocation = new LogicInventoryLocation(t.getToWarehouseId(), t.getToCargoOwnerId(),
                t.getToLogicLocationCode());
            return PurchaseIntransitInventoryCommand
                .builder()
                .fromLocation(fromLocation)
                .toLocation(toLocation)
                .commandType(commandType)
                .skuId(t.getSkuId())
                .orderSource(credentialHeader.getOrderSource())
                .orderNo(credentialHeader.getOrderNo())
                .qty(t.getQty())
                .expectArriveTime(credentialHeader.getExpectArriveTime())
                .lotId(t.getLotId())
                .usageCode(t.getUsageCode())
                .orderTag(t.getOrderTag())
                .build();
        }).collect(Collectors.toList());
    }



    public static List<CommandTypeEnum> getCommandTypeEnum(Integer orderOperateType){
        OrderOperateTypeEnum orderOperateTypeEnum = OrderOperateTypeEnum.fromCode(orderOperateType);
        return ORDER_OPERATOR_COMMAND_MAP.get(orderOperateTypeEnum);
    }



}
