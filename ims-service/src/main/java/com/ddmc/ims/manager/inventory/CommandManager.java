package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.command.CommandHandleFactory;
import com.ddmc.ims.command.InventoryCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.dto.InOutNumDto;
import com.ddmc.ims.service.credential.InventoryCredentialService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandManager {


    @Resource
    private InventoryCredentialService inventoryCredentialService;

    @Resource
    private CommandHandleFactory commandHandleFactory;

    public List<CommandInventoryNumDto> getCommandInventoryNumList(
        List<CredentialHeader> credentialHeaderAndDetails) {
        List<? extends SkuInventoryCommand> skuInventoryCommandList = credentialHeaderAndDetails.stream()
            .map(t -> inventoryCredentialService.getSkuInventoryCommand(t)).flatMap(List::stream).collect(
                Collectors.toList());
        Map<CommandTypeEnum, ? extends List<? extends SkuInventoryCommand>> skuInventoryCommandMap = skuInventoryCommandList
            .stream().collect(Collectors.groupingBy(SkuInventoryCommand::getCommandType));
        List<CommandInventoryNumDto> commandInventoryNumList = Lists
            .newArrayListWithExpectedSize(skuInventoryCommandList.size());
        skuInventoryCommandMap.forEach((k, v) -> {
            InventoryCommandHandle inventoryCommandHandle = commandHandleFactory
                .getCommandHandle(k.getCommandHandleClassName());
            commandInventoryNumList.addAll(inventoryCommandHandle.getCommandInventoryNum(v));
        });
        return commandInventoryNumList;
    }

    public List<InOutNumDto> getFmsInOutNumDto(
        List<CredentialHeader> credentialHeaderAndDetails) {
        return credentialHeaderAndDetails.stream()
            .filter(c -> !OrderTypeEnum.POSITIVE_MANUFACTURE.getCode().equals(c.getOrderType())
                && !OrderTypeEnum.LOT_ADJUST.getCode().equals(c.getOrderType())
                && !OrderTypeEnum.LOT_STOCK_CHANGE.getCode().equals(c.getOrderType())).map(credential -> {
                List<? extends SkuInventoryCommand> skuInventoryCommandList = inventoryCredentialService.getSkuInventoryCommand(
                    credential);
                Map<CommandTypeEnum, ? extends List<? extends SkuInventoryCommand>> skuInventoryCommandMap = skuInventoryCommandList
                    .stream().collect(Collectors.groupingBy(SkuInventoryCommand::getCommandType));
                List<CommandInventoryNumDto> commandInventoryNumDtos = skuInventoryCommandMap.entrySet().stream()
                    .map(entry -> {
                        InventoryCommandHandle inventoryCommandHandle = commandHandleFactory
                            .getCommandHandle(entry.getKey().getCommandHandleClassName());
                        List<CommandInventoryNumDto> commandInventoryNum = inventoryCommandHandle.getCommandInventoryNum(
                            entry.getValue());
                        return commandInventoryNum;
                    }).flatMap(List::stream).collect(Collectors.toList());
                //按品继续聚合
                Map<String, List<CommandInventoryNumDto>> warehouseSkuGroup = commandInventoryNumDtos.stream()
                    .collect(Collectors.groupingBy(t -> t.getLocation().getWarehouseId() + "_" + t.getSkuId()));

                return warehouseSkuGroup.values().stream().map(inventoryNumDtos -> {
                    BigDecimal totalInQty = inventoryNumDtos.stream()
                        .map(CommandInventoryNumDto::getInQty).reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal totalOutQty = inventoryNumDtos.stream()
                        .map(CommandInventoryNumDto::getOutQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                    InOutNumDto fmsInOutNumDto = new InOutNumDto();
                    fmsInOutNumDto.setOutQty(BigDecimal.ZERO.max(totalOutQty.subtract(totalInQty)));
                    fmsInOutNumDto.setInQty(BigDecimal.ZERO.max(totalInQty.subtract(totalOutQty)));
                    boolean orderType = OrderTypeEnum.BASIC_REVERSE_MANUFACTURE.getCode().equals(credential.getOrderType())
                        || OrderTypeEnum.BASIC_POSITIVE_MANUFACTURE.getCode().equals(credential.getOrderType());
                    if (orderType && OrderOperateTypeEnum.FINISH_PRODUCTION.getCode().equals(credential.getOrderOperateType())) {
                        log.info("[基础制造] 凭证{}", JsonUtil.toJson(credential));
                        fmsInOutNumDto.setOutQty(totalOutQty);
                        fmsInOutNumDto.setInQty(totalInQty);
                    }
                    fmsInOutNumDto.setSkuId(inventoryNumDtos.get(0).getSkuId());
                    fmsInOutNumDto.setWarehouseId(inventoryNumDtos.get(0).getLocation().getWarehouseId());
                    return fmsInOutNumDto;
                }).collect(Collectors.toList());
            }).flatMap(List::stream).collect(Collectors.toList());
    }
}
