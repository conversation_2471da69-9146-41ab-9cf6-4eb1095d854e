package com.ddmc.ims.manager;

import com.ddmc.fms.inv.dto.recon.InvDailySettlementDTO;
import com.ddmc.fms.inv.enums.DsStatus;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsRemoteInvocationException;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.rpc.fms.FmsBaseClient;
import com.ddmc.ims.rpc.fms.ImsInvReconClient;
import com.ddmc.ims.rpc.fms.dto.FmsBaseResponseVo;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsDto;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsDto.CorporationInfo;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * 调用fms
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class FmsManager {

    @Resource
    private FmsBaseClient fmsBaseClient;

    @Resource
    private ImsInvReconClient imsInvReconClient;

    private static final String CACHE_KEY_CORPORATION_INFO = "FMS_CORPORATION_INFO ";

    /**
     * 获取所有结算主体
     *
     * @return 结算主体信息
     */
    public List<CorporationInfo> getAllCorporations() {
        List<CorporationInfo> corporationInfoList = CacheConfig.FMS_CORPORATION_INFO
            .getIfPresent(CACHE_KEY_CORPORATION_INFO);
        if (CollectionUtils.isNotEmpty(corporationInfoList)) {
            return corporationInfoList;
        }
        FmsBaseResponseVo<GetCorporationsDto> fmsBaseResponseVo = fmsBaseClient
            .getCorporations(GetCorporationsRequest.builder().build());
        if (!fmsBaseResponseVo.isSuccess() || Objects.isNull(fmsBaseResponseVo.getData())) {
            log.info("[FmsManager] 调用fms接口异常 {}", JsonUtil.toJson(fmsBaseResponseVo));
            throw new ImsRemoteInvocationException(CommonErrorCode.FMS_SERVER_ERROR, fmsBaseResponseVo.getMsg());
        }
        corporationInfoList = fmsBaseResponseVo.getData().getRecords();
        if (CollectionUtils.isEmpty(corporationInfoList)) {
            return Collections.emptyList();
        }
        CacheConfig.FMS_CORPORATION_INFO.put(CACHE_KEY_CORPORATION_INFO, corporationInfoList);
        return corporationInfoList;
    }


    /**
     * 获取结算主体Map
     *
     * @return 返回数据 key 结算主体code->结算主体名称
     */
    public Map<String, String> mapCorporationNoException() {
        try {
            return mapCorporation();
        }catch (Exception e){
            log.error("[FmsManager] 接口异常",e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取结算主体Map
     *
     * @return 返回数据 key 结算主体code->结算主体名称
     */
    public Map<String, String> mapCorporation() {
        List<CorporationInfo> corporationInfoList = getAllCorporations();
        if (CollectionUtils.isEmpty(corporationInfoList)) {
            return Collections.emptyMap();
        }
        return corporationInfoList.stream()
            .collect(Collectors.toMap(CorporationInfo::getCorporationCode, CorporationInfo::getCorporationName));
    }

    public void dailySettlement(String dsDay){
        InvDailySettlementDTO dto = new InvDailySettlementDTO();
        dto.setDsDay(dsDay);
        dto.setStatus(DsStatus.RECON_READY.getValue());
        dto.setFinishTime(CurrentDateUtil.newDate());
        dto.setTargetSys("IMS");
        imsInvReconClient.dailySettlement(dto);
    }

}
