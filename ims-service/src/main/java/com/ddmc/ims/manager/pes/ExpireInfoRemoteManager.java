package com.ddmc.ims.manager.pes;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.rpc.pes.PesExpireInfoClient;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.pes.client.request.ExpireInfoBatchQueryRequest;
import com.ddmc.pes.client.request.ExpireInfoQueryRequest;
import com.ddmc.pes.client.response.ExpireInfoQueryResponse;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ExpireInfoRemoteManager {

    @Resource
    private PesExpireInfoClient pesExpireInfoClient;

    @Resource
    private AlertService alertService;

    @Resource
    private LocalParamService localParamService;

    private Set<Long> getNotExistSku() {
        return localParamService.getLongSetValue(LocalParamsConstants.NOT_EXISTS_SKU_IDS, Collections.emptySet());
    }

    /**
     * 批量获取效期数据
     */
    public List<ExpireInfoQueryResponse> batchGetExpireInfo(List<ExpireInfoQueryRequest> queryList) {
        if (CollectionUtils.isEmpty(queryList)) {
            return Collections.emptyList();
        }
        Set<Long> notExistSku = getNotExistSku();
        queryList = queryList.stream()
            .filter(t -> !notExistSku.contains(t.getSkuId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(queryList)) {
            return Collections.emptyList();
        }
        List<ExpireInfoQueryResponse> resultList = new ArrayList<>();
        Lists.partition(queryList, 20).forEach(querys -> {
            ExpireInfoBatchQueryRequest request = new ExpireInfoBatchQueryRequest();
            request.setQueryList(querys);
            ResponseBaseVo<List<ExpireInfoQueryResponse>> ret = pesExpireInfoClient.batchGetExpireInfo(request);
            if (!ret.isSuccess()) {
                alertService.alertWarning("【请求效期服务错误】调用批量获取失败",
                    "请求数据" + JsonUtil.toJson(request) + ", 返回数据{}" + JsonUtil.toJson(ret));
            } else if (CollectionUtils.isNotEmpty(ret.getData())) {
                resultList.addAll(ret.getData());
            }
        });
        return resultList;
    }


}
