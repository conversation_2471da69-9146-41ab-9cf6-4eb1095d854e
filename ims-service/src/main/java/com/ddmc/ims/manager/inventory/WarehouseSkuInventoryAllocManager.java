package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.bo.command.OutBoundInventoryCommand;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryAllocDetailMapper;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WarehouseSkuInventoryAllocManager {

    @Resource
    private WarehouseSkuInventoryAllocDetailMapper warehouseSkuInventoryAllocDetailMapper;

    /**
     * 插入或更新
     *
     * @param needInsert needInsert
     */
    public void insertOrUpdateAllocQty(List<WarehouseSkuInventoryAllocDetail> needInsert) {
        if (CollectionUtils.isNotEmpty(needInsert)) {
            Lists.partition(needInsert, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(warehouseSkuInventoryAllocDetailMapper::insertList);
        }
    }



    /**
     * 查询已存在的占用
     *
     * @param commands commands
     * @return result
     */
    public List<WarehouseSkuInventoryAllocDetail> getExistAllocList(List<OutBoundInventoryCommand> commands) {
        OutBoundInventoryCommand command = commands.get(0);
        List<Long> skuIds = commands.stream().map(OutBoundInventoryCommand::getSkuId).distinct()
            .collect(Collectors.toList());
        List<WarehouseSkuInventoryAllocDetail> result = Lists.newArrayListWithCapacity(skuIds.size());
        Lists.partition(skuIds, CommonConstants.BATCH_SELECT_DB_200)
            .forEach(subList -> result.addAll(warehouseSkuInventoryAllocDetailMapper
                .selectByOrderNoAndOrderSourceAndSkuIdIn(command.getOrderNo(), command.getOrderSource(), subList)));
        return result;
    }


    /**
     * 查询已存在的占用
     *
     * @param commands commands
     * @return result
     */
    public List<WarehouseSkuInventoryAllocDetail> getExistAllocMapByExeOrderNo(
        List<OutBoundInventoryCommand> commands) {
        OutBoundInventoryCommand command = commands.get(0);
        List<Long> skuIds = commands.stream().map(OutBoundInventoryCommand::getSkuId).distinct()
            .collect(Collectors.toList());
        List<WarehouseSkuInventoryAllocDetail> result = Lists.newArrayListWithCapacity(skuIds.size());
        Lists.partition(skuIds, CommonConstants.BATCH_SELECT_DB_200)
            .forEach(subList -> result.addAll(warehouseSkuInventoryAllocDetailMapper
                .selectByExeOrderNoAndExeOrderSourceAndSkuIdIn(command.getExeOrderNo(), subList)));
        return result;
    }


    /**
     * 根据命令扣减占用量
     *
     * @param existAllocMap existAllocMap
     */
    public void deleteAllocQty(List<WarehouseSkuInventoryAllocDetail> existAllocMap) {
        if (CollectionUtils.isNotEmpty(existAllocMap)) {
            List<Long> ids = existAllocMap.stream().map(WarehouseSkuInventoryAllocDetail::getId)
                .collect(Collectors.toList());
            warehouseSkuInventoryAllocDetailMapper.deleteBatchIds(ids);
        }
    }


    /**
     * 根据exeOrderNo删除
     *
     * @param exeOrderNo exeOrderNo
     */
    public void deleteAllocQty(String exeOrderNo) {
        warehouseSkuInventoryAllocDetailMapper.deleteByExeOrderNo(exeOrderNo);
    }


    /**
     * 根据orderNo与orderSource删除
     *
     * @param orderNo orderNo
     * @param orderSource orderSource
     * @param skuIds  skuIds
     */
    public void deleteAllocQty(String orderNo, String orderSource, List<Long> skuIds) {
        warehouseSkuInventoryAllocDetailMapper.deleteByOrderNoAndOrderSourceAndSkuIdIn(orderNo, orderSource, skuIds);
    }

}
