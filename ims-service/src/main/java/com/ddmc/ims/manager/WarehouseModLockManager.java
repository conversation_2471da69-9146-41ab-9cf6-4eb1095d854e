package com.ddmc.ims.manager;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.dal.mapper.ims.WarehouseModLockMapper;
import com.ddmc.ims.dal.model.ims.WarehouseModLock;
import com.google.common.collect.Lists;
import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
public class WarehouseModLockManager {

    @Resource
    private WarehouseModLockMapper warehouseModLockMapper;

    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void addWarehouseModLock(Long warehouseId) {
        List<WarehouseModLock> warehouseModLocks = warehouseModLockMapper.selectByWarehouseId(warehouseId);
        if (CollectionUtils.isNotEmpty(warehouseModLocks)) {
            return;
        }
        List<WarehouseModLock> insertWarehouseModLocks = Lists.newArrayListWithExpectedSize(
            CommonConstants.WAREHOUSE_MOD_LOCK_2048);
        for (int i = 0; i < CommonConstants.WAREHOUSE_MOD_LOCK_2048; i++) {
            WarehouseModLock warehouseModLock = new WarehouseModLock();
            warehouseModLock.setWarehouseId(warehouseId);
            warehouseModLock.setModNum(i);
            insertWarehouseModLocks.add(warehouseModLock);
        }
        warehouseModLockMapper.batchInsert(insertWarehouseModLocks);
    }

}
