package com.ddmc.ims.manager.lot;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.InventoryLotInfoConverter;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.manager.pes.ExpireInfoRemoteManager;
import com.ddmc.ims.rpc.lot.ImsLotClient;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.ddmc.lot.client.response.LotDTO;
import com.ddmc.lot.request.ScanLotReq;
import com.ddmc.pes.client.request.ExpireInfoQueryRequest;
import com.ddmc.pes.client.response.ExpireInfoQueryResponse;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LotManager {

    @Resource
    private AlertService alertService;

    @Value("${ims.scanLotLimit:1000}")
    private int scanLotLimit;

    @Resource
    private ExpireInfoRemoteManager expireInfoRemoteManager;

    @Resource
    private ImsLotClient imsLotClient;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private LocalParamConfig localParamConfig;


    public static final String LOT_REQUEST_ERROR = "【请求Lot服务错误】调用批量获取失败";


    public ImmutablePair<List<InventoryLotInfo>, Long> scanLot(Integer shardingIndex, Long lastPrimaryKeyId,
        boolean includeUnsalableDate) {
        ScanLotReq req = new ScanLotReq();
        req.setShardingIndex(shardingIndex);
        req.setStartId(lastPrimaryKeyId);
        req.setLimit(scanLotLimit);
        ResponseBaseVo<List<LotDTO>> responseBaseVo = imsLotClient.scanLot(req);
        if (!responseBaseVo.isSuccess()) {
            alertService.alertWarning(LOT_REQUEST_ERROR,
                "请求数据" + JsonUtil.toJson(req) + ", scanLot返回数据{}" + JsonUtil.toJson(responseBaseVo));
            throw new ImsBusinessException(CommonErrorCode.LOT_CALL_ERROR);
        }
        if (CollectionUtils.isEmpty(responseBaseVo.getData())) {
            return new ImmutablePair<>(Collections.emptyList(), Long.MAX_VALUE);
        }
        //查询效期
        List<InventoryLotInfo> inventoryLotInfos = responseBaseVo.getData().stream()
            .map(InventoryLotInfoConverter::converterTo)
            .collect(Collectors.toList());
        if (includeUnsalableDate) {
            addUnsalableDate(inventoryLotInfos);
        }
        return new ImmutablePair<>(inventoryLotInfos,
            responseBaseVo.getData().get(inventoryLotInfos.size() - 1).getPrimaryKeyId());
    }

    public List<InventoryLotInfo> batchGet(List<String> lotIds, boolean includeUnsalableDate) {
        ResponseBaseVo<Map<String, LotDTO>> responseBaseVo = imsLotClient.multiGet(lotIds);
        if (!responseBaseVo.isSuccess()) {
            alertService.alertWarning(LOT_REQUEST_ERROR,
                "请求数据" + JsonUtil.toJson(lotIds) + ", multiGet返回数据{}" + JsonUtil.toJson(responseBaseVo));
            throw new ImsBusinessException(CommonErrorCode.LOT_CALL_ERROR);
        }
        List<InventoryLotInfo> inventoryLotInfos = responseBaseVo.getData().values()
            .stream().map(InventoryLotInfoConverter::converterTo).collect(Collectors.toList());
        if (includeUnsalableDate) {
            addUnsalableDate(inventoryLotInfos);
        }
        return inventoryLotInfos;
    }

    public InventoryLotInfo get(String lotId, boolean includeUnsalableDate) {
        ResponseBaseVo<LotDTO> responseBaseVo = imsLotClient.get(lotId);
        if (!responseBaseVo.isSuccess()) {
            alertService.alertWarning(LOT_REQUEST_ERROR,
                "请求数据" + lotId + ", get返回数据{}" + JsonUtil.toJson(responseBaseVo));
            throw new ImsBusinessException(CommonErrorCode.LOT_CALL_ERROR);
        }
        InventoryLotInfo inventoryLotInfo = InventoryLotInfoConverter.converterTo(responseBaseVo.getData());
        if (includeUnsalableDate) {
            addUnsalableDate(Lists.newArrayList(inventoryLotInfo));
        }
        return inventoryLotInfo;
    }

    public void addUnsalableDate(List<InventoryLotInfo> inventoryLotInfos) {
        //查询效期限
        List<ExpireInfoQueryRequest> expireInfoQueryRequests = inventoryLotInfos.stream().map(t -> {
            ExpireInfoQueryRequest request = new ExpireInfoQueryRequest();
            request.setProductionDate(t.getManufactureDate());
            request.setSkuId(t.getSkuId());
            request.setWarehouseId(t.getWarehouseId());
            return request;
        }).collect(Collectors.toList());

        Map<String, ExpireInfoQueryResponse> expireInfoQueryResponseMap = expireInfoRemoteManager
            .batchGetExpireInfo(expireInfoQueryRequests).stream()
            .collect(Collectors.toMap(t -> t.getSkuId().toString() + t.getWarehouseId() + t.getProductionDate(),
                Function.identity(), (v1, v2) -> v1));

        inventoryLotInfos.forEach(t -> {
            ExpireInfoQueryResponse response = expireInfoQueryResponseMap
                .get(t.getSkuId().toString() + t.getWarehouseId() + t.getManufactureDate());
            if (Objects.isNull(response)) {
                return;
            }
            t.setUnsalableDate(response.getUnsaleDate());
        });
    }

}
