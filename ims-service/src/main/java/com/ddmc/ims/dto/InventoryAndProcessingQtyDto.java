package com.ddmc.ims.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class InventoryAndProcessingQtyDto {

    private BigDecimal inventoryQty;
    private BigDecimal processingQty;

    public InventoryAndProcessingQtyDto(BigDecimal inventoryQty, BigDecimal processingQty) {
        this.inventoryQty = inventoryQty;
        this.processingQty = processingQty;
    }
}
