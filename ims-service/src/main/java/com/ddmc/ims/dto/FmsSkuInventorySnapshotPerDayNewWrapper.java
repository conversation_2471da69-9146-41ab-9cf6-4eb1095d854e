package com.ddmc.ims.dto;

import com.ddmc.ims.dal.model.monitor.FmsSkuInventorySnapshotPerDay;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class FmsSkuInventorySnapshotPerDayNewWrapper {


    /**
     * 期末业务库存数据
     */
    private Map<Long, InventoryAndProcessingQtyDto> endSkuInventorySnapshotPerHours;
    /**
     * 差异数据财务少的部分
     */
    private Map<Long, CommandChangeQtyDto> todayFmsLessInventoryNumMap;
    /**
     * 差异数据财务多的部分
     */
    private Map<Long, CommandChangeQtyDto> todayFmsMoreInventoryMap;

    private Map<Long, CommandInOutQtyDto> skuInventorySnapshotInOutMap;
    /**
     * 业务库存和采购单据维度差异数据
     */
    private Map<Long, CredentialDiffQtyDto> fmsCredentialDiffQtyMap;

    /**
     * 在途数据
     */
    private Map<Long, BigDecimal> transferIntransitQtyMap;

    public FmsSkuInventorySnapshotPerDayNewWrapper(
        Map<Long, InventoryAndProcessingQtyDto> endSkuInventorySnapshotPerHours,
        Map<Long, CommandChangeQtyDto> fmsLessInventoryNumMap,
        Map<Long, CommandChangeQtyDto> fmsMoreInventoryMap,
        Map<Long, CommandInOutQtyDto> skuInventorySnapshotInOutMap,
        Map<Long, CredentialDiffQtyDto> fmsCredentialDiffQtyMap,
        Map<Long, BigDecimal> transferIntransitQtyMap) {
        this.endSkuInventorySnapshotPerHours = endSkuInventorySnapshotPerHours;
        this.todayFmsLessInventoryNumMap = fmsLessInventoryNumMap;
        this.todayFmsMoreInventoryMap = fmsMoreInventoryMap;
        this.skuInventorySnapshotInOutMap = skuInventorySnapshotInOutMap;
        this.fmsCredentialDiffQtyMap = fmsCredentialDiffQtyMap;
        this.transferIntransitQtyMap = transferIntransitQtyMap;
    }

    /**
     * FMS-END = FMS-START +（IMS-END - IMS-START）- DIFF(3.1)
     * DIFF(3.1) = （业务日期 = 3.1，但归结日期 ！=3.1）- (业务日期！=3.1，归结日期=3.1)
     */
    public List<FmsSkuInventorySnapshotPerDay> getEndFmsSkuInventorySnapshotList(
        Date snapshotDate,
        Long warehouseId) {
        Set<Long> skuIdSet = new HashSet<>(endSkuInventorySnapshotPerHours.keySet());
        skuIdSet.addAll(todayFmsLessInventoryNumMap.keySet());
        skuIdSet.addAll(todayFmsMoreInventoryMap.keySet());
        skuIdSet.addAll(skuInventorySnapshotInOutMap.keySet());
        skuIdSet.addAll(fmsCredentialDiffQtyMap.keySet());
        skuIdSet.addAll(transferIntransitQtyMap.keySet());
        return skuIdSet.stream().map(skuId -> {
            InventoryAndProcessingQtyDto endInventoryQty = endSkuInventorySnapshotPerHours.get(skuId);

            CommandChangeQtyDto fmsLessInventoryNum = todayFmsLessInventoryNumMap.get(skuId);
            CommandChangeQtyDto fmsMoreInventoryNum = todayFmsMoreInventoryMap.get(skuId);
            CommandInOutQtyDto inventoryInOutQty = skuInventorySnapshotInOutMap.get(skuId);
            BigDecimal fmsInQty = getFmsInQty(inventoryInOutQty, fmsLessInventoryNum, fmsMoreInventoryNum);
            BigDecimal fmsOutQty = getFmsOutQty(inventoryInOutQty, fmsLessInventoryNum, fmsMoreInventoryNum);

            BigDecimal transferIntransitQty = transferIntransitQtyMap.getOrDefault(skuId, BigDecimal.ZERO);
            CredentialDiffQtyDto credentialDiffQtyDto = fmsCredentialDiffQtyMap.get(skuId);
            if (fmsInQty.compareTo(BigDecimal.ZERO) == 0
                && fmsOutQty.compareTo(BigDecimal.ZERO) == 0
                && transferIntransitQty.compareTo(BigDecimal.ZERO) == 0
                && Objects.isNull(credentialDiffQtyDto)
                && Objects.isNull(endInventoryQty)) {
                return null;
            }
            BigDecimal inventoryQty = Optional.ofNullable(endInventoryQty)
                .map(InventoryAndProcessingQtyDto::getInventoryQty).orElse(BigDecimal.ZERO);
            BigDecimal processingQty = Optional.ofNullable(endInventoryQty)
                .map(InventoryAndProcessingQtyDto::getProcessingQty).orElse(BigDecimal.ZERO);
            FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay = new FmsSkuInventorySnapshotPerDay();
            fmsSkuInventorySnapshotPerDay.setProcessingQty(processingQty);
            fmsSkuInventorySnapshotPerDay.setSkuId(skuId);
            fmsSkuInventorySnapshotPerDay.setInQty(fmsInQty);
            fmsSkuInventorySnapshotPerDay.setOutQty(fmsOutQty);
            fmsSkuInventorySnapshotPerDay.setImsQty(inventoryQty);
            fmsSkuInventorySnapshotPerDay.setSnapshotDate(snapshotDate);
            fmsSkuInventorySnapshotPerDay.setWarehouseId(warehouseId);
            fmsSkuInventorySnapshotPerDay.setTransferIntransitQty(transferIntransitQty);
            BigDecimal purchasePartInQty = Optional.ofNullable(credentialDiffQtyDto)
                .map(CredentialDiffQtyDto::getPurchasePartInQty).orElse(BigDecimal.ZERO);
            BigDecimal transferPartInQty = Optional.ofNullable(credentialDiffQtyDto)
                .map(CredentialDiffQtyDto::getTransferPartInQty).orElse(BigDecimal.ZERO);
            BigDecimal reverseProcessingQty = Optional.ofNullable(credentialDiffQtyDto)
                .map(CredentialDiffQtyDto::getReverseProcessingQty).orElse(BigDecimal.ZERO);
            BigDecimal otherDiffQty = Optional.ofNullable(credentialDiffQtyDto)
                .map(CredentialDiffQtyDto::getOtherDiffQty).orElse(BigDecimal.ZERO);
            fmsSkuInventorySnapshotPerDay.setPurchasePartInQty(purchasePartInQty);
            fmsSkuInventorySnapshotPerDay.setTransferPartInQty(transferPartInQty);
            fmsSkuInventorySnapshotPerDay.setReverseProcessingQty(reverseProcessingQty);
            fmsSkuInventorySnapshotPerDay.setOtherDiffQty(otherDiffQty);
            BigDecimal endFmsQty = inventoryQty.add(processingQty).add(transferIntransitQty)
                    .add(otherDiffQty).subtract(purchasePartInQty).subtract(reverseProcessingQty);
            fmsSkuInventorySnapshotPerDay.setQty(endFmsQty);
            return fmsSkuInventorySnapshotPerDay;
        }).filter(Objects::nonNull).collect(Collectors.toList());

    }


    private BigDecimal getFmsInQty(CommandInOutQtyDto inventoryInOutQty, CommandChangeQtyDto fmsLessInventoryNum,
        CommandChangeQtyDto fmsMoreInventoryNum) {
        BigDecimal inventoryQty = Optional.ofNullable(inventoryInOutQty)
            .map(CommandInOutQtyDto::getInQty).orElse(BigDecimal.ZERO);
        BigDecimal fmsLessQty = Optional.ofNullable(fmsLessInventoryNum)
            .map(CommandChangeQtyDto::getInQty).orElse(BigDecimal.ZERO);
        BigDecimal fmsMoreQty = Optional.ofNullable(fmsMoreInventoryNum)
            .map(CommandChangeQtyDto::getInQty).orElse(BigDecimal.ZERO);
        return inventoryQty.add(fmsMoreQty).subtract(fmsLessQty);
    }


    private BigDecimal getFmsOutQty(CommandInOutQtyDto inventoryInOutQty, CommandChangeQtyDto fmsLessInventoryNum,
        CommandChangeQtyDto fmsMoreInventoryNum) {
        BigDecimal inventoryQty = Optional.ofNullable(inventoryInOutQty)
            .map(CommandInOutQtyDto::getOutQty).orElse(BigDecimal.ZERO);
        BigDecimal fmsLessQty = Optional.ofNullable(fmsLessInventoryNum)
            .map(CommandChangeQtyDto::getOutQty).orElse(BigDecimal.ZERO);
        BigDecimal fmsMoreQty = Optional.ofNullable(fmsMoreInventoryNum)
            .map(CommandChangeQtyDto::getOutQty).orElse(BigDecimal.ZERO);
        return inventoryQty.add(fmsMoreQty).subtract(fmsLessQty);
    }


}
