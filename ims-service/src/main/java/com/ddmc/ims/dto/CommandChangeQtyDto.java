package com.ddmc.ims.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CommandChangeQtyDto {

    private BigDecimal allQty = BigDecimal.ZERO;
    private BigDecimal inQty = BigDecimal.ZERO;
    private BigDecimal outQty = BigDecimal.ZERO;


    public void addQty(InOutNumDto numDto) {
        this.allQty = allQty.add(numDto.getInQty().subtract(numDto.getOutQty()));
        this.inQty = inQty.add(numDto.getInQty());
        this.outQty = outQty.add(numDto.getOutQty());
    }

}
