package com.ddmc.ims.dto;

import com.ddmc.ims.common.bo.LogicInventoryLocationWithSku;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 单品在库库存包装器
 */
public class SingleSkuWarehouseInventoryWrapper {

    private final LogicInventoryLocationWithSku logicInventoryLocationWithSku;

    /**
     * 变更的用途库存
     */
    private Map<String, WarehouseSkuInventory> changedWarehouseSkuUsageInventoriesMap;

    /**
     * 新增的用途库存
     */
    private Map<String, WarehouseSkuInventory> addWarehouseSkuUseageInventoriesMap;

    /**
     * 全量的用途库存
     */
    private final Map<String, WarehouseSkuInventory> warehouseSkuUsageInventoryMap;

    /**
     * 变更的批次库存
     */
    private Map<String, WarehouseSkuLotInventory> changedWarehouseSkuLotInventoriesMap;

    /**
     * 新增的批次库存
     */
    private Map<String, WarehouseSkuLotInventory> addWarehouseSkuLotInventoriesMap;

    /**
     * 全量的批次库存
     */
    private final Map<String, WarehouseSkuLotInventory> warehouseSkuLotInventoryMap;

    /**
     * 批次信息lotId->InventoryLotInfo
     */
    private final Map<String, InventoryLotInfo> inventoryLotInfoMap;


    public SingleSkuWarehouseInventoryWrapper(LogicInventoryLocationWithSku logicInventoryLocationWithSku,@NotNull List<WarehouseSkuInventory> warehouseSkuInventorys, @NotNull List<WarehouseSkuLotInventory> warehouseSkuLotInventories,
        List<InventoryLotInfo> inventoryLotInfos) {
        this.warehouseSkuUsageInventoryMap = warehouseSkuInventorys.stream().collect(Collectors.toMap(WarehouseSkuInventory::getUsageCode,
            Function.identity(), (v1, v2) -> v1));
        this.logicInventoryLocationWithSku = logicInventoryLocationWithSku;
        this.warehouseSkuLotInventoryMap = warehouseSkuLotInventories.stream().collect(Collectors.toMap(WarehouseSkuLotInventory::getLotId,
            Function.identity(), (v1, v2) -> v1));
        if (CollectionUtils.isEmpty(inventoryLotInfos)) {
            this.inventoryLotInfoMap = Collections.emptyMap();
        } else {
            this.inventoryLotInfoMap = inventoryLotInfos.stream().collect(Collectors.toMap(InventoryLotInfo::getLotId,
                Function.identity(), (v1, v2) -> v1));
        }
    }

    List<WarehouseSkuInventory> getChangedWarehouseSkuUseageInventories() {
        return new ArrayList<>(getChangedWarehouseSkuUsageInventoriesMap().values());
    }

    List<WarehouseSkuLotInventory> getChangedWarehouseSkuLotInventories() {
        return new ArrayList<>(getChangedWarehouseSkuLotInventoriesMap().values());
    }

    private Map<String, WarehouseSkuInventory> getChangedWarehouseSkuUsageInventoriesMap() {
        changedWarehouseSkuUsageInventoriesMap = Objects.isNull(changedWarehouseSkuUsageInventoriesMap) ? Maps.newHashMap()
            : changedWarehouseSkuUsageInventoriesMap;
        return changedWarehouseSkuUsageInventoriesMap;
    }

    public List<WarehouseSkuInventory> getAddWarehouseSkuUseageInventories() {
        return new ArrayList<>(getAddWarehouseSkuUseageInventoriesMap().values());
    }

    public List<WarehouseSkuLotInventory> getAddWarehouseSkuLotInventories() {
        return new ArrayList<>(getAddWarehouseSkuLotInventoriesMap().values());
    }

    private Map<String, WarehouseSkuLotInventory> getChangedWarehouseSkuLotInventoriesMap() {
        changedWarehouseSkuLotInventoriesMap = Objects.isNull(changedWarehouseSkuLotInventoriesMap) ? Maps.newHashMap()
            : changedWarehouseSkuLotInventoriesMap;
        return changedWarehouseSkuLotInventoriesMap;
    }

    public Map<String, WarehouseSkuInventory> getAddWarehouseSkuUseageInventoriesMap() {
        addWarehouseSkuUseageInventoriesMap = Objects.isNull(addWarehouseSkuUseageInventoriesMap) ? Maps.newHashMap()
            : addWarehouseSkuUseageInventoriesMap;
        return addWarehouseSkuUseageInventoriesMap;
    }

    private Map<String, WarehouseSkuLotInventory> getAddWarehouseSkuLotInventoriesMap() {
        addWarehouseSkuLotInventoriesMap = Objects.isNull(addWarehouseSkuLotInventoriesMap) ? Maps.newHashMap()
            : addWarehouseSkuLotInventoriesMap;
        return addWarehouseSkuLotInventoriesMap;
    }



    /**
     * 调整品维度库存
     * @param addFreeQty 增加可用库存，若为减少，addFreeQty小于0
     * @param addFrozenQty 增加冻结库存，若为减少，addFrozenQty小于0
     * @param addAllocQty 增加占用库存，若为减少，addAllocQty小于0
     */
    void changeWarehouseSkuInventory(@NotEmpty String usage, @NotNull BigDecimal addFreeQty, @NotNull BigDecimal addFrozenQty, @NotNull BigDecimal addAllocQty) {
        WarehouseSkuInventory warehouseSkuInventory = warehouseSkuUsageInventoryMap.get(usage);
        if (Objects.isNull(warehouseSkuInventory)) {
            warehouseSkuInventory = new WarehouseSkuInventory();
            LogicInventoryLocation logicInventoryLocation = logicInventoryLocationWithSku.getLogicInventoryLocation();
            warehouseSkuInventory.setWarehouseId(logicInventoryLocation.getWarehouseId());
            warehouseSkuInventory.setCargoOwnerId(logicInventoryLocation.getCargoOwnerId());
            warehouseSkuInventory.setLogicInventoryLocationCode(logicInventoryLocation.getLogicInventoryLocationCode());
            warehouseSkuInventory.setSkuId(logicInventoryLocationWithSku.getSkuId());
            warehouseSkuInventory.setAllocQty(addAllocQty);
            warehouseSkuInventory.setFreeQty(addFreeQty);
            warehouseSkuInventory.setFrozenQty(addFrozenQty);
            warehouseSkuInventory.setCreateTime(CurrentDateUtil.newDate());
            warehouseSkuInventory.setUpdateTime(CurrentDateUtil.newDate());
            warehouseSkuInventory.setVersion(0);
            warehouseSkuInventory.setUsageCode(usage);
            addWarehouseSkuUseageInventory(warehouseSkuInventory);
        } else {
            warehouseSkuInventory.setFreeQty(warehouseSkuInventory.getFreeQty().add(addFreeQty));
            warehouseSkuInventory.setFrozenQty(warehouseSkuInventory.getFrozenQty().add(addFrozenQty));
            warehouseSkuInventory.setAllocQty(warehouseSkuInventory.getAllocQty().add(addAllocQty));
            addChangedWarehouseSkuUseageInventory(warehouseSkuInventory);
        }

    }

    void changeWarehouseSkuLotInventory(@NotEmpty String lotId,@NotEmpty String useage, @NotNull BigDecimal addFreeQty, @NotNull BigDecimal addFrozenQty) {
        WarehouseSkuLotInventory warehouseSkuLotInventory = warehouseSkuLotInventoryMap.get(lotId);
        if (Objects.isNull(warehouseSkuLotInventory)) {
            warehouseSkuLotInventory = new WarehouseSkuLotInventory();
            warehouseSkuLotInventory.setWarehouseId(logicInventoryLocationWithSku.getLogicInventoryLocation().getWarehouseId());
            warehouseSkuLotInventory.setSkuId(logicInventoryLocationWithSku.getSkuId());
            warehouseSkuLotInventory.setLogicInventoryLocationCode(logicInventoryLocationWithSku.getLogicInventoryLocation().getLogicInventoryLocationCode());
            warehouseSkuLotInventory.setCargoOwnerId(logicInventoryLocationWithSku.getLogicInventoryLocation().getCargoOwnerId());
            warehouseSkuLotInventory.setLotId(lotId);
            warehouseSkuLotInventory.setFreeQty(addFreeQty);
            warehouseSkuLotInventory.setFrozenQty(addFrozenQty);
            warehouseSkuLotInventory.setVersion(0);
            addWarehouseSkuLotInventory(warehouseSkuLotInventory);
        } else {
            warehouseSkuLotInventory.setFreeQty(warehouseSkuLotInventory.getFreeQty().add(addFreeQty));
            warehouseSkuLotInventory.setFrozenQty(warehouseSkuLotInventory.getFrozenQty().add(addFrozenQty));
            addChangedWarehouseSkuLotInventory(warehouseSkuLotInventory);
        }

        //变更货品维度批次库存
        changeWarehouseSkuInventory(useage,addFreeQty, addFrozenQty, BigDecimal.ZERO);
    }

    private void addWarehouseSkuUseageInventory(WarehouseSkuInventory warehouseSkuInventory) {

        getAddWarehouseSkuUseageInventoriesMap().put(warehouseSkuInventory.getUsageCode(), warehouseSkuInventory);

        warehouseSkuUsageInventoryMap.put(warehouseSkuInventory.getUsageCode(), warehouseSkuInventory);
    }

    private void addWarehouseSkuLotInventory(WarehouseSkuLotInventory warehouseSkuLotInventory) {

        getAddWarehouseSkuLotInventoriesMap().put(warehouseSkuLotInventory.getLotId(), warehouseSkuLotInventory);

        warehouseSkuLotInventoryMap.put(warehouseSkuLotInventory.getLotId(), warehouseSkuLotInventory);
    }

    private void addChangedWarehouseSkuUseageInventory(WarehouseSkuInventory warehouseSkuInventory) {
        if (getAddWarehouseSkuUseageInventoriesMap().containsKey(warehouseSkuInventory.getUsageCode())) {
            return;
        }
        getChangedWarehouseSkuUsageInventoriesMap().put(warehouseSkuInventory.getUsageCode(), warehouseSkuInventory);
    }

    private void addChangedWarehouseSkuLotInventory(WarehouseSkuLotInventory warehouseSkuLotInventory) {
        if (getAddWarehouseSkuLotInventoriesMap().containsKey(warehouseSkuLotInventory.getLotId())) {
            return;
        }
        getChangedWarehouseSkuLotInventoriesMap().put(warehouseSkuLotInventory.getLotId(), warehouseSkuLotInventory);
    }

    public LogicInventoryLocationWithSku getLogicInventoryLocationWithSku() {
        return logicInventoryLocationWithSku;
    }


    public WarehouseSkuLotInventory getWarehouseSkuLotInventory(String lotId) {
        return this.warehouseSkuLotInventoryMap.get(lotId);
    }

    public WarehouseSkuInventory getWarehouseSkuUseageInventory(String useage) {
        return this.warehouseSkuUsageInventoryMap.get(useage);
    }

    /**
     * 获取可用数量（剔除占用后）
     * @return 剔除占用的可用数量
     */
    BigDecimal getFreeQtyAfterAlloc() {
        return this.warehouseSkuUsageInventoryMap
            .values().stream().map(t->t.getFreeQty().subtract(t.getAllocQty())).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取效期可用数量（剔除占用后）
     * @param demandDate 需求日期
     * @param allocDetails 占用明细
     * @return 剔除占用的可用数量
     */
    BigDecimal getFreeQtyAfterAlloc(@NotNull Date demandDate, @NotNull List<WarehouseSkuInventoryAllocDetail> allocDetails) {
        //按日期做库存聚合 效期-》库存
        Map<Date, BigDecimal> expireInventoryMap = transferExpireInventoryMap();

        List<Date> dates = expireInventoryMap.keySet().stream().sorted(Date::compareTo).collect(Collectors.toList());
        //执行库存占用
        doAlloc(allocDetails, expireInventoryMap, dates);

        BigDecimal freeQtyAfterAllocOfExpire = dates.stream().filter(d -> demandDate.before(d) || demandDate.equals(d))
            .map(expireInventoryMap::get)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal freeQtyAfterAllocOfSku = getFreeQtyAfterAlloc();
        return freeQtyAfterAllocOfExpire.min(freeQtyAfterAllocOfSku);
    }

    /**
     * 按批次的临期日期聚合库存
     * @return 临期日期->可用库存数量
     */
    private Map<Date, BigDecimal> transferExpireInventoryMap() {
        return this.inventoryLotInfoMap.values().stream()
            .filter(t -> Objects.nonNull(t.getUnsalableDate()))
            .collect(Collectors.groupingBy(InventoryLotInfo::getUnsalableDate,
                Collectors.mapping(t -> getFreeQtyForLot(t.getLotId())
                    , Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    /**
     * 执行占用逻辑，按照先过期先占用的原则；
     *
     * expireInventoryMap
     * 例如 2022-10-01 5
     *      2022-10-02 10
     *
     * allocDetails
     * 占用需求日期 2022-09-30 10
     * 占用结果:2022-10-01 5(占用数量) 2022-10-02 5(占用数量)
     *
     *
     * @param allocDetails 占用明细
     * @param expireInventoryMap 效期库存集合
     * @param dates 升序的效期集合
     */
    private void doAlloc(List<WarehouseSkuInventoryAllocDetail> allocDetails, Map<Date, BigDecimal> expireInventoryMap, List<Date> dates) {
        allocDetails.forEach(d -> {
            BigDecimal needQty = d.getAllocQty();
            for (Date dt : dates) {
                if (d.getDemandTime().after(dt)) {
                    continue;
                }
                BigDecimal qty = expireInventoryMap.get(dt);
                if (qty.compareTo(needQty) >= 0) {
                    expireInventoryMap.put(dt, qty.subtract(needQty));
                    return;
                } else {
                    expireInventoryMap.put(dt, BigDecimal.ZERO);
                    needQty = needQty.subtract(qty);
                }
            }
        });
    }

    private BigDecimal getFreeQtyForLot(String lotId) {
        WarehouseSkuLotInventory lotInventory = this.warehouseSkuLotInventoryMap.get(lotId);
        return Objects.isNull(lotInventory) ? BigDecimal.ZERO : lotInventory.getFreeQty();
    }
}
