package com.ddmc.ims.dto;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CommandInventoryNumDto {

    private LogicInventoryLocation location;
    private Long skuId;
    private String lotId;
    private BigDecimal freeQty;
    private BigDecimal frozenQty;
    private BigDecimal transferIntransitQty;
    private String usageCode;

    private BigDecimal inQty;
    private BigDecimal outQty;
    private LogicInventoryLocation toLocation;

}
