package com.ddmc.ims.dto;

import com.ddmc.ims.common.bo.LogicInventoryLocationWithLot;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithSku;
import com.ddmc.ims.common.errorcode.WarehouseInventoryAllocErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.constraints.NotEmpty;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 多品在库库存包装器
 */
public class MultiSkuWarehouseInventoryWrapper {

    private final Map<LogicInventoryLocationWithSku, SingleSkuWarehouseInventoryWrapper> withSkuWarehouseInventoryWrapperMap;

    public MultiSkuWarehouseInventoryWrapper(@NotEmpty List<SingleSkuWarehouseInventoryWrapper> warehouseInventoryWrappers) {
        this.withSkuWarehouseInventoryWrapperMap = warehouseInventoryWrappers.stream()
            .collect(Collectors.toMap(SingleSkuWarehouseInventoryWrapper::getLogicInventoryLocationWithSku,
                Function.identity(), (v1, v2) -> v1));
    }


    /**
     * 修改logicInventoryLocationWithSku的库存信息，逻辑库位不关联实物库存时使用
     * @param logicInventoryLocationWithSku 位于定位货品维度数据
     * @param addFreeQty 增加可用库存，若为减少操作，addFreeQty<0
     * @param addFrozenQty 增加冻结库存，若为减少操作，addFrozenQty<0
     * @param addAllocQty 增加占用库存，若为减少操作，addAllocQty<0
     */
    public void changeWarehouseInventory(LogicInventoryLocationWithSku logicInventoryLocationWithSku,String usage, BigDecimal addFreeQty,
                                         BigDecimal addFrozenQty, BigDecimal addAllocQty) {
        SingleSkuWarehouseInventoryWrapper warehouseInventoryWrapper = getSingleSkuWarehouseInventoryWrapper(logicInventoryLocationWithSku);
        warehouseInventoryWrapper.changeWarehouseSkuInventory(usage,addFreeQty, addFrozenQty, addAllocQty);
    }



    /**
     * 修改logicInventoryLocationWithSku的库存信息，逻辑库位关联实物库存时使用
     * @param logicInventoryLocationWithLot 位于定位货品维度数据
     * @param addFreeQty 增加可用库存，若为减少操作，addFreeQty<0
     * @param addFrozenQty 增加冻结库存，若为减少操作，addFrozenQty<0
     */
    public void changeWarehouseInventoryWithLot(LogicInventoryLocationWithLot logicInventoryLocationWithLot, BigDecimal addFreeQty,
                                         BigDecimal addFrozenQty) {
        LogicInventoryLocationWithSku logicInventoryLocationWithSku = new LogicInventoryLocationWithSku(
            logicInventoryLocationWithLot.getLogicInventoryLocation(), logicInventoryLocationWithLot.getSkuId());
        SingleSkuWarehouseInventoryWrapper warehouseInventoryWrapper = getSingleSkuWarehouseInventoryWrapper(logicInventoryLocationWithSku);
        warehouseInventoryWrapper.changeWarehouseSkuLotInventory(logicInventoryLocationWithLot.getLotId(),logicInventoryLocationWithLot.getUsage(), addFreeQty, addFrozenQty);
    }

    /**
     * 获取单品在库库存相关信息
     *
     * @param logicInventoryLocationWithSku logicInventoryLocationWithSku
     * @return result
     */
    public SingleSkuWarehouseInventoryWrapper getSingleSkuWarehouseInventoryWrapper(
        LogicInventoryLocationWithSku logicInventoryLocationWithSku) {
        SingleSkuWarehouseInventoryWrapper warehouseInventoryWrapper = withSkuWarehouseInventoryWrapperMap
            .get(logicInventoryLocationWithSku);
        if (Objects.isNull(warehouseInventoryWrapper)) {
            warehouseInventoryWrapper = new SingleSkuWarehouseInventoryWrapper(logicInventoryLocationWithSku, Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
            withSkuWarehouseInventoryWrapperMap.put(warehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                warehouseInventoryWrapper);
        }
        return warehouseInventoryWrapper;
    }

    /**
     * 获得变更的货品维度库存
     * @return 发生变更的货品维度库存
     */
    public List<WarehouseSkuInventory> getChangedWarehouseSkuInventories() {
        return this.withSkuWarehouseInventoryWrapperMap.values().stream()
            .map(SingleSkuWarehouseInventoryWrapper::getChangedWarehouseSkuUseageInventories).filter(Objects::nonNull)
            .flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 获得新增的货品维度库存
     * @return 新增的货品维度库存
     */
    public List<WarehouseSkuInventory> getAddWarehouseSkuInventories() {
        return
            this.withSkuWarehouseInventoryWrapperMap.values().stream().map(SingleSkuWarehouseInventoryWrapper::getAddWarehouseSkuUseageInventories).filter(Objects::nonNull)
                .flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 获得变更的批次维度库存
     * @return 发生变更的批次维度库存
     */
    public List<WarehouseSkuLotInventory> getChangedWarehouseSkuLotInventories() {
            return this.withSkuWarehouseInventoryWrapperMap.values().stream()
            .map(SingleSkuWarehouseInventoryWrapper::getChangedWarehouseSkuLotInventories).filter(Objects::nonNull)
            .flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 获得新增的批次维度库存
     * @return 新增的批次维度库存
     */
    public List<WarehouseSkuLotInventory> getAddWarehouseSkuLotInventories() {
        return this.withSkuWarehouseInventoryWrapperMap.values().stream()
            .map(SingleSkuWarehouseInventoryWrapper::getAddWarehouseSkuLotInventories).filter(Objects::nonNull)
            .flatMap(List::stream).collect(Collectors.toList());
    }


    /**
     * 获取可用数量（剔除占用后）
     * @param logicInventoryLocationWithSku 逻辑库位
     * @return 剔除占用的可用数量
     */
    public BigDecimal getFreeQtyAfterAlloc(LogicInventoryLocationWithSku logicInventoryLocationWithSku) {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = this.withSkuWarehouseInventoryWrapperMap.get(logicInventoryLocationWithSku);
        return Objects.isNull(singleSkuWarehouseInventoryWrapper) ? BigDecimal.ZERO : singleSkuWarehouseInventoryWrapper.getFreeQtyAfterAlloc();
    }

    /**
     * 获取效期可用数量（剔除占用后）
     * 1.批次库存的可售期为空，不参与计算可占用库存
     * 2.先过期先占用
     * 3.可售期=需求日期也参与计算
     * 4.可占用库存 = min(批次可占用库存，货品维度可占用库存)
     * @param logicInventoryLocationWithSku 逻辑库位
     * @param demandDate 需求日期
     * @param allocDetails 占用明细
     * @return 剔除占用的可用数量
     */
    public BigDecimal getFreeQtyAfterAlloc(LogicInventoryLocationWithSku logicInventoryLocationWithSku, Date demandDate, List<WarehouseSkuInventoryAllocDetail> allocDetails) {
        if (CollectionUtils.isEmpty(allocDetails)) {
            allocDetails = new ArrayList<>();
        }
        List<Long> skuIds = allocDetails.stream().map(WarehouseSkuInventoryAllocDetail::getSkuId).distinct().collect(Collectors.toList());
        if (!skuIds.isEmpty() && !skuIds.get(0).equals(logicInventoryLocationWithSku.getSkuId())) {
            throw new ImsBusinessException(WarehouseInventoryAllocErrorCode.SKU_ID_NOT_MATCH);
        }

        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = this.withSkuWarehouseInventoryWrapperMap.get(logicInventoryLocationWithSku);
        return Objects.isNull(singleSkuWarehouseInventoryWrapper) ? BigDecimal.ZERO : singleSkuWarehouseInventoryWrapper.getFreeQtyAfterAlloc(
            demandDate, allocDetails);
    }
}