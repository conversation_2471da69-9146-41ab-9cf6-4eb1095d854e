package com.ddmc.ims.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CredentialDiffQtyDto {

    private Long  skuId;

    /**
     * 采购部分入库数量
     */
    private BigDecimal purchasePartInQty;


    /**
     * 调拨部分入数量
     */
    private BigDecimal transferPartInQty;


    /**
     * 反加工未完工提前入库数量
     */
    private BigDecimal reverseProcessingQty;


    /**
     * 其他差异数量
     */
    private BigDecimal otherDiffQty;

    public CredentialDiffQtyDto(Long skuId,  BigDecimal purchasePartInQty,
        BigDecimal transferPartInQty, BigDecimal reverseProcessingQty, BigDecimal otherDiffQty) {
        this.skuId = skuId;
        this.purchasePartInQty = purchasePartInQty;
        this.transferPartInQty = transferPartInQty;
        this.reverseProcessingQty = reverseProcessingQty;
        this.otherDiffQty = otherDiffQty;
    }
}
