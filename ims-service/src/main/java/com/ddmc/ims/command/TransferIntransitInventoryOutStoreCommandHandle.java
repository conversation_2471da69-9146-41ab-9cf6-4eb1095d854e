package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.command.helper.TransferInTransitInventoryOutStoreHelper;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.common.util.PatternUtils;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TransferIntransitInventoryOutStoreCommandHandle extends
    AbstractInventoryCommandHandle<TransferIntransitInventoryCommand> {

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private WarehouseSkuInventoryService warehouseSkuInventoryService;

    @Resource
    private TransferTransitUsageDetailManager transferTransitUsageDetailManager;

    @Override
    public void handleInventoryCommand(List<TransferIntransitInventoryCommand> commands) {
        List<TransferIntransitInventoryCommand> outCommands = commands.stream()
            .filter(t -> !CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.equals(t.getToLocation())
                && Objects.equals(t.getCommandType(), CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outCommands)) {
            return;
        }
        //判断是否是非四要素订单，兼容历史数据与灰度数据
        TransferIntransitInventoryCommand command = outCommands.get(0);
        if (!PatternUtils.isExactlyThreeHyphens(command.getOrderNo())) {
            //出库扣减
            changOutStoreInventory(outCommands);
            return;
        }


        //调拨在途出库处理减少计划未发量，增加在途数量
        handleTransferOutStore(outCommands);
        //出库扣减
        changOutStoreInventory(outCommands);

    }



    /**
     * 调拨在途出库处理减少计划未发量，增加在途数量
     *
     * @param commands 可用转出库命令
     */
    public List<TransferIntransitInventory> handleTransferOutStore(List<TransferIntransitInventoryCommand> commands) {
        Map<TransferInTransitItem, List<TransferIntransitInventory>> insertMap = Maps
            .newHashMapWithExpectedSize(commands.size());

        //四要素+品+出库仓+出库货主+入库仓+入库货主+入库逻辑库位编码+用途，调拨在途map
        Map<TransferInTransitItem, List<TransferIntransitInventory>> existMap = getExistTransferMap(commands);

        //按命令依次处理,填充或修改调拨在途
        TransferInTransitInventoryOutStoreHelper.processTransferInTransitInventoryCommands(commands, insertMap, existMap);
        //保存或更新在途数据
        return updateOrInsertInventory(insertMap, existMap);
    }







    /**
     * 保存或更新在途数据
     *
     * @param insertMap insertMap
     * @param existMap existMap
     * @return result
     */
    private List<TransferIntransitInventory> updateOrInsertInventory(
        Map<TransferInTransitItem, List<TransferIntransitInventory>> insertMap,
        Map<TransferInTransitItem, List<TransferIntransitInventory>> existMap) {
        List<TransferIntransitInventory> insertList = insertMap.values().stream().flatMap(List::stream)
            .collect(Collectors.toList());
        List<TransferIntransitInventory> updateList = existMap.values().stream().flatMap(List::stream)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(insertList)) {
            Lists.partition(insertList, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(t -> transferIntransitInventoryMapper.batchInsert(t));
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.sort(Comparator.comparing(TransferIntransitInventory::getId));
            Lists.partition(updateList, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(t -> transferIntransitInventoryMapper.batchUpdate(t));
        }

        List<TransferIntransitInventory> result = Lists
            .newArrayListWithExpectedSize(insertList.size() + updateList.size());
        result.addAll(insertList);
        result.addAll(updateList);
        return result;
    }






    /**
     * 查询已存在的调拨在途
     *
     * @param commands commands
     * @return result
     */
    private Map<TransferInTransitItem, List<TransferIntransitInventory>> getExistTransferMap(
        List<TransferIntransitInventoryCommand> commands) {
        List<TransferIntransitInventory> result = transferTransitUsageDetailManager.getTransferInTransitInventoryList(commands);
        return result.stream().collect(Collectors
            .groupingBy(t -> TransferInTransitItemConverter.convertToCondition(t, true)));
    }





    /**
     * 调拨在途出库，减少在库
     *
     * @param inventoryCommands 可用转出库命令
     */
    public void changOutStoreInventory(List<TransferIntransitInventoryCommand> inventoryCommands) {
        inventoryCommands.stream().collect(Collectors.groupingBy(TransferIntransitInventoryCommand::getFromLocation))
            .forEach((k, v) -> singleOutStoreChangInventory(v));
    }

    /**
     * 单逻辑库位调拨在途出库，减少在库
     *
     * @param inventoryCommands 可用转出库命令
     */
    private void singleOutStoreChangInventory(List<TransferIntransitInventoryCommand> inventoryCommands) {
        TransferIntransitInventoryCommand inventoryCommand = inventoryCommands.get(0);
        InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(inventoryCommand.getFromLocation());
        List<InventoryChangeItem> inventoryChangeItems = inventoryCommands.stream().map(i -> {
            InventoryChangeItem.InventoryChangeItemBuilder item = InventoryChangeItem.builder();
            item.skuId(i.getSkuId());
            item.usage(i.getUsageCode());
            item.lotId(i.getLotId());
            item.inventoryOperateTypeEnum(InventoryOperateTypeEnum.DECREASE);
            item.qty(i.getQty());
            if (i.getFromInventoryStatus() == InventoryStatusEnum.NOT_AVAILABLE) {
                item.inventoryWorkTypeEnum(InventoryWorkTypeEnum.FROZEN);
            } else {
                item.inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE);
            }
            return item.build();
        }).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItems);
        warehouseSkuInventoryService.changInventory(inventoryChange);
    }


    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<TransferIntransitInventoryCommand> commands) {
        return commands.stream().filter(i ->
            !CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.getLogicInventoryLocationCode()
                .equals(i.getToLocation().getLogicInventoryLocationCode())).map(i -> {
            CommandInventoryNumDto item = new CommandInventoryNumDto();
            item.setSkuId(i.getSkuId());
            item.setLotId(i.getLotId());
            item.setLocation(i.getFromLocation());
            item.setFreeQty(i.getQty().negate());
            item.setFrozenQty(BigDecimal.ZERO);
            item.setTransferIntransitQty(i.getQty());
            item.setOutQty(i.getQty());
            item.setInQty(BigDecimal.ZERO);
            item.setUsageCode(i.getUsageCode());
            return item;
        }).collect(Collectors.toList());
    }


}
