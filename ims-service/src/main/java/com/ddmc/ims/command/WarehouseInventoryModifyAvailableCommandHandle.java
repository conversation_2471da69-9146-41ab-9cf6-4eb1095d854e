package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.WarehouseInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WarehouseInventoryModifyAvailableCommandHandle extends
    AbstractInventoryCommandHandle<WarehouseInventoryCommand> {

    @Resource
    private WarehouseSkuInventoryService warehouseSkuInventoryService;

    @Override
    public void handleInventoryCommand(List<WarehouseInventoryCommand> commands) {
        commands.stream().collect(Collectors.groupingBy(WarehouseInventoryCommand::getFromLocation))
            .forEach((location, v) -> modifyInventoryAvailable(v));
    }



    private void modifyInventoryAvailable(List<WarehouseInventoryCommand> inventoryCommands) {
        WarehouseInventoryCommand inventoryCommand = inventoryCommands.get(0);
        InventoryChange inventoryChange = new InventoryChange();
        LogicInventoryLocation logicInventoryLocation = inventoryCommand.getFromLocation().equals(CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION)
            ? inventoryCommand.getToLocation() : inventoryCommand.getFromLocation();
        inventoryChange.setLogicInventoryLocation(logicInventoryLocation);
        List<InventoryChangeItem> inventoryChangeItems = inventoryCommands.stream().map(i -> {
            InventoryChangeItem.InventoryChangeItemBuilder item = InventoryChangeItem.builder();
            item.skuId(i.getSkuId());
            item.usage(i.getUsageCode());
            item.lotId(i.getLotId());
            item.inventoryOperateTypeEnum(i.getQty().compareTo(BigDecimal.ZERO) >= 0 ? InventoryOperateTypeEnum.ADD
                : InventoryOperateTypeEnum.DECREASE);
            item.qty(i.getQty().abs());
            item.inventoryWorkTypeEnum(i.getFromInventoryWorkTypeEnum());
            return item.build();
        }).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItems);
        warehouseSkuInventoryService.changInventory(inventoryChange);
    }

    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<WarehouseInventoryCommand> commands){
        return  commands.stream().map(i -> {
            CommandInventoryNumDto item = new CommandInventoryNumDto();
            item.setSkuId(i.getSkuId());
            item.setLotId(i.getLotId());
            item.setLocation(i.getFromLocation());
            item.setUsageCode(i.getUsageCode());
            if (i.getFromInventoryStatus() == InventoryStatusEnum.NOT_AVAILABLE) {
                item.setFreeQty(BigDecimal.ZERO);
                item.setFrozenQty(i.getQty());
            } else {
                item.setFreeQty(i.getQty());
                item.setFrozenQty(BigDecimal.ZERO);
            }
            item.setTransferIntransitQty(BigDecimal.ZERO);
            //加工中的数据只有出库，执照完成时，如果为正数，对应的出库数量减少
            if (i.getQty().compareTo(BigDecimal.ZERO) >= 0) {
                item.setInQty(i.getQty());
                item.setOutQty(BigDecimal.ZERO);
            } else {
                item.setInQty(BigDecimal.ZERO);
                item.setOutQty(i.getQty().negate());
            }
            return item;
        }).collect(Collectors.toList());
    }
}
