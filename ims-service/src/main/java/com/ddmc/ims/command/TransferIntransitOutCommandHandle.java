package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.command.helper.TransferInTransitInventoryOutStoreHelper;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.PatternUtils;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.converter.TransferIntransitInventoryConverter;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryOutDetailMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryOutDetail;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TransferIntransitOutCommandHandle extends
    AbstractInventoryCommandHandle<TransferIntransitInventoryCommand> {

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private TransferTransitUsageDetailManager transferTransitUsageDetailManager;

    @Resource
    private TransferIntransitInventoryOutDetailMapper transferIntransitInventoryOutDetailMapper;



    @Override
    public void handleInventoryCommand(List<TransferIntransitInventoryCommand> commands) {
        List<TransferIntransitInventoryCommand> outCommands = commands.stream()
            .filter(t -> !CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.equals(t.getToLocation())
                && Objects.equals(t.getCommandType(), CommandTypeEnum.TRANSFER_IN_TRANSIT_OUT))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outCommands)) {
            return;
        }
        //判断是否是非四要素订单，兼容历史数据与灰度数据
        TransferIntransitInventoryCommand command = outCommands.get(0);
        if (!PatternUtils.isExactlyThreeHyphens(command.getOrderNo())) {
            log.warn("[TransferIntransitOutCommandHandle] 非四要素过滤:{}", JsonUtil.toJson(command.getOrderNo()));
            return;
        }


        //调拨在途出库处理减少计划未发量，增加在途数量
        handleTransferOutStore(outCommands);
        //处理调拨在途出库明细
        handleTransferIntransitInventoryOutDetail(outCommands);


    }

    /**
     * 调拨在途出库处理减少计划未发量，增加在途数量
     *
     * @param commands 可用转出库命令
     */
    public List<TransferIntransitInventory> handleTransferOutStore(List<TransferIntransitInventoryCommand> commands) {
        Map<TransferInTransitItem, List<TransferIntransitInventory>> insertMap = Maps
            .newHashMapWithExpectedSize(commands.size());

        //四要素+品+出库仓+出库货主+入库仓+入库货主+入库逻辑库位编码+用途，调拨在途map
        Map<TransferInTransitItem, List<TransferIntransitInventory>> existMap = getExistTransferMap(commands);

        //按命令依次处理,填充或修改调拨在途
        TransferInTransitInventoryOutStoreHelper.processTransferInTransitInventoryCommands(commands, insertMap, existMap);
        //保存或更新在途数据
        return updateOrInsertInventory(insertMap, existMap);
    }




    /**
     * 处理调拨在途库存批次明细
     *
     * @param outCommands outCommands
     */
    public void handleTransferIntransitInventoryOutDetail(List<TransferIntransitInventoryCommand> outCommands) {

        TransferIntransitInventoryCommand command = outCommands.get(0);
        List<Long> skuIds = outCommands.stream().map(TransferIntransitInventoryCommand::getSkuId).distinct()
            .collect(Collectors.toList());
        List<TransferIntransitInventoryOutDetail> existOutDetails = transferIntransitInventoryOutDetailMapper
            .selectByRefOrderNoAndRefOrderSourceAndRefExeOrderNoAndRefExeOrderSource(command.getOrderNo(),
                command.getOrderSource(), command.getExeOrderNo(), command.getExeOrderSource(),skuIds);

        //四要素+品+出库仓+出库货主+入库仓+入库货主+入库逻辑库位编码+用途+批次，调拨在途出库明细map
        Map<TransferInTransitItem, TransferIntransitInventoryOutDetail> existOutMap = existOutDetails.stream()
            .collect(Collectors.toMap(TransferInTransitItemConverter::convertByInventoryOutDetail, Function.identity()));

        Map<TransferInTransitItem, TransferIntransitInventoryOutDetail> insertMap = Maps
            .newHashMapWithExpectedSize(outCommands.size());

        outCommands.forEach(t -> {
            TransferInTransitItem item = TransferInTransitItemConverter.convertByCommandLot(t);
            TransferIntransitInventoryOutDetail existOutDetail = existOutMap.get(item);
            if (Objects.nonNull(existOutDetail)) {
                existOutDetail.setOutQty(existOutDetail.getOutQty().add(t.getQty()));
                existOutMap.put(item, existOutDetail);
            } else {
                TransferIntransitInventoryOutDetail existInsetDetail = insertMap.get(item);
                if (Objects.isNull(existInsetDetail)) {
                    TransferIntransitInventoryOutDetail outDetail = TransferIntransitInventoryConverter
                        .convertToTransferIntransitInventoryOutDetail(t);
                    insertMap.put(item, outDetail);
                } else {
                    existInsetDetail.setOutQty(existInsetDetail.getOutQty().add(t.getQty()));
                    insertMap.put(item, existInsetDetail);
                }
            }
        });

        insertOrUpdateOutDetail(new ArrayList<>(insertMap.values()), new ArrayList<>(existOutMap.values()));

    }

    /**
     * 保存或更新调拨在途出库明细数据
     *
     * @param insertList insertList
     * @param updateList updateList
     */
    private void insertOrUpdateOutDetail(
        List<TransferIntransitInventoryOutDetail> insertList,
        List<TransferIntransitInventoryOutDetail> updateList) {

        if (CollectionUtils.isNotEmpty(insertList)) {
            Lists.partition(insertList, CommonConstants.BATCH_INSERT_DB_300)
                .forEach(t -> transferIntransitInventoryOutDetailMapper.insertList(t));
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.sort(Comparator.comparing(TransferIntransitInventoryOutDetail::getId));
            Lists.partition(updateList, CommonConstants.BATCH_UPDATE_DB_300)
                .forEach(t -> transferIntransitInventoryOutDetailMapper.batchUpdate(t));
        }
    }


    /**
     * 保存或更新在途数据
     *
     * @param insertMap insertMap
     * @param existMap existMap
     * @return result
     */
    private List<TransferIntransitInventory> updateOrInsertInventory(
        Map<TransferInTransitItem, List<TransferIntransitInventory>> insertMap,
        Map<TransferInTransitItem, List<TransferIntransitInventory>> existMap) {
        List<TransferIntransitInventory> insertList = insertMap.values().stream().flatMap(List::stream)
            .collect(Collectors.toList());
        List<TransferIntransitInventory> updateList = existMap.values().stream().flatMap(List::stream)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(insertList)) {
            Lists.partition(insertList, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(t -> transferIntransitInventoryMapper.batchInsert(t));
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.sort(Comparator.comparing(TransferIntransitInventory::getId));
            Lists.partition(updateList, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(t -> transferIntransitInventoryMapper.batchUpdate(t));
        }

        List<TransferIntransitInventory> result = Lists
            .newArrayListWithExpectedSize(insertList.size() + updateList.size());
        result.addAll(insertList);
        result.addAll(updateList);
        return result;
    }




    /**
     * 查询已存在的调拨在途
     *
     * @param commands commands
     * @return result
     */
    private Map<TransferInTransitItem, List<TransferIntransitInventory>> getExistTransferMap(
        List<TransferIntransitInventoryCommand> commands) {
        List<TransferIntransitInventory> result = transferTransitUsageDetailManager.getTransferInTransitInventoryList(commands);
        return result.stream().collect(Collectors
            .groupingBy(t -> TransferInTransitItemConverter.convertToCondition(t, true)));
    }






    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<TransferIntransitInventoryCommand> commands){
        return  commands.stream().map(i -> {
            CommandInventoryNumDto item = new CommandInventoryNumDto();
            item.setSkuId(i.getSkuId());
            item.setLotId(i.getLotId());
            item.setLocation(i.getFromLocation());
            item.setUsageCode(i.getUsageCode());
            item.setFreeQty(BigDecimal.ZERO);
            item.setFrozenQty(BigDecimal.ZERO);
            item.setTransferIntransitQty(i.getQty());
            item.setInQty(BigDecimal.ZERO);
            item.setOutQty(BigDecimal.ZERO);
            item.setToLocation(i.getToLocation());
            return item;
        }).collect(Collectors.toList());
    }


}
