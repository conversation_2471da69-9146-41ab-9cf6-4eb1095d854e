package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PurchaseIntransitInventoryInStoreCommandHandle extends
    AbstractInventoryCommandHandle<PurchaseIntransitInventoryCommand> {

    @Resource
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;
    @Resource
    private WarehouseSkuInventoryService warehouseSkuInventoryService;


    @Override
    public void handleInventoryCommand(List<PurchaseIntransitInventoryCommand> commands) {
        Set<String> orderSet = commands.stream().map(PurchaseIntransitInventoryCommand::getOrderNo).collect(Collectors.toSet());
        checkOrderCount(orderSet.size());
        purchaseInTransitInStore(commands);
        purchaseIntransitAddInventoryChange(commands);

    }


    /**
     * 采购在途转入库修改在途数据
     */
    public void purchaseInTransitInStore(List<PurchaseIntransitInventoryCommand> commands) {
        //根据单号和商品id加载采购在途库存
        PurchaseIntransitInventoryCommand purchaseIntransitInventoryCommand = commands.get(0);
        List<PurchaseIntransitInventory> purchaseIntransitInventories = purchaseIntransitInventoryMapper
            .listByOrderSourceAndPurchaseNo(purchaseIntransitInventoryCommand.getOrderSource(), purchaseIntransitInventoryCommand.getOrderNo());
        if(CollectionUtils.isEmpty(purchaseIntransitInventories)){
            return;
        }
        List<PurchaseIntransitInventory> needUpdate = subIntransitQty(commands, purchaseIntransitInventories);

        if (CollectionUtils.isEmpty(needUpdate)) {
            return;
        }
        needUpdate.sort(Comparator.comparing(PurchaseIntransitInventory::getSkuId));
        Lists.partition(needUpdate, CommonConstants.BATCH_UPDATE_DB_100).forEach(purchaseIntransitInventoryMapper::batchUpdate);

    }

    private List<PurchaseIntransitInventory> subIntransitQty(List<PurchaseIntransitInventoryCommand> commands, List<PurchaseIntransitInventory> purchaseIntransitInventories) {
        Map<ImmutablePair<Long, String>, PurchaseIntransitInventory> purchaseIntransitInventoryMap = purchaseIntransitInventories.stream()
            .collect(Collectors.toMap(intransit -> ImmutablePair.of(intransit.getSkuId(), intransit.getUsageCode()), Function.identity()));

        List<PurchaseIntransitInventory> needUpdate = Lists.newArrayListWithExpectedSize(commands.size());
        //扣减采购在途数量
        commands.forEach(i -> {
            PurchaseIntransitInventory purchaseIntransitInventory = purchaseIntransitInventoryMap.get(ImmutablePair.of(i.getSkuId(), i.getUsageCode()));
            if (Objects.isNull(purchaseIntransitInventory)) {
                log.error("[入库货品在采购单中不存在],货品id{}", i.getSkuId());
                return;
            }
            //在途数量小于入库数量（按重量计数的货品无法精确处理，实际到货数量可能比计划量大），在途数量置为0
            BigDecimal newIntransitQty = purchaseIntransitInventory.getIntransitQty().subtract(i.getQty()).max(BigDecimal.ZERO);
            purchaseIntransitInventory.setIntransitQty(newIntransitQty);

            BigDecimal newBookedIntransitQty = purchaseIntransitInventory.getBookedIntransitQty().subtract(i.getQty()).max(BigDecimal.ZERO);
            purchaseIntransitInventory.setBookedIntransitQty(newBookedIntransitQty);
            needUpdate.add(purchaseIntransitInventory);
        });
        return needUpdate;
    }

    /**
     * 采购在途接收更新在库库存
     *
     */
    private void purchaseIntransitAddInventoryChange(List<PurchaseIntransitInventoryCommand> commands) {
        commands.stream().collect(Collectors.groupingBy(PurchaseIntransitInventoryCommand::getFromLocation))
            .forEach((k, v) -> singlePurchaseIntransitAddInventoryChange(v));
    }
    /**
     * 单逻辑库位采购在途接收更新在库库存
     */
    private void singlePurchaseIntransitAddInventoryChange(List<PurchaseIntransitInventoryCommand> commands) {
        InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(commands.get(0).getFromLocation());
        List<InventoryChangeItem> inventoryChangeItemList = commands.stream()
            .map(t -> InventoryChangeItem.builder()
                .skuId(t.getSkuId())
                .usage(t.getUsageCode())
                .lotId(t.getLotId()).qty(t.getQty())
                .inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
                .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE).build()).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItemList);
        warehouseSkuInventoryService.changInventory(inventoryChange);
    }

    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<PurchaseIntransitInventoryCommand> commands){
        return  commands.stream().map(i -> {
            CommandInventoryNumDto item = new CommandInventoryNumDto();
            item.setSkuId(i.getSkuId());
            item.setLotId(i.getLotId());
            item.setLocation(i.getFromLocation());
            item.setFreeQty(i.getQty());
            item.setFrozenQty(BigDecimal.ZERO);
            item.setTransferIntransitQty(BigDecimal.ZERO);
            item.setInQty(i.getQty());
            item.setOutQty(BigDecimal.ZERO);
            item.setUsageCode(i.getUsageCode());
            return item;
        }).collect(Collectors.toList());
    }


}
