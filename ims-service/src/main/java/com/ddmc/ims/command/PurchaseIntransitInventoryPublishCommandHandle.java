package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.converter.PurchaseIntransitInventoryConverter;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PurchaseIntransitInventoryPublishCommandHandle extends
    AbstractInventoryCommandHandle<PurchaseIntransitInventoryCommand> {

    @Resource
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;


    @Override
    public void handleInventoryCommand(List<PurchaseIntransitInventoryCommand> commands) {
        Set<String> orderSet = commands.stream().map(PurchaseIntransitInventoryCommand::getOrderNo).collect(Collectors.toSet());
        checkOrderCount(orderSet.size());
        publishPurchaseInTransit(commands);

    }

    /**
     * 发布采购在途
     *
     * @param commands 发布采购在途命令
     */
    public void publishPurchaseInTransit(List<PurchaseIntransitInventoryCommand> commands){
        if (CollectionUtils.isEmpty(commands)){
            return;
        }
        List<PurchaseIntransitInventory> purchaseIntransitInventoryList = commands.stream()
            .map(PurchaseIntransitInventoryConverter::converterSavePurchaseInTransitInventory).collect(
                Collectors.toList());
        Lists.partition(purchaseIntransitInventoryList, CommonConstants.BATCH_INSERT_DB_100)
            .forEach(purchaseIntransitInventoryMapper::batchInsert);
    }


}
