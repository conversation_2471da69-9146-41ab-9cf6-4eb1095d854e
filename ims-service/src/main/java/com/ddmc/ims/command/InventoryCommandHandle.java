package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface InventoryCommandHandle<T extends SkuInventoryCommand> {

    /**
     * 根据库存指令处理库存
     *
     * @param commands 库存指令
     */
    void handleInventoryCommand(List<T> commands);


    List<CommandInventoryNumDto> getCommandInventoryNum(List<T> commands);
}
