package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.common.util.JsonUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class EmptyOperateCommandHandle extends AbstractInventoryCommandHandle<SkuInventoryCommand> {

    @Override
    public void handleInventoryCommand(List<SkuInventoryCommand> commands) {
        log.info("[空命令],凭证信息{}", JsonUtil.toJson(commands));
    }


}
