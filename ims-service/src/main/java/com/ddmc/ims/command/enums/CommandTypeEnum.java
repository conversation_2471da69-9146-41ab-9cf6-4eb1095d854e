package com.ddmc.ims.command.enums;

import com.ddmc.ims.command.CleanAllocCommandHandle;
import com.ddmc.ims.command.EmptyOperateCommandHandle;
import com.ddmc.ims.command.InventoryCommandHandle;
import com.ddmc.ims.command.OutBoundInventoryCommandHandle;
import com.ddmc.ims.command.PublishAllocCommandHandle;
import com.ddmc.ims.command.PurchaseBookedIntransitInventoryCleanCommandHandle;
import com.ddmc.ims.command.PurchaseIntransitInventoryCleanCommandHandle;
import com.ddmc.ims.command.PurchaseIntransitInventoryInStoreCommandHandle;
import com.ddmc.ims.command.PurchaseIntransitInventoryModifyArrivalTimeCommandHandle;
import com.ddmc.ims.command.PurchaseIntransitInventoryPublishCommandHandle;
import com.ddmc.ims.command.TransferIntransitInCommandHandle;
import com.ddmc.ims.command.TransferIntransitInventoryCleanCommandHandle;
import com.ddmc.ims.command.TransferIntransitInventoryInStoreCommandHandle;
import com.ddmc.ims.command.TransferIntransitInventoryOutStoreCommandHandle;
import com.ddmc.ims.command.TransferIntransitInventoryPublishCommandHandle;
import com.ddmc.ims.command.TransferIntransitInventoryRejectCommandHandle;
import com.ddmc.ims.command.TransferIntransitOutCommandHandle;
import com.ddmc.ims.command.WarehouseInventoryModifyAvailableCommandHandle;
import com.ddmc.ims.command.WarehouseInventoryTransferCommandHandle;
import com.ddmc.ims.common.enums.StringEnumInterface;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public enum CommandTypeEnum implements StringEnumInterface {


    PUBLISH_PURCHASE_IN_TRANSIT("PUBLISH_PURCHASE_IN_TRANSIT", "登记采购在途", PurchaseIntransitInventoryPublishCommandHandle.class,1),
    MODIFY_PURCHASE_ARRIVAL_TIME("MODIFY_PURCHASE_ARRIVAL_TIME", "修改采购预计到货时间",
        PurchaseIntransitInventoryModifyArrivalTimeCommandHandle.class,1),
    PURCHASE_IN_TRANSIT_TO_AVAILABLE("PURCHASE_IN_TRANSIT_TO_AVAILABLE", "采购在途转可用",
        PurchaseIntransitInventoryInStoreCommandHandle.class,1),
    CLEAN_PURCHASE_IN_TRANSIT("CLEAN_PURCHASE_IN_TRANSIT", "清理采购在途", PurchaseIntransitInventoryCleanCommandHandle.class,2),
    CLEAN_BOOKED_PURCHASE_IN_TRANSIT("CLEAN_BOOKED_PURCHASE_IN_TRANSIT", "清理预约在途量", PurchaseBookedIntransitInventoryCleanCommandHandle.class,2),

    PUBLISH_TRANSFER_IN_TRANSIT("PUBLISH_TRANSFER_IN_TRANSIT", "登记调拨在途", TransferIntransitInventoryPublishCommandHandle.class,1),
    TRANSFER_REJECT("TRANSFER_REJECT", "调拨拒收", TransferIntransitInventoryRejectCommandHandle.class,1),
    AVAILABLE_TO_TRANSFER_IN_TRANSIT("AVAILABLE_TO_TRANSFER_IN_TRANSIT", "可用转调拨在途",
        TransferIntransitInventoryOutStoreCommandHandle.class,1),
    TRANSFER_IN_TRANSIT_OUT("TRANSFER_IN_TRANSIT_OUT", "调拨在途出库",
        TransferIntransitOutCommandHandle.class,1),
    TRANSFER_IN_TRANSIT_TO_AVAILABLE("TRANSFER_IN_TRANSIT_TO_AVAILABLE", "调拨在途转可用",
        TransferIntransitInventoryInStoreCommandHandle.class,1),
    CLEAN_TRANSFER_IN_TRANSIT("CLEAN_TRANSFER_IN_TRANSIT", "清理调拨在途", TransferIntransitInventoryCleanCommandHandle.class,2),
    TRANSFER_IN_TRANSIT_IN("TRANSFER_IN_TRANSIT_IN", "调拨在途入库",
        TransferIntransitInCommandHandle.class,1),

    MODIFY_INVENTORY_AVAILABLE("MODIFY_INVENTORY_AVAILABLE", "变更可用", WarehouseInventoryModifyAvailableCommandHandle.class,1),
    TRANSFER_INVENTORY("TRANSFER_INVENTORY", "库位转移", WarehouseInventoryTransferCommandHandle.class,1),
    EMPTY_OPERATE("EMPTY_OPERATE", "空操作", EmptyOperateCommandHandle.class,1),

    PUBLISH_ALLOC("PUBLISH_ALLOC", "登记占用", PublishAllocCommandHandle.class,1),
    OUTBOUND_INVENTORY("OUTBOUND_INVENTORY", "出库完成", OutBoundInventoryCommandHandle.class,1),
    CLEAN_ALLOC("CLEAN_ALLOC", "清理占用", CleanAllocCommandHandle.class,1),


    ;


    private final String code;

    private final String desc;

    /**
     * 组件类名
     */
    private final Class<? extends InventoryCommandHandle>  commandHandleClassName;

    private final int order;

    CommandTypeEnum(String code, String desc,Class<? extends InventoryCommandHandle> commandHandleClassName, int order) {
        this.code = code;
        this.desc = desc;
        this.commandHandleClassName = commandHandleClassName;
        this.order = order;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public Class<? extends InventoryCommandHandle> getCommandHandleClassName() {
        return commandHandleClassName;
    }

    public int getOrder() {
        return order;
    }


    public static CommandTypeEnum fromCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (CommandTypeEnum s : CommandTypeEnum.values()) {
            if (s.getCode().equals(code)) {
                return s;
            }
        }
        return null;
    }
}
