package com.ddmc.ims.command;


import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.converter.TransferIntransitInventoryConverter;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.manager.inventory.TransferInTransitManager;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TransferIntransitInventoryPublishCommandHandle extends
    AbstractInventoryCommandHandle<TransferIntransitInventoryCommand> {

    @Resource
    private TransferInTransitManager transferInTransitManager;

    @Resource
    private TransferTransitUsageDetailManager transferTransitUsageDetailManager;

    @Override
    public void handleInventoryCommand(List<TransferIntransitInventoryCommand> commands) {
        handleTransferInTransitInventory(commands);
    }


    /**
     * 处理调拨在途，转换命令登记调拨在途
     *
     * @param commands commands
     */
    private void handleTransferInTransitInventory(List<TransferIntransitInventoryCommand> commands) {
        //发布调拨在途，命令转在途对象
        List<TransferIntransitInventory> newInventories = convertToTransferInTransitInventoryList(commands);

        //查询已存在的调拨在途
        Map<TransferInTransitItem, TransferIntransitInventory> existMap = getExistTransferMap(commands);

        List<TransferIntransitInventory> needInsert = new ArrayList<>();
        List<TransferIntransitInventory> needUpdate = new ArrayList<>();

        //判断是否已存在相同,存在的话进行累加
        newInventories.forEach(t -> {
            TransferIntransitInventory exist = existMap.get(TransferInTransitItemConverter.convertToCondition(t));
            if (Objects.isNull(exist)) {
                needInsert.add(t);
            } else {
                t.setId(exist.getId());
                needUpdate.add(t);
            }
        });

        //插入或更新
        transferInTransitManager.insertOrAddPlanOutWaitAllocQty(needInsert, needUpdate);
    }



    /**
     * 发布调拨在途，命令转在途对象
     *
     * @param commands 调拨在途发布命令
     * @return 在途数据
     */
    private List<TransferIntransitInventory> convertToTransferInTransitInventoryList(
        List<TransferIntransitInventoryCommand> commands) {
        return commands.stream().map(TransferIntransitInventoryConverter::convertToPublishTransferInTransitInventory)
            .collect(Collectors.toList());
    }

    /**
     * 查询已存在的调拨在途
     *
     * @param commands commands
     * @return result
     */
    private Map<TransferInTransitItem, TransferIntransitInventory> getExistTransferMap(
        List<TransferIntransitInventoryCommand> commands) {
        List<TransferIntransitInventory> result = transferTransitUsageDetailManager.getTransferInTransitInventoryList(commands);
        return result.stream().collect(Collectors
            .toMap(TransferInTransitItemConverter::convertToCondition, Function.identity(), (n1, n2) -> n1));
    }




}
