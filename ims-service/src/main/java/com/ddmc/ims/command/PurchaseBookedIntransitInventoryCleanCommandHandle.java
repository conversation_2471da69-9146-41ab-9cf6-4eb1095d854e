package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.utils.json.JsonUtil;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PurchaseBookedIntransitInventoryCleanCommandHandle extends
    AbstractInventoryCommandHandle<PurchaseIntransitInventoryCommand> {

    @Resource
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;


    @Override
    public void handleInventoryCommand(List<PurchaseIntransitInventoryCommand> commands) {
        Set<String> orderSet = commands.stream().map(PurchaseIntransitInventoryCommand::getOrderNo).collect(Collectors.toSet());
        checkOrderCount(orderSet.size());
        cleanPurchaseInTransit(commands);
    }


    public void cleanPurchaseInTransit(List<PurchaseIntransitInventoryCommand> commands) {
        PurchaseIntransitInventoryCommand command = commands.get(0);
        List<Long> skuIds = commands.stream().map(PurchaseIntransitInventoryCommand::getSkuId).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());

        List<PurchaseIntransitInventory> purchaseInTransitInventories = purchaseIntransitInventoryMapper
            .selectByOrderSourceAndOrderNoAndSkuIdIn(command.getOrderSource(), command.getOrderNo(), skuIds);
        if(CollectionUtils.isEmpty(purchaseInTransitInventories)){
            log.warn("采购预约在途数据为空, 采购在途命令{}", JsonUtil.toJSON(commands));
            return;
        }
        List<Long> ids = purchaseInTransitInventories.stream().map(PurchaseIntransitInventory::getId).sorted()
            .collect(Collectors.toList());
        Lists.partition(ids, CommonConstants.BATCH_UPDATE_DB_100)
            .forEach(
                subList -> purchaseIntransitInventoryMapper.updateBookedIntransitQtyByIdIn(BigDecimal.ZERO, subList));
    }

}
