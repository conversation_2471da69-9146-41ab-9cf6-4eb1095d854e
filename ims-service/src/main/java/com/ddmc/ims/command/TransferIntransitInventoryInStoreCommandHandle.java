package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.command.helper.TransferInTransitInventoryInStoreHelper;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.common.util.PatternUtils;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TransferIntransitInventoryInStoreCommandHandle extends
    AbstractInventoryCommandHandle<TransferIntransitInventoryCommand> {

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;
    @Resource
    private WarehouseSkuInventoryService warehouseSkuInventoryService;
    @Resource
    private TransferTransitUsageDetailManager transferTransitUsageDetailManager;


    @Override
    public void handleInventoryCommand(List<TransferIntransitInventoryCommand> commands) {
        //判断是否是非四要素订单，兼容历史数据与灰度数据
        TransferIntransitInventoryCommand command = commands.get(0);
        if (!PatternUtils.isExactlyThreeHyphens(command.getOrderNo())) {
            //入库增加库存
            inStoreChangInventory(commands);
            return;
        }

        //调拨在途入库，减少在途数量
        handleTransferInStore(commands);

        //入库增加库存
        inStoreChangInventory(commands);
    }

    /**
     * 调拨在途入库，减少在途数量
     *
     * @param commands 入库转可用命令
     */
    public void handleTransferInStore(List<TransferIntransitInventoryCommand> commands) {
        //四要素+品+出库仓+出库货主+出库逻辑库位编码+入库仓+入库货主+用途，调拨在途map
        Map<TransferInTransitItem, List<TransferIntransitInventory>> skuTransferInventoryMap = getExistTransferMap(
            commands);

        //将命令依次处理，不存在则忽略,存在则更新数据塞入skuTransferInventoryMap
        TransferInTransitInventoryInStoreHelper
            .processTransferInTransitInventoryCommands(commands, skuTransferInventoryMap);

        List<TransferIntransitInventory> changeList = skuTransferInventoryMap.values().stream().flatMap(List::stream)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(changeList)) {
            changeList.sort(Comparator.comparing(TransferIntransitInventory::getId));
            Lists.partition(changeList, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(transferIntransitInventoryMapper::batchUpdateInTransitQty);
        }
    }




    /**
     * 入库变动在库库存
     *
     * @param inventoryCommands 入库转可用命令
     */
    public void inStoreChangInventory(List<TransferIntransitInventoryCommand> inventoryCommands) {
        inventoryCommands.stream().collect(Collectors.groupingBy(TransferIntransitInventoryCommand::getFromLocation))
            .forEach((k, v) -> singleInStoreChangInventory(v));
    }

    /**
     * 单逻辑库位入库变动在库库存
     *
     * @param inventoryCommands 入库转可用命令
     */
    private void singleInStoreChangInventory(List<TransferIntransitInventoryCommand> inventoryCommands) {
        TransferIntransitInventoryCommand inventoryCommand = inventoryCommands.get(0);
        InventoryChange inventoryChange = new InventoryChange();

        //兼容旧代码逻辑
        if (inventoryCommand.getToLocation().equals(CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION)) {
            inventoryChange.setLogicInventoryLocation(inventoryCommand.getFromLocation());
        } else {
            inventoryChange.setLogicInventoryLocation(inventoryCommand.getToLocation());
        }

        List<InventoryChangeItem> inventoryChangeItems = inventoryCommands.stream().map(i ->
            InventoryChangeItem.builder()
                .skuId(i.getSkuId())
                .usage(i.getToUsageCode())
                .lotId(i.getLotId())
                .inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
                .qty(i.getQty())
                .inventoryWorkTypeEnum(
                    i.getToInventoryStatus() == InventoryStatusEnum.NOT_AVAILABLE ? InventoryWorkTypeEnum.FROZEN
                        : InventoryWorkTypeEnum.FREE)
                .build()
        ).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItems);
        warehouseSkuInventoryService.changInventory(inventoryChange);
    }


    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<TransferIntransitInventoryCommand> commands) {
        return commands.stream().map(i -> {
            CommandInventoryNumDto item = new CommandInventoryNumDto();
            item.setSkuId(i.getSkuId());
            item.setLotId(i.getLotId());
            if (CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.getLogicInventoryLocationCode()
                .equals(i.getToLocation().getLogicInventoryLocationCode())) {
                item.setLocation(i.getFromLocation());
            } else {
                item.setLocation(i.getToLocation());
            }
            item.setFreeQty(i.getQty());
            item.setFrozenQty(BigDecimal.ZERO);
            item.setTransferIntransitQty(i.getQty().negate());
            item.setInQty(i.getQty());
            item.setOutQty(BigDecimal.ZERO);
            item.setUsageCode(i.getUsageCode());
            return item;
        }).collect(Collectors.toList());
    }

    /**
     * 查询已存在的调拨在途
     *
     * @param commands commands
     * @return result
     */
    private Map<TransferInTransitItem, List<TransferIntransitInventory>> getExistTransferMap(
        List<TransferIntransitInventoryCommand> commands) {
        List<TransferIntransitInventory> result = transferTransitUsageDetailManager
            .getTransferInTransitInventoryList(commands);
        return result.stream().collect(Collectors
            .groupingBy(t -> TransferInTransitItemConverter.convertToCondition(t, false)));
    }




}
