package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PurchaseIntransitInventoryModifyArrivalTimeCommandHandle extends
    AbstractInventoryCommandHandle<PurchaseIntransitInventoryCommand> {

    @Resource
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;

    @Override
    public void handleInventoryCommand(List<PurchaseIntransitInventoryCommand> commands) {
        Set<String> orderSet = commands.stream().map(PurchaseIntransitInventoryCommand::getOrderNo).collect(Collectors.toSet());
        checkOrderCount(orderSet.size());
        modifyPurchaseArrivalTime(commands);

    }


    /**
     * 修改采购在途发布时间
     *
     * @param commands 采购在途改期
     */
    public void modifyPurchaseArrivalTime(List<PurchaseIntransitInventoryCommand> commands){
        PurchaseIntransitInventoryCommand command = commands.get(0);
        purchaseIntransitInventoryMapper
            .updateExpectArriveTimeByOrder(command.getExpectArriveTime(), command.getOrderSource(),command.getOrderNo());
    }


}
