package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.command.helper.TransferInTransitInventoryInStoreHelper;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.util.PatternUtils;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.converter.TransferIntransitInventoryConverter;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryInDetailMapper;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryInDetail;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TransferIntransitInCommandHandle extends
    AbstractInventoryCommandHandle<TransferIntransitInventoryCommand> {

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;
    @Resource
    private TransferTransitUsageDetailManager transferTransitUsageDetailManager;
    @Resource
    private TransferIntransitInventoryInDetailMapper transferIntransitInventoryInDetailMapper;
    @Resource
    private LocalParamService localParamService;

    @Override
    public void handleInventoryCommand(List<TransferIntransitInventoryCommand> commands) {
        //判断是否是非四要素订单，兼容历史数据与灰度数据
        TransferIntransitInventoryCommand command = commands.get(0);
        if (!PatternUtils.isExactlyThreeHyphens(command.getOrderNo())) {
            return;
        }

        //调拨在途入库，减少在途数量
        handleTransferInStore(commands);
        //处理调拨在途入库明细
        handleTransferIntransitInventoryInDetail(commands);
    }

    /**
     * 调拨在途入库，减少在途数量
     *
     * @param commands 入库转可用命令
     */
    public void handleTransferInStore(List<TransferIntransitInventoryCommand> commands) {
        //四要素+品+出库仓+出库货主+出库逻辑库位编码+入库仓+入库货主+用途，调拨在途map
        Map<TransferInTransitItem, List<TransferIntransitInventory>> skuTransferInventoryMap = getExistTransferMap(
            commands);

        //将命令依次处理，不存在则忽略,存在则更新数据塞入skuTransferInventoryMap
        TransferInTransitInventoryInStoreHelper
            .processTransferInTransitInventoryCommands(commands, skuTransferInventoryMap);

        List<TransferIntransitInventory> changeList = skuTransferInventoryMap.values().stream().flatMap(List::stream)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(changeList)) {
            changeList.sort(Comparator.comparing(TransferIntransitInventory::getId));
            Lists.partition(changeList, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(transferIntransitInventoryMapper::batchUpdateInTransitQty);
        }
    }



    /**
     * 查询已存在的调拨在途
     *
     * @param commands commands
     * @return result
     */
    private Map<TransferInTransitItem, List<TransferIntransitInventory>> getExistTransferMap(
        List<TransferIntransitInventoryCommand> commands) {
        List<TransferIntransitInventory> result = transferTransitUsageDetailManager
            .getTransferInTransitInventoryList(commands);
        return result.stream().collect(Collectors
            .groupingBy(t -> TransferInTransitItemConverter.convertToCondition(t, false)));
    }


    /**
     * 处理调拨在途库存批次明细
     *
     * @param outCommands outCommands
     */
    public void handleTransferIntransitInventoryInDetail(List<TransferIntransitInventoryCommand> outCommands) {

        Boolean enable = localParamService
            .getBooleanValue(LocalParamsConstants.ENABLE_HANDLE_TRANSFER_IN_DETAIL, true);
        if (Boolean.FALSE.equals(enable)) {
            return;
        }

        TransferIntransitInventoryCommand command = outCommands.get(0);
        List<Long> skuIds = outCommands.stream().map(TransferIntransitInventoryCommand::getSkuId).distinct()
            .collect(Collectors.toList());
        List<TransferIntransitInventoryInDetail> existOutDetails = transferIntransitInventoryInDetailMapper
            .selectByRefOrderNoAndRefOrderSourceAndRefExeOrderNoAndRefExeOrderSource(command.getOrderNo(),
                command.getOrderSource(), command.getExeOrderNo(), command.getExeOrderSource(), skuIds);

        //四要素+品+出库仓+出库货主+入库仓+入库货主+入库逻辑库位编码+用途+批次，调拨在途出库明细map
        Map<TransferInTransitItem, TransferIntransitInventoryInDetail> existOutMap = existOutDetails.stream()
            .collect(Collectors.toMap(TransferInTransitItemConverter::convertByInventoryInDetail, Function.identity(),
                (k1, k2) -> k2));

        Map<TransferInTransitItem, TransferIntransitInventoryInDetail> insertMap = Maps
            .newHashMapWithExpectedSize(outCommands.size());

        outCommands.forEach(t -> {
            TransferInTransitItem item = TransferInTransitItemConverter.convertByCommandLot(t);
            TransferIntransitInventoryInDetail existOutDetail = existOutMap.get(item);
            if (Objects.nonNull(existOutDetail)) {
                existOutDetail.setInQty(existOutDetail.getInQty().add(t.getQty()));
                existOutMap.put(item, existOutDetail);
            } else {
                TransferIntransitInventoryInDetail existInsetDetail = insertMap.get(item);
                if (Objects.isNull(existInsetDetail)) {
                    TransferIntransitInventoryInDetail outDetail = TransferIntransitInventoryConverter
                        .convertToTransferIntransitInventoryInDetail(t);
                    insertMap.put(item, outDetail);
                } else {
                    existInsetDetail.setInQty(existInsetDetail.getInQty().add(t.getQty()));
                    insertMap.put(item, existInsetDetail);
                }
            }
        });

        insertOrUpdateInDetail(new ArrayList<>(insertMap.values()), new ArrayList<>(existOutMap.values()));

    }

    /**
     * 保存或更新调拨在途入库明细数据
     *
     * @param insertList insertList
     * @param updateList updateList
     */
    private void insertOrUpdateInDetail(
        List<TransferIntransitInventoryInDetail> insertList,
        List<TransferIntransitInventoryInDetail> updateList) {

        if (CollectionUtils.isNotEmpty(insertList)) {
            Lists.partition(insertList, CommonConstants.BATCH_INSERT_DB_300)
                .forEach(t -> transferIntransitInventoryInDetailMapper.insertList(t));
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.sort(Comparator.comparing(TransferIntransitInventoryInDetail::getId));
            Lists.partition(updateList, CommonConstants.BATCH_UPDATE_DB_300)
                .forEach(t -> transferIntransitInventoryInDetailMapper.batchUpdate(t));
        }
    }

    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<TransferIntransitInventoryCommand> commands){
        return  commands.stream().map(i -> {
            CommandInventoryNumDto item = new CommandInventoryNumDto();
            item.setSkuId(i.getSkuId());
            item.setLotId(i.getLotId());
            item.setLocation(i.getFromLocation());
            item.setUsageCode(i.getUsageCode());
            item.setFreeQty(BigDecimal.ZERO);
            item.setFrozenQty(BigDecimal.ZERO);
            item.setTransferIntransitQty(i.getQty().negate());
            item.setInQty(BigDecimal.ZERO);
            item.setOutQty(BigDecimal.ZERO);
            item.setToLocation(i.getToLocation());
            return item;
        }).collect(Collectors.toList());
    }


}
