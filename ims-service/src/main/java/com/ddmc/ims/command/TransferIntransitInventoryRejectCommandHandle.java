package com.ddmc.ims.command;


import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.converter.TransferIntransitInventoryConverter;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.TransferInTransitManager;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TransferIntransitInventoryRejectCommandHandle extends
    AbstractInventoryCommandHandle<TransferIntransitInventoryCommand> {

    @Resource
    private TransferInTransitManager transferInTransitManager;

    @Resource
    private TransferTransitUsageDetailManager transferTransitUsageDetailManager;

    @Override
    public void handleInventoryCommand(List<TransferIntransitInventoryCommand> commands) {
        //处理调拨在途
        handleTransferInTransitInventory(commands);

    }


    /**
     * 处理调拨在途，转换命令登记调拨在途
     *
     * @param commands commands
     */
    private void handleTransferInTransitInventory(List<TransferIntransitInventoryCommand> commands) {

        //查询已存在的调拨在途
        Map<TransferInTransitItem, TransferIntransitInventory> existMap = getExistTransferMap(commands);
        //发布调拨在途，命令转在途对象
        List<TransferIntransitInventory> needUpdate = new ArrayList<>();
        List<TransferIntransitInventory> needInsert = new ArrayList<>();

        //判断是否已存在相同,存在的话进行累加
        commands.forEach(t -> {
            TransferInTransitItem item = TransferInTransitItemConverter.convertByCommandAll(t);
            TransferIntransitInventory inventory = existMap.get(item);
            if (Objects.isNull(inventory)) {
                TransferIntransitInventory transferIntransitInventory = TransferIntransitInventoryConverter
                    .convertToCommonTransferIntransitInventory(t);
                transferIntransitInventory.setPlanQty(BigDecimal.ZERO);
                transferIntransitInventory.setIntransitQty(t.getQty());
                transferIntransitInventory.setAllocQty(BigDecimal.ZERO);
                transferIntransitInventory.setWaitAllocQty(BigDecimal.ZERO);
                needInsert.add(transferIntransitInventory);
                log.warn("调拨拒收原调拨在途不存在item:{}", JsonUtil.toJson(item));
            } else {
                //增加已发待收量
                inventory.setIntransitQty(inventory.getIntransitQty().add(t.getQty()));
                needUpdate.add(inventory);
            }
        });

        //插入或更新
        transferInTransitManager.updateInTransitQty(needUpdate, needInsert);
    }


    /**
     * 查询已存在的调拨在途
     *
     * @param commands commands
     * @return result
     */
    public Map<TransferInTransitItem, TransferIntransitInventory> getExistTransferMap(
        List<TransferIntransitInventoryCommand> commands) {
        List<TransferIntransitInventory> result = transferTransitUsageDetailManager
            .getTransferInTransitInventoryList(commands);
        return result.stream().collect(Collectors
            .toMap(TransferInTransitItemConverter::convertToCondition, Function.identity(), (n1, n2) -> n1));
    }

    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<TransferIntransitInventoryCommand> commands){
        return  commands.stream().map(i -> {
            CommandInventoryNumDto item = new CommandInventoryNumDto();
            item.setSkuId(i.getSkuId());
            item.setLotId(i.getLotId());
            item.setLocation(i.getFromLocation());
            item.setUsageCode(i.getUsageCode());
            item.setFreeQty(BigDecimal.ZERO);
            item.setFrozenQty(BigDecimal.ZERO);
            item.setTransferIntransitQty(i.getQty());
            item.setInQty(BigDecimal.ZERO);
            item.setOutQty(BigDecimal.ZERO);
            item.setToLocation(i.getToLocation());
            return item;
        }).collect(Collectors.toList());
    }


}
