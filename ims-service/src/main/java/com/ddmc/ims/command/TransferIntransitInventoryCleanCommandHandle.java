package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.TransferOrderNoAndSource;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TransferIntransitInventoryCleanCommandHandle extends
    AbstractInventoryCommandHandle<TransferIntransitInventoryCommand> {

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;
    @Resource
    private TransferTransitUsageDetailManager transferTransitUsageDetailManager;


    @Override
    public void handleInventoryCommand(List<TransferIntransitInventoryCommand> commands) {
        Set<String> orderSet = commands.stream().map(TransferIntransitInventoryCommand::getOrderNo)
            .collect(Collectors.toSet());
        if (orderSet.size() != 1) {
            throw new ImsBusinessException("命令中存在多个订单号");
        }
        TransferIntransitInventoryCommand command = commands.get(0);
        OrderOperateTypeEnum orderOperateTypeEnum = OrderOperateTypeEnum.fromCode(command.getOrderOperateType());
        if (Objects.isNull(orderOperateTypeEnum)) {
            log.error("操作类型为空{}", JsonUtil.toJson(commands));
            return;
        }
        switch (orderOperateTypeEnum) {
            case CANCEL_OUT_APPLY:
                cancelTransferInTransit(commands);
                break;
            case FINISH_IN_OF_STOCK:
                cleanTransferInTransit(commands);
                break;
            case OUT_OF_STOCK:
                handleDoFinishCleanWaitAllocQty(commands);
                break;
            case FINISH_OUT_OF_STOCK:
                outFinishCleanTransferInTransit(commands);
                break;
            default:
                log.error("不支持的类型{}", JsonUtil.toJson(commands));
        }


    }

    /**
     * do关闭或取消清除调拨未发量
     *
     * @param commands commands
     */
    private void cancelTransferInTransit(List<TransferIntransitInventoryCommand> commands) {
        //调拨在途
        List<TransferIntransitInventory> inventoryList = transferTransitUsageDetailManager
            .getTransferInTransitInventoryList(commands);

        //出库仓+出库逻辑库位+出库货主+入库仓+入库逻辑库位+入库货主+sku+用途,调拨在途map
        Map<TransferInTransitItem, TransferIntransitInventory> inventoryMap = inventoryList.stream()
            .collect(Collectors.toMap(TransferInTransitItemConverter::convertByInventoryAllUsage, Function.identity(),
                (n1, n2) -> n1));

        List<TransferIntransitInventory> needUpdate = Lists.newArrayListWithExpectedSize(inventoryList.size());
        //按命令依次处理
        commands.forEach(t -> {
            TransferInTransitItem item = TransferInTransitItemConverter.convertByCommandAll(t);
            TransferIntransitInventory inTransitInventory = inventoryMap.get(item);
            if (Objects.isNull(inTransitInventory)) {
                log.warn("[TransferIntransitInventoryCleanCommandHandle] cancelTransferInTransit 不存在item:{}",
                    JsonUtil.toJson(item));
                return;
            }
            BigDecimal afterQty = inTransitInventory.getWaitAllocQty().subtract(t.getQty());
            inTransitInventory.setWaitAllocQty(afterQty.max(BigDecimal.ZERO));
            inTransitInventory.setPlanQty(inTransitInventory.getPlanQty().subtract(t.getQty()).max(BigDecimal.ZERO));
            needUpdate.add(inTransitInventory);
        });

        if (CollectionUtils.isNotEmpty(needUpdate)) {
            needUpdate.sort(Comparator.comparing(TransferIntransitInventory::getId));
            Lists.partition(needUpdate, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(subList -> transferIntransitInventoryMapper.batchUpdateWaitAllocQtyAndPlanQty(subList));
        }
    }


    /**
     * 清除调拨在途数据
     *
     * @param commands 清除调拨在途
     */
    private void cleanTransferInTransit(List<TransferIntransitInventoryCommand> commands) {

        List<TransferIntransitInventory> inventoryList = transferTransitUsageDetailManager
            .getTransferInTransitInventoryList(commands);

        Map<TransferInTransitItem, TransferIntransitInventory> inventoryMap = inventoryList.stream()
            .collect(Collectors
                .toMap(TransferInTransitItemConverter::convertToInventoryAll, Function.identity(), (n1, n2) -> n1));

        List<TransferIntransitInventory> needUpdate = Lists.newArrayListWithExpectedSize(inventoryList.size());
        //按命令依次处理
        commands.stream().filter(t -> t.getQty().signum() > 0).forEach(t -> {
            TransferInTransitItem item = TransferInTransitItemConverter.convertByCommandAll(t);
            TransferIntransitInventory inTransitInventory = inventoryMap.get(item);
            if (Objects.isNull(inTransitInventory)) {
                log.warn("[TransferIntransitInventoryCleanCommandHandle] cleanTransferInTransit 不存在item:{}",
                    JsonUtil.toJson(item));
                return;
            }
            BigDecimal afterQty = inTransitInventory.getIntransitQty().subtract(t.getQty());
            inTransitInventory.setIntransitQty(afterQty.max(BigDecimal.ZERO));
            needUpdate.add(inTransitInventory);
        });

        if (CollectionUtils.isNotEmpty(needUpdate)) {
            needUpdate.sort(Comparator.comparing(TransferIntransitInventory::getId));
            Lists.partition(needUpdate, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(subList -> transferIntransitInventoryMapper.batchUpdate(subList));
        }


    }


    /**
     * 出库完成，收工处理
     *
     * @param commands commands
     */
    private void outFinishCleanTransferInTransit(List<TransferIntransitInventoryCommand> commands) {

        List<Long> skuIds = commands.stream().map(TransferIntransitInventoryCommand::getSkuId).distinct()
            .filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(skuIds)) {
            TransferIntransitInventoryCommand command = commands.get(0);
            transferIntransitInventoryMapper
                .updateWaitAllocQtyToZeroBySku(
                    new TransferOrderNoAndSource(command.getOrderSource(), command.getOrderNo()), skuIds);
        } else {
            TransferIntransitInventoryCommand command = commands.get(0);
            transferIntransitInventoryMapper
                .updateWaitAllocQtyToZero(new TransferOrderNoAndSource(command.getOrderSource(), command.getOrderNo()));
        }
    }


    /**
     * do出库完成清除计划未发量
     *
     * @param commands commands
     */
    private void handleDoFinishCleanWaitAllocQty(List<TransferIntransitInventoryCommand> commands) {
        commands = commands.stream()
            .filter(t -> Objects.equals(t.getCommandType(), CommandTypeEnum.CLEAN_TRANSFER_IN_TRANSIT))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(commands)) {
            return;
        }
        //调拨在途
        List<TransferIntransitInventory> skuInTransitList = transferTransitUsageDetailManager
            .getTransferInTransitInventoryList(commands);

        //调拨在途map
        Map<TransferInTransitItem, TransferIntransitInventory> skuInTransitMap = skuInTransitList.stream()
            .collect(Collectors.toMap(TransferInTransitItemConverter::convertByInventoryAllUsage, Function.identity(),
                (n1, n2) -> n1));

        List<TransferIntransitInventory> needUpdate = Lists.newArrayListWithExpectedSize(commands.size());
        //按命令依次处理
        commands.stream().filter(t -> t.getQty().signum() > 0).forEach(t -> {
            TransferInTransitItem item = TransferInTransitItemConverter.convertByCommandAll(t);
            TransferIntransitInventory inventory = skuInTransitMap.get(item);
            if (Objects.isNull(inventory)) {
                log.warn("[TransferIntransitInventoryCleanCommandHandle] cleanTransferInTransit 不存在item:{}",
                    JsonUtil.toJson(item));
                return;
            }
            inventory.setWaitAllocQty(inventory.getWaitAllocQty().subtract(t.getQty()).max(BigDecimal.ZERO));
            needUpdate.add(inventory);
        });

        if (CollectionUtils.isNotEmpty(needUpdate)) {
            needUpdate.sort(Comparator.comparing(TransferIntransitInventory::getId));
            Lists.partition(needUpdate, CommonConstants.BATCH_UPDATE_DB_100)
                .forEach(subList -> transferIntransitInventoryMapper.batchUpdateWaitAllocQty(subList));
        }


    }


    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<TransferIntransitInventoryCommand> commands) {
        return commands.stream().filter(t -> Objects.nonNull(t.getFromLocation()) && Objects.nonNull(t.getSkuId())
                && Objects.equals(OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode(), t.getOrderOperateType()))
            .map(i -> {
                CommandInventoryNumDto item = new CommandInventoryNumDto();
                item.setSkuId(i.getSkuId());
                item.setLotId(i.getLotId());
                item.setLocation(i.getFromLocation());
                item.setUsageCode(i.getUsageCode());
                item.setFreeQty(BigDecimal.ZERO);
                item.setFrozenQty(BigDecimal.ZERO);
                item.setTransferIntransitQty(i.getQty().negate());
                item.setInQty(BigDecimal.ZERO);
                item.setOutQty(BigDecimal.ZERO);
                item.setToLocation(i.getToLocation());
                return item;
            }).collect(Collectors.toList());
    }


}
