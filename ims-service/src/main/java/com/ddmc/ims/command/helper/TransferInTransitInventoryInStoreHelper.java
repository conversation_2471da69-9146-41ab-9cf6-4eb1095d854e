package com.ddmc.ims.command.helper;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.bo.inventory.TransferManufactureDateItem;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.converter.TransferManufactureItemConverter;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryLotDetail;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 调拨在途入库命令数据处理帮助类
 *
 * <AUTHOR>
 */
@Slf4j
public class TransferInTransitInventoryInStoreHelper {


    private TransferInTransitInventoryInStoreHelper() {

    }

    /**
     * 将命令依次处理，不存在则忽略,存在则更新数据塞入changeList
     *
     * @param commands commands
     * @param skuTransferInventoryMap skuTransferInventoryMap
     */
    public static void processTransferInTransitInventoryCommands(List<TransferIntransitInventoryCommand> commands,
        Map<TransferInTransitItem, List<TransferIntransitInventory>> skuTransferInventoryMap) {
        commands.forEach(t -> {
            TransferInTransitItem condition = TransferInTransitItemConverter
                .convertByCommand(t, false);
            //获取四要素+品+出库仓+出库货主+出库逻辑库位编码+入库仓+入库货主+用途的在途数据
            List<TransferIntransitInventory> inTransitInventoryList = skuTransferInventoryMap.get(condition);

            if (CollectionUtils.isEmpty(inTransitInventoryList)) {
                log.error("[TransferIntransitInventoryInStoreCommandHandle] handleTransferInStore 不存在,condition{},请求{}",
                    JsonUtil.toJson(condition), JsonUtil.toJson(t));
                return;
            }
            //入库数量大于在途数量时，设置在途数据为0
            changeTransferSkuInventory(inTransitInventoryList, t.getQty(),
                t.getToLocation().getLogicInventoryLocationCode());
        });
    }


    /**
     * 将命令依次处理，不存在则忽略,存在则更新数据塞入existMap
     *
     * @param commands commands
     * @param existMap existMap
     */
    public static void processTransferInTransitInventoryLotDetailCommands(
        List<TransferIntransitInventoryCommand> commands, Map<String, InventoryLotInfo> lotInfoMap,
        Map<TransferManufactureDateItem, List<TransferIntransitInventoryLotDetail>> existMap) {
        commands.forEach(t -> {
            TransferManufactureDateItem item = TransferManufactureItemConverter
                .convertToByCommand(t, false, lotInfoMap, t.getUsageCode());
            //获取待处理的lotDetail明细
            List<TransferIntransitInventoryLotDetail> inventoryLotDetails = existMap.get(item);

            BigDecimal deductionQty = t.getQty();
            if (CollectionUtils.isEmpty(inventoryLotDetails)) {
                log.warn("[TransferIntransitInventoryInStoreCommandHandle] handleTransferLotInStore 不存在item:{}",
                    JsonUtil.toJson(item));
                //处理未匹配到同日期的数据
                handleNotMatchLotDetail(existMap, t, deductionQty);

            } else {
                Optional<TransferIntransitInventoryLotDetail> matchOptional = inventoryLotDetails.stream()
                    .filter(inventory -> inventory.getToLogicInventoryLocationCode()
                        .equals(t.getToLocation().getLogicInventoryLocationCode())).findFirst();
                TransferIntransitInventoryLotDetail lotDetail = matchOptional
                    .orElseGet(() -> inventoryLotDetails.get(0));
                BigDecimal inTransitQty = lotDetail.getIntransitQty().subtract(deductionQty).max(BigDecimal.ZERO);
                lotDetail.setIntransitQty(inTransitQty);
            }
        });
    }

    /**
     * 处理未匹配到同日期的数据
     *
     * @param existMap existMap
     * @param command command
     * @param deductionQty deductionQty
     */
    private static void handleNotMatchLotDetail(
        Map<TransferManufactureDateItem, List<TransferIntransitInventoryLotDetail>> existMap,
        TransferIntransitInventoryCommand command, BigDecimal deductionQty) {
        //匹配不到同日期的话，继续匹配。这时候可能会有多个入库的逻辑库位+多个日期。按日期排序，同入库逻辑库位排序
        List<TransferIntransitInventoryLotDetail> lotDetails = existMap.values().stream().flatMap(List::stream)
            .sorted(Comparator.comparing(TransferIntransitInventoryLotDetail::getManufactureDate)
                .thenComparing(detail -> detail.getToLogicInventoryLocationCode()
                    .equals(command.getToLocation().getLogicInventoryLocationCode()))).collect(Collectors.toList());

        for (TransferIntransitInventoryLotDetail inventory : lotDetails) {
            if (deductionQty.compareTo(BigDecimal.ZERO) <= 0) {
                // 扣减数量用尽，退出循环
                break;
            }
            //待扣量与计划未出量相比取小值
            BigDecimal deductQty = deductionQty.min(inventory.getIntransitQty());
            BigDecimal inTransitQty = inventory.getIntransitQty().subtract(deductQty).max(BigDecimal.ZERO);
            inventory.setIntransitQty(inTransitQty);
            deductionQty = deductionQty.subtract(deductQty);
        }
    }


    /**
     * 扣减调拨在途量
     *
     * @param inventoryList inventoryList
     * @param qty qty
     * @param toLogicLocationCode 目标逻辑库位
     */
    private static void changeTransferSkuInventory(List<TransferIntransitInventory> inventoryList, BigDecimal qty,
        String toLogicLocationCode) {
        //特殊情况下会有多条记录，优先匹配同目标逻辑库位的记录，剩下的再按顺序分配，只能保证总数一致
        Optional<TransferIntransitInventory> matchOptional = inventoryList.stream()
            .filter(t -> t.getToLogicInventoryLocationCode().equals(toLogicLocationCode)).findFirst();
        //存在则是完全匹配，按登记的调拨在途处理
        if (matchOptional.isPresent()) {
            TransferIntransitInventory inventory = matchOptional.get();
            changeInventoryQty(qty, inventory);
            return;
        }
        log.warn("[TransferInTransitInventoryInStoreHelper] 不存在:{}", inventoryList.get(0).getOrderNo());
        //不存在认为是登记了调拨在途的入库逻辑库位编码与实际入库的逻辑库位编码不一致。则调拨在途的相应数据按登记的入库逻辑库位变更
        for (TransferIntransitInventory inventory : inventoryList) {
            if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                // 扣减数量用尽，退出循环
                break;
            }
            //待扣量与计划未出量相比取小值
            BigDecimal deductQty = qty.min(inventory.getIntransitQty());
            changeInventoryQty(deductQty, inventory);
            qty = qty.subtract(deductQty);
        }
    }

    /**
     * 扣减调拨在途量
     *
     * @param qty qty
     * @param inventory inventory
     */
    private static void changeInventoryQty(BigDecimal qty, TransferIntransitInventory inventory) {
        BigDecimal inTransitQty = inventory.getIntransitQty().subtract(qty);
        inventory.setIntransitQty(inTransitQty.max(BigDecimal.ZERO));
    }
}
