package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.OutBoundInventoryCommand;
import com.ddmc.ims.manager.inventory.WarehouseSkuInventoryAllocManager;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CleanAllocCommandHandle extends AbstractInventoryCommandHandle<OutBoundInventoryCommand> {

    @Resource
    private WarehouseSkuInventoryAllocManager warehouseSkuInventoryAllocManager;

    @Override
    public void handleInventoryCommand(List<OutBoundInventoryCommand> commands) {
        //占用量扣减
        OutBoundInventoryCommand command = commands.get(0);
        List<Long> skuIds = commands.stream().map(OutBoundInventoryCommand::getSkuId).filter(Objects::nonNull).distinct()
            .collect(Collectors.toList());
        warehouseSkuInventoryAllocManager.deleteAllocQty(command.getOrderNo(), command.getOrderSource(), skuIds);
    }


}
