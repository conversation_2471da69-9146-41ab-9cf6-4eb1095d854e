package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.WarehouseInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class WarehouseInventoryTransferCommandHandle extends
    AbstractInventoryCommandHandle<WarehouseInventoryCommand> {

    @Resource
    private WarehouseSkuInventoryService warehouseSkuInventoryService;

    @Override
    public void handleInventoryCommand(List<WarehouseInventoryCommand> commands) {
        commands.stream().collect(Collectors.groupingBy(WarehouseInventoryCommand::getFromLocation))
            .forEach((location, v) -> transferInventory(v));
    }


    private void transferInventory(List<WarehouseInventoryCommand> inventoryCommands) {

        InventoryChange fromInventoryChange = buildFromTransferInventory(inventoryCommands);

        List<InventoryChange> toInventoryChangeList = new ArrayList<>();
        inventoryCommands.stream().collect(Collectors.groupingBy(WarehouseInventoryCommand::getToLocation))
            .forEach((location, v) -> {
                InventoryChange toInventoryChange = buildToTransferInventory(v);
                if (fromInventoryChange.getLogicInventoryLocation()
                    .equals(toInventoryChange.getLogicInventoryLocation())) {
                    fromInventoryChange.getInventoryChangeItemList()
                        .addAll(toInventoryChange.getInventoryChangeItemList());
                } else {
                    toInventoryChangeList.add(toInventoryChange);
                }
            });
        warehouseSkuInventoryService.changInventory(fromInventoryChange);
        toInventoryChangeList.forEach(warehouseSkuInventoryService::changInventory);
    }


    InventoryChange buildFromTransferInventory(List<WarehouseInventoryCommand> inventoryCommands) {
        WarehouseInventoryCommand inventoryCommand = inventoryCommands.get(0);
        InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(inventoryCommand.getFromLocation());
        List<InventoryChangeItem> inventoryChangeItems = inventoryCommands.stream().map(i -> {
            InventoryChangeItem.InventoryChangeItemBuilder item = InventoryChangeItem.builder();
            item.skuId(i.getSkuId());
            item.lotId(i.getLotId());
            item.usage(i.getUsageCode());
            item.inventoryOperateTypeEnum(InventoryOperateTypeEnum.DECREASE);
            item.qty(i.getQty());
            item.inventoryWorkTypeEnum(i.getFromInventoryWorkTypeEnum());
            return item.build();
        }).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItems);
        return inventoryChange;
    }

    InventoryChange buildToTransferInventory(List<WarehouseInventoryCommand> inventoryCommands) {
        WarehouseInventoryCommand inventoryCommand = inventoryCommands.get(0);
        InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(inventoryCommand.getToLocation());
        List<InventoryChangeItem> inventoryChangeItems = inventoryCommands.stream().map(i -> {
            InventoryChangeItem.InventoryChangeItemBuilder item = InventoryChangeItem.builder();
            item.skuId(i.getSkuId());
            item.lotId(i.getLotId());
            item.usage(i.getToUsageCode());
            item.inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD);
            item.qty(i.getQty());
            item.inventoryWorkTypeEnum(i.getToInventoryWorkTypeEnum());
            return item.build();
        }).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItems);
        return inventoryChange;
    }

    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<WarehouseInventoryCommand> commands) {
        return commands.stream().map(i -> {
            CommandInventoryNumDto item = new CommandInventoryNumDto();
            item.setSkuId(i.getSkuId());
            item.setLotId(i.getLotId());
            item.setLocation(i.getFromLocation());
            if (InventoryWorkTypeEnum.FREE.equals(i.getFromInventoryWorkTypeEnum())) {
                item.setFreeQty(i.getQty().negate());
                item.setFrozenQty(BigDecimal.ZERO);
            } else {
                item.setFreeQty(BigDecimal.ZERO);
                item.setFrozenQty(i.getQty().negate());
            }
            item.setTransferIntransitQty(BigDecimal.ZERO);
            item.setInQty(BigDecimal.ZERO);
            item.setOutQty(BigDecimal.ZERO);
            item.setUsageCode(i.getUsageCode());
            CommandInventoryNumDto item1 = new CommandInventoryNumDto();
            item1.setSkuId(i.getSkuId());
            item1.setLotId(i.getLotId());
            item1.setLocation(i.getToLocation());
            if (InventoryWorkTypeEnum.FREE.equals(i.getToInventoryWorkTypeEnum())) {
                item1.setFreeQty(i.getQty());
                item1.setFrozenQty(BigDecimal.ZERO);
            } else {
                item1.setFreeQty(BigDecimal.ZERO);
                item1.setFrozenQty(i.getQty());
            }
            item1.setTransferIntransitQty(BigDecimal.ZERO);
            item1.setInQty(BigDecimal.ZERO);
            item1.setOutQty(BigDecimal.ZERO);
            item1.setUsageCode(i.getToUsageCode());
            return Lists.newArrayList(item, item1);
        }).flatMap(List::stream).collect(Collectors.toList());
    }

}
