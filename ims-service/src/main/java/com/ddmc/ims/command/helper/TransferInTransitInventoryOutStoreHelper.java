package com.ddmc.ims.command.helper;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.converter.TransferIntransitInventoryConverter;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 调拨在途出库命令数据处理帮助类
 *
 * <AUTHOR>
 */
public class TransferInTransitInventoryOutStoreHelper {

    private TransferInTransitInventoryOutStoreHelper() {

    }


    /**
     * 将命令依次处理，不存在则构建数据塞入insertMap,存在则更新数据塞入existMap
     *
     * @param commands commands
     * @param insertMap insertMap
     * @param existMap existMap
     */
    public static void processTransferInTransitInventoryCommands(List<TransferIntransitInventoryCommand> commands,
        Map<TransferInTransitItem, List<TransferIntransitInventory>> insertMap,
        Map<TransferInTransitItem, List<TransferIntransitInventory>> existMap) {
        commands.forEach(t -> {
            TransferInTransitItem condition = TransferInTransitItemConverter
                .convertByCommand(t, true);
            //获取同四要素+品+出库仓+出库货主+入库仓+入库货主+入库逻辑库位编码+用途的在途数据
            List<TransferIntransitInventory> inTransitInventoryList = existMap
                .getOrDefault(condition, insertMap.get(condition));
            if (CollectionUtils.isEmpty(inTransitInventoryList)) {
                TransferIntransitInventory inTransit = TransferIntransitInventoryConverter
                    .convertToOutTransferInTransitInventory(t);
                inTransit.setPlanQty(BigDecimal.ZERO);
                List<TransferIntransitInventory> newList = new ArrayList<>();
                newList.add(inTransit);
                insertMap.put(condition, newList);
            } else {
                changeTransferSkuInventory(inTransitInventoryList, t.getQty(), t.getFromLocation()
                    .getLogicInventoryLocationCode());
            }
        });
    }


    /**
     * 扣减计划未发量，增加出库量，增加调拨在途量
     *
     * @param inventoryList inventoryList
     * @param qty qty
     * @param fromLogicLocationCode 来源逻辑库位
     */
    private static void changeTransferSkuInventory(List<TransferIntransitInventory> inventoryList, BigDecimal qty,
        String fromLogicLocationCode) {
        //特殊情况下会有多条记录，优先匹配同来源逻辑库位的记录，剩下的再按顺序分配，只能保证总数一致
        Optional<TransferIntransitInventory> matchOptional = inventoryList.stream()
            .filter(t -> t.getFromLogicInventoryLocationCode().equals(fromLogicLocationCode)).findFirst();
        //存在则是完全匹配，按登记的调拨在途处理
        if (matchOptional.isPresent()) {
            TransferIntransitInventory inventory = matchOptional.get();
            changeInventoryQty(qty, inventory);
            return;
        }
        //不存在认为是登记了调拨在途的出库逻辑库位编码与实际出库的逻辑库位编码不一致。则调拨在途的相应数据按登记的出库逻辑库位变更
        for (TransferIntransitInventory inventory : inventoryList) {
            if (qty.compareTo(BigDecimal.ZERO) <= 0) {
                // 扣减数量用尽，退出循环
                break;
            }
            //待扣量与计划未出量相比取小值
            BigDecimal deductQty = qty.min(inventory.getWaitAllocQty());
            changeInventoryQty(deductQty, inventory);
            qty = qty.subtract(deductQty);
        }
        //最后还有剩则为实出量大于所以的计划待出量,则随便取第一个给到
        if (qty.signum() > 0) {
            TransferIntransitInventory inventory = inventoryList.get(0);
            changeInventoryQty(qty, inventory);
        }
    }

    /**
     * 增加调拨在途量，增加出库量，扣减计划待出量
     *
     * @param qty qty
     * @param inventory inventory
     */
    private static void changeInventoryQty(BigDecimal qty, TransferIntransitInventory inventory) {
        BigDecimal allocQty = inventory.getAllocQty().add(qty);
        BigDecimal inTransitQty = inventory.getIntransitQty().add(qty);
        BigDecimal waitAllocQty = inventory.getWaitAllocQty().subtract(qty);
        inventory.setIntransitQty(inTransitQty);
        inventory.setWaitAllocQty(waitAllocQty.max(BigDecimal.ZERO));
        inventory.setAllocQty(allocQty);
    }




}
