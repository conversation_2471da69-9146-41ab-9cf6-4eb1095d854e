package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.OutBoundInventoryCommand;
import com.ddmc.ims.converter.WarehouseSkuInventoryAllocConverter;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail;
import com.ddmc.ims.manager.inventory.WarehouseSkuInventoryAllocManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PublishAllocCommandHandle extends AbstractInventoryCommandHandle<OutBoundInventoryCommand> {

    @Resource
    private WarehouseSkuInventoryAllocManager warehouseSkuInventoryAllocManager;


    @Override
    public void handleInventoryCommand(List<OutBoundInventoryCommand> commands) {

        //命令转占用对象
        List<WarehouseSkuInventoryAllocDetail> warehouseSkuInventoryAllocDetails = WarehouseSkuInventoryAllocConverter
            .convertToWarehouseSkuInventoryAllocDetail(commands);

        List<WarehouseSkuInventoryAllocDetail> existAlloc = warehouseSkuInventoryAllocManager
            .getExistAllocList(commands);
        //已存在的orderNo+orderSource+skuId的数据
        Map<ImmutablePair<Long,String>, WarehouseSkuInventoryAllocDetail> existAllocMap = existAlloc.stream()
            .collect(Collectors.toMap(t->ImmutablePair.of(t.getSkuId(), t.getUsageCode()), Function
                .identity(), (n1, n2) -> n1));

        List<WarehouseSkuInventoryAllocDetail> needInsert = new ArrayList<>();
        warehouseSkuInventoryAllocDetails.forEach(t -> {
            WarehouseSkuInventoryAllocDetail exist = existAllocMap.get(ImmutablePair.of(t.getSkuId(),t.getUsageCode()));
            if (Objects.isNull(exist)) {
                needInsert.add(t);
            }
        });

        //插入
        warehouseSkuInventoryAllocManager.insertOrUpdateAllocQty(needInsert);


    }

}
