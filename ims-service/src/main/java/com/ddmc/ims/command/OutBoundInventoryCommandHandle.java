package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.OutBoundInventoryCommand;
import com.ddmc.ims.bo.command.WarehouseInventoryCommand;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.WarehouseSkuInventoryAllocManager;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OutBoundInventoryCommandHandle extends AbstractInventoryCommandHandle<OutBoundInventoryCommand> {

    @Resource
    private WarehouseSkuInventoryAllocManager warehouseSkuInventoryAllocManager;

    @Resource
    private WarehouseInventoryModifyAvailableCommandHandle warehouseInventoryModifyAvailableCommandHandle;

    @Override
    public void handleInventoryCommand(List<OutBoundInventoryCommand> commands) {
        Optional<OutBoundInventoryCommand> skuIdOptional = commands.stream().filter(t -> Objects.nonNull(t.getSkuId()))
            .findAny();
        if (skuIdOptional.isEmpty()) {
            OutBoundInventoryCommand command = commands.get(0);
            warehouseSkuInventoryAllocManager.deleteAllocQty(command.getExeOrderNo());
            return;
        }

        //已存在的orderNo+orderSource+skuId的数据
        List<WarehouseSkuInventoryAllocDetail> existAlloc = warehouseSkuInventoryAllocManager
            .getExistAllocMapByExeOrderNo(commands);
        //扣减占用量
        warehouseSkuInventoryAllocManager.deleteAllocQty(existAlloc);

        //扣减在库库存
        warehouseInventoryModifyAvailableCommandHandle
            .handleInventoryCommand(convertToWarehouseInventoryCommand(commands));

    }


    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<OutBoundInventoryCommand> commands) {
        List<OutBoundInventoryCommand> filterCommands = commands.stream().filter(t -> Objects.nonNull(t.getSkuId()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterCommands)) {
            return Collections.emptyList();
        }
        return warehouseInventoryModifyAvailableCommandHandle
            .getCommandInventoryNum(convertToWarehouseInventoryCommand(filterCommands));
    }


    private List<WarehouseInventoryCommand> convertToWarehouseInventoryCommand(
        List<OutBoundInventoryCommand> commands) {
        List<WarehouseInventoryCommand> results = Lists.newArrayListWithCapacity(commands.size());
        commands.stream().filter(t->StringUtils.isNotEmpty(t.getLotId())).forEach(t -> {
            WarehouseInventoryCommand command = WarehouseInventoryCommand
                .builder()
                .fromLocation(t.getFromLocation())
                .toLocation(t.getToLocation())
                .commandType(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE)
                .skuId(t.getSkuId())
                .lotId(t.getLotId())
                .qty(t.getQty())
                .fromInventoryStatus(t.getFromInventoryStatus())
                .toInventoryStatus(t.getToInventoryStatus())
                .usageCode(t.getUsageCode())
                .toUsageCode(t.getToUsageCode())
                .build();
            results.add(command);
        });

        return results;
    }


}
