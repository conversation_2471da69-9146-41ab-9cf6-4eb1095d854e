package com.ddmc.ims.command;

import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractInventoryCommandHandle<T extends SkuInventoryCommand> implements
    InventoryCommandHandle<T> {


    @Override
    public List<CommandInventoryNumDto> getCommandInventoryNum(List<T> commands) {
        return Collections.emptyList();
    }


    public void checkOrderCount(int count) {
        if (count > 1) {
            throw new ImsBusinessException("命令中存在多个订单号");
        }
    }


}
