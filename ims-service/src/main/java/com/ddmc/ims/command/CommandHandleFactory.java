package com.ddmc.ims.command;

import com.ddmc.ims.common.exception.ImsBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CommandHandleFactory implements ApplicationContextAware {

    private ApplicationContext applicationContext;


    public InventoryCommandHandle getCommandHandle(String commandHandleClassName) {
        try {
            return (InventoryCommandHandle) applicationContext.getBean(Class.forName(commandHandleClassName));
        } catch (ClassNotFoundException e) {
            log.error("转换命令处理器异常,入参{}", commandHandleClassName, e);
            throw new ImsBusinessException(e);
        }
    }

    public InventoryCommandHandle getCommandHandle(Class<? extends InventoryCommandHandle> clazz) {
        return applicationContext.getBean(clazz);

    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
}
