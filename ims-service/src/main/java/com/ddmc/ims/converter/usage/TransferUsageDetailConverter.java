package com.ddmc.ims.converter.usage;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.PatternUtils;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.inventory.TransferInTransitUsageDetailFinishInManager;
import com.ddmc.ims.manager.inventory.TransferInTransitUsageDetailInManager;
import com.ddmc.ims.manager.inventory.TransferInTransitUsageDetailOutManager;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class TransferUsageDetailConverter implements UsageDetailConverter {

    @Resource
    private TransferInTransitUsageDetailOutManager transferInTransitUsageDetailOutManager;

    @Resource
    private TransferInTransitUsageDetailInManager transferInTransitUsageDetailInManager;

    @Resource
    private TransferInTransitUsageDetailFinishInManager transferInTransitUsageDetailFinishInManager;

    @Override
    public List<CredentialUseageDetail> convert(ConfirmContext confirmContext) {
        CredentialHeader credentialHeaderAndDetail = confirmContext.getCredentialHeader();

        //非四要素则为老逻辑
        if (!PatternUtils.isExactlyThreeHyphens(credentialHeaderAndDetail.getOrderNo())) {
            return credentialHeaderAndDetail.getCredentialDetailList().stream()
                .map(CredentialUsageDetailConverter::getSingleCredentialUsageDetail).collect(Collectors.toList());
        }


        Integer orderOperateType = credentialHeaderAndDetail.getOrderOperateType();
        OrderOperateTypeEnum orderOperateTypeEnum = OrderOperateTypeEnum.fromCode(orderOperateType);
        if (Objects.isNull(orderOperateTypeEnum)) {
            throw new ImsBusinessException("不支持的操作类型");
        }
        switch (orderOperateTypeEnum) {
            case APPLY_OUT_OF_STOCK:
            case TRANSFER_REJECT:
            case CANCEL_OUT_APPLY:
                return credentialHeaderAndDetail.getCredentialDetailList().stream()
                    .map(CredentialUsageDetailConverter::getSingleCredentialUsageDetail).collect(Collectors.toList());
            case FINISH_IN_OF_STOCK:
                return transferInTransitUsageDetailFinishInManager.handleTransferFinishInBound(credentialHeaderAndDetail);
            default:
                //do nothing
        }
        if (isTransferOutBoundOperateType(credentialHeaderAndDetail)) {
            return transferInTransitUsageDetailOutManager.handleTransferOutBound(credentialHeaderAndDetail);
        } else {
            return transferInTransitUsageDetailInManager.handleTransferInBound(credentialHeaderAndDetail);
        }

    }

    @Override
    public Boolean isSupport(String orderType, Integer orderOperateType) {
        return OrderTypeEnum.TRANSFER_OUTBOUND.getCode().equals(orderType) || OrderTypeEnum.TRANSFER_DO_OUTBOUND
            .getCode().equals(orderType) || OrderTypeEnum.TRANSFER_INBOUND.getCode().equals(orderType);
    }


    /**
     * 是否调拨出库操作
     */
    private boolean isTransferOutBoundOperateType(CredentialHeader credentialHeaderAndDetail) {
        return OrderTypeEnum.TRANSFER_OUTBOUND.getCode().equals(credentialHeaderAndDetail.getOrderType()) ||
            OrderTypeEnum.TRANSFER_DO_OUTBOUND.getCode().equals(credentialHeaderAndDetail.getOrderType());
    }


}
