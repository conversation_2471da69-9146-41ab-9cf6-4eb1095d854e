package com.ddmc.ims.converter;

import com.ddmc.ims.bo.command.OutBoundInventoryCommand;
import com.ddmc.ims.common.enums.ims.AllocDimensionEnum;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail;
import com.google.common.collect.Lists;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.time.DateUtils;

/**
 * <AUTHOR>
 */
public class WarehouseSkuInventoryAllocConverter {

    private WarehouseSkuInventoryAllocConverter() {

    }


    public static List<WarehouseSkuInventoryAllocDetail> convertToWarehouseSkuInventoryAllocDetail(
        List<OutBoundInventoryCommand> commands) {
        List<WarehouseSkuInventoryAllocDetail> results = Lists.newArrayListWithCapacity(commands.size());
        commands.forEach(c -> {
            WarehouseSkuInventoryAllocDetail detail = new WarehouseSkuInventoryAllocDetail();
            detail.setLogicInventoryLocationCode(c.getFromLocation().getLogicInventoryLocationCode());
            detail.setSkuId(c.getSkuId());
            detail.setWarehouseId(c.getFromLocation().getWarehouseId());
            detail.setCargoOwnerId(c.getFromLocation().getCargoOwnerId());
            detail.setAllocQty(c.getQty());
            if (Objects.nonNull(c.getExpectOutTime())) {
                detail.setDemandTime(DateUtils.truncate(c.getExpectOutTime(), Calendar.DATE));
            } else {
                detail.setDemandTime(DateUtils.truncate(new Date(), Calendar.DATE));
            }
            detail.setAllocDimension(AllocDimensionEnum.SKU.getCode());
            detail.setOrderSource(c.getOrderSource());
            detail.setOrderNo(c.getOrderNo());
            detail.setOrderType(c.getOrderType());
            detail.setUsageCode(c.getUsageCode());
            detail.setExeOrderNo(c.getExeOrderNo());
            detail.setExeOrderSource(c.getExeOrderSource());
            detail.setVersion(0);
            results.add(detail);
        });
        return results;
    }

}
