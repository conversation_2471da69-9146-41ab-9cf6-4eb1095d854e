package com.ddmc.ims.converter;

import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.lot.client.response.LotDTO;

public class InventoryLotInfoConverter {

    private InventoryLotInfoConverter() {

    }

    public static InventoryLotInfo converterTo(LotDTO lotDTO) {
        InventoryLotInfo info = new InventoryLotInfo();
        info.setSkuId(lotDTO.getSkuId());
        info.setWarehouseId(lotDTO.getWarehouseId());
        info.setReceiveDate(lotDTO.getReceiveDate());
        info.setUnsalableDate(lotDTO.getNearExpireDate());
        info.setManufactureDate(lotDTO.getManufactureDate());
        info.setLotId(lotDTO.getLotId());
        info.setScmLotNo(lotDTO.getScmLotNo());
        info.setPurchaseNo(lotDTO.getPurchaseNo());
        info.setRegionCodePath(lotDTO.getPlaceOriginCode());
        info.setVendorId(lotDTO.getVendorId());
        return info;
    }
}
