package com.ddmc.ims.converter.usage;

import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class OutStoreAdjustWarehouseUsageDetailConverter extends AdjustInventoryWarehouseUsageDetailConverter {


    @Override
    public Boolean isSupport(String orderType, Integer orderOperateType) {
        return isOutBoundOperateType(orderOperateType) && !(isTransferOutBoundOperateType(orderType));
    }

    /**
     * 是否出库操作
     */
    private boolean isOutBoundOperateType(Integer orderOperateType) {
        return OrderOperateTypeEnum.OUT_OF_STOCK.getCode().equals(orderOperateType) ||
            OrderOperateTypeEnum.FINISH_OUT_OF_STOCK.getCode().equals(orderOperateType);
    }

    /**
     * 是否调拨出库操作
     */
    private boolean isTransferOutBoundOperateType(String orderType) {
        return OrderTypeEnum.TRANSFER_OUTBOUND.getCode().equals(orderType) ||
            OrderTypeEnum.TRANSFER_DO_OUTBOUND.getCode().equals(orderType);
    }

    /**
     * 扣减数量溢出时的默认用途
     * @param usageCodes 凭证传入的用途
     * @return 扣减数量溢出时的默认用途
     */
    @Override
    public String getDefaultUsageForQtyOver(List<String> usageCodes) {
        if (CollectionUtils.isEmpty(usageCodes)) {
            return null;
        }
        return usageCodes.get(usageCodes.size() - 1);
    }

    /**
     * 出库走调整单模式，不过ocs传入正值，变动值取反
     */
    @Override
    public BigDecimal getChangeInventoryQty(BigDecimal qty) {
        return qty.negate();
    }

    @Override
    protected BigDecimal getDirectUsageQty(BigDecimal qty) {
        return qty;
    }

    @Override
    protected List<CredentialDetail> getSortQtyDetailList(List<CredentialDetail> credentialDetailList) {
        return credentialDetailList
            .stream().sorted(Comparator.comparing(CredentialDetail::getQty)).collect(Collectors.toList());
    }


}
