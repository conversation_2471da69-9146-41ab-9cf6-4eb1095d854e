package com.ddmc.ims.converter.usage;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface UsageDetailConverter {

    /**
     * 转换凭证用途明细
     *
     * @param confirmContext 凭证信息
     * @return 凭证用途明细
     */
    List<CredentialUseageDetail> convert(ConfirmContext confirmContext);

    /**
     * 执行器是否支持
     *
     * @param orderType 单据类型
     * @param orderOperateType 操作类型
     * @return 响应
     */
    Boolean isSupport(String orderType, Integer orderOperateType);
}
