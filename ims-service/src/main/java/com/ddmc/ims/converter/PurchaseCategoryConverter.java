package com.ddmc.ims.converter;

import com.ddmc.greenhouse.carbon.admin.third.api.client.response.category.ManageCategoryTreeNodeResponse;
import com.ddmc.ims.common.bo.ManageCategory.ProductAdmPurchaseCategory;
import com.google.common.collect.Lists;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;

public class PurchaseCategoryConverter {

    private PurchaseCategoryConverter() {
    }

    /**
     * 转换为PurchaseCategoryVO List
     *
     * @param responses responses
     * @return result
     */
    public static List<ProductAdmPurchaseCategory> convertToPurchaseCategory(
        List<ManageCategoryTreeNodeResponse> responses) {
        List<ProductAdmPurchaseCategory> result = Lists.newArrayListWithCapacity(responses.size());
        responses.forEach(response -> {
            ProductAdmPurchaseCategory vo = new ProductAdmPurchaseCategory();
            vo.setText(response.getName());
            vo.setId(response.getId());
            vo.setPath(response.getPath());
            vo.setParent(response.getParentId());
            vo.setState("open");
            List<ProductAdmPurchaseCategory> children = convertToPurchaseCategory(
                response.getTreeNodeList());
            if (CollectionUtils.isNotEmpty(children)) {
                vo.setChildren(children);
            }
            result.add(vo);
        });
        return result;
    }
}
