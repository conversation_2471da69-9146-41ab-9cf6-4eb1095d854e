package com.ddmc.ims.converter.usage;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.bo.credential.WarehouseLotInventoryBo;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.inventory.WarehouseSkuLotInventoryManager;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component("productInUsageDetailConverter")
public class ProductInUsageDetailConverter implements UsageDetailConverter {

    @Resource
    private WarehouseSkuLotInventoryManager warehouseSkuLotInventoryManager;

    @Override
    public List<CredentialUseageDetail> convert(ConfirmContext confirmContext) {
        CredentialHeader credentialHeaderAndDetail = confirmContext.getCredentialHeader();
        List<CredentialUseageDetail> allCredentialUseageDetails = credentialHeaderAndDetail.getCredentialDetailList()
            .stream()
            .filter(t -> !CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE.equals(t.getFromLogicLocationCode()))
            .map(t -> {
                CredentialUseageDetail singleCredentialUsageDetail = CredentialUsageDetailConverter.getSingleCredentialUsageDetail(
                    t);
                singleCredentialUsageDetail.setCommandType(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode());
                return singleCredentialUsageDetail;
            }).collect(Collectors.toList());
        List<CredentialDetail> processingCredentialDetailList = credentialHeaderAndDetail.getCredentialDetailList()
            .stream()
            .filter(t -> CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE.equals(t.getFromLogicLocationCode()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processingCredentialDetailList)) {
            return allCredentialUseageDetails;
        }
        CredentialDetail credentialDetail = processingCredentialDetailList.get(0);
        LogicInventoryLocation location = new LogicInventoryLocation(credentialDetail.getFromWarehouseId(),
            credentialDetail.getFromCargoOwnerId(), credentialDetail.getFromLogicLocationCode());
        List<Long> skuIds = processingCredentialDetailList.stream().map(CredentialDetail::getSkuId)
            .collect(Collectors.toList());
        List<WarehouseLotInventoryBo> warehouseSkuLotInventories = warehouseSkuLotInventoryManager.getWarehouseLotInventoryBo(skuIds, location);

        Map<Long, List<WarehouseLotInventoryBo>> skuLotInventoryMap = warehouseSkuLotInventories.stream()
            .collect(Collectors.groupingBy(WarehouseLotInventoryBo::getSkuId));
        List<CredentialUseageDetail> processingUsageDetailList = processingCredentialDetailList.stream()
            .map(t -> getProcessingUseageDetail(t, skuLotInventoryMap.get(t.getSkuId()))).flatMap(List::stream)
            .collect(Collectors.toList());
        allCredentialUseageDetails.addAll(processingUsageDetailList);
        return allCredentialUseageDetails;

    }


    private List<CredentialUseageDetail> getProcessingUseageDetail(CredentialDetail credentialDetail,
        List<WarehouseLotInventoryBo> warehouseLotInventoryBos) {
        List<CredentialUseageDetail> credentialUseageDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(warehouseLotInventoryBos)) {
            credentialUseageDetailList.add(
                getCredentialUsageDetailByLot(credentialDetail, warehouseSkuLotInventoryManager.getLotId(credentialDetail.getWarehouseId(),
                    credentialDetail.getSkuId()), credentialDetail.getQty()));
            return credentialUseageDetailList;
        }
        List<WarehouseLotInventoryBo> positiveInventoryBoList = warehouseLotInventoryBos.stream()
            .filter(t -> t.getFreeQty().signum() > 0).sorted(Comparator.comparing(WarehouseLotInventoryBo::getLotId))
            .collect(Collectors.toList());
        //负数扣减取反
        BigDecimal deductedQty = credentialDetail.getQty().negate();
        for (WarehouseLotInventoryBo warehouseLotInventoryBo : positiveInventoryBoList) {
            if (deductedQty.compareTo(BigDecimal.ZERO) == 0) {
                return credentialUseageDetailList;
            }
            BigDecimal minQty = warehouseLotInventoryBo.getFreeQty().min(deductedQty);
            credentialUseageDetailList.add(
                getCredentialUsageDetailByLot(credentialDetail, warehouseLotInventoryBo.getLotId(), minQty.negate()));
            warehouseLotInventoryBo.setFreeQty(warehouseLotInventoryBo.getFreeQty().subtract(minQty));
            deductedQty = deductedQty.subtract(minQty);
        }
        if (deductedQty.compareTo(BigDecimal.ZERO) != 0 && CollectionUtils.isNotEmpty(credentialUseageDetailList)) {

            CredentialUseageDetail credentialUseageDetail = credentialUseageDetailList.get(
                credentialUseageDetailList.size() - 1);
            credentialUseageDetail.setQty(credentialUseageDetail.getQty().add(deductedQty.negate()));
            return credentialUseageDetailList;
        }
        if (deductedQty.compareTo(BigDecimal.ZERO) != 0 ) {
            WarehouseLotInventoryBo warehouseLotInventoryBo = warehouseLotInventoryBos.get(
                warehouseLotInventoryBos.size() - 1);
            credentialUseageDetailList.add(
                getCredentialUsageDetailByLot(credentialDetail, warehouseLotInventoryBo.getLotId(), deductedQty.negate()));
        }
        return credentialUseageDetailList;
    }

    @Override
    public Boolean isSupport(String orderType, Integer orderOperateType) {
        return OrderTypeEnum.PRODUCT_INBOUND.getCode().equals(orderType);
    }


    public static CredentialUseageDetail getCredentialUsageDetailByLot(CredentialDetail credentialDetail, String lotId,
        BigDecimal qty) {
        CredentialUseageDetail usageDetail = CredentialUsageDetailConverter.getSingleCredentialUsageDetail(
            credentialDetail);
        usageDetail.setQty(qty);
        usageDetail.setLotId(lotId);
        usageDetail.setCommandType(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode());
        return usageDetail;
    }



}
