package com.ddmc.ims.converter.usage;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component("commonUsageDetailConverter")
public class CommonUsageDetailConverter implements UsageDetailConverter {

    @Override
    public List<CredentialUseageDetail> convert(ConfirmContext confirmContext) {
        CredentialHeader credentialHeaderAndDetail = confirmContext.getCredentialHeader();
        return credentialHeaderAndDetail
            .getCredentialDetailList().stream().map(CredentialUsageDetailConverter
                ::getSingleCredentialUsageDetail).collect(Collectors.toList());
    }

    @Override
    public Boolean isSupport(String orderType, Integer orderOperateType) {
        return false;
    }
}
