package com.ddmc.ims.converter.usage;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.inventory.PurchaseIntransitUsageDetailManager;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class PurchaseInUsageDetailConverter implements UsageDetailConverter {

    @Resource
    private PurchaseIntransitUsageDetailManager purchaseIntransitUsageDetailManager;

    @Override
    public List<CredentialUseageDetail> convert(ConfirmContext confirmContext) {
        CredentialHeader credentialHeaderAndDetail = confirmContext.getCredentialHeader();
        return purchaseIntransitUsageDetailManager.getUseageDetail(credentialHeaderAndDetail);
    }

    @Override
    public Boolean isSupport(String orderType, Integer orderOperateType) {
        return OrderTypeEnum.PURCHASE.getCode().equals(orderType);
    }
}
