package com.ddmc.ims.converter.usage;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.bo.credential.WarehouseLotInventoryBo;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.inventory.WarehouseSkuLotInventoryManager;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component("manufactureUsageDetailConverter")
public class ManufactureUsageDetailConverter implements UsageDetailConverter {


    @Resource
    private WarehouseSkuLotInventoryManager warehouseSkuLotInventoryManager;

    @Override
    public List<CredentialUseageDetail> convert(ConfirmContext confirmContext) {
        CredentialHeader credentialHeaderAndDetail = confirmContext.getCredentialHeader();
        List<CredentialDetail> processingCredentialDetailList = credentialHeaderAndDetail.getCredentialDetailList()
            .stream()
            .filter(t -> CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE.equals(t.getFromLogicLocationCode()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processingCredentialDetailList)) {
            return Collections.emptyList();
        }
        CredentialDetail credentialDetail = processingCredentialDetailList.get(0);
        LogicInventoryLocation location = new LogicInventoryLocation(credentialDetail.getFromWarehouseId(),
            credentialDetail.getFromCargoOwnerId(), credentialDetail.getFromLogicLocationCode());
        List<Long> skuIds = processingCredentialDetailList.stream().map(CredentialDetail::getSkuId)
            .distinct().collect(Collectors.toList());
        List<WarehouseLotInventoryBo> warehouseSkuLotInventories = warehouseSkuLotInventoryManager.getWarehouseLotInventoryBo(
            skuIds, location);

        Map<Long, List<WarehouseLotInventoryBo>> skuLotInventoryMap = warehouseSkuLotInventories.stream()
            .collect(Collectors.groupingBy(WarehouseLotInventoryBo::getSkuId));
        List<CredentialUseageDetail> processingUsageDetailList = processingCredentialDetailList.stream()
            .map(t -> getProcessingUseageDetail(t, skuLotInventoryMap.get(t.getSkuId()))).flatMap(List::stream)
            .collect(Collectors.toList());
        return distinctSkuAndLot(processingUsageDetailList);
    }

    private List<CredentialUseageDetail> distinctSkuAndLot(List<CredentialUseageDetail> processingUsageDetailList) {
        Map<String, CredentialUseageDetail> mutableTripleCredentialUseageDetailMap = processingUsageDetailList.stream()
            .collect(Collectors.toMap(
                t -> t.getLotId() + "_" + t.getSkuId() + "_" + t.getFromLogicLocationCode() + "_" + t.getUsageCode(),
                Function.identity(), (k1, k2) -> {
                    k1.setQty(k1.getQty().add(k2.getQty()));
                    return k1;
                }));
        return new ArrayList<>(mutableTripleCredentialUseageDetailMap.values());
    }

    @Override
    public Boolean isSupport(String orderType, Integer orderOperateType) {
        return OrderOperateTypeEnum.FINISH_PRODUCTION.getCode().equals(orderOperateType) && (
            OrderTypeEnum.POSITIVE_MANUFACTURE.getCode().equals(orderType)
                || OrderTypeEnum.REVERSE_MANUFACTURE.getCode().equals(orderType));
    }

    private List<CredentialUseageDetail> getProcessingUseageDetail(CredentialDetail credentialDetail,
        List<WarehouseLotInventoryBo> warehouseLotInventoryBos) {
        List<CredentialUseageDetail> credentialUseageDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(warehouseLotInventoryBos)) {
            credentialUseageDetailList.add(getCredentialUsageDetailByLot(credentialDetail,
                warehouseSkuLotInventoryManager.getLotId(credentialDetail.getWarehouseId(),
                    credentialDetail.getSkuId()), credentialDetail.getQty()));
            return credentialUseageDetailList;
        }
        balanceNegativeInventory(credentialDetail, credentialUseageDetailList, warehouseLotInventoryBos);
        if (credentialDetail.getQty().signum() == 0) {
            return credentialUseageDetailList;
        }
        manufactureInventory(credentialDetail, credentialUseageDetailList, warehouseLotInventoryBos);
        return credentialUseageDetailList;
    }

    private void manufactureInventory(CredentialDetail credentialDetail,
        List<CredentialUseageDetail> credentialUseageDetailList,
        List<WarehouseLotInventoryBo> warehouseLotInventoryBos) {
        List<WarehouseLotInventoryBo> negativeInventoryBoList = warehouseLotInventoryBos.stream()
            .filter(t -> t.getFreeQty().signum() < 0).sorted(Comparator.comparing(WarehouseLotInventoryBo::getLotId))
            .collect(Collectors.toList());
        List<WarehouseLotInventoryBo> positiveInventoryBoList = warehouseLotInventoryBos.stream()
            .filter(t -> t.getFreeQty().signum() > 0).sorted(Comparator.comparing(WarehouseLotInventoryBo::getLotId))
            .collect(Collectors.toList());
        BigDecimal deductedQty = credentialDetail.getQty();
        if (deductedQty.signum() > 0) {
            for (WarehouseLotInventoryBo warehouseLotInventoryBo : negativeInventoryBoList) {
                if (deductedQty.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                BigDecimal minQty = warehouseLotInventoryBo.getFreeQty().abs().min(deductedQty);
                credentialUseageDetailList.add(
                    getCredentialUsageDetailByLot(credentialDetail, warehouseLotInventoryBo.getLotId(), minQty));
                warehouseLotInventoryBo.setFreeQty(warehouseLotInventoryBo.getFreeQty().add(minQty));
                deductedQty = deductedQty.subtract(minQty);
            }
        }
        if (deductedQty.signum() < 0) {
            for (WarehouseLotInventoryBo warehouseLotInventoryBo : positiveInventoryBoList) {
                if (deductedQty.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                BigDecimal minQty = warehouseLotInventoryBo.getFreeQty().min(deductedQty.abs());
                credentialUseageDetailList.add(
                    getCredentialUsageDetailByLot(credentialDetail, warehouseLotInventoryBo.getLotId(),
                        minQty.negate()));
                warehouseLotInventoryBo.setFreeQty(warehouseLotInventoryBo.getFreeQty().subtract(minQty));
                deductedQty = deductedQty.add(minQty);
            }
        }
        if (deductedQty.compareTo(BigDecimal.ZERO) != 0 && CollectionUtils.isNotEmpty(credentialUseageDetailList)) {
            CredentialUseageDetail credentialUseageDetail = credentialUseageDetailList.get(
                credentialUseageDetailList.size() - 1);
            credentialUseageDetail.setQty(credentialUseageDetail.getQty().add(deductedQty));
            return;
        }
        if (deductedQty.compareTo(BigDecimal.ZERO) != 0) {
            credentialUseageDetailList.add(getCredentialUsageDetailByLot(credentialDetail,
                warehouseLotInventoryBos.get(warehouseLotInventoryBos.size() - 1).getLotId(), deductedQty));
        }

    }

    private void balanceNegativeInventory(CredentialDetail credentialDetail,
        List<CredentialUseageDetail> credentialUseageDetailList,
        List<WarehouseLotInventoryBo> warehouseLotInventoryBos) {
        List<WarehouseLotInventoryBo> negativeInventoryBoList = warehouseLotInventoryBos.stream()
            .filter(t -> t.getFreeQty().signum() < 0).sorted(Comparator.comparing(WarehouseLotInventoryBo::getLotId))
            .collect(Collectors.toList());
        List<WarehouseLotInventoryBo> positiveInventoryBoList = warehouseLotInventoryBos.stream()
            .filter(t -> t.getFreeQty().signum() > 0).sorted(Comparator.comparing(WarehouseLotInventoryBo::getLotId))
            .collect(Collectors.toList());

        for (WarehouseLotInventoryBo negativeLotInventoryBo : negativeInventoryBoList) {
            BigDecimal negativeFreeQty = negativeLotInventoryBo.getFreeQty();
            for (WarehouseLotInventoryBo positiveLotInventoryBo : positiveInventoryBoList) {
                BigDecimal minQty = negativeFreeQty.abs().min(positiveLotInventoryBo.getFreeQty());
                positiveLotInventoryBo.setFreeQty(positiveLotInventoryBo.getFreeQty().subtract(minQty));
                if (minQty.signum() != 0) {
                    credentialUseageDetailList.add(
                        getCredentialUsageDetailByLot(credentialDetail, negativeLotInventoryBo.getLotId(), minQty));
                    credentialUseageDetailList.add(
                        getCredentialUsageDetailByLot(credentialDetail, positiveLotInventoryBo.getLotId(),
                            minQty.negate()));
                }
                negativeFreeQty = negativeFreeQty.add(minQty);
                if (negativeFreeQty.signum() == 0) {
                    break;
                }
            }
            negativeLotInventoryBo.setFreeQty(negativeFreeQty);
            if (negativeFreeQty.signum() < 0) {
                break;
            }
        }
    }


    public static CredentialUseageDetail getCredentialUsageDetailByLot(CredentialDetail credentialDetail, String lotId,
        BigDecimal qty) {
        CredentialUseageDetail usageDetail = CredentialUsageDetailConverter.getSingleCredentialUsageDetail(
            credentialDetail);
        usageDetail.setQty(qty);
        usageDetail.setLotId(lotId);
        usageDetail.setCommandType(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode());
        return usageDetail;
    }


}
