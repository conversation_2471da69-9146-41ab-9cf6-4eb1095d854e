package com.ddmc.ims.converter;

import com.ddmc.ims.bo.snapshot.SnapshotInOutItem;
import com.ddmc.ims.bo.snapshot.SnapshotItem;
import com.ddmc.ims.bo.snapshot.SnapshotTransferItem;
import com.ddmc.ims.bo.snapshot.SnapshotUsageItem;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.model.monitor.SkuInventoryInOutSnapshot;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHour;
import com.ddmc.ims.dal.model.monitor.SkuTransferInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.dto.InOutNumDto;
import com.ddmc.ims.dto.InventoryNumDto;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;

/**
 * <AUTHOR>
 */
@Slf4j
public class SnapshotConverter {


    private SnapshotConverter() {

    }

    public static List<SkuInventorySnapshotPerHour> convertToSkuInventorySnapshotPerHour(
        List<SkuInventorySnapshotPerHour> lastSnapshotList, List<CommandInventoryNumDto> commandInventoryNumList,
        Date snapshotDateTime) {
        Map<SnapshotItem, InventoryNumDto> commandInventoryNumDtoMap = commandInventoryNumList.stream().collect(
            Collectors.toMap(SnapshotConverter::getSnapshotItemByCommandDto, SnapshotConverter::getInventoryNumDto,
                (existingItem, newItem) -> {
                    existingItem.setFreeQty(existingItem.getFreeQty().add(newItem.getFreeQty()));
                    existingItem.setFrozenQty(existingItem.getFrozenQty().add(newItem.getFrozenQty()));
                    existingItem.setTransferIntransitQty(
                        existingItem.getTransferIntransitQty().add(newItem.getTransferIntransitQty()));
                    return existingItem;
                }));

        Map<SnapshotItem, SkuInventorySnapshotPerHour> perHourMap = lastSnapshotList.stream()
            .collect(Collectors.toMap(SnapshotConverter::getSnapshotItem, Function.identity(), (n1, n2) -> {
                n1.setFreeQty(n1.getFreeQty().add(n2.getFreeQty()));
                n1.setFrozenQty(n1.getFrozenQty().add(n2.getFrozenQty()));
                n1.setTransferIntransitQty(n1.getTransferIntransitQty().add(n2.getTransferIntransitQty()));
                return n1;
            }));

        List<SkuInventorySnapshotPerHour> result = Lists.newArrayList();

        // 遍历 commandInventoryNumDtoMap，处理每一个 key
        commandInventoryNumDtoMap.forEach((snapshotItem, commandInventoryNumDto) -> {
            SkuInventorySnapshotPerHour perHour = perHourMap.get(snapshotItem);
            if (perHour != null) {
                // 如果 perHourMap 中存在相同的 key，则累加两个对象的属性值
                perHour.setFreeQty(perHour.getFreeQty().add(commandInventoryNumDto.getFreeQty()));
                perHour.setFrozenQty(perHour.getFrozenQty().add(commandInventoryNumDto.getFrozenQty()));
                perHour.setTransferIntransitQty(
                    perHour.getTransferIntransitQty().add(commandInventoryNumDto.getTransferIntransitQty()));
                perHour.setSnapshotDateTime(snapshotDateTime);
                result.add(perHour);
            } else {
                // 如果 perHourMap 中不存在相同的 key，则创建一个新的对象，并将值拷贝过来
                SkuInventorySnapshotPerHour newPerHour = new SkuInventorySnapshotPerHour();
                newPerHour.setSkuId(snapshotItem.getSkuId());
                newPerHour.setWarehouseId(snapshotItem.getWarehouseId());
                newPerHour.setLogicLocationCode(snapshotItem.getLogicLocationCode());
                newPerHour.setCargoOwnerId(snapshotItem.getCargoOwnerId());
                newPerHour.setLotId(snapshotItem.getLotId());
                newPerHour.setSnapshotDateTime(snapshotDateTime);
                newPerHour.setFreeQty(commandInventoryNumDto.getFreeQty());
                newPerHour.setFrozenQty(commandInventoryNumDto.getFrozenQty());
                newPerHour.setTransferIntransitQty(commandInventoryNumDto.getTransferIntransitQty());
                result.add(newPerHour);
            }
        });

        // 遍历 perHourMap，将剩余的对象添加到 result 中
        perHourMap.forEach((snapshotItem, perHour) -> {
            if (!commandInventoryNumDtoMap.containsKey(snapshotItem)) {
                perHour.setSnapshotDateTime(snapshotDateTime);
                result.add(perHour);
            }
        });
        return result.stream().filter(t -> t.getFreeQty().signum() != 0 || t.getFrozenQty().signum() != 0)
            .collect(Collectors.toList());
    }

    public static List<SkuInventorySnapshotUsagePerHour> convertToSkuInventorySnapshotUsagePerHour(
        List<SkuInventorySnapshotUsagePerHour> lastSnapshotList, List<CommandInventoryNumDto> commandInventoryNumList,
        Date snapshotDateTime) {
        Map<SnapshotUsageItem, InventoryNumDto> commandInventoryNumDtoMap = commandInventoryNumList.stream().collect(
            Collectors.toMap(SnapshotConverter::getSnapshotUsageItemByCommandDto, SnapshotConverter::getInventoryNumDto,
                (existingItem, newItem) -> {
                    existingItem.setFreeQty(existingItem.getFreeQty().add(newItem.getFreeQty()));
                    existingItem.setFrozenQty(existingItem.getFrozenQty().add(newItem.getFrozenQty()));
                    existingItem.setTransferIntransitQty(
                        existingItem.getTransferIntransitQty().add(newItem.getTransferIntransitQty()));
                    return existingItem;
                }));

        Map<SnapshotUsageItem, SkuInventorySnapshotUsagePerHour> perHourMap = lastSnapshotList.stream()
            .collect(Collectors.toMap(SnapshotConverter::getSnapshotUsageItem, Function.identity(), (n1, n2) -> n1));

        List<SkuInventorySnapshotUsagePerHour> result = Lists.newArrayList();

        // 遍历 commandInventoryNumDtoMap，处理每一个 key
        commandInventoryNumDtoMap.forEach((snapshotItem, commandInventoryNumDto) -> {
            SkuInventorySnapshotUsagePerHour perHour = perHourMap.get(snapshotItem);
            if (perHour != null) {
                // 如果 perHourMap 中存在相同的 key，则累加两个对象的属性值
                perHour.setFreeQty(perHour.getFreeQty().add(commandInventoryNumDto.getFreeQty()));
                perHour.setFrozenQty(perHour.getFrozenQty().add(commandInventoryNumDto.getFrozenQty()));
                perHour.setTransferIntransitQty(
                    perHour.getTransferIntransitQty().add(commandInventoryNumDto.getTransferIntransitQty()));
                perHour.setSnapshotDateTime(snapshotDateTime);
                result.add(perHour);
            } else {
                // 如果 perHourMap 中不存在相同的 key，则创建一个新的对象，并将值拷贝过来
                SkuInventorySnapshotUsagePerHour newPerHour = new SkuInventorySnapshotUsagePerHour();
                newPerHour.setSkuId(snapshotItem.getSkuId());
                newPerHour.setWarehouseId(snapshotItem.getWarehouseId());
                newPerHour.setLogicLocationCode(snapshotItem.getLogicLocationCode());
                newPerHour.setCargoOwnerId(snapshotItem.getCargoOwnerId());
                newPerHour.setUsageCode(snapshotItem.getUsage());
                newPerHour.setSnapshotDateTime(snapshotDateTime);
                newPerHour.setFreeQty(commandInventoryNumDto.getFreeQty());
                newPerHour.setFrozenQty(commandInventoryNumDto.getFrozenQty());
                newPerHour.setTransferIntransitQty(commandInventoryNumDto.getTransferIntransitQty());
                result.add(newPerHour);
            }
        });

        // 遍历 perHourMap，将剩余的对象添加到 result 中
        perHourMap.forEach((snapshotItem, perHour) -> {
            if (!commandInventoryNumDtoMap.containsKey(snapshotItem)) {
                perHour.setSnapshotDateTime(snapshotDateTime);
                result.add(perHour);
            }
        });
        return result.stream().filter(t -> t.getFreeQty().signum() != 0 || t.getFrozenQty().signum() != 0)
            .collect(Collectors.toList());
    }


    public static List<SkuInventoryInOutSnapshot> convertToSkuInventorySnapshotPerHour(SnapshotTask snapshotTask,
        List<InOutNumDto> fmsInOutNumDtos, List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots) {

        Map<SnapshotInOutItem, InOutNumDto> commandInventoryNumDtoMap = fmsInOutNumDtos.stream().collect(
            Collectors.toMap(SnapshotConverter::getSnapshotItemNoneLot, Function.identity(),
                (existingItem, newItem) -> {
                    existingItem.setInQty(existingItem.getInQty().add(newItem.getInQty()));
                    existingItem.setOutQty(existingItem.getOutQty().add(newItem.getOutQty()));
                    return existingItem;
                }));

        Map<SnapshotInOutItem, SkuInventoryInOutSnapshot> inOutSnapshotMap = skuInventoryInOutSnapshots.stream()
            .collect(Collectors.toMap(SnapshotConverter::getSnapshotItemNoneLot, Function.identity(), (n1, n2) -> n1));

        List<SkuInventoryInOutSnapshot> result = Lists.newArrayList();

        // 遍历 commandInventoryNumDtoMap，处理每一个 key
        commandInventoryNumDtoMap.forEach((snapshotItem, fmsInOutNumDto) -> {
            SkuInventoryInOutSnapshot snapshot = inOutSnapshotMap.get(snapshotItem);
            if (snapshot != null) {
                // 如果 inOutSnapshotMap 中存在相同的 key，则累加两个对象的属性值
                snapshot.setInQty(snapshot.getInQty().add(fmsInOutNumDto.getInQty()));
                snapshot.setOutQty(snapshot.getOutQty().add(fmsInOutNumDto.getOutQty()));
                snapshot.setLotId("");
                snapshot.setSnapshotDateTime(snapshotTask.getSnapshotTime());
                result.add(snapshot);
            } else {
                // 如果 inOutSnapshotMap 中不存在相同的 key，则创建一个新的对象，并将值拷贝过来
                SkuInventoryInOutSnapshot newSnapshot = new SkuInventoryInOutSnapshot();
                newSnapshot.setSkuId(fmsInOutNumDto.getSkuId());
                newSnapshot.setWarehouseId(fmsInOutNumDto.getWarehouseId());
                newSnapshot.setLogicLocationCode(StringUtils.EMPTY);
                newSnapshot.setCargoOwnerId(1L);
                newSnapshot.setLotId(StringUtils.EMPTY);
                newSnapshot.setSnapshotDateTime(snapshotTask.getSnapshotTime());
                newSnapshot.setInQty(fmsInOutNumDto.getInQty());
                newSnapshot.setOutQty(fmsInOutNumDto.getOutQty());
                result.add(newSnapshot);
            }
        });

        // 遍历 inOutSnapshotMap，将剩余的对象添加到 result 中
        inOutSnapshotMap.forEach((snapshotItem, perHour) -> {
            if (!commandInventoryNumDtoMap.containsKey(snapshotItem)) {
                perHour.setSnapshotDateTime(snapshotTask.getSnapshotTime());
                perHour.setLotId(StringUtils.EMPTY);
                result.add(perHour);
            }
        });
        return result.stream().filter(t -> t.getInQty().signum() != 0
            || t.getOutQty().signum() != 0).collect(Collectors.toList());
    }


    public static SnapshotInOutItem getSnapshotItemNoneLot(SkuInventoryInOutSnapshot t) {
        return new SnapshotInOutItem(t.getSkuId(), t.getWarehouseId());
    }

    public static SnapshotInOutItem getSnapshotItemNoneLot(InOutNumDto item) {
        return new SnapshotInOutItem(item.getSkuId(), item.getWarehouseId());
    }

    public static SnapshotUsageItem getSnapshotUsageItemByCommandDto(CommandInventoryNumDto item) {
        return new SnapshotUsageItem(item.getSkuId(), item.getUsageCode(), item.getLocation().getWarehouseId(),
            item.getLocation().getLogicInventoryLocationCode(), item.getLocation().getCargoOwnerId());
    }

    public static SnapshotUsageItem getSnapshotUsageItem(SkuInventorySnapshotUsagePerHour t) {
        return new SnapshotUsageItem(t.getSkuId(), t.getUsageCode(), t.getWarehouseId(), t.getLogicLocationCode(),
            t.getCargoOwnerId());
    }

    public static SnapshotItem getSnapshotItem(SkuInventorySnapshotPerHour t) {
        return new SnapshotItem(t.getSkuId(), t.getLotId(), t.getWarehouseId(), t.getLogicLocationCode(),
            t.getCargoOwnerId());
    }

    public static InventoryNumDto getInventoryNumDto(CommandInventoryNumDto item) {
        return InventoryNumDto.builder().freeQty(item.getFreeQty()).frozenQty(item.getFrozenQty())
            .transferIntransitQty(item.getTransferIntransitQty()).build();
    }

    public static SnapshotItem getSnapshotItemByCommandDto(CommandInventoryNumDto item) {
        return new SnapshotItem(item.getSkuId(), item.getLotId(), item.getLocation().getWarehouseId(),
            item.getLocation().getLogicInventoryLocationCode(), item.getLocation().getCargoOwnerId());
    }


    public static List<SnapshotTask> convertToSnapshotTask(List<Long> warehouseIds, Date snapshotDate,
        SnapshotTypeEnum snapshotTypeEnum) {
        List<SnapshotTask> result = Lists.newArrayListWithExpectedSize(warehouseIds.size());
        warehouseIds.forEach(w -> {
            SnapshotTask snapshotTask = new SnapshotTask();
            snapshotTask.setWarehouseId(w);
            snapshotTask.setSnapshotType(snapshotTypeEnum);
            snapshotTask.setSnapshotTime(snapshotDate);
            snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);
            result.add(snapshotTask);
        });
        return result;

    }


    public static List<SkuTransferInventorySnapshotPerHour> convertToSkuTransferInventorySnapshotPerHour(
        List<SkuTransferInventorySnapshotPerHour> lastSnapshotList, Long warehouseId,
        List<CommandInventoryNumDto> commandInventoryNumList, Date snapshotDateTime) {
        Map<SnapshotTransferItem, BigDecimal> commandInventoryNumDtoMap = commandInventoryNumList.stream()
            .filter(t -> Objects.nonNull(t.getToLocation()) && Objects.equals(warehouseId,
                t.getToLocation().getWarehouseId())).collect(
                Collectors.toMap(SnapshotConverter::getSnapshotTransferItemByCommandDto,
                    CommandInventoryNumDto::getTransferIntransitQty, BigDecimal::add));

        Map<SnapshotTransferItem, SkuTransferInventorySnapshotPerHour> perHourMap = lastSnapshotList.stream()
            .collect(Collectors.toMap(SnapshotConverter::getSnapshotTransferItem, Function.identity(), (n1, n2) -> n1));

        List<SkuTransferInventorySnapshotPerHour> result = Lists.newArrayList();

        // 遍历 commandInventoryNumDtoMap，处理每一个 key
        commandInventoryNumDtoMap.forEach((snapshotItem, transferIntransitQty) -> {
            SkuTransferInventorySnapshotPerHour perHour = perHourMap.get(snapshotItem);
            BigDecimal perTransferIntransitQty = BigDecimal.ZERO;
            if (perHour != null) {
                perTransferIntransitQty = perHour.getTransferIntransitQty();
                // 如果 perHourMap 中存在相同的 key，则累加两个对象的属性值
                perHour.setTransferIntransitQty(perHour.getTransferIntransitQty().add(transferIntransitQty));
                perHour.setSnapshotDateTime(snapshotDateTime);
                result.add(perHour);
            } else {
                // 如果 perHourMap 中不存在相同的 key，则创建一个新的对象，并将值拷贝过来
                SkuTransferInventorySnapshotPerHour newPerHour = new SkuTransferInventorySnapshotPerHour();
                newPerHour.setSkuId(snapshotItem.getSkuId());
                newPerHour.setFromWarehouseId(snapshotItem.getFromWarehouseId());
                newPerHour.setFromLogicLocationCode(snapshotItem.getFromLogicLocationCode());
                newPerHour.setFromCargoOwnerId(snapshotItem.getFromCargoOwnerId());
                newPerHour.setToWarehouseId(snapshotItem.getToWarehouseId());
                newPerHour.setToLogicLocationCode(snapshotItem.getToLogicLocationCode());
                newPerHour.setToCargoOwnerId(snapshotItem.getToCargoOwnerId());
                newPerHour.setSnapshotDateTime(snapshotDateTime);
                newPerHour.setTransferIntransitQty(transferIntransitQty);
                result.add(newPerHour);
            }
            log.info("[调拨在途快照数据] {} 快照 {} 变动值 {}", JsonUtil.toJson(snapshotItem), perTransferIntransitQty,
                transferIntransitQty);
        });

        // 遍历 perHourMap，将剩余的对象添加到 result 中
        perHourMap.forEach((snapshotItem, perHour) -> {
            if (!commandInventoryNumDtoMap.containsKey(snapshotItem)) {
                perHour.setSnapshotDateTime(snapshotDateTime);
                result.add(perHour);
                log.info("[调拨在途快照数据] {} 快照 {} 无变动值", JsonUtil.toJson(snapshotItem));
            }
        });
        return result.stream().filter(t -> t.getTransferIntransitQty().signum() != 0).collect(Collectors.toList());
    }

    public static SnapshotTransferItem getSnapshotTransferItemByCommandDto(CommandInventoryNumDto item) {
        return new SnapshotTransferItem(item.getSkuId(), item.getLocation().getWarehouseId(),
            item.getLocation().getLogicInventoryLocationCode(), item.getLocation().getCargoOwnerId(),
            item.getToLocation().getWarehouseId(), item.getToLocation().getLogicInventoryLocationCode(),
            item.getToLocation().getCargoOwnerId());
    }

    public static SnapshotTransferItem getSnapshotTransferItem(SkuTransferInventorySnapshotPerHour item) {
        return new SnapshotTransferItem(item.getSkuId(), item.getFromWarehouseId(), item.getFromLogicLocationCode(),
            item.getFromCargoOwnerId(), item.getToWarehouseId(), item.getToLogicLocationCode(),
            item.getToCargoOwnerId());
    }


}
