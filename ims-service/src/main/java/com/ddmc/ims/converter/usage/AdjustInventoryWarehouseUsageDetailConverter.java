package com.ddmc.ims.converter.usage;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithUsage;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class AdjustInventoryWarehouseUsageDetailConverter implements UsageDetailConverter {

    @Resource
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;

    @Override
    public List<CredentialUseageDetail> convert(ConfirmContext confirmContext) {
        CredentialHeader credentialHeaderAndDetail = confirmContext.getCredentialHeader();
        if (isSingleUsage(credentialHeaderAndDetail.getCredentialDetailList())) {
            return credentialHeaderAndDetail.getCredentialDetailList().stream().map(
                credentialDetail -> getCredentialUsageDetail(credentialDetail, credentialDetail.getUsageList().get(0),
                    credentialDetail.getQty())).collect(Collectors.toList());
        }
        Map<LogicInventoryLocationWithUsage, WarehouseSkuInventory> locationWithUsageMap = getUsageInventoryMap(
            credentialHeaderAndDetail);
        List<CredentialDetail> sortQtyDetailList = getSortQtyDetailList(
            credentialHeaderAndDetail.getCredentialDetailList());
        return sortQtyDetailList.stream().map(t -> getCredentialUsageDetailList(t, locationWithUsageMap))
            .flatMap(List::stream).collect(Collectors.toList());
    }

    protected List<CredentialDetail> getSortQtyDetailList(List<CredentialDetail> credentialDetailList) {
        return credentialDetailList.stream().sorted(Comparator.comparing(CredentialDetail::getQty).reversed())
            .collect(Collectors.toList());
    }

    @Override
    public Boolean isSupport(String orderType, Integer orderOperateType) {
        return OrderOperateTypeEnum.ADJUSTMENT.getCode().equals(orderOperateType)
            && (!OrderTypeEnum.FREEZE_LOCK.getCode().equals(orderType) &&
            !OrderTypeEnum.INNER_BOUND_TRANSFER.getCode().equals(orderType));
    }

    /**
     * 查询在库库存，按用途进行分组
     */
    private Map<LogicInventoryLocationWithUsage, WarehouseSkuInventory> getUsageInventoryMap(
        CredentialHeader credentialHeaderAndDetail) {
        Set<String> codeSet = Sets.newHashSetWithExpectedSize(
            credentialHeaderAndDetail.getCredentialDetailList().size());
        Set<Long> skuIdSet = Sets.newHashSetWithExpectedSize(
            credentialHeaderAndDetail.getCredentialDetailList().size());
        credentialHeaderAndDetail.getCredentialDetailList().forEach(t -> {
            codeSet.add(t.getFromLogicLocationCode());
            skuIdSet.add(t.getSkuId());
        });
        List<WarehouseSkuInventory> warehouseSkuInventoryList = Lists.partition(Lists.newArrayList(skuIdSet),
            CommonConstants.BATCH_UPDATE_DB_100).stream().map(
            t -> warehouseSkuInventoryMapper.selectWarehouseIdAndSkuIds(credentialHeaderAndDetail.getWarehouseId(),
                codeSet, t)).flatMap(List::stream).collect(Collectors.toList());
        return warehouseSkuInventoryList.stream()
            .collect(Collectors.toMap(this::getLocationWithUsage, Function.identity(), (k1, k2) -> k1));
    }


    private List<CredentialUseageDetail> getCredentialUsageDetailList(CredentialDetail credentialDetail,
        Map<LogicInventoryLocationWithUsage, WarehouseSkuInventory> locationWithUsageMap) {
        BigDecimal changeQty = getChangeInventoryQty(credentialDetail.getQty());
        if (changeQty.compareTo(BigDecimal.ZERO) == 0) {
            return Collections.singletonList(
                getCredentialUsageDetail(credentialDetail, credentialDetail.getUsageList().get(0),
                    credentialDetail.getQty()));
        }

        if (changeQty.compareTo(BigDecimal.ZERO) > 0) {
            addLocationWithUsageQty(changeQty, credentialDetail, locationWithUsageMap);
            return Collections.singletonList(
                getCredentialUsageDetail(credentialDetail, credentialDetail.getUsageList().get(0),
                    credentialDetail.getQty()));
        }
        BigDecimal remainUsage = changeQty.negate();
        List<CredentialUseageDetail> credentialUseageDetails = Lists.newArrayListWithExpectedSize(
            credentialDetail.getUsageList().size());
        for (String usage : credentialDetail.getUsageList()) {
            if (remainUsage.compareTo(BigDecimal.ZERO) <= 0) {
                // 扣减数量用尽，退出循环
                break;
            }
            LogicInventoryLocationWithUsage locationWithUsage = getLocationWithUsage(credentialDetail, usage);
            //当不存在在库库存，直接扣减下一个库存用途
            WarehouseSkuInventory warehouseSkuInventory = locationWithUsageMap.get(locationWithUsage);
            if (Objects.isNull(warehouseSkuInventory)) {
                continue;
            }
            boolean frozenFlag = InventoryStatusEnum.NOT_AVAILABLE.equals(credentialDetail.getInventoryStatus());
            BigDecimal inventoryQty =
                frozenFlag ? warehouseSkuInventory.getFrozenQty() : warehouseSkuInventory.getFreeQty();
            //当当前库存数量小于0时，直接扣减下一个库存用途
            if (inventoryQty.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            // 取可用数量和扣减数量的较小值
            BigDecimal deductedQty = inventoryQty.min(remainUsage);
            CredentialUseageDetail credentialUsageDetail = getCredentialUsageDetail(credentialDetail, usage,
                getDirectUsageQty(deductedQty));
            credentialUseageDetails.add(credentialUsageDetail);
            remainUsage = remainUsage.subtract(deductedQty);
            inventoryQty = inventoryQty.subtract(deductedQty);
            if (frozenFlag) {
                warehouseSkuInventory.setFrozenQty(inventoryQty);
            } else {
                warehouseSkuInventory.setFreeQty(inventoryQty);
            }
        }
        //当库存用途扣减后还有剩余，直接给之后一个用途
        if (remainUsage.compareTo(BigDecimal.ZERO) != 0) {
            String defaultUsage = getDefaultUsageForQtyOver(credentialDetail.getUsageList());
            //入股最后一个用途未分配过进行插入，否则进行修改
            Map<String, CredentialUseageDetail> useageDetailMap = credentialUseageDetails.stream()
                    .collect(Collectors.toMap(CredentialUseageDetail::getUsageCode, Function.identity(), (v1, v2) -> v1));
            if (useageDetailMap.containsKey(defaultUsage)) {
                CredentialUseageDetail useageDetail = useageDetailMap.get(defaultUsage);
                useageDetail.setQty(useageDetail.getQty().add(getDirectUsageQty(remainUsage)));
            } else {
                CredentialUseageDetail credentialUsageDetail = getCredentialUsageDetail(credentialDetail, defaultUsage,
                    getDirectUsageQty(remainUsage));
                credentialUseageDetails.add(credentialUsageDetail);
            }
        }
        return credentialUseageDetails;
    }

    /**
     * 扣减数量溢出时的默认用途
     * @param usageCodes 凭证传入的用途
     * @return 扣减数量溢出时的默认用途
     */
    public String getDefaultUsageForQtyOver(List<String> usageCodes) {
        if (CollectionUtils.isEmpty(usageCodes)) {
            return null;
        }
        return usageCodes.get(0);
    }

    private void addLocationWithUsageQty(BigDecimal changeQty, CredentialDetail credentialDetail,
        Map<LogicInventoryLocationWithUsage, WarehouseSkuInventory> locationWithUsageMap) {
        LogicInventoryLocationWithUsage locationWithUsage = getLocationWithUsage(credentialDetail,
            credentialDetail.getUsageList().get(0));
        WarehouseSkuInventory warehouseSkuInventory = locationWithUsageMap.getOrDefault(locationWithUsage,
            getDefaultWarehouseSkuInventory());
        boolean frozenFlag = InventoryStatusEnum.NOT_AVAILABLE.equals(credentialDetail.getInventoryStatus());
        if (frozenFlag) {
            warehouseSkuInventory.setFrozenQty(warehouseSkuInventory.getFrozenQty().add(changeQty));
        } else {
            warehouseSkuInventory.setFreeQty(warehouseSkuInventory.getFreeQty().add(changeQty));
        }
        locationWithUsageMap.put(locationWithUsage, warehouseSkuInventory);
    }

    private WarehouseSkuInventory getDefaultWarehouseSkuInventory() {
        WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setFrozenQty(BigDecimal.ZERO);
        warehouseSkuInventory.setFreeQty(BigDecimal.ZERO);
        return warehouseSkuInventory;
    }

    protected BigDecimal getChangeInventoryQty(BigDecimal qty) {
        return qty;
    }

    protected BigDecimal getDirectUsageQty(BigDecimal qty) {
        return qty.negate();
    }

    /**
     * 是否用途优先级只有一个
     */
    private boolean isSingleUsage(List<CredentialDetail> credentialDetailList) {
        for (CredentialDetail credentialDetail : credentialDetailList) {
            if (credentialDetail.getUsageList().size() > 1) {
                return false;
            }
        }
        return true;
    }

    private LogicInventoryLocationWithUsage getLocationWithUsage(WarehouseSkuInventory warehouseSkuInventory) {
        LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(
            warehouseSkuInventory.getWarehouseId(), warehouseSkuInventory.getCargoOwnerId(),
            warehouseSkuInventory.getLogicInventoryLocationCode());
        return new LogicInventoryLocationWithUsage(logicInventoryLocation, warehouseSkuInventory.getSkuId(),
            warehouseSkuInventory.getUsageCode());
    }

    private LogicInventoryLocationWithUsage getLocationWithUsage(CredentialDetail credentialDetail, String usage) {
        LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(
            credentialDetail.getFromWarehouseId(), credentialDetail.getFromCargoOwnerId(),
            credentialDetail.getFromLogicLocationCode());
        return new LogicInventoryLocationWithUsage(logicInventoryLocation, credentialDetail.getSkuId(), usage);
    }

    protected CredentialUseageDetail getCredentialUsageDetail(CredentialDetail credentialDetail, String usage,
        BigDecimal deductedQty) {
        return CredentialUsageDetailConverter.getCredentialUsageDetail(
            credentialDetail, usage, deductedQty);
    }
}
