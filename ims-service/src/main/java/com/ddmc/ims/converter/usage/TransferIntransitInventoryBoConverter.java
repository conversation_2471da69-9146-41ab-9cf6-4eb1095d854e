package com.ddmc.ims.converter.usage;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.time.DateUtils;

public class TransferIntransitInventoryBoConverter {

    private TransferIntransitInventoryBoConverter() {
    }



    /**
     * 将changeQty转换为UsageDetail输出
     *
     * @return result
     */
    public static CredentialUseageDetail convertToUsageDetail(TransferIntransitInventoryBo transit,
        BigDecimal changeQty, Long headerId, String lotId) {
        CredentialUseageDetail detail = new CredentialUseageDetail();
        detail.setCredentialHeaderId(headerId);
        detail.setFromLogicLocationCode(transit.getFromLogicInventoryLocationCode());
        detail.setFromCargoOwnerId(transit.getFromCargoOwnerId());
        detail.setFromWarehouseId(transit.getFromWarehouseId());
        detail.setToLogicLocationCode(transit.getToLogicInventoryLocationCode());
        detail.setToCargoOwnerId(transit.getToCargoOwnerId());
        detail.setToWarehouseId(transit.getToWarehouseId());
        detail.setSkuId(transit.getSkuId());
        detail.setQty(changeQty);
        detail.setUsageCode(transit.getUsageCode());
        detail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        detail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        detail.setLotId(lotId);
        detail.setOrderTag("");
        detail.setToUsageCode(transit.getToUsageCode());
        detail.setDemand(CurrentDateUtil.newDate());
        return detail;
    }


    /**
     * 将changeQty转换为UsageDetail输出
     *
     * @return result
     */
    public static CredentialUseageDetail convertToUsageDetailByCredential(CredentialDetail credentialDetail,
        BigDecimal changeQty, Long headerId) {
        CredentialUseageDetail detail = new CredentialUseageDetail();
        detail.setCredentialHeaderId(headerId);
        detail.setFromLogicLocationCode(credentialDetail.getFromLogicLocationCode());
        detail.setFromCargoOwnerId(credentialDetail.getFromCargoOwnerId());
        detail.setFromWarehouseId(credentialDetail.getFromWarehouseId());
        detail.setToLogicLocationCode(credentialDetail.getToLogicLocationCode());
        detail.setToCargoOwnerId(credentialDetail.getToCargoOwnerId());
        detail.setToWarehouseId(credentialDetail.getToWarehouseId());
        detail.setSkuId(credentialDetail.getSkuId());
        detail.setQty(changeQty);
        detail.setUsageCode(credentialDetail.getUsageCode());
        detail.setInventoryStatus(credentialDetail.getInventoryStatus());
        detail.setToInventoryStatus(credentialDetail.getToInventoryStatus());
        detail.setLotId(credentialDetail.getLotId());
        detail.setOrderTag(credentialDetail.getOrderTag());
        detail.setToUsageCode(credentialDetail.getToUsageCode());
        detail.setDemand(CurrentDateUtil.newDate());
        return detail;
    }



    public static TransferIntransitInventoryBo convertToOutTransferInTransitInventory(
        CredentialHeader header, CredentialUseageDetail detail) {
        TransferIntransitInventoryBo inTransitInventory = convertToUsageTransferInTransitInventory(header, detail);
        //生成调拨在途有两种途径，一种是登记，一种是出库的时候没有在途数据则新增一条。第二种情况取目标用途
        inTransitInventory.setUsageCode(detail.getUsageCode());
        inTransitInventory.setToUsageCode(detail.getToUsageCode());
        return inTransitInventory;
    }

    public static TransferIntransitInventoryBo convertToUsageTransferInTransitInventory(
        CredentialHeader header, CredentialUseageDetail detail) {
        TransferIntransitInventoryBo inTransitInventory = new TransferIntransitInventoryBo();
        inTransitInventory.setFromCargoOwnerId(detail.getFromCargoOwnerId());
        inTransitInventory.setFromWarehouseId(detail.getFromWarehouseId());
        inTransitInventory.setFromLogicInventoryLocationCode(detail.getFromLogicLocationCode());
        inTransitInventory.setToCargoOwnerId(detail.getToCargoOwnerId());
        inTransitInventory.setToLogicInventoryLocationCode(detail.getToLogicLocationCode());
        inTransitInventory.setToWarehouseId(detail.getToWarehouseId());
        inTransitInventory.setSkuId(detail.getSkuId());
        inTransitInventory.setOrderSource(header.getOrderSource());
        inTransitInventory.setOrderNo(header.getOrderNo());
        Integer deliveryMode = Objects.isNull(header.getDeliveryMode()) ? -1 : header.getDeliveryMode();
        inTransitInventory.setDeliveryMode(deliveryMode);
        Date expectOutTime = Objects.isNull(header.getExpectOutTime()) ? DateUtils.truncate(CurrentDateUtil.newDate(), Calendar.DATE)
            : DateUtils.truncate(header.getExpectOutTime(), Calendar.DATE);
        inTransitInventory.setExpectOutTime(expectOutTime);
        Date expectInTime = Objects.isNull(header.getExpectInTime()) ? DateUtils.truncate(CurrentDateUtil.newDate(), Calendar.DATE)
            : DateUtils.truncate(header.getExpectInTime(), Calendar.DATE);
        inTransitInventory.setExpectInTime(expectInTime);
        inTransitInventory.setPlanQty(detail.getQty());
        inTransitInventory.setIntransitQty(detail.getQty());
        inTransitInventory.setAllocQty(detail.getQty());
        inTransitInventory.setWaitAllocQty(BigDecimal.ZERO);
        inTransitInventory.setVersion(0);

        inTransitInventory.setUsageCode(detail.getUsageCode());
        inTransitInventory.setToUsageCode(detail.getToUsageCode());
        return inTransitInventory;
    }


    public static List<TransferIntransitInventoryBo> convert(List<TransferIntransitInventory> inventories) {
        List<TransferIntransitInventoryBo> bos = Lists.newArrayListWithCapacity(inventories.size());
        inventories.forEach(t->{
            TransferIntransitInventoryBo bo = new TransferIntransitInventoryBo();
            bo.setId(t.getId());
            bo.setFromCargoOwnerId(t.getFromCargoOwnerId());
            bo.setFromWarehouseId(t.getFromWarehouseId());
            bo.setFromLogicInventoryLocationCode(t.getFromLogicInventoryLocationCode());
            bo.setToCargoOwnerId(t.getToCargoOwnerId());
            bo.setToLogicInventoryLocationCode(t.getToLogicInventoryLocationCode());
            bo.setToWarehouseId(t.getToWarehouseId());
            bo.setSkuId(t.getSkuId());
            bo.setOrderSource(t.getOrderSource());
            bo.setOrderNo(t.getOrderNo());
            bo.setDeliveryMode(t.getDeliveryMode());
            bo.setExpectOutTime(t.getExpectOutTime());
            bo.setExpectInTime(t.getExpectInTime());
            bo.setPlanQty(t.getPlanQty());
            bo.setIntransitQty(t.getIntransitQty());
            bo.setAllocQty(t.getAllocQty());
            bo.setWaitAllocQty(t.getWaitAllocQty());
            bo.setCreateTime(t.getCreateTime());
            bo.setUpdateTime(t.getUpdateTime());
            bo.setVersion(t.getVersion());
            bo.setUsageCode(t.getUsageCode());
            bo.setToUsageCode(t.getToUsageCode());
            bos.add(bo);
        });
        return bos;
    }

}
