package com.ddmc.ims.converter;

import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;

/**
 * <AUTHOR>
 */
public class PurchaseIntransitInventoryConverter {

    private PurchaseIntransitInventoryConverter() {
    }


    public static PurchaseIntransitInventory converterSavePurchaseInTransitInventory(
        PurchaseIntransitInventoryCommand bo) {
        PurchaseIntransitInventory purchaseIntransitInventory = new PurchaseIntransitInventory();
        purchaseIntransitInventory.setOrderNo(bo.getOrderNo());
        purchaseIntransitInventory.setOrderSource(bo.getOrderSource());
        LogicInventoryLocation location = bo.getFromLocation();
        purchaseIntransitInventory.setCargoOwnerId(location.getCargoOwnerId());
        purchaseIntransitInventory.setLogicInventoryLocationCode(location.getLogicInventoryLocationCode());
        purchaseIntransitInventory.setWarehouseId(location.getWarehouseId());
        purchaseIntransitInventory.setIntransitQty(bo.getQty());
        purchaseIntransitInventory.setBookedIntransitQty(bo.getQty());
        purchaseIntransitInventory.setExpectArriveTime(bo.getExpectArriveTime());
        purchaseIntransitInventory.setSkuId(bo.getSkuId());
        purchaseIntransitInventory.setPlanQty(bo.getQty());
        purchaseIntransitInventory.setVersion(1);
        purchaseIntransitInventory.setUsageCode(bo.getUsageCode());
        purchaseIntransitInventory.setOrderTag(bo.getOrderTag());
        return purchaseIntransitInventory;
    }
}
