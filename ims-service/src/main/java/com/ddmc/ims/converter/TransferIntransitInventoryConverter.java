package com.ddmc.ims.converter;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryInDetail;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryLotDetail;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryOutDetail;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.time.DateUtils;

/**
 * <AUTHOR>
 */
public class TransferIntransitInventoryConverter {

    private TransferIntransitInventoryConverter() {

    }


    public static TransferIntransitInventory convertToPublishTransferInTransitInventory(
        TransferIntransitInventoryCommand command) {
        TransferIntransitInventory inTransitInventory = convertToCommonTransferIntransitInventory(command);
        inTransitInventory.setPlanQty(command.getQty());
        inTransitInventory.setIntransitQty(BigDecimal.ZERO);
        inTransitInventory.setAllocQty(BigDecimal.ZERO);
        inTransitInventory.setWaitAllocQty(command.getQty());
        return inTransitInventory;
    }

    public static TransferIntransitInventory convertToOutTransferInTransitInventory(
        TransferIntransitInventoryCommand command) {
        TransferIntransitInventory inTransitInventory = convertToCommonTransferIntransitInventory(command);
        //生成调拨在途有两种途径，一种是登记，一种是出库的时候没有在途数据则新增一条。第二种情况取目标用途
        inTransitInventory.setUsageCode(command.getUsageCode());
        inTransitInventory.setToUsageCode(command.getToUsageCode());
        return inTransitInventory;
    }



    public static TransferIntransitInventory convertToCommonTransferIntransitInventory(
        TransferIntransitInventoryCommand command) {
        if (Objects.isNull(command.getToLocation())) {
            throw new ImsBusinessException(CommonErrorCode.TRANSFER_IN_LOGIC_EMPTY);
        }
        TransferIntransitInventory inTransitInventory = new TransferIntransitInventory();
        inTransitInventory.setFromCargoOwnerId(command.getFromLocation().getCargoOwnerId());
        inTransitInventory.setFromWarehouseId(command.getFromLocation().getWarehouseId());
        inTransitInventory.setFromLogicInventoryLocationCode(command.getFromLocation().getLogicInventoryLocationCode());
        inTransitInventory.setToCargoOwnerId(command.getToLocation().getCargoOwnerId());
        inTransitInventory.setToLogicInventoryLocationCode(command.getToLocation().getLogicInventoryLocationCode());
        inTransitInventory.setToWarehouseId(command.getToLocation().getWarehouseId());
        inTransitInventory.setSkuId(command.getSkuId());
        inTransitInventory.setOrderSource(command.getOrderSource());
        inTransitInventory.setOrderNo(command.getOrderNo());
        Integer deliveryMode = Objects.isNull(command.getDeliveryMode()) ? -1 : command.getDeliveryMode().getCode();
        inTransitInventory.setDeliveryMode(deliveryMode);
        Date expectOutTime = getExceptDate(command.getExpectOutTime(), command.getOrderNo());
        inTransitInventory.setExpectOutTime(expectOutTime);
        Date expectInTime = getExceptDate(command.getExpectInTime(), command.getOrderNo());
        inTransitInventory.setExpectInTime(expectInTime);
        inTransitInventory.setPlanQty(command.getQty());
        inTransitInventory.setIntransitQty(command.getQty());
        inTransitInventory.setAllocQty(command.getQty());
        inTransitInventory.setWaitAllocQty(BigDecimal.ZERO);
        inTransitInventory.setVersion(0);

        inTransitInventory.setUsageCode(command.getUsageCode());
        inTransitInventory.setToUsageCode(command.getToUsageCode());
        return inTransitInventory;
    }



    private static Date getExceptDate(Date date, String orderNo) {
        if (Objects.isNull(date)) {
            String[] array = orderNo.split("-");
            return ThreadLocalDateUtils.parseYmd2(array[2]);
        }
        return date;
    }

    public static TransferIntransitInventoryLotDetail convertToCommonTransferInTransitLotInventory(
        TransferIntransitInventoryCommand command, Map<String, InventoryLotInfo> lotInfoMap,
        TransferIntransitInventory inventory) {
        if (Objects.isNull(command.getToLocation())) {
            throw new ImsBusinessException(CommonErrorCode.TRANSFER_IN_LOGIC_EMPTY);
        }
        InventoryLotInfo lotInfo = lotInfoMap.get(command.getLotId());
        TransferIntransitInventoryLotDetail lotDetail = new TransferIntransitInventoryLotDetail();
        lotDetail.setTransferIntransitInventoryId(inventory.getId());
        lotDetail.setFromCargoOwnerId(inventory.getFromCargoOwnerId());
        lotDetail.setFromWarehouseId(inventory.getFromWarehouseId());
        lotDetail.setFromLogicInventoryLocationCode(inventory.getFromLogicInventoryLocationCode());
        lotDetail.setToCargoOwnerId(command.getToLocation().getCargoOwnerId());
        lotDetail.setToLogicInventoryLocationCode(command.getToLocation().getLogicInventoryLocationCode());
        lotDetail.setToWarehouseId(command.getToLocation().getWarehouseId());
        lotDetail.setOrderSource(command.getOrderSource());
        lotDetail.setOrderNo(command.getOrderNo());
        lotDetail.setSkuId(command.getSkuId());
        lotDetail.setLotId(command.getLotId());
        Date now = CurrentDateUtil.newDate();
        Date expectOutTime = getExceptDate(command.getExpectOutTime(), command.getOrderNo());
        lotDetail.setOutTime(expectOutTime);
        Date expectInTime = getExceptDate(command.getExpectInTime(), command.getOrderNo());
        lotDetail.setExpectInTime(expectInTime);
        lotDetail.setIntransitQty(command.getQty());
        lotDetail.setOutQty(command.getQty());
        lotDetail.setVersion(0);
        lotDetail.setUsageCode(command.getToUsageCode());
        lotDetail.setUnsalableDate(
            Objects.isNull(lotInfo) ? DateUtils.truncate(now, Calendar.DATE) : lotInfo.getUnsalableDate());
        lotDetail.setManufactureDate(
            Objects.isNull(lotInfo) ? DateUtils.truncate(now, Calendar.DATE) : lotInfo.getManufactureDate());
        return lotDetail;
    }


    public static TransferIntransitInventoryOutDetail convertToTransferIntransitInventoryOutDetail(TransferIntransitInventoryCommand t) {
        TransferIntransitInventoryOutDetail outDetail = new TransferIntransitInventoryOutDetail();
        outDetail.setRefOrderSource(t.getOrderSource());
        outDetail.setRefOrderNo(t.getOrderNo());
        outDetail.setRefExeOrderSource(t.getExeOrderSource());
        outDetail.setRefExeOrderNo(t.getExeOrderNo());
        outDetail.setFromCargoOwnerId(t.getFromLocation().getCargoOwnerId());
        outDetail.setFromWarehouseId(t.getFromLocation().getWarehouseId());
        outDetail.setFromLogicInventoryLocationCode(t.getFromLocation().getLogicInventoryLocationCode());
        outDetail.setToCargoOwnerId(t.getToLocation().getCargoOwnerId());
        outDetail.setToLogicInventoryLocationCode(t.getToLocation().getLogicInventoryLocationCode());
        outDetail.setToWarehouseId(t.getToLocation().getWarehouseId());
        outDetail.setSkuId(t.getSkuId());
        outDetail.setDeliveryMode(t.getDeliveryMode().getCode());
        outDetail.setLotId(t.getLotId());
        outDetail.setExpectOutTime(t.getExpectOutTime());
        outDetail.setExpectInTime(t.getExpectInTime());
        outDetail.setOutTime(t.getBusinessTime());
        outDetail.setOutQty(t.getQty());
        return outDetail;
    }

    public static TransferIntransitInventoryInDetail convertToTransferIntransitInventoryInDetail(TransferIntransitInventoryCommand t) {
        TransferIntransitInventoryInDetail outDetail = new TransferIntransitInventoryInDetail();
        outDetail.setRefOrderSource(t.getOrderSource());
        outDetail.setRefOrderNo(t.getOrderNo());
        outDetail.setRefExeOrderSource(t.getExeOrderSource());
        outDetail.setRefExeOrderNo(t.getExeOrderNo());
        outDetail.setFromCargoOwnerId(t.getFromLocation().getCargoOwnerId());
        outDetail.setFromWarehouseId(t.getFromLocation().getWarehouseId());
        outDetail.setFromLogicInventoryLocationCode(t.getFromLocation().getLogicInventoryLocationCode());
        outDetail.setToCargoOwnerId(t.getToLocation().getCargoOwnerId());
        outDetail.setToLogicInventoryLocationCode(t.getToLocation().getLogicInventoryLocationCode());
        outDetail.setToWarehouseId(t.getToLocation().getWarehouseId());
        outDetail.setSkuId(t.getSkuId());
        outDetail.setDeliveryMode(t.getDeliveryMode().getCode());
        outDetail.setLotId(t.getLotId());
        outDetail.setExpectOutTime(t.getExpectOutTime());
        outDetail.setExpectInTime(t.getExpectInTime());
        outDetail.setInTime(t.getBusinessTime());
        outDetail.setInQty(t.getQty());
        return outDetail;
    }

}
