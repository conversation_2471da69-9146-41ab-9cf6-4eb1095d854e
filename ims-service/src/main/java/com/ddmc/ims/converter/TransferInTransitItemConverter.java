package com.ddmc.ims.converter;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.bo.inventory.TransferInTransitItem.TransferInTransitItemBuilder;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryInDetail;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryOutDetail;

public class TransferInTransitItemConverter {

    private TransferInTransitItemConverter() {

    }

    public static TransferInTransitItem convertToCondition(TransferIntransitInventory inventory) {
        TransferInTransitItemBuilder condition = getDefaultCondition(inventory);
        condition.fromLogicInventoryLocationCode(inventory.getFromLogicInventoryLocationCode());
        condition.toLogicInventoryLocationCode(inventory.getToLogicInventoryLocationCode());
        condition.usageCode(inventory.getUsageCode());
        condition.toUsageCode(inventory.getToUsageCode());
        return condition.build();
    }

    public static TransferInTransitItem convertToCondition(TransferIntransitInventory inventory, boolean outOrIn) {
        TransferInTransitItemBuilder condition = getDefaultCondition(inventory);
        if (outOrIn) {
            condition.toLogicInventoryLocationCode(inventory.getToLogicInventoryLocationCode());
        } else {
            condition.fromLogicInventoryLocationCode(inventory.getFromLogicInventoryLocationCode());
        }
        condition.usageCode(inventory.getUsageCode());
        condition.toUsageCode(inventory.getToUsageCode());
        return condition.build();
    }



    public static TransferInTransitItem convertToCondition(TransferIntransitInventoryBo inventory, boolean outOrIn) {
        TransferInTransitItemBuilder condition = getDefaultCondition(inventory);
        if (outOrIn) {
            condition.toLogicInventoryLocationCode(inventory.getToLogicInventoryLocationCode());
        } else {
            condition.fromLogicInventoryLocationCode(inventory.getFromLogicInventoryLocationCode());
        }
        condition.usageCode(inventory.getUsageCode());
        condition.toUsageCode(inventory.getToUsageCode());
        return condition.build();
    }


    public static TransferInTransitItem convertToOutStoreCondition(TransferIntransitInventoryBo inventory) {
        TransferInTransitItemBuilder condition = getDefaultCondition(inventory);
        condition.toLogicInventoryLocationCode(inventory.getToLogicInventoryLocationCode());
        return condition.build();
    }

    private static TransferInTransitItemBuilder getDefaultCondition(TransferIntransitInventoryBo inventory) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(inventory.getFromCargoOwnerId());
        condition.fromWarehouseId(inventory.getFromWarehouseId());
        condition.toCargoOwnerId(inventory.getToCargoOwnerId());
        condition.toWarehouseId(inventory.getToWarehouseId());
        condition.skuId(inventory.getSkuId());
        return condition;
    }


    private static TransferInTransitItemBuilder getDefaultCondition(TransferIntransitInventory inventory) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(inventory.getFromCargoOwnerId());
        condition.fromWarehouseId(inventory.getFromWarehouseId());
        condition.toCargoOwnerId(inventory.getToCargoOwnerId());
        condition.toWarehouseId(inventory.getToWarehouseId());
        condition.skuId(inventory.getSkuId());
        return condition;
    }




    public static TransferInTransitItem convertByCredential(CredentialDetail credentialDetail,
        boolean isOut) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(credentialDetail.getFromCargoOwnerId());
        condition.fromWarehouseId(credentialDetail.getFromWarehouseId());
        condition.toCargoOwnerId(credentialDetail.getToCargoOwnerId());
        condition.toWarehouseId(credentialDetail.getToWarehouseId());
        condition.skuId(credentialDetail.getSkuId());
        if (isOut) {
            condition.toLogicInventoryLocationCode(credentialDetail.getToLogicLocationCode());
        } else {
            condition.fromLogicInventoryLocationCode(credentialDetail.getFromLogicLocationCode());
        }
        return condition.build();
    }


    public static TransferInTransitItem convertByCommand(TransferIntransitInventoryCommand command,
        boolean outOrIn) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(command.getFromLocation().getCargoOwnerId());
        condition.fromWarehouseId(command.getFromLocation().getWarehouseId());
        condition.toCargoOwnerId(command.getToLocation().getCargoOwnerId());
        condition.toWarehouseId(command.getToLocation().getWarehouseId());
        condition.skuId(command.getSkuId());
        condition.usageCode(command.getUsageCode());
        condition.toUsageCode(command.getToUsageCode());
        if (outOrIn) {
            condition.toLogicInventoryLocationCode(command.getToLocation().getLogicInventoryLocationCode());
        } else {
            condition.fromLogicInventoryLocationCode(command.getFromLocation().getLogicInventoryLocationCode());
        }
        return condition.build();
    }



    public static TransferInTransitItem convertByCredentialUsageDetail(CredentialUseageDetail detail,
        boolean outOrIn) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(detail.getFromCargoOwnerId());
        condition.fromWarehouseId(detail.getFromWarehouseId());
        condition.toCargoOwnerId(detail.getToCargoOwnerId());
        condition.toWarehouseId(detail.getToWarehouseId());
        condition.skuId(detail.getSkuId());
        condition.usageCode(detail.getUsageCode());
        condition.toUsageCode(detail.getToUsageCode());
        if (outOrIn) {
            condition.toLogicInventoryLocationCode(detail.getToLogicLocationCode());
        } else {
            condition.fromLogicInventoryLocationCode(detail.getFromLogicLocationCode());
        }
        return condition.build();
    }


    private static TransferInTransitItemBuilder getInventoryDefaultBuilder(TransferIntransitInventory inventory) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(inventory.getFromCargoOwnerId());
        condition.fromWarehouseId(inventory.getFromWarehouseId());
        condition.fromLogicInventoryLocationCode(inventory.getFromLogicInventoryLocationCode());
        condition.toCargoOwnerId(inventory.getToCargoOwnerId());
        condition.toWarehouseId(inventory.getToWarehouseId());
        condition.toLogicInventoryLocationCode(inventory.getToLogicInventoryLocationCode());
        condition.skuId(inventory.getSkuId());
        return condition;
    }


    public static TransferInTransitItem convertToInventoryAll(TransferIntransitInventory inventory) {
        TransferInTransitItemBuilder condition = getInventoryDefaultBuilder(inventory);
        condition.usageCode(inventory.getUsageCode());
        condition.toUsageCode(inventory.getToUsageCode());
        return condition.build();
    }


    public static TransferInTransitItem convertByCommandAll(TransferIntransitInventoryCommand command) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(command.getFromLocation().getCargoOwnerId());
        condition.fromWarehouseId(command.getFromLocation().getWarehouseId());
        condition.toCargoOwnerId(command.getToLocation().getCargoOwnerId());
        condition.toWarehouseId(command.getToLocation().getWarehouseId());
        condition.skuId(command.getSkuId());
        condition.usageCode(command.getUsageCode());
        condition.toUsageCode(command.getToUsageCode());
        condition.toLogicInventoryLocationCode(command.getToLocation().getLogicInventoryLocationCode());
        condition.fromLogicInventoryLocationCode(command.getFromLocation().getLogicInventoryLocationCode());
        return condition.build();
    }



    public static TransferInTransitItem convertByInventoryAllUsage(TransferIntransitInventory inventory) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(inventory.getFromCargoOwnerId());
        condition.fromWarehouseId(inventory.getFromWarehouseId());
        condition.fromLogicInventoryLocationCode(inventory.getFromLogicInventoryLocationCode());
        condition.skuId(inventory.getSkuId());
        condition.usageCode(inventory.getUsageCode());
        condition.toUsageCode(inventory.getToUsageCode());
        condition.toCargoOwnerId(inventory.getToCargoOwnerId());
        condition.toWarehouseId(inventory.getToWarehouseId());
        condition.toLogicInventoryLocationCode(inventory.getToLogicInventoryLocationCode());
        return condition.build();
    }


    public static TransferInTransitItem convertByInventoryOutDetail(TransferIntransitInventoryOutDetail outDetail) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(outDetail.getFromCargoOwnerId());
        condition.fromWarehouseId(outDetail.getFromWarehouseId());
        condition.fromLogicInventoryLocationCode(outDetail.getFromLogicInventoryLocationCode());
        condition.skuId(outDetail.getSkuId());
        condition.lotId(outDetail.getLotId());
        condition.toCargoOwnerId(outDetail.getToCargoOwnerId());
        condition.toWarehouseId(outDetail.getToWarehouseId());
        condition.toLogicInventoryLocationCode(outDetail.getToLogicInventoryLocationCode());
        return condition.build();
    }

    public static TransferInTransitItem convertByInventoryInDetail(TransferIntransitInventoryInDetail inDetail) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(inDetail.getFromCargoOwnerId());
        condition.fromWarehouseId(inDetail.getFromWarehouseId());
        condition.fromLogicInventoryLocationCode(inDetail.getFromLogicInventoryLocationCode());
        condition.skuId(inDetail.getSkuId());
        condition.lotId(inDetail.getLotId());
        condition.toCargoOwnerId(inDetail.getToCargoOwnerId());
        condition.toWarehouseId(inDetail.getToWarehouseId());
        condition.toLogicInventoryLocationCode(inDetail.getToLogicInventoryLocationCode());
        return condition.build();
    }


    public static TransferInTransitItem convertByCommandLot(TransferIntransitInventoryCommand command) {
        TransferInTransitItemBuilder condition = TransferInTransitItem.builder();
        condition.fromCargoOwnerId(command.getFromLocation().getCargoOwnerId());
        condition.fromWarehouseId(command.getFromLocation().getWarehouseId());
        condition.fromLogicInventoryLocationCode(command.getFromLocation().getLogicInventoryLocationCode());
        condition.skuId(command.getSkuId());
        condition.lotId(command.getLotId());
        condition.toCargoOwnerId(command.getToLocation().getCargoOwnerId());
        condition.toWarehouseId(command.getToLocation().getWarehouseId());
        condition.toLogicInventoryLocationCode(command.getToLocation().getLogicInventoryLocationCode());
        return condition.build();
    }



}
