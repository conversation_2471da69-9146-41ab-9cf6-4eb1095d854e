package com.ddmc.ims.converter;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferManufactureDateItem;
import com.ddmc.ims.bo.inventory.TransferManufactureDateItem.TransferManufactureDateItemBuilder;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.time.DateUtils;

public class TransferManufactureItemConverter {

    private TransferManufactureItemConverter() {

    }


    public static TransferManufactureDateItem convertToByCommand(TransferIntransitInventoryCommand command,
        boolean isOutOrIn, Map<String, InventoryLotInfo> lotInfoMap, String usageCode) {
        TransferManufactureDateItemBuilder item = getDefaultItem(command, lotInfoMap, usageCode);
        if (isOutOrIn) {
            item.toLogicInventoryLocationCode(command.getToLocation().getLogicInventoryLocationCode());
        } else {
            item.fromLogicInventoryLocationCode(command.getFromLocation().getLogicInventoryLocationCode());

        }
        return item.build();
    }


    private static TransferManufactureDateItemBuilder getDefaultItem(TransferIntransitInventoryCommand command,
        Map<String, InventoryLotInfo> lotInfoMap, String usageCode) {
        TransferManufactureDateItemBuilder item = TransferManufactureDateItem.builder();
        item.fromCargoOwnerId(command.getFromLocation().getCargoOwnerId());
        item.fromWarehouseId(command.getFromLocation().getWarehouseId());
        item.toCargoOwnerId(command.getToLocation().getCargoOwnerId());
        item.toWarehouseId(command.getToLocation().getWarehouseId());
        item.skuId(command.getSkuId());
        item.usageCode(usageCode);
        Date nowDate = CurrentDateUtil.newDate();
        if (Objects.nonNull(lotInfoMap.get(command.getLotId()))) {
            item.manufactureDate(lotInfoMap.get(command.getLotId()).getManufactureDate());
        } else {
            item.manufactureDate(DateUtils.truncate(nowDate, Calendar.DATE));
        }

        return item;
    }


}
