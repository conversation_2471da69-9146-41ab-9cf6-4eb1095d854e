package com.ddmc.ims.converter;

import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.common.enums.ims.*;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.PatternUtils;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialHeaderExt;
import com.ddmc.ims.request.credential.oc.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class InventoryCredentialConverter {

    private InventoryCredentialConverter() {

    }


    public static CredentialHeader convertOutboundCredential(OutboundCredentialRequest request,
        Map<String, String> defaultUsageMap) {
        CredentialHeader header = convertOutboundToCredentialHead(request);
        header.setCredentialDetailList(convertCredentialHeadDetail(request.getOperateDetails(), defaultUsageMap));
        return header;
    }

    public static CredentialHeader convertOutboundToCredentialHead(OutboundCredentialRequest outboundCredential) {
        CredentialHeader header = buildCommonCredentialHeader(outboundCredential);
        if (Objects.nonNull(outboundCredential.getExpectOutTime())) {
            header.setExpectOutTime(outboundCredential.getExpectOutTime());
        }
        if (Objects.nonNull(outboundCredential.getExpectInTime())) {
            header.setExpectInTime(outboundCredential.getExpectInTime());
        }
        header.setDeliveryMode(outboundCredential.getDeliveryMode());
        List<CredentialHeaderExt> credentialHeaderExtList = Optional.ofNullable(header.getCredentialHeaderExtList())
            .orElse(Lists.newArrayList());
        if (Objects.nonNull(outboundCredential.getOriDeliveryDate())) {
            String oriDeliveryDateStr = ThreadLocalDateUtils
                .formatYmd(outboundCredential.getOriDeliveryDate());
            CredentialHeaderExt credentialHeaderExt = new CredentialHeaderExt(
                CredentialHeaderExtEnum.ORI_DELIVERY_DATE.getCode(), oriDeliveryDateStr);
            credentialHeaderExtList.add(credentialHeaderExt);
        }
        header.setCredentialHeaderExtList(credentialHeaderExtList);
        return header;
    }


    private static Date getExpectTimeFromOrderNo(String orderType, String orderNo) {
        try {

            if (OrderTypeEnum.isTransfer(orderType)
                && PatternUtils.isExactlyThreeHyphens(orderNo)) {
                String[] split = orderNo.split("-");
                return ThreadLocalDateUtils.parseYmd2(split[2]);
            }
        } catch (Exception e) {
            log.warn("[convertOutboundToCredentialHead] error", e);
        }
        return null;
    }

    private static Integer getDeliveryModeFromOrderNo(String orderType, String orderNo) {
        try {

            if (OrderTypeEnum.isTransfer(orderType)
                && PatternUtils.isExactlyThreeHyphens(orderNo)) {
                String[] split = orderNo.split("-");
                return Integer.parseInt(split[3]);
            }
        } catch (Exception e) {
            log.warn("[convertOutboundToCredentialHead] error", e);
        }
        return DeliveryModeEnum.ONE_DELIVERY.getCode();
    }

    private static CredentialHeader buildCommonCredentialHeader(InventoryOperateCredentialRequest request) {
        CredentialHeader header = new CredentialHeader();
        header.setIdempotentId(request.getIdempotentId());
        header.setOrderSource(request.getOrderSource());
        header.setOrderNo(request.getOrderNo());
        header.setOrderType(request.getOrderType());
        header.setExeOrderNo(request.getExeOrderNo());
        header.setExeOrderSource(request.getExeOrderSource());
        header.setOrderOperateType(request.getOrderOperateType());
        header.setBusinessTime(request.getBusinessTime());
        header.setSeqNo(request.getSeqNo());
        header.setIsStockChange(YesNoEnum.fromCode(request.getIsStockChange()));
        header.setWarehouseId(request.getWarehouseId());
        header.setEndDateTime(request.getDayEndTime());
        header.setDeliveryDate(request.getDeliveryDate());
        header.setStockSourceType(request.getStockSourceType());
        Date expectTimeFromOrderNo = getExpectTimeFromOrderNo(header.getOrderType(), header.getOrderNo());
        header.setExpectOutTime(expectTimeFromOrderNo);
        header.setExpectInTime(expectTimeFromOrderNo);
        return header;
    }

    public static CredentialHeader convertInboundCredential(InboundCredentialRequest request,
        Map<String, String> defaultUsageMap) {
        CredentialHeader header = convertInboundCredentialHeader(request);
        header.setCredentialDetailList(convertCredentialHeadDetail(request.getOperateDetails(), defaultUsageMap));
        return header;
    }

    public static CredentialHeader convertInboundCredentialHeader(InboundCredentialRequest inboundCredentialRequest) {
        CredentialHeader header = buildCommonCredentialHeader(inboundCredentialRequest);
        if (OrderTypeEnum.isTransfer(inboundCredentialRequest.getOrderType())) {
            header.setDeliveryMode(
                    getDeliveryModeFromOrderNo(inboundCredentialRequest.getOrderType(), inboundCredentialRequest.getOrderNo()));
        } else {
            header.setDeliveryMode(Optional.ofNullable(inboundCredentialRequest.getDeliveryMode())
                    .orElse(DeliveryModeEnum.ONE_DELIVERY.getCode()));
            header.setExpectOutTime(inboundCredentialRequest.getExpectOutTime());
        }
        header.setExpectArriveTime(inboundCredentialRequest.getExpectArriveTime());
        return header;
    }

    public static CredentialHeader convertAdjustCredential(AdjustCredentialRequest adjustCredentialRequest,
        Map<String, String> defaultUsageMap) {
        CredentialHeader header = buildCommonCredentialHeader(adjustCredentialRequest);
        header.setCredentialDetailList(
            convertAdjustCredentialHeadDetail(adjustCredentialRequest.getOperateDetails(), defaultUsageMap));
        return header;
    }


    public static CredentialHeader convertCredential(ManufactureCredentialRequest request,
        Map<String, String> defaultUsageMap) {
        CredentialHeader header = buildCommonCredentialHeader(request);
        header.setCredentialDetailList(convertCredentialDetail(request.getOperateDetails(), defaultUsageMap));
        return header;
    }

    public static List<CredentialDetail> convertCredentialDetail(
        List<CredentialDetailRequest> credentialDetailRequests, Map<String, String> defaultUsageMap) {
        credentialDetailRequests.stream().filter(t -> CollectionUtils.isEmpty(t.getUsages())).findAny()
            .ifPresent(t -> log.warn("CredentialDetail convertCredentialDetail usage is empty"));

        return credentialDetailRequests.stream().map(t -> buildCredentialDetail(t, defaultUsageMap))
            .collect(Collectors.toList());
    }


    public static List<CredentialDetail> convertAdjustCredentialHeadDetail(
        List<AdjustCredentialDetailRequest> credentialDetailRequests, Map<String, String> defaultUsageMap) {
        credentialDetailRequests.stream().filter(t -> CollectionUtils.isEmpty(t.getUsages())).findAny()
            .ifPresent(t -> log.warn("CredentialDetail convertAdjustCredentialHeadDetail usage is empty"));

        return credentialDetailRequests.stream().map(t -> buildCredentialDetail(t, defaultUsageMap))
            .collect(Collectors.toList());
    }


    public static List<CredentialDetail> convertCredentialHeadDetail(
        List<CredentialDetailRequest> credentialDetailRequests, Map<String, String> defaultUsageMap) {
        if (CollectionUtils.isEmpty(credentialDetailRequests)) {
            return Collections.emptyList();
        }
        credentialDetailRequests.stream().filter(t -> CollectionUtils.isEmpty(t.getUsages())).findAny()
            .ifPresent(t -> log.warn("CredentialDetail convertCredentialHeadDetail usage is empty"));

        return credentialDetailRequests.stream().map(t -> buildCredentialDetail(t, defaultUsageMap))
            .collect(Collectors.toList());
    }

    private static CredentialDetail buildCredentialDetail(CredentialDetailRequest request,
        Map<String, String> defaultUsageMap) {

        CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setDemand(deFaultDate(request.getDemandDate()));
        credentialDetail.setWarehouseId(request.getFromLocation().getWarehouseId());
        credentialDetail.setFromCargoOwnerId(request.getFromLocation().getCargoOwnerId());
        credentialDetail.setFromLogicLocationCode(request.getFromLocation().getLogicInventoryLocationCode());
        credentialDetail.setFromWarehouseId(request.getFromLocation().getWarehouseId());
        credentialDetail.setToLogicLocationCode(request.getToLocation().getLogicInventoryLocationCode());
        credentialDetail.setToCargoOwnerId(request.getToLocation().getCargoOwnerId());
        credentialDetail.setToWarehouseId(request.getToLocation().getWarehouseId());
        credentialDetail.setSkuId(request.getSkuId());
        credentialDetail.setLotId(StringUtils.isBlank(request.getLotId()) ? Strings.EMPTY : request.getLotId());
        credentialDetail.setQty(request.getQty());
        credentialDetail.setSkuType(SkuManufactureTypeEnum.UN_KNOW);
        credentialDetail.setInventoryStatus(
            Objects.nonNull(request.getFromInventoryStatus()) ? InventoryStatusEnum
                .fromCode(request.getFromInventoryStatus()) : InventoryStatusEnum.AVAILABLE);

        if (CollectionUtils.isEmpty(request.getUsages())) {
            String defaultUsage = defaultUsageMap
                .getOrDefault(request.getFromLocation().getLogicInventoryLocationCode(), Strings.EMPTY);
            credentialDetail.setUsageCode(defaultUsage);
        } else {
            credentialDetail.setUsageCode(String.join(",", request.getUsages()));
        }

        if (CollectionUtils.isEmpty(request.getToUsages())) {
            String defaultUsage = defaultUsageMap
                .getOrDefault(request.getToLocation().getLogicInventoryLocationCode(), Strings.EMPTY);
            credentialDetail.setToUsageCode(defaultUsage);
        } else {
            credentialDetail.setToUsageCode(String.join(",", request.getToUsages()));
        }

        credentialDetail.setToInventoryStatus(
            Objects.nonNull(request.getToInventoryStatus()) ? InventoryStatusEnum
                .fromCode(request.getToInventoryStatus()) : InventoryStatusEnum.AVAILABLE);
        credentialDetail
            .setOrderTag(StringUtils.isNotBlank(request.getOrderTag()) ? request.getOrderTag() : Strings.EMPTY);

        credentialDetail.setCommandType(
            Objects.nonNull(request.getCommandType()) ? request.getCommandType() : Strings.EMPTY);
        credentialDetail.setTodaySale(Objects.nonNull(request.getTodaySale()) ? request.getTodaySale() : Boolean.FALSE);

        return credentialDetail;
    }


    private static Date deFaultDate(Date date) {
        if (Objects.nonNull(date)) {
            return date;
        }
        return CurrentDateUtil.newDate();
    }
}
