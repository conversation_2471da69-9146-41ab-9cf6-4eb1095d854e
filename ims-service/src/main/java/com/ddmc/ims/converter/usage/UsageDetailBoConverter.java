package com.ddmc.ims.converter.usage;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.credential.UsageDetailBo;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

public class UsageDetailBoConverter {


    private UsageDetailBoConverter() {
    }



    public static UsageDetailBo getUsageDetailBo(CredentialDetail credentialDetail,
        TransferIntransitInventoryBo inventory, BigDecimal qty,
        String usageCode, String toUsageCode, CommandTypeEnum commandType) {
        UsageDetailBo usageDetail = new UsageDetailBo();
        usageDetail.setCredentialHeaderId(credentialDetail.getCredentialHeaderId());
        usageDetail.setCredentialDetailId(credentialDetail.getId());
        usageDetail.setDemand(credentialDetail.getDemand());
        usageDetail.setFromCargoOwnerId(inventory.getFromCargoOwnerId());
        usageDetail.setFromWarehouseId(inventory.getFromWarehouseId());
        usageDetail.setFromLogicLocationCode(inventory.getFromLogicInventoryLocationCode());
        usageDetail.setLotId(credentialDetail.getLotId());
        usageDetail.setSkuId(inventory.getSkuId());
        usageDetail.setToCargoOwnerId(inventory.getToCargoOwnerId());
        usageDetail.setToLogicLocationCode(inventory.getToLogicInventoryLocationCode());
        usageDetail.setToInventoryStatus(credentialDetail.getToInventoryStatus());
        usageDetail.setToWarehouseId(inventory.getToWarehouseId());
        usageDetail.setInventoryStatus(credentialDetail.getInventoryStatus());
        usageDetail.setOrderTag(credentialDetail.getOrderTag());
        usageDetail.setUsageCode(usageCode);
        usageDetail.setQty(qty);
        usageDetail.setToUsageCode(toUsageCode);
        usageDetail.setCommandType(commandType.getCode());
        return usageDetail;
    }


    public static List<CredentialUseageDetail> convertToCredentialUsageDetail(List<UsageDetailBo> bos) {
        if (CollectionUtils.isEmpty(bos)) {
            return Collections.emptyList();
        }
        List<CredentialUseageDetail> result = Lists.newArrayListWithExpectedSize(bos.size());

        bos.forEach(t -> {
            CredentialUseageDetail detail = new CredentialUseageDetail();
            detail.setCredentialHeaderId(t.getCredentialHeaderId());
            detail.setFromLogicLocationCode(t.getFromLogicLocationCode());
            detail.setFromCargoOwnerId(t.getFromCargoOwnerId());
            detail.setFromWarehouseId(t.getFromWarehouseId());
            detail.setToLogicLocationCode(t.getToLogicLocationCode());
            detail.setToCargoOwnerId(t.getToCargoOwnerId());
            detail.setToWarehouseId(t.getToWarehouseId());
            detail.setSkuId(t.getSkuId());
            detail.setLotId(t.getLotId());
            detail.setQty(t.getQty());
            detail.setDemand(t.getDemand());
            detail.setCreateTime(t.getCreateTime());
            detail.setUpdateTime(t.getUpdateTime());
            detail.setInventoryStatus(t.getInventoryStatus());
            detail.setToInventoryStatus(t.getToInventoryStatus());
            detail.setUsageCode(t.getUsageCode());
            detail.setOrderTag(t.getOrderTag());
            detail.setToUsageCode(t.getToUsageCode());
            detail.setCommandType(t.getCommandType());
            detail.setTodaySale(t.getTodaySale());
            detail.setToDemand(t.getToDemand());
            result.add(detail);
        });

        return result;
    }


    public static UsageDetailBo getModifyInventoryAvailableCredentialUsageDetail(CredentialDetail credentialDetail, String toUsageCode, BigDecimal qty, CommandTypeEnum commandType) {
        UsageDetailBo usageDetail = new UsageDetailBo();
        usageDetail.setCredentialHeaderId(credentialDetail.getCredentialHeaderId());
        usageDetail.setCredentialDetailId(credentialDetail.getId());
        usageDetail.setDemand(credentialDetail.getDemand());
        usageDetail.setFromCargoOwnerId(credentialDetail.getToCargoOwnerId());
        usageDetail.setFromWarehouseId(credentialDetail.getToWarehouseId());
        usageDetail.setFromLogicLocationCode(credentialDetail.getToLogicLocationCode());
        usageDetail.setInventoryStatus(credentialDetail.getToInventoryStatus());
        usageDetail.setUsageCode(toUsageCode);
        usageDetail.setToUsageCode(StringUtils.EMPTY);
        usageDetail.setLotId(credentialDetail.getLotId());
        usageDetail.setSkuId(credentialDetail.getSkuId());
        usageDetail.setQty(qty);
        usageDetail.setToCargoOwnerId(CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.getCargoOwnerId());
        usageDetail.setToLogicLocationCode(CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.getLogicInventoryLocationCode());
        usageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        usageDetail.setToWarehouseId(CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.getWarehouseId());
        usageDetail.setOrderTag(StringUtils.EMPTY);
        usageDetail.setCommandType(commandType.getCode());
        return usageDetail;
    }

    public static UsageDetailBo getModifyOutInventoryAvailableCredentialUsageDetail(CredentialDetail credentialDetail,
        String outUsageCode, BigDecimal qty, CommandTypeEnum commandType) {
        UsageDetailBo usageDetail = new UsageDetailBo();
        usageDetail.setCredentialHeaderId(credentialDetail.getCredentialHeaderId());
        usageDetail.setCredentialDetailId(credentialDetail.getId());
        usageDetail.setDemand(credentialDetail.getDemand());
        usageDetail.setFromCargoOwnerId(credentialDetail.getFromCargoOwnerId());
        usageDetail.setFromWarehouseId(credentialDetail.getFromWarehouseId());
        usageDetail.setFromLogicLocationCode(credentialDetail.getFromLogicLocationCode());
        usageDetail.setInventoryStatus(credentialDetail.getInventoryStatus());
        usageDetail.setUsageCode(outUsageCode);
        usageDetail.setToUsageCode(StringUtils.EMPTY);
        usageDetail.setLotId(credentialDetail.getLotId());
        usageDetail.setSkuId(credentialDetail.getSkuId());
        usageDetail.setQty(qty);
        usageDetail.setToCargoOwnerId(CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.getCargoOwnerId());
        usageDetail
            .setToLogicLocationCode(CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.getLogicInventoryLocationCode());
        usageDetail.setToInventoryStatus(credentialDetail.getToInventoryStatus());
        usageDetail.setToWarehouseId(CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.getWarehouseId());
        usageDetail.setOrderTag(credentialDetail.getOrderTag());
        usageDetail.setCommandType(commandType.getCode());
        return usageDetail;
    }



    public static UsageDetailBo getDefaultOutStoreCredentialUsageDetail(CredentialDetail credentialDetail,BigDecimal qty,
        String usageCode, String toUsageCode, CommandTypeEnum commandType) {
        UsageDetailBo usageDetail = new UsageDetailBo();
        usageDetail.setCredentialHeaderId(credentialDetail.getCredentialHeaderId());
        usageDetail.setCredentialDetailId(credentialDetail.getId());
        usageDetail.setDemand(credentialDetail.getDemand());
        usageDetail.setFromCargoOwnerId(credentialDetail.getFromCargoOwnerId());
        usageDetail.setFromWarehouseId(credentialDetail.getFromWarehouseId());
        usageDetail.setFromLogicLocationCode(credentialDetail.getToLogicLocationCode());
        usageDetail.setLotId(credentialDetail.getLotId());
        usageDetail.setSkuId(credentialDetail.getSkuId());
        usageDetail.setToCargoOwnerId(credentialDetail.getToCargoOwnerId());
        usageDetail.setToLogicLocationCode(credentialDetail.getToLogicLocationCode());
        usageDetail.setToInventoryStatus(credentialDetail.getToInventoryStatus());
        usageDetail.setToWarehouseId(credentialDetail.getToWarehouseId());
        usageDetail.setInventoryStatus(credentialDetail.getInventoryStatus());
        usageDetail.setOrderTag(credentialDetail.getOrderTag());
        usageDetail.setUsageCode(usageCode);
        usageDetail.setToUsageCode(toUsageCode);
        usageDetail.setCommandType(commandType.getCode());
        usageDetail.setQty(qty);
        return usageDetail;
    }


}
