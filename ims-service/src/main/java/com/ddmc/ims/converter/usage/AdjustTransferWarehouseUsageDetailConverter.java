package com.ddmc.ims.converter.usage;

import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.converter.CredentialUsageDetailConverter;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class AdjustTransferWarehouseUsageDetailConverter extends AdjustInventoryWarehouseUsageDetailConverter {


    @Override
    public Boolean isSupport(String orderType, Integer orderOperateType) {
        return OrderTypeEnum.FREEZE_LOCK.getCode().equals(orderType) ||
            OrderTypeEnum.INNER_BOUND_TRANSFER.getCode().equals(orderType);
    }


    /**
     * 出库走调整单模式，不过ocs传入正值，变动值取反
     */
    @Override
    public BigDecimal getChangeInventoryQty(BigDecimal qty) {
        return qty.negate();
    }

    @Override
    protected BigDecimal getDirectUsageQty(BigDecimal qty) {
        return qty;
    }

    @Override
    protected List<CredentialDetail> getSortQtyDetailList(List<CredentialDetail> credentialDetailList) {
        return credentialDetailList
            .stream().sorted(Comparator.comparing(CredentialDetail::getQty)).collect(Collectors.toList());
    }

    @Override
    protected CredentialUseageDetail getCredentialUsageDetail(CredentialDetail credentialDetail, String usage,
        BigDecimal deductedQty) {
        CredentialUseageDetail credentialUsageDetail = CredentialUsageDetailConverter
            .getCredentialUsageDetail(credentialDetail, usage, deductedQty);
        if (CommandTypeEnum.TRANSFER_INVENTORY.getCode().equals(credentialUsageDetail.getCommandType())
            && credentialUsageDetail.getFromLogicLocationCode()
            .equals(credentialUsageDetail.getToLogicLocationCode())) {
            credentialUsageDetail.setToUsageCode(usage);
        }
        if (StringUtils.isBlank(credentialDetail.getCommandType())) {
            credentialUsageDetail.setCommandType(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode());
        }

        return credentialUsageDetail;
    }
}
