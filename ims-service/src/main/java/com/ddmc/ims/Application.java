package com.ddmc.ims;

import com.ctrip.framework.apollo.ConfigService;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.config.feign.HystrixCredentialsContext;
import com.netflix.hystrix.strategy.HystrixPlugins;
import java.util.Set;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Slf4j
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.ddmc"})
@EnableTransactionManagement(order = Integer.MAX_VALUE - 1)
@EnableFeignClients(basePackages = "com.ddmc")
@EnableAsync
@EnableScheduling
public class Application {

    @Value("${spring.cloud.client.ip-address}")
    private String realIp;

    static {
        //Feign 熔断 线程插件配置
        HystrixPlugins.getInstance().registerCommandExecutionHook(new HystrixCredentialsContext());
    }

    /**
     * @param args 参数
     */
    public static void main(String[] args) {
        SpringApplication.run(com.ddmc.ims.Application.class, args);
        log.info("apollo properties start");
        Set<String> propertyNames = ConfigService.getAppConfig().getPropertyNames();
        for (String propertyName : propertyNames) {
            log.info("{} = {}", propertyName, ConfigService.getAppConfig().getProperty(propertyName, "无配置"));
        }
        log.info("apollo properties end");
    }

    @PostConstruct
    public void init() {
        System.setProperty("rocketmq.client.name", realIp + "@" + CurrentDateUtil.currentTimeMillis());
    }
}
