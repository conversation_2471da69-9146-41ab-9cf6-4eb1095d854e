package com.ddmc.ims.service.credential.impl;

import com.ddmc.ims.command.CommandHandleFactory;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.InventoryCredentialConverter;
import com.ddmc.ims.dal.mapper.ims.CredentialDetailMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import com.ddmc.ims.service.credential.InboundInventoryCredentialService;
import com.ddmc.ims.service.lot.InventoryLotInfoService;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class InboundInventoryCredentialServiceImpl implements InboundInventoryCredentialService {

    @Resource
    private CredentialWrapperManager credentialWrapperManager;
    @Resource
    private CommandHandleFactory commandHandleFactory;
    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;
    @Resource
    private CredentialDetailMapper credentialDetailMapper;

    @Resource
    private InventoryLotInfoService inventoryLotInfoService;

    @Resource
    private LocalParamConfig localParamConfig;


    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryInbound(InboundCredentialRequest request) {
        checkExistCredential(request);
        checkExistLotInfo(request);
        saveInboundCredential(request);
    }


    /**
     * 发布入库操作
     */
    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryPublishInbound(InboundCredentialRequest request) {
        checkExistCredential(request);
        if (Objects.isNull(request.getExpectArriveTime())) {
            throw new ImsBusinessException(CommonErrorCode.EXPECT_ARRIVE_TIME_IS_NULL);
        }
        checkExistLotInfo(request);
        saveInboundCredential(request);
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryFinishInbound(InboundCredentialRequest request) {
        checkExistCredential(request);
        if (Objects.isNull(request.getDayEndTime())
            && !OrderTypeEnum.UN_MANUFACTURE_MATERIAL_INBOUND.getCode().equals(request.getOrderType())
            && !OrderTypeEnum.MATERIAL_RETURN.getCode().equals(request.getOrderType())
            && !OrderTypeEnum.PRODUCT_INBOUND.getCode().equals(request.getOrderType())
            && !OrderTypeEnum.REVERSE_MANUFACTURE_RETURN.getCode().equals(request.getOrderType())) {
            throw new ImsBusinessException(CommonErrorCode.DAY_END_TIME_IS_NULL);
        }
        saveInboundCredential(request);
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryModifyExpectArriveTime(InboundCredentialRequest request) {

        checkTryModifyExpectArriveTime(request);
        saveInboundCredential(request);
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryCancelInbound(InboundCredentialRequest request) {
        checkExistCredential(request);
        saveInboundCredential(request);
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryCleanPlanIn(InboundCredentialRequest request) {
        checkExistCredential(request);
        saveInboundCredential(request);
    }

    private void checkTryModifyExpectArriveTime(InboundCredentialRequest request) {
        checkExistCredential(request);
        if (Objects.isNull(request.getExpectArriveTime())) {
            throw new ImsBusinessException(CommonErrorCode.EXPECT_ARRIVE_TIME_IS_NULL);
        }
    }


    private void checkExistCredential(InboundCredentialRequest request) {
        CredentialHeader credentialHeader = credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId());
        if (Objects.nonNull(credentialHeader)) {
            throw new ImsBusinessException(CommonErrorCode.EXIST_CREDENTIAL);
        }
    }


    /**
     * 保存入库凭证
     *
     * @param request 凭证请求
     */
    private void saveInboundCredential(InboundCredentialRequest request) {
        CredentialHeader header = InventoryCredentialConverter
            .convertInboundCredential(request, localParamConfig.getDefaultLocationUsageCodeMap());
        credentialWrapperManager.saveCredential(header);
    }


    private void checkExistLotInfo(InboundCredentialRequest request) {
        if (CollectionUtils.isNotEmpty(request.getOperateDetails()) && OrderTypeEnum
            .isTransfer(request.getOrderType())) {
            Set<String> lotIds = request.getOperateDetails().stream().map(CredentialDetailRequest::getLotId)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            //查询动作提前，没有批次信息将去查询批次服务
            inventoryLotInfoService.queryInventoryLotInfo(lotIds);
        }
    }

}
