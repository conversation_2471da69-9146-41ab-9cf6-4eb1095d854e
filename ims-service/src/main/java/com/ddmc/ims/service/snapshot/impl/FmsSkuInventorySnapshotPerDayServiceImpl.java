package com.ddmc.ims.service.snapshot.impl;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.enums.ims.WarehouseTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.dal.mapper.monitor.FmsSkuInventorySnapshotPerDayMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuTransferInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.Warehouse;
import com.ddmc.ims.dal.model.monitor.FmsSkuInventorySnapshotPerDay;
import com.ddmc.ims.dal.model.monitor.SkuInventoryInOutSnapshot;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SkuTransferInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.CommandChangeQtyDto;
import com.ddmc.ims.dto.CommandInOutQtyDto;
import com.ddmc.ims.dto.CredentialDiffQtyDto;
import com.ddmc.ims.dto.FmsSkuInventorySnapshotPerDayNewWrapper;
import com.ddmc.ims.dto.InventoryAndProcessingQtyDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.manager.ocs.InventoryCredentialManager;
import com.ddmc.ims.service.snapshot.FmsSkuInventorySnapshotPerDayService;
import com.ddmc.ims.service.snapshot.SnapshotCredentialDiffService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.ddmc.ocs.inventorycredential.response.ManufactureDemandDetailResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FmsSkuInventorySnapshotPerDayServiceImpl implements FmsSkuInventorySnapshotPerDayService {


    @Resource
    private FmsSkuInventorySnapshotPerDayMapper fmsSkuInventorySnapshotPerDayMapper;
    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;
    @Resource
    private CredentialWrapperManager credentialWrapperManager;
    @Resource
    private FmsSkuInventorySnapshotPerDayService selfProxy;
    @Resource
    private CommandManager commandManager;
    @Resource
    private SkuInventorySnapshotPerHourMapper skuInventorySnapshotPerHourMapper;
    @Resource
    private SnapshotCredentialDiffService snapshotCredentialDiffService;
    @Resource
    private SkuInventoryInOutSnapshotMapper skuInventoryInOutSnapshotMapper;
    @Resource
    private FmsSnapshotNotifyMapper fmsSnapshotNotifyMapper;
    @Resource
    private InventoryCredentialManager inventoryCredentialManager;
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private SkuTransferInventorySnapshotPerHourMapper skuTransferInventorySnapshotPerHourMapper;

    @Override
    public void dealEndFmsSkuInventorySnapshot(Long taskId, Long warehouseId, Date snapshotDate) {

        checkData(warehouseId, snapshotDate);

        List<FmsSkuInventorySnapshotPerDay> endFmsSkuInventorySnapshotList = getEndFmsSkuInventorySnapshot(warehouseId,
            snapshotDate);

        selfProxy.updateTaskAndBatchSaveFmsSkuInventorySnapshotPerDay(taskId, endFmsSkuInventorySnapshotList);
    }

    /**
     * 校验快照日期财务数据是否可以生成
     *
     * @param warehouseId 仓库id
     * @param snapshotDate 快照日期
     */
    private void checkData(Long warehouseId, Date snapshotDate) {

        //查询业务库存数据是否已生成
        Integer inventoryCount = snapshotTaskMapper
            .countByWarehouseIdAndSnapshotTimeAndStatus(warehouseId, DateUtils.addDays(snapshotDate, 1),
                SnapshotTypeEnum.INVENTORY_HOUR.getCode(), SnapshotStatusEnum.COMPLETE.getCode());
        if (inventoryCount < 1) {
            //如果昨日生成的数据不为空，表示未生成
            List<SnapshotTask> taskList = snapshotTaskMapper
                .selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(Collections.singleton(warehouseId),
                    SnapshotTypeEnum.INVENTORY_HOUR, snapshotDate);
            if (CollectionUtils.isNotEmpty(taskList)) {
                throw new ImsBusinessException("业务库存数据未生成");
            }
        }
    }


    /**
     * 获取仓库快照时间对应的财务数据
     *
     * @param warehouseId 仓库id
     * @param snapshotDate 快照时间
     * @return 快照数据
     */
    @Override
    public List<FmsSkuInventorySnapshotPerDay> getEndFmsSkuInventorySnapshot(Long warehouseId, Date snapshotDate) {

        Map<Long, InventoryAndProcessingQtyDto> endSkuInventorySnapshotPerHourMap = getSkuInventorySnapshotPerHourFilterProcessingMap(
            warehouseId, DateUtils.addDays(snapshotDate, 1));
        //查询业务库存出入库变动
        Map<Long, CommandInOutQtyDto> skuInventoryInOutSnapshots = getSkuInventoryInOutSnapshots(warehouseId,
            snapshotDate);
        Map<Long, BigDecimal> transferIntransitQtyMap = getTransferIntransitQtyMap(warehouseId,
            DateUtils.addDays(snapshotDate, 1));
        Map<Long, CommandChangeQtyDto> fmsLessInventoryQtyMap = snapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(warehouseId, snapshotDate);
        Map<Long, CommandChangeQtyDto> fmsMoreInventoryQtyMap = snapshotCredentialDiffService
            .getTodayFmsMoreInventoryQtyMap(warehouseId, snapshotDate);
        Map<Long, CredentialDiffQtyDto> fmsCredentialDiffMap = getFmsCredentialDiffMap(warehouseId, snapshotDate);
        FmsSkuInventorySnapshotPerDayNewWrapper fmsSkuInventorySnapshotPerDayWrapper = new FmsSkuInventorySnapshotPerDayNewWrapper(
            endSkuInventorySnapshotPerHourMap,
            fmsLessInventoryQtyMap,
            fmsMoreInventoryQtyMap,
            skuInventoryInOutSnapshots,
            fmsCredentialDiffMap,
            transferIntransitQtyMap);
        return fmsSkuInventorySnapshotPerDayWrapper.getEndFmsSkuInventorySnapshotList(snapshotDate, warehouseId);
    }

    private Map<Long, BigDecimal> getTransferIntransitQtyMap(Long warehouseId, Date snapshotDate) {
        List<SkuTransferInventorySnapshotPerHour> skuTransferInventorySnapshotPerHours = skuTransferInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndToWarehouseId(
            snapshotDate, warehouseId);
        return skuTransferInventorySnapshotPerHours.stream().collect(
            Collectors.toMap(SkuTransferInventorySnapshotPerHour::getSkuId,
                SkuTransferInventorySnapshotPerHour::getTransferIntransitQty, BigDecimal::add));
    }


    private Map<Long, InventoryAndProcessingQtyDto> getSkuInventorySnapshotPerHourFilterProcessingMap(Long warehouseId,
        Date snapshotDate) {
        List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHourList = skuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(snapshotDate, warehouseId);

        return skuInventorySnapshotPerHourList.stream()
            .collect(Collectors.toMap(SkuInventorySnapshotPerHour::getSkuId, s -> {
                if (CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE.equalsIgnoreCase(
                    s.getLogicLocationCode())) {
                    return new InventoryAndProcessingQtyDto(BigDecimal.ZERO, s.getFreeQty().add(s.getFrozenQty()));
                } else {
                    return new InventoryAndProcessingQtyDto(s.getFreeQty().add(s.getFrozenQty()), BigDecimal.ZERO);
                }
            }, (k1, k2) -> {
                k1.setInventoryQty(k1.getInventoryQty().add(k2.getInventoryQty()));
                k1.setProcessingQty(k1.getProcessingQty().add(k2.getProcessingQty()));
                return k1;
            }));
    }


    @Override
    @Transactional(value = CommonConstants.IMS_MONITOR_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void updateTaskAndBatchSaveFmsSkuInventorySnapshotPerDay(Long taskId,
        List<FmsSkuInventorySnapshotPerDay> items) {
        snapshotTaskMapper.updateStatusById(taskId, SnapshotStatusEnum.COMPLETE, SnapshotStatusEnum.PROCESSING);
        Lists.partition(items, CommonConstants.BATCH_INSERT_DB_100)
            .forEach(fmsSkuInventorySnapshotPerDayMapper::batchInsert);
    }

    @Override
    @Transactional(value = CommonConstants.IMS_MONITOR_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void initEndFmsSkuInventorySnapshot(Long warehouseId, Date snapshotDate) {
        //财务期末快照
        List<FmsSkuInventorySnapshotPerDay> endFmsSkuInventorySnapshotList = getEndFmsSkuInventorySnapshot(warehouseId,snapshotDate);
        snapshotTaskMapper.deleteBySnapshotTimeAndSnapshotType(snapshotDate, SnapshotTypeEnum.FMS);
        SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(warehouseId);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(snapshotDate);
        snapshotTask.setStatus(SnapshotStatusEnum.COMPLETE);
        snapshotTaskMapper.insert(snapshotTask);
        fmsSkuInventorySnapshotPerDayMapper.deleteByWarehouseAndSnapshotDate(warehouseId, snapshotDate);
        Lists.partition(endFmsSkuInventorySnapshotList, CommonConstants.BATCH_INSERT_DB_100)
            .forEach(fmsSkuInventorySnapshotPerDayMapper::batchInsert);
    }


    private Map<Long, CommandInOutQtyDto> getSkuInventoryInOutSnapshots(Long warehouseId, Date snapshotDate) {
        List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots = skuInventoryInOutSnapshotMapper
            .selectBySnapshotDateTimeAndWarehouseId(DateUtils.addDays(snapshotDate, 1), warehouseId);
        Map<Long, List<SkuInventoryInOutSnapshot>> snapshotMap = skuInventoryInOutSnapshots.stream()
            .collect(Collectors.groupingBy(SkuInventoryInOutSnapshot::getSkuId));
        Map<Long, CommandInOutQtyDto> inOutQtyMap = Maps.newHashMapWithExpectedSize(snapshotMap.size());
        snapshotMap.forEach((k, v) -> {
            CommandInOutQtyDto commandChangeQtyDto = new CommandInOutQtyDto(BigDecimal.ZERO, BigDecimal.ZERO);
            v.forEach(t -> {
                commandChangeQtyDto.setInQty(commandChangeQtyDto.getInQty().add(t.getInQty()));
                commandChangeQtyDto.setOutQty(commandChangeQtyDto.getOutQty().add(t.getOutQty()));
            });
            inOutQtyMap.put(k, commandChangeQtyDto);
        });
        Map<Long, BigDecimal> fmsManufactureOutQtyMap = getFmsManufactureOutQtyMap(warehouseId, snapshotDate);
        fmsManufactureOutQtyMap.forEach((k, v) -> {
            CommandInOutQtyDto commandChangeQtyDto = inOutQtyMap
                .getOrDefault(k, new CommandInOutQtyDto(BigDecimal.ZERO, BigDecimal.ZERO));
            commandChangeQtyDto.setOutQty(commandChangeQtyDto.getOutQty().add(v));
            inOutQtyMap.put(k, commandChangeQtyDto);
        });
        return inOutQtyMap;


    }

    @Override
    public List<FmsSkuInventorySnapshotPerDay> queryByWarehouseAndSnapshotDate(Long warehouseId, Date snapshotDate,
        Long skuId, Integer limit) {
        return fmsSkuInventorySnapshotPerDayMapper
            .selectByWarehouseIdAndSnapshotDateAndMinSkuId(warehouseId, snapshotDate, skuId, limit);
    }

    @Override
    public Integer countByWarehouseAndSnapshotDate(Long warehouseId, Date snapshotDate) {
        return fmsSkuInventorySnapshotPerDayMapper.countByWarehouseAndSnapshotDate(warehouseId, snapshotDate);
    }

    /**
     * 财务正向加工原料使用数，当成品入库时，ims快照会设置加工中出库
     */
    private Map<Long, BigDecimal> getFmsManufactureOutQtyMap(Long warehouseId, Date snapshotDate) {
        Warehouse warehouse = warehouseService.getWarehouse(warehouseId);
        if (!WarehouseTypeEnum.STORE_TYPE_GENERAL.getCode().equals(warehouse.getType())) {
            return Collections.emptyMap();
        }
        //查询出快照日对应的制造单
        List<CredentialHeader> positiveManufactureHeads = credentialWrapperManager
            .listByWarehouseIdAndOrderOperateTypeAndEndDateTime(warehouseId, OrderTypeEnum.POSITIVE_MANUFACTURE,
                snapshotDate);
        //查询oc服务使用了多少原料
        Map<Long, BigDecimal> credentialFinishQtyMap = getManufactureDemandMap(positiveManufactureHeads);
        //查询成品入库使用了多少原料
        List<CredentialHeader> produceInboundHeads = credentialWrapperManager
            .listByWarehouseIdAndOrderOperateTypeAndEndDateTime(warehouseId, OrderTypeEnum.PRODUCT_INBOUND,
                snapshotDate);
        Map<Long, BigDecimal> manufactureUseQtyMap = produceInboundHeads.stream().map(
                CredentialHeader::getCredentialDetailList)
            .flatMap(List::stream)
            .filter(t -> t.getFromLogicLocationCode().equals(CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE))
            .collect(Collectors.groupingBy(CredentialDetail::getSkuId,
                Collectors.mapping(CredentialDetail::getQty
                    , Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        return Stream.of(credentialFinishQtyMap, manufactureUseQtyMap)
            .flatMap(t -> t.entrySet().stream())
            .collect(Collectors.toMap(Entry::getKey, Entry::getValue, BigDecimal::add));


    }

    private Map<Long, BigDecimal> getManufactureDemandMap(List<CredentialHeader> positiveManufactureHeads) {
        List<ManufactureDemandDetailResponse> allManufactureDemandDetails = Lists.newArrayList();
        positiveManufactureHeads.forEach(t -> {
            List<ManufactureDemandDetailResponse> manufactureDemandDetailResponses = inventoryCredentialManager
                .queryManufactureDemandDetail(t.getExeOrderNo(), t.getExeOrderSource());
            allManufactureDemandDetails.addAll(manufactureDemandDetailResponses);
        });
        return allManufactureDemandDetails.stream()
            .collect(Collectors.groupingBy(ManufactureDemandDetailResponse::getMaterialId,
                Collectors.mapping(
                    ManufactureDemandDetailResponse::getUseQty,
                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    private Map<Long, CredentialDiffQtyDto> getFmsCredentialDiffMap(Long warehouseId, Date snapshotDate) {
        //查询财务少的数据
        Map<Long, CommandChangeQtyDto> purchasePartInQtyMap = snapshotCredentialDiffService
            .getInitFmsLessInventoryQtyMap(warehouseId, snapshotDate, Sets.newHashSet("PURCHASE"));
        //查询财务少的数据
        Map<Long, CommandChangeQtyDto> transferPartInQtyMap = snapshotCredentialDiffService
            .getInitFmsLessInventoryQtyMap(warehouseId, snapshotDate, Sets.newHashSet("TRANSFER", "FDC"));

        //查询财务少的数据
        Map<Long, CommandChangeQtyDto> reverseProcessingPartInQtyMap = snapshotCredentialDiffService
            .getInitFmsLessInventoryQtyMap(warehouseId, snapshotDate, Sets.newHashSet("MES"));

        //查询财务多的数据
        Map<Long, CommandChangeQtyDto> fmsMoreInventoryQtyMap = snapshotCredentialDiffService
            .getInitFmsMoreInventoryQtyMap(warehouseId, snapshotDate);
        Set<Long> skuIds = new HashSet<>(purchasePartInQtyMap.keySet());
        skuIds.addAll(transferPartInQtyMap.keySet());
        skuIds.addAll(reverseProcessingPartInQtyMap.keySet());
        skuIds.addAll(fmsMoreInventoryQtyMap.keySet());
        Map<Long, CredentialDiffQtyDto> fmsCredentialDiffMap = Maps.newHashMapWithExpectedSize(skuIds.size());
        skuIds.forEach(skuId -> {
            BigDecimal purchasePartInQty = Optional.ofNullable(purchasePartInQtyMap.get(skuId))
                .map(CommandChangeQtyDto::getAllQty).orElse(BigDecimal.ZERO);
            BigDecimal transferPartInQty = Optional.ofNullable(transferPartInQtyMap.get(skuId))
                .map(CommandChangeQtyDto::getAllQty).orElse(BigDecimal.ZERO);
            BigDecimal reverseProcessingPartInQty = Optional.ofNullable(reverseProcessingPartInQtyMap.get(skuId))
                .map(CommandChangeQtyDto::getAllQty).orElse(BigDecimal.ZERO);
            BigDecimal otherDiffQty = Optional.ofNullable(fmsMoreInventoryQtyMap.get(skuId))
                .map(CommandChangeQtyDto::getAllQty).orElse(BigDecimal.ZERO);
            CredentialDiffQtyDto credentialDiffQtyDto = new CredentialDiffQtyDto(skuId, purchasePartInQty,
                transferPartInQty, reverseProcessingPartInQty, otherDiffQty);
            fmsCredentialDiffMap.put(skuId, credentialDiffQtyDto);
        });
        return fmsCredentialDiffMap;
    }

}
