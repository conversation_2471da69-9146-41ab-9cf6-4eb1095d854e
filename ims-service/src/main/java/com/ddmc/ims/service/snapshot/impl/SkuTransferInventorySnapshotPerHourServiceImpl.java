package com.ddmc.ims.service.snapshot.impl;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.SnapshotConverter;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryInDetailMapper;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuTransferInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.monitor.SkuTransferInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.snapshot.SkuTransferInventorySnapshotPerHourService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.google.common.collect.Sets;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SkuTransferInventorySnapshotPerHourServiceImpl implements SkuTransferInventorySnapshotPerHourService {

    @Resource
    private CredentialWrapperManager credentialWrapperManager;

    @Resource
    private CommandManager commandManager;


    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;

    @Resource
    private LocalParamConfig localParamConfig;

    @Resource
    private LocalParamService localParamService;

    @Resource
    private SkuTransferInventorySnapshotPerHourMapper skuTransferInventorySnapshotPerHourMapper;
    @Resource
    private TransferIntransitInventoryInDetailMapper transferIntransitInventoryInDetailMapper;
    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;
    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;


    @Override
    public void handleWarehouseSnapshotPerHour(Date snapshotDate, Long warehouseId) {
        skuTransferInventorySnapshotPerHourMapper.deleteByToWarehouseIdInAndSnapshotDateTime(warehouseId, snapshotDate);
        snapshotTaskMapper.deleteBySnapshotTimeAndSnapshotType(snapshotDate, SnapshotTypeEnum.TRANSFER_HOUR);
        List<SnapshotTask> snapshotTasks = SnapshotConverter
            .convertToSnapshotTask(Collections.singletonList(warehouseId), snapshotDate,
                SnapshotTypeEnum.TRANSFER_HOUR);
        Lists.partition(snapshotTasks, CommonConstants.BATCH_INSERT_DB_100)
            .forEach(subList -> snapshotTaskMapper.batchInsert(subList));
        snapshotTasks.forEach(this::handlePerHourSnapshotTask);

    }

    public void handlePerHourSnapshotTask(SnapshotTask snapshotTask) {
        log.info("[skuTransferInventorySnapshotPerHourJob] warehouseId:{}, time:{}",
            snapshotTask.getWarehouseId(),
            snapshotTask.getSnapshotTime());
        if (isStop()) {
            return;
        }
        //睡眠
        sleep();

        Long warehouseId = snapshotTask.getWarehouseId();
        Date snapshotDate = snapshotTask.getSnapshotTime();
        Date beginDate = DateUtils.addDays(snapshotDate, -1);

        Set<String> allOrderNos = getOrderNos(warehouseId, beginDate, snapshotDate);
        List<CredentialHeader> credentialHeaderAndDetails = credentialWrapperManager.getCredentialHeaderByOrderNoAndBusinessTime(
            allOrderNos, beginDate, snapshotDate);
        List<CommandInventoryNumDto> commandInventoryNumList = commandManager
            .getCommandInventoryNumList(credentialHeaderAndDetails);
        saveTransferInventorySnapshotPerHour(beginDate, warehouseId, snapshotDate, commandInventoryNumList);
        snapshotTaskMapper
            .updateStatusById(snapshotTask.getId(), SnapshotStatusEnum.COMPLETE, SnapshotStatusEnum.PROCESSING);
    }

    private Set<String> getOrderNos(Long warehouseId, Date beginDate, Date snapshotDate) {
        Integer credentialBusinessTimeDelay = localParamConfig.getCredentialBusinessTimeDelay();
        Date minUpdateTime = DateUtils.addMinutes(beginDate, -credentialBusinessTimeDelay);

        Date maxCreateTime = DateUtils.addMinutes(snapshotDate, credentialBusinessTimeDelay);

        List<String> orderNos = transferIntransitInventoryMapper.selectByToWarehouseIdAndTime(warehouseId,
            minUpdateTime, maxCreateTime);

        return Sets.newHashSet(orderNos);


    }


    private void saveTransferInventorySnapshotPerHour(Date lastDate, Long warehouseId, Date snapshotDate,
        List<CommandInventoryNumDto> commandInventoryNumList) {
        List<SkuTransferInventorySnapshotPerHour> lastUsageSnapshotList = skuTransferInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndToWarehouseId(lastDate, warehouseId);
        log.info("[skuTransferInventorySnapshotPerHourJob][调拨在途上一次快照数据] {} 仓 {} 数据量 {}]",
            JsonUtil.toJson(lastDate), warehouseId,
            lastUsageSnapshotList.size());

        List<SkuTransferInventorySnapshotPerHour> newSkuInventorySnapshotUsagePerHourList = SnapshotConverter
            .convertToSkuTransferInventorySnapshotPerHour(lastUsageSnapshotList, warehouseId, commandInventoryNumList,
                snapshotDate);
        if (CollectionUtils.isNotEmpty(newSkuInventorySnapshotUsagePerHourList)) {
            Lists.partition(newSkuInventorySnapshotUsagePerHourList, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(subList -> skuTransferInventorySnapshotPerHourMapper.insertList(subList));
        }

    }


    @SneakyThrows
    private void sleep() {
        Thread.sleep(
            localParamService.getLongValue(LocalParamsConstants.TRANSFER_SNAPSHOT_PER_HOUR_SLEEP_INTERVAL, 100L));
    }

    private boolean isStop() {
        return localParamService.getBooleanValue(LocalParamsConstants.TRANSFER_SNAPSHOT_PER_HOUR_STOP, false);
    }

}
