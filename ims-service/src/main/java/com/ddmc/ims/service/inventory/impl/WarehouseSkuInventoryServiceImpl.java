package com.ddmc.ims.service.inventory.impl;

import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithLot;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithSku;
import com.ddmc.ims.common.bo.SkuIdAndLotIdPair;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.dto.MultiSkuWarehouseInventoryWrapper;
import com.ddmc.ims.manager.inventory.WarehouseInventoryWrapperManager;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WarehouseSkuInventoryServiceImpl implements WarehouseSkuInventoryService {

    @Resource
    private WarehouseInventoryWrapperManager warehouseInventoryWrapperManager;

    @Override
    public void changInventory(InventoryChange inventoryChange) {
        LogicInventoryLocation location = inventoryChange.getLogicInventoryLocation();
        YesNoEnum refRealInventory = YesNoEnum.YES;
        List<InventoryChangeItem> inventoryChangeItemList = inventoryChange.getInventoryChangeItemList();
        List<SkuIdAndLotIdPair> pair = inventoryChange.getInventoryChangeItemList().stream()
            .map(t -> new SkuIdAndLotIdPair(t.getSkuId(), t.getLotId())).collect(Collectors.toList());
        MultiSkuWarehouseInventoryWrapper wrappers = warehouseInventoryWrapperManager
            .loadSkuAndLotInventory(location, pair, refRealInventory);
        inventoryChangeItemList.forEach(item -> doInventoryWrapper(location, refRealInventory, wrappers, item));
        warehouseInventoryWrapperManager.saveInventory(wrappers);
    }


    private void doInventoryWrapper(LogicInventoryLocation location, YesNoEnum refRealInventory,
        MultiSkuWarehouseInventoryWrapper wrappers, InventoryChangeItem item) {
        BigDecimal addFreeQty = BigDecimal.ZERO;
        BigDecimal addFrozenQty = BigDecimal.ZERO;
        BigDecimal qty =
            Objects.equals(item.getInventoryOperateTypeEnum(), InventoryOperateTypeEnum.ADD) ? item.getQty()
                : item.getQty().negate();
        switch (item.getInventoryWorkTypeEnum()) {
            case FREE:
                addFreeQty = qty;
                break;
            case FROZEN:
                addFrozenQty = qty;
                break;
            default:
                break;
        }
        //关联实物库存的库位操作
        if (Objects.equals(YesNoEnum.YES, refRealInventory)) {
            LogicInventoryLocationWithLot locationWithLot = new LogicInventoryLocationWithLot(location, item.getSkuId(),
                item.getLotId(), item.getUsage());
            wrappers.changeWarehouseInventoryWithLot(locationWithLot, addFreeQty, addFrozenQty);
        } else {
            LogicInventoryLocationWithSku locationWithUsage = new LogicInventoryLocationWithSku(location,
                item.getSkuId());
            wrappers.changeWarehouseInventory(locationWithUsage, item.getUsage(), addFreeQty, addFrozenQty,
                BigDecimal.ZERO);
        }
    }



}
