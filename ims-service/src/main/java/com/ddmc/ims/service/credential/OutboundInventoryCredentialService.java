package com.ddmc.ims.service.credential;

import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;

/**
 * <AUTHOR>
 */
public interface OutboundInventoryCredentialService {

 void tryOutbound(OutboundCredentialRequest request);

 void tryFinishOutbound(OutboundCredentialRequest request);

 void tryCancelOutbound(OutboundCredentialRequest request);

 void tryModifyOutboundPlanQty(OutboundCredentialRequest request);

 void tryPublishOutbound(OutboundCredentialRequest request);

 void tryModifyExpectOutTime(OutboundCredentialRequest request);

 void tryTransferReject(OutboundCredentialRequest request);
}
