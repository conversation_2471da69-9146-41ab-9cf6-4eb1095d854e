package com.ddmc.ims.service.warehouse;

import com.ddmc.ims.dal.model.ims.Warehouse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface WarehouseService {



    /**
     * 查询所有的仓库
     * @return 仓库信息
     */
    List<Warehouse> getAllWarehouses();

    /**
     * 查询所有仓库Id
     *
     * @return result
     */
    List<Long> getAllWarehouseIds();


    /**
     * 获取仓库信息
     *
     * @param warehouseId 仓库ID
     * @return 仓库
     */
    Warehouse getWarehouse(Long warehouseId);

    /**
     * 根据仓库id查询缓存
     * @param warehouseIds 仓库id
     * @return 仓库
     */
    Map<Long, Warehouse> loadFromCache(Collection<Long> warehouseIds);

}
