package com.ddmc.ims.service.common;


import com.ddmc.ims.common.view.BaseInfoInjectAble;
import com.ddmc.ims.common.view.BaseInfoType;
import java.util.Collection;

/**
 * 依据vo对象field上的注解注入基础数据信息，包括：商品、仓库的信息，见：{@link BaseInfoType}。
 * 注意：只支持对实现了{@link BaseInfoInjectAble}接口的类进行属性注入。
 *
 * @see InjectProperty
 */
public interface BaseInfoInjectService {
    /**
     * @param targetList 需要注入基础数据信息的对象列表
     */
    void doInject(Collection<? extends BaseInfoInjectAble> targetList);


    /**
     * @param target 需要注入基础数据信息的对象
     */
    void doInject(BaseInfoInjectAble target);
}
