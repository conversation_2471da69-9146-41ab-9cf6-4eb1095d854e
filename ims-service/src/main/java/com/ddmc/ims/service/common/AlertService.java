package com.ddmc.ims.service.common;

import com.ddmc.ims.rpc.alert.AlertLevel;

/**
 * 通过企业微信机器人发送告警信息
 */
public interface AlertService {

    /**
     * 触发告警
     *
     * @param title 消息标题
     * @param message 消息详情
     * @param level 告警级别
     */
    void alert(String title, String message, AlertLevel level);

    /**
     * 指定业务模块触发告警
     *
     * @param title 消息标题
     * @param message 消息详情
     * @param level 告警级别
     * @param alertName 告警模块分类名称(scm后台机器人告警配置设置)
     */
    void alertSpecifyModule(String title, String message, AlertLevel level, String alertName);

    /**
     * 出发warning级别告警
     *
     * @param title 消息标题
     * @param message 消息详情
     */
    void alertWarning(String title, String message);

    /**
     * 出发info消息
     *
     * @param title 消息标题
     * @param message 消息详情
     */
    void alertInfo(String title, String message);

    /**
     * 出发info消息
     *
     * @param title 消息标题
     * @param message 消息详情
     * @param notifyKey 指定机器人key
     */
    void alertInfo(String title, String message, String notifyKey);

    /**
     * 出发warning级别告警
     *
     * @param title 消息标题
     * @param message 消息详情
     * @param level 告警级别
     * @param notifyKey 指定机器人key
     */
    void alert(String title, String message, AlertLevel level, String notifyKey);

    /**
     * 出发warning级别告警
     *
     * @param title 消息标题
     * @param message 消息详情
     * @param notifyKey 指定机器人key
     */
    void alertWarning(String title, String message, String notifyKey);
}
