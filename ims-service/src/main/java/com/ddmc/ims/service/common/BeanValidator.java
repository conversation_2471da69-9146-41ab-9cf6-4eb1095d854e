package com.ddmc.ims.service.common;

import com.ddmc.ims.common.constant.VerifyCodeEnum;
import javax.validation.constraints.NotNull;

/**
 * BeanValidator
 *
 * <AUTHOR>
 */
public interface BeanValidator<T> {

    /**
     * 校验对象
     *
     * @param obj obj
     */
    void valid(T obj);

    /**
     * 多个校验器匹配到时的优先级顺序
     *
     * @return 优先级
     */
    default Integer order() {
        return Integer.MAX_VALUE;
    }

    /**
     * 获取匹配的校验码
     *
     * @return verify code
     */
    @NotNull VerifyCodeEnum getVerifyCode();
}
