package com.ddmc.ims.service.common;

import com.ddmc.ims.common.constant.VerifyCodeEnum;
import java.util.function.Function;

/**
 * 校验服务
 *
 * <AUTHOR>
 */
public interface VerifyService {

    /**
     * 校验对象
     *
     * <p>1、优先Hibernate-validator 校验默认分组{@link javax.validation.groups.Default}</p>
     * <p>2、匹配自定义协议校验器。取所有当前对象以及所有父类的校验器 并 按照优先级取最小的第一个进行校验。{@link BeanValidator#order()}</p>
     * <p>3、将本次校验的校验码,发送给统一的预警服务。可以进行配置不同的预警群以及策略</p>
     *
     * <p>异常：针对{@link com.ddmc.ims.common.exception.ImsBusinessException}校验时异常才会统一预警。否则不进行预警。直接进入死信</p>
     *
     * @param obj 对象
     * @param verifyCode 校验code
     * @throws com.ddmc.ims.common.exception.ImsBusinessException 校验异常时发送
     */
    <T> void verify(T obj, VerifyCodeEnum verifyCode);

    /**
     * 校验对象
     *
     * <p>1、优先Hibernate-validator 校验默认分组{@link javax.validation.groups.Default}</p>
     * <p>2、匹配自定义协议校验器。取所有当前对象以及所有父类的校验器 并 按照优先级取最小的第一个进行校验。{@link BeanValidator#order()}</p>
     * <p>3、将本次校验的校验码,发送给统一的预警服务。可以进行配置不同的预警群以及策略</p>
     *
     * <p>异常：针对{@link com.ddmc.ims.common.exception.ImsBusinessException}校验时异常才会统一预警。否则不进行预警。直接进入死信</p>
     *
     * @param obj 对象
     * @param verifyCode 校验code
     * @param getObjIdentifyFunction 获取实体对象的业务唯一键方法，用于预警透传
     * @param <T> T
     * @param <R> 业务唯一键类型
     * @throws com.ddmc.ims.common.exception.ImsBusinessException 校验异常时发送
     */
    <T, R> void verify(T obj, VerifyCodeEnum verifyCode, Function<T, R> getObjIdentifyFunction);
}
