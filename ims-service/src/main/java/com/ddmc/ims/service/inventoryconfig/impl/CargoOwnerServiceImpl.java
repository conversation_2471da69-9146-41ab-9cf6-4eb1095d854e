package com.ddmc.ims.service.inventoryconfig.impl;

import com.ddmc.ims.dal.mapper.ims.CargoOwnerMapper;
import com.ddmc.ims.dal.model.ims.CargoOwner;
import com.ddmc.ims.response.inventory.CargoOwnerResponse;
import com.ddmc.ims.service.inventoryconfig.CargoOwnerService;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CargoOwnerServiceImpl implements CargoOwnerService {

    @Resource
    private CargoOwnerMapper cargoOwnerMapper;



    @Override
    public List<CargoOwnerResponse> queryCargoOwnerList() {
        List<CargoOwner> cargoOwners = cargoOwnerMapper.listAllCargoOwner();
        return cargoOwners.stream().map(t -> new CargoOwnerResponse(t.getId(), t.getCargoOwnerCode(), t.getName()))
            .collect(Collectors.toList());
    }

    @Override
    public Map<Long, CargoOwnerResponse> queryCargoOwnerMap() {
        return queryCargoOwnerList().stream()
            .collect(Collectors.toMap(CargoOwnerResponse::getCargoOwnerId, Function.identity(), (n1, n2) -> n2));
    }



}
