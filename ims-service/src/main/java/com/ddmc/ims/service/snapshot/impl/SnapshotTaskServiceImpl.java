package com.ddmc.ims.service.snapshot.impl;

import com.ddmc.fms.inv.enums.CheckStatus;
import com.ddmc.fms.inv.enums.DsStatus;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.SystemTypeEnum;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.event.producer.FmsCompareProducer;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.SnapshotTaskService;
import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SnapshotTaskServiceImpl implements SnapshotTaskService {


    @Resource
    private WarehouseMapper warehouseMapper;

    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;

    @Resource
    private FmsSnapshotNotifyMapper fmsSnapshotNotifyMapper;
    @Resource
    private FmsCompareProducer fmsCompareProducer;

    @Resource
    private AlertService alertService;

    @Override
    @Transactional(value = CommonConstants.IMS_MONITOR_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void dayEndTimeNotice(Date dayEndTime) {
        FmsSnapshotNotify fmsSnapshotNotify = fmsSnapshotNotifyMapper.selectByDayEndDate(dayEndTime);
        //当核对完成，并且之前不是ocs推送的，则与财务发起对账
        if (Objects.nonNull(fmsSnapshotNotify)) {
            if (DsStatus.RECON_READY.getValue().equals(fmsSnapshotNotify.getStatus())
                && SystemTypeEnum.SYSTEM.getDesc().equals(fmsSnapshotNotify.getSource())) {
                fmsCompareProducer.sendFmsCompareEvent(dayEndTime);
            }
            if (SystemTypeEnum.SYSTEM.getDesc().equals(fmsSnapshotNotify.getSource())) {
                fmsSnapshotNotify.setSource(SystemTypeEnum.OCS.getDesc());
                fmsSnapshotNotifyMapper.updateById(fmsSnapshotNotify);
            }

            return;
        }
        saveFmsSnapshotNotifyAndTask(dayEndTime, SystemTypeEnum.OCS.getDesc());
    }

    private void saveFmsSnapshotNotifyAndTask(Date dayEndTime, String source) {
        saveFmsSnapshotNotify(dayEndTime, source);
        List<Long> allWarehouseIds = warehouseMapper.selectAllId();
        List<SnapshotTask> taskList = allWarehouseIds.stream()
            .map(warehouseId -> buildTask(warehouseId, dayEndTime)).collect(Collectors.toList());
        Lists.partition(taskList, CommonConstants.BATCH_INSERT_DB_100).forEach(snapshotTaskMapper::batchInsert);
    }


    @Override
    @Transactional(value = CommonConstants.IMS_MONITOR_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void dayEndTimeNoticeJob(Date dayEndTime) {
        FmsSnapshotNotify fmsSnapshotNotify = fmsSnapshotNotifyMapper.selectByDayEndDate(dayEndTime);
        if (Objects.nonNull(fmsSnapshotNotify)) {
            return;
        }
        saveFmsSnapshotNotifyAndTask(dayEndTime, SystemTypeEnum.SYSTEM.getDesc());
    }

    @Override
    public void compareResult(Date dayEndTime, String status) {
        FmsSnapshotNotify fmsSnapshotNotify = fmsSnapshotNotifyMapper.selectByDayEndDate(dayEndTime);
        if (Objects.isNull(fmsSnapshotNotify)) {
            return;
        }
        log.info("财务通知前状态, {}", JsonUtil.toJson(fmsSnapshotNotify));
        if (CheckStatus.RECON_FAIL.getValue().equals(status)) {
            alertService.alert("日结失败", "日结失败", AlertLevel.WARNING);
        }
        fmsSnapshotNotify.setStatus(status);
        fmsSnapshotNotifyMapper.updateStatusByDayEndDate(dayEndTime, status);
    }

    private void saveFmsSnapshotNotify(Date dayEndTime, String source) {
        FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setDayEndDate(dayEndTime);
        fmsSnapshotNotify.setStatus(DsStatus.SYNC_SUCC.getValue());
        fmsSnapshotNotify.setSource(source);
        fmsSnapshotNotifyMapper.insert(fmsSnapshotNotify);
    }

    private SnapshotTask buildTask(Long warehouseId, Date snapshotDate) {
        SnapshotTask task = new SnapshotTask();
        task.setSnapshotType(SnapshotTypeEnum.FMS);
        task.setSnapshotTime(snapshotDate);
        task.setStatus(SnapshotStatusEnum.INIT);
        task.setWarehouseId(warehouseId);
        return task;
    }
}
