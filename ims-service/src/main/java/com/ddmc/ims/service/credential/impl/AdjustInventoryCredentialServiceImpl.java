package com.ddmc.ims.service.credential.impl;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.InventoryCredentialConverter;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.request.credential.oc.AdjustCredentialRequest;
import com.ddmc.ims.service.credential.AdjustInventoryCredentialService;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * admin
 */
@Service
public class AdjustInventoryCredentialServiceImpl implements AdjustInventoryCredentialService {

    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;
    @Resource
    private CredentialWrapperManager credentialWrapperManager;
    @Resource
    private LocalParamConfig localParamConfig;



    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryAdjust(AdjustCredentialRequest request) {
        checkTryAdjust(request);
        saveAdjustCredential(request);
    }

    private void checkTryAdjust(AdjustCredentialRequest request) {
        CredentialHeader credentialHeader = credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId());
        if (Objects.nonNull(credentialHeader)) {
            throw new ImsBusinessException(CommonErrorCode.EXIST_CREDENTIAL);
        }
    }

    /**
     * 保存入库凭证
     *
     * @param request 凭证请求
     */
    private void saveAdjustCredential(AdjustCredentialRequest request) {
        CredentialHeader header = InventoryCredentialConverter
            .convertAdjustCredential(request, localParamConfig.getDefaultLocationUsageCodeMap());
        credentialWrapperManager.saveCredential(header);
    }

}
