package com.ddmc.ims.service.snapshot;

import com.ddmc.ims.dto.CommandChangeQtyDto;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface SnapshotCredentialDiffService {

    /**
     * 根据仓库和快照时间查询差异表中业务库存多的数据
     *
     * @param warehouseId 仓库id
     * @param snapshotDate 快照时间
     * @return 返回
     */
    Map<Long, CommandChangeQtyDto> getTodayFmsLessInventoryQtyMap(Long warehouseId, Date snapshotDate);

    /**
     * 根据仓库和快照时间查询差异表中财务多的数据
     *
     * @param warehouseId 仓库id
     * @param snapshotDate 快照时间
     * @return 返回
     */
    Map<Long, CommandChangeQtyDto> getTodayFmsMoreInventoryQtyMap(Long warehouseId, Date snapshotDate);


    Map<Long, CommandChangeQtyDto> getInitFmsLessInventoryQtyMap(Long warehouseId, Date snapshotDate,
        Set<String> orderSourceSet);

    /**
     * 根据仓库和快照时间查询差异表中财务多的数据
     *
     * @param warehouseId 仓库id
     * @param snapshotDate 快照时间
     * @return 返回
     */
    Map<Long, CommandChangeQtyDto> getInitFmsMoreInventoryQtyMap(Long warehouseId, Date snapshotDate);

}
