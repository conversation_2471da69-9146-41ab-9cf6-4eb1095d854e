package com.ddmc.ims.service.snapshot.impl;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.SnapshotConverter;
import com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.monitor.SkuInventoryInOutSnapshot;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHour;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.dto.InOutNumDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.snapshot.SkuInventorySnapshotPerHourService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SkuInventorySnapshotPerHourServiceImpl implements SkuInventorySnapshotPerHourService {

    @Resource
    private CredentialWrapperManager credentialWrapperManager;

    @Resource
    private CommandManager commandManager;

    @Resource
    private SkuInventorySnapshotPerHourMapper skuInventorySnapshotPerHourMapper;

    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;

    @Resource
    private SkuInventoryInOutSnapshotMapper skuInventoryInOutSnapshotMapper;

    @Resource
    private LocalParamConfig localParamConfig;

    @Resource
    private LocalParamService localParamService;

    @Resource
    private SkuInventorySnapshotUsagePerHourMapper skuInventorySnapshotUsagePerHourMapper;

    private boolean isReplay() {
        return localParamService.getBooleanValue(LocalParamsConstants.SKU_INVENTORY_SNAPSHOT_PER_HOUR_REPLAY, false);
    }



    @Override
    public void handleWarehouseSnapshotPerHour(Date snapshotDate, List<Long> shardingWarehouseIds) {

        List<SnapshotTask> existSnapshotTasks = snapshotTaskMapper
            .selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(shardingWarehouseIds, SnapshotTypeEnum.INVENTORY_HOUR,
                snapshotDate);
        if (isReplay()) {
            List<SnapshotTask> needReplay = existSnapshotTasks.stream()
                .filter(t -> !Objects.equals(t.getStatus(), SnapshotStatusEnum.COMPLETE)).collect(Collectors.toList());
            needReplay.forEach(this::handlePerHourSnapshotTask);
            return;
        }

        if (CollectionUtils.isNotEmpty(existSnapshotTasks)) {
            throw new ImsBusinessException("已存在相同的快照任务");
        }

        List<SnapshotTask> snapshotTasks = SnapshotConverter
            .convertToSnapshotTask(shardingWarehouseIds, snapshotDate, SnapshotTypeEnum.INVENTORY_HOUR);
        Lists.partition(snapshotTasks, CommonConstants.BATCH_INSERT_DB_100)
            .forEach(subList -> snapshotTaskMapper.batchInsert(subList));
        snapshotTasks.forEach(this::handlePerHourSnapshotTask);

    }

    @Override
    public void handlePerHourSnapshotTask(SnapshotTask snapshotTask) {
        log.info("[SkuInventorySnapshotPerHourService] warehouseId:{}, time:{}", snapshotTask.getWarehouseId(),
            snapshotTask.getSnapshotTime());
        if (localParamConfig.isInterruptSnapshotPerHour()) {
            throw new ImsBusinessException("逻辑库存小时快照中断");
        }
        //睡眠
        sleep();

        Long warehouseId = snapshotTask.getWarehouseId();
        Date snapshotDate = snapshotTask.getSnapshotTime();
        Date lastSnapshotPerHourDate = skuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(warehouseId, snapshotDate);

        Date lastSnapshotInOutDate = skuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(warehouseId, snapshotDate);

        Date lastDate = DateUtils.addDays(snapshotDate, -1);

        if (Objects.isNull(lastSnapshotPerHourDate) && Objects.nonNull(lastSnapshotInOutDate)) {
            lastDate = lastSnapshotInOutDate;
        }

        if (Objects.nonNull(lastSnapshotPerHourDate) && Objects.isNull(lastSnapshotInOutDate)) {
            lastDate = lastSnapshotPerHourDate;
        }

        if (Objects.nonNull(lastSnapshotPerHourDate) && Objects.nonNull(lastSnapshotInOutDate)) {
            lastDate = lastSnapshotPerHourDate.compareTo(lastSnapshotInOutDate) >= 0 ? lastSnapshotPerHourDate
                : lastSnapshotInOutDate;
        }

        List<CredentialHeader> credentialHeaderAndDetails = credentialWrapperManager.getCredentialHeaders(
            warehouseId, lastDate, snapshotDate);
        List<CommandInventoryNumDto> commandInventoryNumList = commandManager
            .getCommandInventoryNumList(credentialHeaderAndDetails);

        saveSnapshotPerHour(lastDate, warehouseId, snapshotDate, commandInventoryNumList);
        saveSkuInventoryInOutSnapshots(credentialHeaderAndDetails, snapshotTask);
        saveSkuInventorySnapshotUsagePerHour(lastDate, warehouseId, snapshotDate, commandInventoryNumList);
        snapshotTaskMapper
            .updateStatusById(snapshotTask.getId(), SnapshotStatusEnum.COMPLETE, SnapshotStatusEnum.PROCESSING);
    }


    private void saveSnapshotPerHour(Date lastDate, Long warehouseId, Date snapshotDate,
        List<CommandInventoryNumDto> commandInventoryNumList) {
        List<SkuInventorySnapshotPerHour> lastSnapshotList = skuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(lastDate, warehouseId);

        List<SkuInventorySnapshotPerHour> snapshotPerHours = SnapshotConverter
            .convertToSkuInventorySnapshotPerHour(lastSnapshotList, commandInventoryNumList, snapshotDate);

        if (CollectionUtils.isNotEmpty(snapshotPerHours)) {
            Lists.partition(snapshotPerHours, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(subList -> skuInventorySnapshotPerHourMapper.insertList(subList));
        }

    }

    private void saveSkuInventoryInOutSnapshots(List<CredentialHeader> credentialHeaderAndDetails,
        SnapshotTask snapshotTask) {
        List<InOutNumDto> fmsInOutNumDto = commandManager.getFmsInOutNumDto(credentialHeaderAndDetails);
        List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots = getSkuInventoryInOutSnapshots(snapshotTask,
            fmsInOutNumDto);

        if (CollectionUtils.isNotEmpty(skuInventoryInOutSnapshots)) {
            Lists.partition(skuInventoryInOutSnapshots, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(subList -> skuInventoryInOutSnapshotMapper.insertList(subList));
        }
    }

    private void saveSkuInventorySnapshotUsagePerHour(Date lastDate, Long warehouseId, Date snapshotDate,
        List<CommandInventoryNumDto> commandInventoryNumList) {
        List<SkuInventorySnapshotUsagePerHour> lastUsageSnapshotList = skuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(lastDate, warehouseId);

        List<SkuInventorySnapshotUsagePerHour> newSkuInventorySnapshotUsagePerHourList = SnapshotConverter
            .convertToSkuInventorySnapshotUsagePerHour(lastUsageSnapshotList, commandInventoryNumList, snapshotDate);
        if (CollectionUtils.isNotEmpty(newSkuInventorySnapshotUsagePerHourList)
            && localParamConfig.enableInsertUsageSnapshot()) {
            Lists.partition(newSkuInventorySnapshotUsagePerHourList, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(subList -> skuInventorySnapshotUsagePerHourMapper.insertList(subList));
        }

    }



    private List<SkuInventoryInOutSnapshot> getSkuInventoryInOutSnapshots(SnapshotTask snapshotTask,
        List<InOutNumDto> fmsInOutNumDtos) {
        int currentHour = DateUtil.getDateHour(snapshotTask.getSnapshotTime());
        List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots = new ArrayList<>();
        if (currentHour == 0) {
            Date start = DateUtil.getPlusStartOfDay(snapshotTask.getSnapshotTime(), -1);
            Date end = DateUtil.getPlusEndOfDay(snapshotTask.getSnapshotTime(), -1);
            skuInventoryInOutSnapshots = skuInventoryInOutSnapshotMapper
                .selectBySnapshotDateTimeBetween(start, end, snapshotTask.getWarehouseId());
        }
        return SnapshotConverter
            .convertToSkuInventorySnapshotPerHour(snapshotTask, fmsInOutNumDtos, skuInventoryInOutSnapshots);
    }


    @Override
    public void clearSnapshotPerHour(List<Long> warehouseIds, Date snapshotHour) {
        warehouseIds.forEach(t -> {
            List<Long> singletonList = Collections.singletonList(t);
            snapshotTaskMapper.deleteByWarehouseIdInAndSnapshotTimeAndSnapshotType(singletonList, snapshotHour,
                SnapshotTypeEnum.INVENTORY_HOUR);
            skuInventorySnapshotPerHourMapper.deleteByWarehouseIdInAndSnapshotDateTime(singletonList, snapshotHour);
            skuInventoryInOutSnapshotMapper.deleteByWarehouseIdInAndSnapshotDateTime(singletonList, snapshotHour);
            skuInventorySnapshotUsagePerHourMapper.deleteByWarehouseIdInAndSnapshotDateTime(singletonList,
                snapshotHour);
        });
    }


    @SneakyThrows
    private void sleep() {
        Thread.sleep(localParamConfig.getSnapshotPerHourSleepInterval());
    }

}
