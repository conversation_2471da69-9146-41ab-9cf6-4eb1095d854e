package com.ddmc.ims.service.common.impl;

import com.ddmc.scm.common.config.client.service.LocalParamCacheService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class LocalParamCacheServiceImpl implements LocalParamCacheService {

    /**
     * 参数缓存
     */
    public static final Cache<String, String> PARAM_CHANGE_CACHE = CacheBuilder.newBuilder()
        .maximumSize(200)
        .expireAfterWrite(5, TimeUnit.SECONDS)
        .build();


    @Override
    public String getValue(String cacheKey) {
        return PARAM_CHANGE_CACHE.getIfPresent(cacheKey);
    }

    @Override
    public void setValue(String cacheKey, String paramValue) {
        if (StringUtils.isNotBlank(paramValue)) {
            PARAM_CHANGE_CACHE.put(cacheKey, paramValue);
        }
    }
}
