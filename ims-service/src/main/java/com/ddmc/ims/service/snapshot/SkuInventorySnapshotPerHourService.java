package com.ddmc.ims.service.snapshot;

import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import java.util.Date;
import java.util.List;

public interface SkuInventorySnapshotPerHourService {

    /**
     * 将以下仓库按小时进行快照
     *
     * @param snapshotDate 快照日期-精确到小时
     * @param shardingWarehouseIds 待处理的仓库id
     */
    void handleWarehouseSnapshotPerHour(Date snapshotDate, List<Long> shardingWarehouseIds);

    /**
     * 小时快照任务处理
     *
     * @param snapshotTask snapshotTask
     */
    void handlePerHourSnapshotTask(SnapshotTask snapshotTask);

    void clearSnapshotPerHour(List<Long> warehouseIds, Date snapshotHour);
}
