package com.ddmc.ims.service.common;

import com.ddmc.ims.common.bo.ManageCategory.ProductAdmPurchaseCategory;
import java.util.List;
import java.util.Map;

/**
 * 采购分类
 */
public interface ManageCategoryService {

    /**
     * 获取所有采购类目列表
     *
     * @return 采购分类
     */
    List<ProductAdmPurchaseCategory> getMcPurchaseCategoryList();



    /**
     * 获取所有采购分类名称信息
     * @return <采购分类ID,采购分类信息>
     */
    Map<Long,ProductAdmPurchaseCategory> getAllCategoryInfoMap();

}
