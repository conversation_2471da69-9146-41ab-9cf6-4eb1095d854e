package com.ddmc.ims.service.common.impl;

import com.ddmc.ims.common.view.BaseInfoInjectAble;
import com.ddmc.ims.common.view.BaseInfoType;
import com.ddmc.ims.common.view.InjectProperty;
import com.ddmc.ims.manager.CategoryGroupRemoteManager;
import com.ddmc.ims.manager.PmsCommonRemoteManager;
import com.ddmc.ims.manager.SkuQueryManager;
import com.ddmc.ims.manager.SsoManager;
import com.ddmc.ims.service.common.BaseInfoInjectService;
import com.ddmc.ims.service.common.ManageCategoryService;
import com.ddmc.ims.service.inventoryconfig.CargoOwnerService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class BaseInfoInjectServiceImpl implements BaseInfoInjectService {

    private final Map<Class<? extends BaseInfoInjectAble>, Map<BaseInfoType, Set<Field>>> supportedFields = new ConcurrentHashMap<>();
    private final Map<Field, InjectProperty> baseFieldConfigs = new HashMap<>();
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private SkuQueryManager skuQueryManager;
    @Resource
    private SsoManager ssoManager;
    @Resource
    private ManageCategoryService manageCategoryService;
    @Resource
    private CategoryGroupRemoteManager categoryGroupRemoteManager;
    @Resource
    private PmsCommonRemoteManager pmsCommonRemoteManager;
    @Resource
    private CargoOwnerService cargoOwnerService;


    /**
     * 获取关联对象。当定义新的BaseInfoType时，需修改此方法获取关联对象。
     *
     * @param baseInfoType 资源类型
     * @param fields 需要设置的field
     * @param targetList 模板对象列表
     * @return key为关联对象id，value为关联对象。
     */
    private Map<?, ?> getDataMap(BaseInfoType baseInfoType, Set<Field> fields,
        Collection<? extends BaseInfoInjectAble> targetList) {
        switch (baseInfoType) {
            case STORE:
                Set<Long> storeIds = parseIds(targetList, fields);
                if (CollectionUtils.isNotEmpty(storeIds)) {
                    return warehouseService.loadFromCache(storeIds);
                }
                break;
            case PRODUCT:
                Set<Long> skuIds = parseIds(targetList, fields);
                if (CollectionUtils.isNotEmpty(skuIds)) {
                    return skuQueryManager.batchGetSkuBySkuIds(new ArrayList<>(skuIds));
                }
                break;
            case CITY:
                return ssoManager.ssoCityMap();
            case CATEGORY_NAME:
                return manageCategoryService.getAllCategoryInfoMap();
            case RESPONSIBLE_GROUP:
                Set<String> groupIds = parseIds(targetList, fields);
                if (CollectionUtils.isNotEmpty(groupIds)){
                    return categoryGroupRemoteManager.getCategoryGroups(groupIds);
                }
                break;
            case ZONE:
                return pmsCommonRemoteManager.listZoneMap();
            case CARGO_OWNER:
                return cargoOwnerService.queryCargoOwnerMap();
            default:
                return Collections.emptyMap();
        }
        return Collections.emptyMap();
    }

    @Override
    public void doInject(Collection<? extends BaseInfoInjectAble> targetList) {
        if (CollectionUtils.isEmpty(targetList)) {
            return;
        }
        Class<? extends BaseInfoInjectAble> type = targetList.iterator().next().getClass();
        Map<BaseInfoType, Set<Field>> fields = supportedFields.get(type);
        if (fields == null) {
            fields = initSupportedFields(type);
        }
        if (MapUtils.isEmpty(fields)) {
            return;
        }
        //分别对每一种类型的基础数据进行注入
        for (Map.Entry<BaseInfoType, Set<Field>> entry : fields.entrySet()) {
            Map<?, ?> dataMap;
            dataMap = getDataMap(entry.getKey(), entry.getValue(), targetList);
            for (BaseInfoInjectAble o : targetList) {
                try {
                    writeProperty(o, entry.getValue(), dataMap);
                } catch (Exception e) {
                    log.error("invalid Annotation", e);
                }
            }
        }
    }

    @Override
    public void doInject(BaseInfoInjectAble target) {
        if (target == null) {
            return;
        }
        doInject(Collections.singletonList(target));
    }

    /**
     * @param clazz 需要注入信息的类
     * @return 需要注入信息的所有方法。map的key为信息类型（eg: 商品、仓库）,map的value为需要该类型属性的所有方法（eg: 名称）
     */
    private synchronized Map<BaseInfoType, Set<Field>> initSupportedFields(Class<? extends BaseInfoInjectAble> clazz) {
        Map<BaseInfoType, Set<Field>> map = supportedFields.get(clazz);
        if (map != null) {
            return map;
        }

        map = new EnumMap<>(BaseInfoType.class);
        for (Field field : FieldUtils.getAllFields(clazz)) {
            InjectProperty injectProperty;
            if (((injectProperty = field.getAnnotation(InjectProperty.class)) == null)) {
                continue;
            }

            map.computeIfAbsent(BaseInfoType.valueOf(injectProperty.type().name()), e -> new HashSet<>()).add(field);
            baseFieldConfigs.put(field, injectProperty);

        }
        supportedFields.put(clazz, map);
        return map;
    }

    @SuppressWarnings("unchecked")
    private <E> E getId(BaseInfoInjectAble o, String field) {
        try {
            return (E) FieldUtils.readField(o, field, true);
        } catch (Exception e) {
            log.error("read id value error", e);
            return null;
        }
    }

    private <E> Set<E> parseIds(Collection<? extends BaseInfoInjectAble> targetList, Set<Field> fields) {
        Set<E> ids = Sets.newHashSetWithExpectedSize(targetList.size() * fields.size());
        for (Field field : fields) {
            InjectProperty config = baseFieldConfigs.get(field);
            if (config == null) {
                continue;
            }
            for (BaseInfoInjectAble o : targetList) {
                E id = getId(o, config.id());
                if (id != null) {
                    ids.add(id);
                }
            }
        }
        return ids;
    }

    private void writeProperty(BaseInfoInjectAble o, Set<Field> fields, Map<?, ?> dataMap) {
        for (Field field : fields) {
            InjectProperty baseConfig;
            if ((baseConfig = baseFieldConfigs.get(field)) != null) {
                Object id = getId(o, baseConfig.id());
                readAndWrite(dataMap.get(id), baseConfig.property(), o, field);
            }
        }
    }

    private void readAndWrite(Object fromObject, String fromProperty, Object targetObject, Field targetField) {
        if (fromObject == null || StringUtils.isEmpty(fromProperty)) {
            return;
        }

        try {
            Object value = FieldUtils.readField(fromObject, fromProperty, true);
            FieldUtils.writeField(targetField, targetObject, value, true);
        } catch (Exception e) {
            log.error("copy property error", e);
        }
    }

}
