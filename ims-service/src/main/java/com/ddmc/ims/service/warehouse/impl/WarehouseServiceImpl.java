package com.ddmc.ims.service.warehouse.impl;

import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.model.ims.Warehouse;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.google.common.collect.Maps;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WarehouseServiceImpl implements WarehouseService {

    @Resource
    private WarehouseMapper warehouseMapper;



    @Override
    public List<Warehouse> getAllWarehouses() {
        return warehouseMapper.getAllWarehouses();
    }

    @Override
    public List<Long> getAllWarehouseIds() {
        return warehouseMapper.selectAllId();

    }


    @Override
    public Warehouse getWarehouse(Long warehouseId) {
        try {
            return CacheConfig.WAREHOUSE.get(warehouseId, () -> warehouseMapper.selectById(warehouseId));
        } catch (Exception ex) {
            log.error("[WarehouseService#getWarehouse] get-error! warehouseId:{}", warehouseId, ex);
            return warehouseMapper.selectById(warehouseId);
        }
    }



    /**
     * 从查询缓存读取仓库信息
     *
     * @param warehouseIds 仓库id列表
     * @return Map<Long, Warehouse>
     */
    @Override
    public Map<Long, Warehouse> loadFromCache(Collection<Long> warehouseIds) {
        Map<Long, Warehouse> cached = Maps.newHashMapWithExpectedSize(warehouseIds.size());
        warehouseIds.forEach(id -> {
            Warehouse warehouse = getWarehouse(id);
            if (warehouse != null) {
                cached.put(id, warehouse);
            }
        });
        return cached;
    }


}
