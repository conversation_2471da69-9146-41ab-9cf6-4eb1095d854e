package com.ddmc.ims.service.inventoryconfig;

import com.ddmc.ims.response.inventory.CargoOwnerResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CargoOwnerService {

    /**
     * 查询所有的货主信息
     *
     * @return 货主信息
     */
    List<CargoOwnerResponse> queryCargoOwnerList();

    /**
     * 查询所有的货主信息
     *
     * @return <货主id, 货主信息>
     */
    Map<Long, CargoOwnerResponse> queryCargoOwnerMap();



}
