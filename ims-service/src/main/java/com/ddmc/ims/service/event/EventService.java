package com.ddmc.ims.service.event;

import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.request.common.PageRequest;
import com.github.pagehelper.Page;
import java.util.Date;
import java.util.List;

/**
 * Event事件
 */
public interface EventService {

    /**
     * 按照查询条件分页展示
     * @return 分页信息
     */
    Page<EventEntity> pageEventEntity(PageRequest page, Date dateStart, Date dateEnd,List<String> keyTypeList);
    /**
     * 根据条件查询事件集合
     *
     * @param condition 查询条件
     * @return 事件集合
     */
    List<EventEntity> listByCondition(ListEventCondition condition);

    /**
     * 根据事件id查询事件
     *
     * @param id 事件id
     * @return 事件
     */
    EventEntity getById(Long id);

    /**
     * 批量更新事件状态
     *
     * @param eventIdList 事件id集合
     * @param newStatus 新状态
     * @param oldStatus 老状态
     */
    int batchUpdateStatus(List<Long> eventIdList, Integer newStatus, Integer oldStatus);

    /**
     * 更新事件状态
     */
    int updateStatus(List<Long> eventIdList, Integer newStatus);

}
