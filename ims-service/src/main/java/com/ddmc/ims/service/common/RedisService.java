package com.ddmc.ims.service.common;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * redis操作基础类
 **/
public interface RedisService {

    String get(String key);

    /**
     * 批量获取string
     * @param key key集合
     * @return 响应结果
     */
    List<String> multiGet(List<String> key);

    void set(String key, String value);

    void set(String key, String value, Long expireTime);

    Boolean delete(String key);

    Boolean hasKey(String key);

    Long increment(String key);

    Long increment(String key, Long value, Long expireTime);

    Boolean setIfAbsent(String key, String value, Long expireTime);

    <T> T setIfNotExists(String key, Supplier<T> supplier, Long timeOut, TimeUnit timeUnit, Class<T> clazz);

    <T> List<T> setListIfNotExists(String key, Supplier<List<T>> supplier, Long timeOut, TimeUnit timeUnit,
        Class<T> clazz);
}
