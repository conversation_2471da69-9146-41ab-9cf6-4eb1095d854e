package com.ddmc.ims.service.common.impl;

import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.service.common.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Service
public class RedisServiceImpl implements RedisService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    @Override
    public List<String> multiGet(List<String> key) {
        return stringRedisTemplate.opsForValue().multiGet(key);
    }


    @Override
    public void set(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    @Override
    public void set(String key, String value, Long expireTime) {
        stringRedisTemplate.opsForValue().set(key, value, expireTime, TimeUnit.SECONDS);
    }

    @Override
    public Boolean delete(String key) {
        return stringRedisTemplate.delete(key);
    }

    @Override
    public Boolean hasKey(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    @Override
    public Long increment(String key) {
        stringRedisTemplate.expire(key, 60, TimeUnit.SECONDS);
        return stringRedisTemplate.opsForValue().increment(key, 1L);
    }

    @Override
    public Long increment(String key, Long value, Long expireTime) {
        stringRedisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
        return stringRedisTemplate.opsForValue().increment(key, value);
    }

    @Override
    public Boolean setIfAbsent(String key, String value, Long expireTime) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value, expireTime, TimeUnit.SECONDS);
    }

    @Override
    public <T> T setIfNotExists(String key, Supplier<T> supplier, Long timeOut, TimeUnit timeUnit, Class<T> clazz) {
        T resultObj;
        String result = get(key);
        if (StringUtils.isBlank(result)) {
            T supplierResult = supplier.get();
            if (supplierResult != null) {
                set(key, JsonUtils.toJson(supplierResult), timeUnit.toSeconds(timeOut));
                resultObj = supplierResult;
            } else {
                resultObj = null;
            }
        } else {
            resultObj = JsonUtils.parse(result, clazz);
        }
        return resultObj;
    }

    @Override
    public <T> List<T> setListIfNotExists(String key, Supplier<List<T>> supplier, Long timeOut, TimeUnit timeUnit,
        Class<T> clazz) {
        List<T> resultObj;
        String result = get(key);
        if (StringUtils.isEmpty(result)) {
            List<T> supplierResult = supplier.get();
            if (CollectionUtils.isNotEmpty(supplierResult)) {
                set(key, JsonUtils.toJson(supplierResult), timeUnit.toSeconds(timeOut));
                resultObj = supplierResult;
            } else {
                resultObj = Collections.emptyList();
            }
        } else {
            resultObj = JsonUtils.parseList(result, clazz);
        }
        return resultObj;
    }
}
