package com.ddmc.ims.service.credential.impl;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.command.CommandHandleFactory;
import com.ddmc.ims.command.InventoryCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.RealChangeRowNumberChecker;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseModLockMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.dto.AsyncConfirmOrderTypeConfig;
import com.ddmc.ims.event.producer.AsyncConfirmEventProducer;
import com.ddmc.ims.event.producer.SnapshotCredentialDiffProducer;
import com.ddmc.ims.event.producer.TransferOutChangeNotifyProducer;
import com.ddmc.ims.manager.conversion.CommandConversionFactory;
import com.ddmc.ims.manager.inventory.CredentialUsageDetailManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.manager.inventory.SnapshotCredentialDiffManager;
import com.ddmc.ims.metrics.DocMetricsService;
import com.ddmc.ims.service.credential.InventoryCredentialService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class InventoryCredentialServiceImpl implements InventoryCredentialService {

    public static final String ALL_ORDER_TYPE = "ALL";

    @Resource
    private CredentialWrapperManager credentialWrapperManager;
    @Resource
    private CommandHandleFactory commandHandleFactory;
    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;
    @Resource
    private LocalParamConfig localParamConfig;
    @Resource
    private SnapshotCredentialDiffProducer snapshotCredentialDiffProducer;
    @Resource
    private SnapshotCredentialDiffManager snapshotCredentialDiffManager;

    @Resource
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;
    @Resource
    private LocalParamService localParamService;
    @Resource
    private CredentialUsageDetailManager credentialUsageDetailManager;

    @Resource
    private AsyncConfirmEventProducer asyncConfirmEventProducer;
    @Resource
    private WarehouseModLockMapper warehouseModLockMapper;

    @Resource
    private TransferOutChangeNotifyProducer transferOutChangeNotifyProducer;

    @Resource
    private DocMetricsService docMetricsService;

    @Resource
    private InventoryCredentialService selfProxy;

    @Value("${ims.lockWarehouseModeLock:true}")
    private boolean lockWarehouseModeLock;

    @Value("${ims.lockWarehouseInventoryLock:true}")
    private boolean lockWarehouseInventoryLock;

    @Value("${ims.printLockWarehouseModeLock:true}")
    private boolean printLockWarehouseModeLock;

    @Value("${ims.enableNotifyBooking:true}")
    private boolean enableNotifyBooking;

    @Value("${ims.enableOrderTypeLimit:true}")
    private boolean enableOrderTypeLimit;

    @Value("${ims.asyncConfirmOrderType:}")
    private String asyncConfirmOrderType;

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Throwable.class)
    public void confirm(String idempotentId) {
        CredentialHeader credentialHeaderAndDetail = credentialWrapperManager
            .getCredentialHeaderAndDetail(idempotentId);
        if (Objects.isNull(credentialHeaderAndDetail)) {
            log.info("凭证->{},凭证不存在", idempotentId);
            return;
        }
        if (asyncConfirm(credentialHeaderAndDetail.getOrderType(), credentialHeaderAndDetail.getWarehouseId())) {
            asyncConfirmEventProducer.postSendAsyncConfirmEvent(credentialHeaderAndDetail.getIdempotentId(),
                credentialHeaderAndDetail.getWarehouseId());
            return;
        }
        if (!enableOrderTypeLimit) {
            selfProxy.directHandleCredential(credentialHeaderAndDetail);
            return;
        }
        //增加单据类型维度限流
        String resource =
            CommonConstants.CONFIRM_INTERFACE_ORDER_TYPE_LIMIT_PREFIX + credentialHeaderAndDetail.getOrderType();
        try (Entry ignored = SphU.entry(resource, EntryType.IN)) {
            selfProxy.directHandleCredential(credentialHeaderAndDetail);
        } catch (BlockException e) {
            log.warn("[confirm] confirm credential err! over qps limit, async confirm, credentialId -> {}",
                idempotentId, e);
            asyncConfirmEventProducer.postSendAsyncConfirmEvent(credentialHeaderAndDetail.getIdempotentId(),
                credentialHeaderAndDetail.getWarehouseId());
        }
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Throwable.class)
    public void directHandleCredential(CredentialHeader credentialHeaderAndDetail) {
        switch (credentialHeaderAndDetail.getStatus()) {
            case CONFIRM_SUCCESS:
                log.info("凭证->{}已经被确认，幂等处理！", credentialHeaderAndDetail.getIdempotentId());
                return;
            case CANCEL:
                throw new ImsBusinessException(CommonErrorCode.CREDENTIAL_CANCELED);
            case INIT:
                StopWatch stopWatch = new StopWatch("directHandleCredential");

                stopWatch.start("dealSnapshotCredentialDiff");
                int count = credentialHeaderMapper
                    .confirmCredentialByIdempotentId(credentialHeaderAndDetail.getIdempotentId());
                RealChangeRowNumberChecker.checkAndException(1, count, "确认凭证时，变更凭证状态异常");
                //记录和财务维度不一样凭证
                dealSnapshotCredentialDiff(credentialHeaderAndDetail);
                stopWatch.stop();
                //查询批次
                ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(credentialHeaderAndDetail)
                    .build();

                stopWatch.start("addLock");
                addLock(credentialHeaderAndDetail);
                stopWatch.stop();
                docMetricsService.metricDelayTime(credentialHeaderAndDetail.getOrderType(),
                    credentialHeaderAndDetail.getOrderOperateType(), credentialHeaderAndDetail.getBusinessTime());
                stopWatch.start("saveCredentialUsageDetail");
                credentialUsageDetailManager.saveCredentialUsageDetail(confirmContext);
                stopWatch.stop();

                //判断调拨登记是否在发布之后
                if (isTransferPublishAfterOutStore(confirmContext)) {
                    log.info("[isTransferOutStoreBeforePublish]->{}",
                        JsonUtil.toJson(confirmContext.getCredentialHeader()));
                    return;
                }

                stopWatch.start("getSkuInventoryCommand");
                List<? extends SkuInventoryCommand> skuInventoryCommandList = getSkuInventoryCommand(
                    credentialHeaderAndDetail);
                stopWatch.stop();

                stopWatch.start("doProcessInventoryCommand");
                doProcessInventoryCommand(skuInventoryCommandList);
                stopWatch.stop();

                //调拨在途事件
                postConfirmCredential(credentialHeaderAndDetail);
                log.info("[directHandleCredential cost]->{}", stopWatch.prettyPrint());
                return;
            default:
                log.error("不支持的凭证状态类型, 凭证信息{}", JsonUtil.toJson(credentialHeaderAndDetail));
                throw new ImsBusinessException("不支持的类型");
        }
    }

    private boolean isTransferPublishAfterOutStore(ConfirmContext confirmContext) {
        CredentialHeader credentialHeader = confirmContext.getCredentialHeader();

        //只处理调拨出库Do单，且申请出库
        if (Objects.equals(credentialHeader.getOrderType(), OrderTypeEnum.TRANSFER_DO_OUTBOUND.getCode()) && Objects
            .equals(credentialHeader.getOrderOperateType(), OrderOperateTypeEnum.APPLY_OUT_OF_STOCK.getCode())) {

            CredentialHeader outStockHeader = credentialHeaderMapper
                .selectByOrderNoAndExeOrderNoAndOrderTypeAndOrderOperateType(credentialHeader.getOrderNo(),
                    credentialHeader.getExeOrderNo(), OrderTypeEnum.TRANSFER_DO_OUTBOUND.getCode(),
                    OrderOperateTypeEnum.OUT_OF_STOCK.getCode());
            return Objects.nonNull(outStockHeader) && Objects
                .equals(outStockHeader.getStatus(), CredentialStatus.CONFIRM_SUCCESS);

        }

        return false;

    }


    @Override
    public List<? extends SkuInventoryCommand> getSkuInventoryCommand(CredentialHeader credentialHeaderAndDetail) {

        //有commandType的时候，必须所有的明细都要有commandType
        List<CredentialUseageDetail> usageDetailList = credentialHeaderAndDetail.getCredentialUseageDetailList();
        Optional<CredentialUseageDetail> commandTypeOptional = usageDetailList.stream()
            .filter(t -> StringUtils.isNotEmpty(t.getCommandType())).findAny();
        if (commandTypeOptional.isPresent()) {
            Optional<CredentialUseageDetail> any = usageDetailList.stream()
                .filter(t -> StringUtils.isEmpty(t.getCommandType())).findAny();
            any.ifPresent(t -> {
                log.error("[同一个凭证不允许CommandType同时存在有值与空值] {}",
                    JsonUtil.toJson(credentialHeaderAndDetail));
                throw new ImsBusinessException("同一个凭证不允许CommandType同时存在有值与空值");
            });

            Map<String, List<CredentialUseageDetail>> commandTypeDetailMap = usageDetailList
                .stream().collect(Collectors.groupingBy(CredentialUseageDetail::getCommandType));
            return commandTypeDetailMap.entrySet().stream().map(t -> {
                CommandTypeEnum commandType = CommandTypeEnum.fromCode(t.getKey());
                return CommandConversionFactory.getSkuInventoryCommand(credentialHeaderAndDetail, t.getValue(),
                    Collections.singletonList(commandType));
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }

        List<CommandTypeEnum> commandTypeEnumList = localParamConfig.getCommandTypeEnumList(credentialHeaderAndDetail);
        return CommandConversionFactory.getSkuInventoryCommand(credentialHeaderAndDetail, commandTypeEnumList);
    }

    @Override
    public void addLock(CredentialHeader credentialHeaderAndDetail) {
        if (CollectionUtils.isEmpty(credentialHeaderAndDetail.getCredentialDetailList())) {
            return;
        }
        List<Long> skuIds = credentialHeaderAndDetail.getCredentialDetailList().stream().map(CredentialDetail::getSkuId)
            .distinct().sorted().collect(Collectors.toList());
        Set<String> logicLocationCodes = Sets.newHashSet();
        credentialHeaderAndDetail.getCredentialDetailList().forEach(d -> {
            logicLocationCodes.add(d.getFromLogicLocationCode());
            logicLocationCodes.add(d.getToLogicLocationCode());
        });
        if (lockWarehouseInventoryLock) {
            List<String> logicLocationCodesSorted = logicLocationCodes.stream()
                .filter(c -> !CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.getLogicInventoryLocationCode().equals(c))
                .sorted().collect(Collectors.toList());
            logicLocationCodesSorted.forEach(code -> Lists.partition(skuIds, CommonConstants.BATCH_SELECT_STORE_500)
                .forEach(si -> warehouseSkuInventoryMapper
                    .addLock(credentialHeaderAndDetail.getWarehouseId(), Lists.newArrayList(code), si)));
        }

        if (lockWarehouseModeLock) {
            Set<Long> modNumSet = skuIds.stream()
                .map(t -> t % CommonConstants.WAREHOUSE_MOD_LOCK_2048).collect(Collectors.toSet());
            if (printLockWarehouseModeLock) {
                log.info("[addModeLock] warehouseId -> {}, skuIds -> {}, mod -> {}",
                    credentialHeaderAndDetail.getWarehouseId(),
                    JsonUtil.toJson(skuIds),
                    JsonUtil.toJson(modNumSet));
            }
            warehouseModLockMapper.addLock(credentialHeaderAndDetail.getWarehouseId(), modNumSet);
        }
    }


    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Throwable.class)
    public void cancel(String idempotentId) {
        CredentialHeader credentialHeader = credentialHeaderMapper.selectByIdempotentId(idempotentId);
        if (Objects.isNull(credentialHeader)) {
            log.warn("根据幂等键->{}取消凭证时，凭证不存在，幂等处理！", idempotentId);
            return;
        }
        if (CredentialStatus.CONFIRM_SUCCESS.equals(credentialHeader.getStatus())) {
            throw new ImsBusinessException(CommonErrorCode.CREDENTIAL_CONFIRMED);
        } else if (CredentialStatus.CANCEL.equals(credentialHeader.getStatus())) {
            log.info("凭证->{}已经被取消，幂等处理！", idempotentId);
            return;
        }
        credentialWrapperManager.cancel(idempotentId);
    }

    /**
     * 处理库存指令操作
     *
     * @param skuInventoryCommandList 库存指令
     */
    protected void doProcessInventoryCommand(List<? extends SkuInventoryCommand> skuInventoryCommandList) {
        Map<CommandTypeEnum, ? extends List<? extends SkuInventoryCommand>> skuInventoryCommandMap = skuInventoryCommandList
            .stream().collect(Collectors.groupingBy(SkuInventoryCommand::getCommandType));
        List<Map.Entry<CommandTypeEnum, ? extends List<? extends SkuInventoryCommand>>> list = new ArrayList<>(
            skuInventoryCommandMap.entrySet());
        list.sort(Comparator.comparing(t -> t.getKey().getOrder()));
        list.forEach(t -> {
            InventoryCommandHandle inventoryCommandHandle = commandHandleFactory
                .getCommandHandle(t.getKey().getCommandHandleClassName());
            inventoryCommandHandle.handleInventoryCommand(t.getValue());
        });
    }


    private void dealSnapshotCredentialDiff(CredentialHeader header) {
        //领用出库不记录差异
        if (OrderTypeEnum.MANUFACTURE_SELF_USE.getCode().equals(header.getOrderType())
            || OrderTypeEnum.MATERIAL_RETURN.getCode().equals(header.getOrderType())) {
            return;
        }
        if (Objects.nonNull(header.getEndDateTime())
            && !OrderOperateTypeEnum.IN_STOCK.getCode().equals(header.getOrderOperateType())
            && !DateUtil.checkSameDay(header.getBusinessTime(), header.getEndDateTime())) {
            snapshotCredentialDiffManager.saveCompletedCredentialDiff(header);
        }
        // 入库完成的进入事件
        snapshotCredentialDiffProducer.sendSnapshotCredentialDiffEvent(header);
    }


    private boolean asyncConfirm(String orderType, Long warehouseId) {
        AsyncConfirmOrderTypeConfig asyncConfirmOrderTypeConfig =
            JsonUtil.fromJson(
                asyncConfirmOrderType,
                AsyncConfirmOrderTypeConfig.class);
        Set<String> switchAllWarehouseIdOrderType = Optional.ofNullable(asyncConfirmOrderTypeConfig)
            .map(AsyncConfirmOrderTypeConfig::getSwitchAllWarehouseOrderTypes).orElse(Collections.emptySet());
        if (switchAllWarehouseIdOrderType.contains(ALL_ORDER_TYPE) || switchAllWarehouseIdOrderType.contains(
            orderType)) {
            return true;
        }
        Map<String, Set<Long>> orderTypeWarehouseIdMap = Optional.ofNullable(asyncConfirmOrderTypeConfig)
            .map(AsyncConfirmOrderTypeConfig::getItems)
            .orElse(Collections.emptyList()).stream()
            .collect(Collectors.toMap(AsyncConfirmOrderTypeConfig.AsyncConfirmOrderTypeItem::getOrderType,
                AsyncConfirmOrderTypeConfig.AsyncConfirmOrderTypeItem::getWarehouseIds, (v1, v2) -> v1));
        return orderTypeWarehouseIdMap.getOrDefault(orderType, Collections.emptySet()).contains(warehouseId);
    }

    private void postConfirmCredential(CredentialHeader credentialHeaderAndDetail) {
        //1.调拨相关通知ims-admin触发缺货预定库存计算
        String orderType = credentialHeaderAndDetail.getOrderType();
        if (!enableNotifyBooking) {
            return;
        }
        if (OrderTypeEnum.isTransferOut(orderType)) {
            transferOutChangeNotifyProducer.postTransferOutChangeNotifyEvent(credentialHeaderAndDetail,
                credentialHeaderAndDetail.getCredentialDetailList());
        }
    }
}
