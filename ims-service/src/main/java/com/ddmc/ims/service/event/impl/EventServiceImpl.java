package com.ddmc.ims.service.event.impl;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.mapper.ims.EventMapper;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.request.common.PageRequest;
import com.ddmc.ims.service.event.EventService;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EventServiceImpl implements EventService {

    @Resource
    private EventMapper eventMapper;

    /**
     * 分页查询event msg
     *
     * @param page      分页信息
     * @param dateStart 开始日期
     * @param dateEnd   结束日期
     * @param keyTypeList {@link String}
     * @return 分页信息
     */
    @Override
    public Page<EventEntity> pageEventEntity(PageRequest page, Date dateStart, Date dateEnd, @Nullable List<String> keyTypeList) {
        PageMethod.startPage(page.getPageNo(), page.getPageSize());
        //先分页查ids
        Page<Long> pageIds = (Page<Long>) eventMapper.listIdsByTimeAndType(dateStart, dateEnd, keyTypeList);
        //根据ids去查询分页信息
        Page<EventEntity> pageEvent = Lists.partition(pageIds, CommonConstants.BATCH_INSERT_DB_100)
                .stream()
                .map(eventMapper::getByIds)
                .flatMap(List::stream)
                .collect(Collectors.toCollection(Page::new));
        pageEvent.setTotal(pageIds.getTotal());
        pageEvent.setPageSize(pageIds.getPageSize());
        pageEvent.setPageNum(pageIds.getPageNum());
        return pageEvent;
    }

    @Override
    public List<EventEntity> listByCondition(ListEventCondition condition) {
        return eventMapper.listByCondition(condition);
    }

    @Override
    public EventEntity getById(Long id) {
        return eventMapper.getById(id);
    }

    @Override
    public int batchUpdateStatus(List<Long> eventIdList, Integer newStatus, Integer oldStatus) {
        return eventMapper.batchUpdateStatus(eventIdList, newStatus, oldStatus);
    }

    @Override
    public int updateStatus(List<Long> eventIdList, Integer newStatus) {
        return eventMapper.updateStatus(eventIdList, newStatus);
    }
}
