package com.ddmc.ims.service.common.impl;

import cn.hutool.core.util.TypeUtil;
import com.ddmc.ims.common.constant.VerifyCodeEnum;
import com.ddmc.ims.common.errorcode.Asserts;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.errorcode.ErrorConstants;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.ValidationUtils;
import com.ddmc.ims.response.base.ValidationResult;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.common.BeanValidator;
import com.ddmc.ims.service.common.VerifyService;
import java.lang.reflect.Type;
import java.util.Comparator;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

/**
 * VerifyService
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class VerifyServiceImpl implements VerifyService, BeanPostProcessor {

    private final Map<VerifyCodeEnum, Map<Class<?>, BeanValidator>> beanValidatorMap = new ConcurrentHashMap<>();

    @Resource
    private AlertService alertService;

    private static final String BIZ_KEY_VERIFY_FAILED_TEMPLATE = "业务唯一键: %s,\n失败信息: %s,\ntraceId: %s";
    private static final String VERIFY_FAILED_TEMPLATE = "失败信息: %s,\ntraceId: %s";

    @Override
    public <T> void verify(T obj, VerifyCodeEnum verifyCode) {
        verify(obj, verifyCode, null);
    }

    @Override
    public <T, R> void verify(T obj, VerifyCodeEnum verifyCode, Function<T, R> getObjIdentifyFunction) {
        Asserts.notNull(obj, CommonErrorCode.ILLEGAL_PARAMETER, ErrorConstants.PARAM_IS_EMPTY);
        Asserts.notNull(verifyCode, CommonErrorCode.ILLEGAL_PARAMETER, ErrorConstants.PARAM_IS_EMPTY);

        try {
            // 优先hibernate validate
            ValidationResult entity = ValidationUtils.validateEntity(obj);

            if (entity.isHasErrors()) {
                log.error("[VerifyService#verify] verify-failed!obj:{},result:{}", obj, entity);
                throw new ImsBusinessException(CommonErrorCode.ILLEGAL_PARAMETER, entity.toErrorMsgString());
            }

            // 获取自定义
            Map<Class<?>, BeanValidator> validatorMap = beanValidatorMap.get(verifyCode);

            if (MapUtils.isEmpty(validatorMap)) {
                return;
            }

            // 匹配
            // 按照优先级匹配
            validatorMap.entrySet()
                .stream()
                // 匹配
                .filter(e -> e.getKey().isAssignableFrom(obj.getClass()))
                .min(Comparator.comparing(o -> o.getValue().order()))
                .ifPresent(k -> k.getValue().valid(obj));
        } catch (ImsBusinessException ex) {
            log.error("[VerifyService#verify] verify failed! obj:{},verifyCode:{}", obj, verifyCode, ex);
            doAlertVerifyFailed(obj, getObjIdentifyFunction, verifyCode, ex);
            throw new ImsBusinessException(ex.getCodeMsg());
        } catch (Exception ex) {
            log.error("[VerifyService#verify] verify failed! obj:{},verifyCode:{}", obj, verifyCode, ex);
            throw ex;
        }
    }

    /**
     * 格式化消息
     *
     * @param obj verify obj
     * @param getObjIdentifyFunction get identify func
     * @param errorInfo error
     * @param <T> T
     * @param <R> R
     * @return msg
     */
    private <T, R> String formatAlarmMsg(T obj, Function<T, R> getObjIdentifyFunction, String errorInfo) {

        String errorMsg;
        if (Objects.isNull(getObjIdentifyFunction)) {
            errorMsg = String.format(VERIFY_FAILED_TEMPLATE, errorInfo, MDC.get("traceId"));
        } else {
            errorMsg = String
                .format(BIZ_KEY_VERIFY_FAILED_TEMPLATE, getObjIdentifyFunction.apply(obj),
                    errorInfo, MDC.get("traceId"));
        }
        return errorMsg;
    }

    /**
     * 校验失败消息预警
     *
     * @param verifyCode 本次校验码
     * @param failedEx 失败Ex
     */
    private <T, R> void doAlertVerifyFailed(T obj, Function<T, R> getObjIdentifyFunction,
        VerifyCodeEnum verifyCode, Throwable failedEx) {

        alertService
            .alertWarning(verifyCode.getCode(), formatAlarmMsg(obj, getObjIdentifyFunction, failedEx.getMessage()));
    }

    @Override
    public Object postProcessAfterInitialization(@NonNull Object bean, @NonNull String beanName) {

        if (bean instanceof BeanValidator) {

            BeanValidator validator = (BeanValidator) bean;

            VerifyCodeEnum verifyCode = validator.getVerifyCode();

            Type typeArgument = TypeUtil.getTypeArgument(validator.getClass());
            Class<?> paramClazz = TypeUtil.getClass(typeArgument);

            log.info(">>>>>>>>>>--BeanValidator#Initalize,beanName:{},code:{},param:{}", beanName,
                validator.getVerifyCode(), paramClazz.getName());

            Map<Class<?>, BeanValidator> clazz2ValidatorMap = beanValidatorMap
                .getOrDefault(verifyCode, new ConcurrentHashMap<>(5));
            beanValidatorMap.put(verifyCode, clazz2ValidatorMap);
            clazz2ValidatorMap.put(paramClazz, validator);
        }
        return bean;
    }
}
