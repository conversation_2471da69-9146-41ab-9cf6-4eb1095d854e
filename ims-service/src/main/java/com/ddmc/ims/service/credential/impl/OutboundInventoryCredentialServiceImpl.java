package com.ddmc.ims.service.credential.impl;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.InventoryCredentialConverter;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;
import com.ddmc.ims.service.credential.OutboundInventoryCredentialService;
import com.ddmc.ims.service.lot.InventoryLotInfoService;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
public class OutboundInventoryCredentialServiceImpl implements OutboundInventoryCredentialService {

    @Resource
    private CredentialWrapperManager credentialWrapperManager;
    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;
    @Resource
    private InventoryLotInfoService inventoryLotInfoService;
    @Resource
    private LocalParamConfig localParamConfig;


    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryOutbound(OutboundCredentialRequest request) {
        checkTryOutbound(request);
        checkExistLotInfo(request);
        saveOutboundCredential(request);
    }




    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryFinishOutbound(OutboundCredentialRequest request) {
        checkTryOutbound(request);
        checkExistLotInfo(request);
        saveOutboundCredential(request);

    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryCancelOutbound(OutboundCredentialRequest request) {
        checkTryOutbound(request);
        saveOutboundCredential(request);
    }

    @Override
    public void tryModifyOutboundPlanQty(OutboundCredentialRequest request) {
        throw new UnsupportedOperationException();
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryPublishOutbound(OutboundCredentialRequest request) {
        checkTryOutbound(request);
        saveOutboundCredential(request);
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryModifyExpectOutTime(OutboundCredentialRequest request) {
        checkTryOutbound(request);
        checkTryModifyExpectOutTime(request);
        saveOutboundCredential(request);
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryTransferReject(OutboundCredentialRequest request) {
        checkTryOutbound(request);
        saveOutboundCredential(request);
    }

    private void checkTryModifyExpectOutTime(OutboundCredentialRequest request) {
        if (Objects.isNull(request.getExpectOutTime())) {
            throw new ImsBusinessException(CommonErrorCode.EXCEPTION_OUT_TIME_IS_NULL);
        }
    }


    private void checkTryOutbound(OutboundCredentialRequest request) {
        CredentialHeader credentialHeader = credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId());
        if (Objects.nonNull(credentialHeader)) {
            throw new ImsBusinessException(CommonErrorCode.EXIST_CREDENTIAL);
        }
    }


    private void checkExistLotInfo(OutboundCredentialRequest request) {
        if (CollectionUtils.isNotEmpty(request.getOperateDetails()) && OrderTypeEnum
            .isTransfer(request.getOrderType())) {
            Set<String> lotIds = request.getOperateDetails().stream().map(CredentialDetailRequest::getLotId)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            //查询动作提前，没有批次信息将去查询批次服务
            inventoryLotInfoService.queryInventoryLotInfo(lotIds);
        }
    }


    private void saveOutboundCredential(OutboundCredentialRequest request) {
        CredentialHeader header = InventoryCredentialConverter
            .convertOutboundCredential(request, localParamConfig.getDefaultLocationUsageCodeMap());
        credentialWrapperManager.saveCredential(header);
    }
}
