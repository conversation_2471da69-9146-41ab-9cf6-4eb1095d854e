package com.ddmc.ims.service.credential.impl;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.InventoryCredentialConverter;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.InventoryOperateCredentialRequest;
import com.ddmc.ims.request.credential.oc.ManufactureCredentialRequest;
import com.ddmc.ims.service.credential.ManufactureInventoryCredentialService;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 制造单库存凭证处理
 */
@Slf4j
@Service
public class ManufactureInventoryCredentialServiceImpl implements ManufactureInventoryCredentialService {


    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;

    @Resource
    private CredentialWrapperManager credentialWrapperManager;

    @Resource
    private LocalParamConfig localParamConfig;

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void tryManufacture(ManufactureCredentialRequest request) {
        checkRepeat(request);
        Set<LogicInventoryLocation> logicInventoryLocations = request.getOperateDetails().stream()
            .map(CredentialDetailRequest::getFromLocation).collect(Collectors.toSet());
        checkLogicInventoryLocation(logicInventoryLocations);

        CredentialHeader header = InventoryCredentialConverter
            .convertCredential(request, localParamConfig.getDefaultLocationUsageCodeMap());
        credentialWrapperManager.saveCredential(header);
    }


    private void checkRepeat(InventoryOperateCredentialRequest request) {
        CredentialHeader credentialHeader = credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId());
        if (Objects.nonNull(credentialHeader)) {
            throw new ImsBusinessException(CommonErrorCode.EXIST_CREDENTIAL);
        }
    }

    private void checkLogicInventoryLocation(Set<LogicInventoryLocation> logicInventoryLocations) {
        //过滤空逻辑库位
        logicInventoryLocations = logicInventoryLocations.stream().filter(t -> !CommonConstants.EMPTY_LOGIC_INVENTORY_LOCATION.equals(t))
            .collect(Collectors.toSet());
        Set<Long> warehouseIds = logicInventoryLocations.stream().map(LogicInventoryLocation::getWarehouseId).collect(Collectors.toSet());
        if (warehouseIds.size() > 1) {
            throw new ImsBusinessException(CommonErrorCode.CREDENTIAL_EXIST_MULTIPLE_WAREHOUSE);
        }
    }
}
