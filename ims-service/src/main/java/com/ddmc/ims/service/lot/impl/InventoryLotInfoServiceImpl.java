package com.ddmc.ims.service.lot.impl;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.LotIdUtils;
import com.ddmc.ims.dal.conditions.WarehouseIdLotIdPair;
import com.ddmc.ims.dal.mapper.ims.InventoryLotInfoMapper;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.manager.lot.LotManager;
import com.ddmc.ims.message.lot.LotProducer;
import com.ddmc.ims.message.lot.dto.LotIdInfo;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.lot.InventoryLotInfoService;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class InventoryLotInfoServiceImpl implements InventoryLotInfoService {

    @Resource
    private InventoryLotInfoMapper inventoryLotInfoMapper;

    @Resource
    private LotManager lotManager;

    @Resource
    private InventoryLotInfoService selfProxy;

    @Resource
    private LotProducer lotProducer;

    @Resource
    private AlertService alertService;


    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void replaceInventoryLotInfoList(List<InventoryLotInfo> inventoryLotInfoList) {
        inventoryLotInfoList = inventoryLotInfoList.stream()
            .filter(i -> Objects.nonNull(i.getUnsalableDate())).collect(Collectors.toList());

        List<WarehouseIdLotIdPair> pairs = inventoryLotInfoList.stream()
            .map(i -> new WarehouseIdLotIdPair(i.getWarehouseId(), i.getLotId()))
            .collect(Collectors.toList());

        Map<WarehouseIdLotIdPair, InventoryLotInfo> oldInventoryLotInfoMap = Lists.partition(pairs, CommonConstants.BATCH_SELECT_DB_200)
            .stream().map(inventoryLotInfoMapper::batchGetByWarehouseIdAndLotId)
            .flatMap(List::stream).collect(Collectors.toMap(i -> new WarehouseIdLotIdPair(i.getWarehouseId(), i.getLotId()),
                Function.identity(), (v1, v2) -> v1));

        List<InventoryLotInfo> needInsert = Lists.newArrayListWithExpectedSize(inventoryLotInfoList.size());
        List<InventoryLotInfo> needUpdate = Lists.newArrayListWithExpectedSize(inventoryLotInfoList.size());
        inventoryLotInfoList.forEach(i -> {
            if(!oldInventoryLotInfoMap.containsKey(new WarehouseIdLotIdPair(i.getWarehouseId(), i.getLotId()))){
                needInsert.add(i);
            }else{
                InventoryLotInfo oldInventoryLotInfo = oldInventoryLotInfoMap.get(new WarehouseIdLotIdPair(i.getWarehouseId(), i.getLotId()));
                if (oldInventoryLotInfo.equals(i)) {
                    return ;
                }
                i.setId(oldInventoryLotInfo.getId());
                i.setCreateTime(oldInventoryLotInfo.getCreateTime());
                i.setUpdateTime(CurrentDateUtil.newDate());
                needUpdate.add(i);
            }
        });

        if (CollectionUtils.isNotEmpty(needInsert)) {
            inventoryLotInfoMapper.batchInsert(needInsert);
        }

        if (CollectionUtils.isNotEmpty(needUpdate)) {
            inventoryLotInfoMapper.batchUpdate(needUpdate);
        }
    }



    @Override
    public List<InventoryLotInfo> addMissInventoryLotInfo(Long warehouseId, List<String> lotIds) {
        if (CollectionUtils.isEmpty(lotIds)) {
            return Collections.emptyList();
        }
        Set<String> existsLotIds = Lists.partition(lotIds, CommonConstants.BATCH_SELECT_DB_200).stream()
            .map(lis -> inventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(warehouseId, lis))
            .filter(Objects::nonNull).flatMap(List::stream).filter(t -> Objects.nonNull(t.getUnsalableDate()))
            .map(InventoryLotInfo::getLotId).collect(Collectors.toSet());
        List<String> notExists = lotIds.stream().filter(l -> !existsLotIds.contains(l)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notExists)) {
            return Collections.emptyList();
        }

        List<InventoryLotInfo> inventoryLotInfos;
        try {
            inventoryLotInfos = lotManager.batchGet(notExists, true);
        } catch (Exception e) {
            log.warn("查询批次效期异常", e);
            //发生到mq进行重试
            LotIdInfo lotIdInfo = new LotIdInfo();
            lotIdInfo.setLotIds(lotIds);
            lotProducer.sendLotIdMessage(lotIdInfo);
            return Collections.emptyList();
        }
        selfProxy.replaceInventoryLotInfoList(inventoryLotInfos);
        return inventoryLotInfos;

    }


    @Override
    public Map<String, InventoryLotInfo> queryInventoryLotInfo(Set<String> lotIds) {
        if (CollectionUtils.isEmpty(lotIds)) {
            return Collections.emptyMap();
        }
        Map<Long, List<String>> warehouseLotMap = lotIds.stream()
            .collect(Collectors.groupingBy(LotIdUtils::getWarehouseIdFromLotId));

        List<InventoryLotInfo> result = Lists.newArrayListWithCapacity(lotIds.size());
        warehouseLotMap.forEach((warehouseId, subLotIds) -> {
            List<InventoryLotInfo> lotInfoList = inventoryLotInfoMapper
                .batchGetByWarehouseIdAndLotIds(warehouseId, subLotIds);
            result.addAll(lotInfoList);
        });
        Set<String> existLotIds = result.stream().map(InventoryLotInfo::getLotId).collect(Collectors.toSet());
        Set<String> needAddLotIds = lotIds.stream().filter(t -> !existLotIds.contains(t)).collect(Collectors.toSet());
        result.addAll(addMissInventoryLotInfo(needAddLotIds));
        return result.stream().collect(Collectors.toMap(InventoryLotInfo::getLotId, Function.identity()));
    }

    private List<InventoryLotInfo> addMissInventoryLotInfo(Set<String> lotIds) {
        if (CollectionUtils.isEmpty(lotIds)) {
            return Collections.emptyList();
        }
        Map<Long, List<String>> warehouseLotMap = lotIds.stream()
            .collect(Collectors.groupingBy(LotIdUtils::getWarehouseIdFromLotId));
        List<InventoryLotInfo> result = Lists.newArrayListWithExpectedSize(lotIds.size());
        warehouseLotMap.forEach((k, v) -> result.addAll(addMissInventoryLotInfo(k, v)));

        Set<String> resultLots = result.stream().map(InventoryLotInfo::getLotId).collect(Collectors.toSet());
        lotIds.removeAll(resultLots);
        if (CollectionUtils.isNotEmpty(lotIds)) {
            log.error("[InventoryLotInfoService] 批次缺失:{}", JsonUtil.toJson(lotIds));
            alertService.alert("批次信息缺失", "批次缺失" + JsonUtil.toJson(lotIds), AlertLevel.WARNING);
        }
        return result;
    }

}
