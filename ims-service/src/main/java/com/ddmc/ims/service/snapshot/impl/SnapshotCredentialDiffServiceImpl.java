package com.ddmc.ims.service.snapshot.impl;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dto.CommandChangeQtyDto;
import com.ddmc.ims.dto.InOutNumDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.snapshot.SnapshotCredentialDiffService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SnapshotCredentialDiffServiceImpl implements SnapshotCredentialDiffService {


    @Resource
    private SnapshotCredentialDiffMapper snapshotCredentialDiffMapper;

    @Resource
    private CredentialWrapperManager credentialWrapperManager;

    @Resource
    private CommandManager commandManager;

    @Override
    public Map<Long, CommandChangeQtyDto> getTodayFmsLessInventoryQtyMap(Long warehouseId, Date snapshotDate) {

        List<Long> headIds = snapshotCredentialDiffMapper
            .selectByWarehouseIdAndBusinessTime(warehouseId, snapshotDate);
        return getFmsDiffSkuQty(headIds);
    }


    @Override
    public Map<Long, CommandChangeQtyDto> getTodayFmsMoreInventoryQtyMap(Long warehouseId, Date snapshotDate) {
        List<Long> headIds = snapshotCredentialDiffMapper
            .selectByWarehouseIdAndEndDateTime(warehouseId, snapshotDate);
        return getFmsDiffSkuQty(headIds);
    }

    @Override
    public Map<Long, CommandChangeQtyDto> getInitFmsLessInventoryQtyMap(Long warehouseId, Date snapshotDate,Set<String> orderSourceSet) {
        List<Long> headIds = snapshotCredentialDiffMapper
            .selectByWarehouseIdAndBusinessTimeLessEndDateTime(warehouseId, snapshotDate,orderSourceSet);

        List<Long> endDateTimeIsnullHeadIds = snapshotCredentialDiffMapper
            .selectByWarehouseIdAndEndDateTimeIsNull(warehouseId, snapshotDate,orderSourceSet);
        headIds.addAll(endDateTimeIsnullHeadIds);
        return getFmsDiffSkuQty(headIds);
    }

    @Override
    public Map<Long, CommandChangeQtyDto> getInitFmsMoreInventoryQtyMap(Long warehouseId, Date snapshotDate) {
        List<Long> headIds = snapshotCredentialDiffMapper
            .selectByWarehouseIdAndEndDateTimeLessBusinessTime(warehouseId, snapshotDate);
        return getFmsDiffSkuQty(headIds);
    }


    public void buildDiffSkuQtyMap(Map<Long, CommandChangeQtyDto> diffSkuQtyMap,
        List<InOutNumDto> inOutNumDtos) {

        Map<Long, List<InOutNumDto>> commandInventoryNumMap =
            inOutNumDtos.stream()
                .collect(Collectors.groupingBy(InOutNumDto::getSkuId));
        commandInventoryNumMap.forEach((k, v) -> {
            CommandChangeQtyDto commandChangeQtyDto = diffSkuQtyMap.getOrDefault(k, new CommandChangeQtyDto());
            v.forEach(commandChangeQtyDto::addQty);
            diffSkuQtyMap.put(k, commandChangeQtyDto);
        });
    }

    /**
     * 根据凭证获取库存变动数量,按照sku汇总
     *
     * @param headIds 凭证id
     * @return 变动数量, 按sku汇总
     */
    private Map<Long, CommandChangeQtyDto> getFmsDiffSkuQty(List<Long> headIds) {
        Map<Long, CommandChangeQtyDto> diffMap = Maps.newHashMapWithExpectedSize(headIds.size());
        Lists.partition(headIds, CommonConstants.BATCH_SELECT_STORE_100)
            .forEach(t -> buildDiffSkuQtyMap(diffMap, getCommandInventoryNumListByHeadIds(t)));
        return diffMap;

    }

    /**
     * 根据凭证获取库存变动数量
     *
     * @param headIds 凭证id
     * @return 变动数量
     */
    public List<InOutNumDto> getCommandInventoryNumListByHeadIds(List<Long> headIds) {
        if (CollectionUtils.isEmpty(headIds)) {
            return Collections.emptyList();
        }

        List<CredentialHeader> credentialHeaderAndDetails = credentialWrapperManager
            .getCredentialHeaderAndDetailList(headIds);
        return commandManager
            .getFmsInOutNumDto(credentialHeaderAndDetails);
    }
}
