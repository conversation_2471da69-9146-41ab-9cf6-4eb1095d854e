package com.ddmc.ims.service.publish.impl;

import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.message.alert.WxAlertMessage;
import com.ddmc.ims.message.alert.WxAlertMsgProducer;
import com.ddmc.ims.service.publish.PublishAlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PublishAlertServiceImpl implements PublishAlertService {

    @Resource
    private WxAlertMsgProducer wxAlertMsgProducer;

    @Override
    public void publish(WxAlertMessage message) {
        log.info("PublishAlertServiceImpl.publish WxAlertMessage param={}", JsonUtil.toJson(message));
        wxAlertMsgProducer.alertMsgOutput(JsonUtil.toJson(message));
    }

}
