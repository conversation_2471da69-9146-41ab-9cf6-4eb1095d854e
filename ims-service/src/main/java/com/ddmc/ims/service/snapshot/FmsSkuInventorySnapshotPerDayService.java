package com.ddmc.ims.service.snapshot;

import com.ddmc.ims.dal.model.monitor.FmsSkuInventorySnapshotPerDay;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface FmsSkuInventorySnapshotPerDayService {

    /**
     * 处理财务口径快照数据
     *
     * @param taskId 财务快照任务id
     * @param warehouseId 仓库id
     * @param snapshotDate 快照日期
     */
    void dealEndFmsSkuInventorySnapshot(Long taskId, Long warehouseId, Date snapshotDate);

    /**
     * 更新快照状态 保存财务口径快照数据
     *
     * @param taskId 财务快照任务id
     * @param items 快照日期
     */
    void updateTaskAndBatchSaveFmsSkuInventorySnapshotPerDay(Long taskId, List<FmsSkuInventorySnapshotPerDay> items);

    /**
     * 初始化财务库存快照
     *
     * @param warehouseId 仓库id
     * @param snapshotDate 快照时间
     */
    void initEndFmsSkuInventorySnapshot(Long warehouseId, Date snapshotDate);

    /**
     * 根据仓库id和快照时间获取财务快照
     *
     * @param warehouseId 仓库id
     * @param snapshotDate 快照时间
     * @return 财务快照
     */
    List<FmsSkuInventorySnapshotPerDay> queryByWarehouseAndSnapshotDate(Long warehouseId, Date snapshotDate,Long skuId,
        Integer limit);

    Integer countByWarehouseAndSnapshotDate(Long warehouseId, Date snapshotDate);

    List<FmsSkuInventorySnapshotPerDay> getEndFmsSkuInventorySnapshot(Long warehouseId, Date snapshotDate);
}
