package com.ddmc.ims.service.credential;

import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;

/**
 * <AUTHOR>
 */
public interface InboundInventoryCredentialService {


    void tryInbound(InboundCredentialRequest request);

    void tryPublishInbound(InboundCredentialRequest request);

    void tryFinishInbound(InboundCredentialRequest request);

    void tryModifyExpectArriveTime(InboundCredentialRequest request);

    void tryCancelInbound(InboundCredentialRequest request);

    void tryCleanPlanIn(InboundCredentialRequest request);
}
