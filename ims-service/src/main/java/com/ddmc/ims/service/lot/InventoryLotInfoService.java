package com.ddmc.ims.service.lot;

import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface InventoryLotInfoService {

    /**
     * 使用入参替换存储数据
     * @param inventoryLotInfoList 库存批次信息
     */
    void replaceInventoryLotInfoList(List<InventoryLotInfo> inventoryLotInfoList);


    /**
     * 补充缺失的批次信息（带临期日期）
     * @param warehouseId 批次所在的大仓
     * @param lotIds 批次id
     * @return result
     */
    List<InventoryLotInfo> addMissInventoryLotInfo(Long warehouseId, List<String> lotIds);

    /**
     * 查询批次信息
     *
     * @param lotIds lotIds
     * @return result
     */
    Map<String, InventoryLotInfo> queryInventoryLotInfo(Set<String> lotIds);
}
