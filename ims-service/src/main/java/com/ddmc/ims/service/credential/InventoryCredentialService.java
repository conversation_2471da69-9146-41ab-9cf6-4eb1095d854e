package com.ddmc.ims.service.credential;

import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.dal.model.ims.CredentialHeader;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InventoryCredentialService {

    /**
     * 单据中心凭证确认,同步库存
     *
     * @param idempotentId 单据中心幂等id
     */
    void confirm(String idempotentId);

    /**
     * 直接处理凭证
     * @param credentialHeaderAndDetail 凭证头和详情
     */
    void directHandleCredential(CredentialHeader credentialHeaderAndDetail);

    /**
     * 单据中心凭证取消,业务库存取消
     *
     * @param idempotentId 单据中心幂等id
     */
    void cancel(String idempotentId);


    List<? extends SkuInventoryCommand> getSkuInventoryCommand(CredentialHeader credentialHeaderAndDetail);

    void addLock(CredentialHeader credentialHeaderAndDetail);
}
