package com.ddmc.ims.service.common.impl;

import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.SpringProfileConfig;
import com.ddmc.ims.message.alert.AlertNameConstant;
import com.ddmc.ims.message.alert.WxAlertMessage;
import com.ddmc.ims.rpc.alert.AlertClient;
import com.ddmc.ims.rpc.alert.AlertEnvironment;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.rpc.alert.AlertMessage;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.ddmc.ims.service.publish.PublishAlertService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

@Slf4j
@Service
public class AlertServiceImpl implements AlertService {

    private static final List<String> ALERT_ENVIRONMENTAL = new ArrayList<>();

    static {
        ALERT_ENVIRONMENTAL.add("PE");
        ALERT_ENVIRONMENTAL.add("TE");
        ALERT_ENVIRONMENTAL.add("RE");
        ALERT_ENVIRONMENTAL.add("ME");
        ALERT_ENVIRONMENTAL.add("XE");
        ALERT_ENVIRONMENTAL.add("GRAY");
        ALERT_ENVIRONMENTAL.add("SHT4PE");
        ALERT_ENVIRONMENTAL.add("SHT5PE");
        ALERT_ENVIRONMENTAL.add("SHHW1PE");
        ALERT_ENVIRONMENTAL.add("EAST1GRAY");
        ALERT_ENVIRONMENTAL.add("EAST1PE");
    }

    @Value("${spring.profiles.active:dev}")
    private String profile;

    @Value("${spring.application.name}")
    private String appName;

    @Value("${wechat.notifyKey.common}")
    private String wechatNotifyCommonKey;

    @Resource
    private SpringProfileConfig profileConfig;

    private AlertEnvironment environment;

    @Resource
    private AlertClient alertClient;

    @Resource(name = "AlertExecutor")
    private ExecutorService executorService;

    @Resource
    private PublishAlertService publishAlertService;

    /**
     * 告警通知切换至common服务 true为切换 false为原AlertClient接口方式
     */
    @Value("${switch.alertToCommonService:false}")
    private boolean switchAlertToCommonService;

    public boolean getBaseSwitchAlertToCommonService() {
        return localParamService
            .getBooleanValue(LocalParamsConstants.BASE_SWITCH_ALERT_TO_COMMON_SERVICE, switchAlertToCommonService);
    }



    @Resource
    private LocalParamService localParamService;

    private String getWechatNotifyCommonKey() {
        return localParamService.getStringValue(LocalParamsConstants.WECHAT_NOTIFY_KEY_COMMON, wechatNotifyCommonKey);
    }

    @PostConstruct
    public void init() {
        environment = AlertEnvironment.DEV;
        if (profile == null) {
            return;
        }
        profile = profile.toUpperCase();
        for (AlertEnvironment env : AlertEnvironment.values()) {
            if (env.name().equals(profile)) {
                environment = env;
                break;
            }
        }
    }

    @Override
    public void alert(String title, String message, AlertLevel level) {
        if (getBaseSwitchAlertToCommonService()) {
            // 如果为 CRITICAL 级别告警，则触发电话告警
            String alertName = AlertNameConstant.DEFAULT_BASE_ALERT_NAME;
            if (AlertLevel.CRITICAL.equals(level)) {
                alertName = AlertNameConstant.BASE_PHONE_ALERT_NAME;
            }
            WxAlertMessage wxAlertMessage = createWxAlertMessage(alertName, title, message, level);
            try {
                publishAlertService.publish(wxAlertMessage);
            }catch (Exception e) {
                log.error("AlertServiceImpl.alert 下发告警异常，自动降级至alertClient告警 errorMsg:{} wxAlertMessage:{}",
                    JsonUtil.toJson(e), JsonUtil.toJson(wxAlertMessage));
            }
        }else {
            alert(title, message, level, getWechatNotifyCommonKey());
        }
    }

    @Override
    public void alertSpecifyModule(String title, String message, AlertLevel level, String alertParam) {
        // 如果没有设置指定业务模块告警类型则默认使用通用告警配置
        String alertName = StringUtils.isNotBlank(alertParam) ? alertParam : AlertNameConstant.DEFAULT_BASE_ALERT_NAME;
        WxAlertMessage wxAlertMessage = createWxAlertMessage(alertName, title, message, level);
        try {
            publishAlertService.publish(wxAlertMessage);
        }catch (Exception e) {
            log.error("AlertServiceImpl.alertSpecifyModule 下发告警异常，自动降级至alertClient告警 errorMsg:{} wxAlertMessage:{}",
                JsonUtil.toJson(e), JsonUtil.toJson(wxAlertMessage));
        }
    }

    @Override
    public void alertWarning(String title, String message) {
        alert(title, message, AlertLevel.WARNING);
    }

    @Override
    public void alertInfo(String title, String message) {
        alert(title, message, AlertLevel.OK);
    }

    @Override
    public void alertInfo(String title, String message, String notifyKey) {
        alert(title, message, AlertLevel.OK, notifyKey);
    }

    @Override
    public void alert(String title, String message, AlertLevel level, String notifyKey) {
        AlertMessage msg = new AlertMessage();
        msg.setEnvironment(environment);
        msg.setService(appName);
        msg.setId(title);
        msg.setLevel(level);
        msg.setMessage(message);

        if (!ALERT_ENVIRONMENTAL.contains(profileConfig.getProfile().toUpperCase())) {
            log.warn("[alertService] {}", msg);
            return;
        }

        Map<String, Object> jo = new HashMap<>();
        jo.put("msgtype", "text");

        Map<String, Object> textJo = new HashMap<>();
        textJo.put("content", msg.toString());
        jo.put("text", textJo);

        executorService.submit(() -> alertClient.alert(notifyKey, jo));
    }

    @Override
    public void alertWarning(String title, String message, String notifyKey) {
        alert(title, message, AlertLevel.WARNING, notifyKey);
    }

    private WxAlertMessage createWxAlertMessage(String alertName, String title, String message, AlertLevel level) {
        WxAlertMessage wxAlertMessage = new WxAlertMessage();
        wxAlertMessage.setAlertName(alertName);
        wxAlertMessage.setTitle(title);
        wxAlertMessage.setMessage(message);
        wxAlertMessage.setLevel(level.name());
        return wxAlertMessage;
    }

}
