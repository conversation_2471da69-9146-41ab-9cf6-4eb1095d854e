package com.ddmc.ims.service.common.impl;


import com.ddmc.core.view.CodeMsg;
import com.ddmc.greenhouse.carbon.admin.third.api.client.response.category.ManageCategoryTreeNodeResponse;
import com.ddmc.greenhouse.carbon.admin.third.api.client.service.category.IManageCategoryThirdClient;
import com.ddmc.greenhouse.stone.base.client.response.ResponseDto;
import com.ddmc.ims.common.bo.ManageCategory.ProductAdmPurchaseCategory;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.converter.PurchaseCategoryConverter;
import com.ddmc.ims.service.common.ManageCategoryService;
import com.ddmc.utils.BusinessException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 采购分类
 */
@Slf4j
@Service
public class ManageCategoryServiceImpl implements ManageCategoryService {

    /**
     * 新采购分类接口，成功code
     */
    public static final int RPC_PURCHASE_SUCCESS_CODE = 200;
    /**
     * 采购分类 缓存KEY
     */
    private static final String CACHE_KEY_PURCHASE_CATEGORY = "PURCHASE_CATEGORY_LEAVES";
    @Resource
    private IManageCategoryThirdClient iManageCategoryThirdClient;

    /**
     * 获取所有采购类目列表
     *
     * @return 采购分类
     */
    @Override
    public List<ProductAdmPurchaseCategory> getMcPurchaseCategoryList() {
        try {
            return CacheConfig.CATEGORY_ALL
                .get(CACHE_KEY_PURCHASE_CATEGORY, () -> {
                    ResponseDto<List<ManageCategoryTreeNodeResponse>> treeResponse = iManageCategoryThirdClient
                        .tree();
                    if (treeResponse.getCode() != RPC_PURCHASE_SUCCESS_CODE) {
                        throw new BusinessException(
                            new CodeMsg("-1", "[carbon-admin-third-api-service.tree]" + treeResponse.getMessage()));
                    }
                    return PurchaseCategoryConverter.convertToPurchaseCategory(treeResponse.getData());
                });
        } catch (ExecutionException e) {
            throw new BusinessException(new CodeMsg("-1", "获取采购分类缓存异常[" + e.getMessage() + "]"));
        }
    }

    /**
     * 查询所有采购分类信息
     *
     * @return 采购分类
     */
    private List<ProductAdmPurchaseCategory> getAllCategoryInfo(List<ProductAdmPurchaseCategory> result,
        List<ProductAdmPurchaseCategory> categories) {
        if (CollectionUtils.isEmpty(categories) || Objects.isNull(result)) {
            return Collections.emptyList();
        }
        for (ProductAdmPurchaseCategory category : categories) {
            result.add(category);
            List<ProductAdmPurchaseCategory> children = category.getChildren();
            this.getAllCategoryInfo(result, children);
        }
        return result;
    }



    /**
     * 获取所有采购分类名称信息
     *
     * @return <采购分类ID,采购分类信息>
     */
    @Override
    public Map<Long, ProductAdmPurchaseCategory> getAllCategoryInfoMap() {
        List<ProductAdmPurchaseCategory> allCategoryInfo = this
            .getAllCategoryInfo(new ArrayList<>(), getMcPurchaseCategoryList());
        return allCategoryInfo.stream().collect(Collectors.toMap(ProductAdmPurchaseCategory::getId,
            item -> item, (n1, n2) -> n1));
    }


}
