package com.ddmc.ims.service.snapshot.impl;

import com.ddmc.fms.inv.enums.DsStatus;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.SystemTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.event.producer.FmsCompareProducer;
import com.ddmc.ims.service.snapshot.FmsSnapshotNotifyService;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FmsSnapshotNotifyServiceImpl implements FmsSnapshotNotifyService {

    @Resource
    private FmsSnapshotNotifyMapper snapshotNotifyMapper;
    @Resource
    private FmsCompareProducer fmsCompareProducer;

    @Override
    @Transactional(value = CommonConstants.IMS_MONITOR_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void notifyFmsCheck(Date dayEndDate) {
        log.info("通知财务对账 {}", ThreadLocalDateUtils.formatYmd(dayEndDate));
        int count = snapshotNotifyMapper.updateStatusByDayEndDate(dayEndDate, DsStatus.RECON_READY.getValue());
        FmsSnapshotNotify fmsSnapshotNotify = snapshotNotifyMapper.selectByDayEndDate(dayEndDate);
        if (count > 0 && SystemTypeEnum.OCS.getDesc().equals(fmsSnapshotNotify.getSource())) {
            fmsCompareProducer.sendFmsCompareEvent(dayEndDate);
        }

    }
}
