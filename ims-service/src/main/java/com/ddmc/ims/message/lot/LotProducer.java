package com.ddmc.ims.message.lot;

import com.ddmc.ims.message.lot.dto.LotIdInfo;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@EnableBinding({LotMsgProcessor.class})
public class LotProducer {

    @Resource
    private LotMsgProcessor lotMsgProcessor;

    public boolean sendLotIdMessage(LotIdInfo message) {
        return lotMsgProcessor.imsLotInfoOutput()
            .send(MessageBuilder.withPayload(message).build());
    }
}
