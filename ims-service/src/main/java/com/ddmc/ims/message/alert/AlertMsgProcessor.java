package com.ddmc.ims.message.alert;

import com.ddmc.ims.common.constant.MessageConstant;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

/**
 * <AUTHOR>
 */
public interface AlertMsgProcessor {

    /**
     * 发送告警消息
     *
     * @return MessageChannel
     */
    @Output(MessageConstant.SCM_ALERT_MSG_OUTPUT)
    MessageChannel scmAlertMsgOutput();
}
