package com.ddmc.ims.message.inventory;

import com.ddmc.ims.common.constant.MessageConstant;
import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

public interface AsyncConfirmMsgProcessor {
    /**
     * 发送异步确认消息
     */
    @Output(MessageConstant.IMS_CONFIRM_OUTPUT)
    MessageChannel imsConfirmOutput();

    /**
     * 消费异步确认消息
     */
    @Input(MessageConstant.IMS_CONFIRM_INPUT)
    MessageChannel imsConfirmInput();
}
