package com.ddmc.ims.message.inventory;

import com.ddmc.ims.common.constant.MessageConstant;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.message.inventory.dto.AsyncConfirmMsg;
import com.ddmc.ims.service.credential.InventoryCredentialService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.context.annotation.Description;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 仓库消息
 */
@Slf4j
@EnableBinding({AsyncConfirmMsgProcessor.class})
@RequiredArgsConstructor
public class AsyncConfirmReceiver {

    @Resource
    private InventoryCredentialService inventoryCredentialService;

    @Resource
    private CredentialWrapperManager credentialWrapperManager;

    /**
     * 消费异步确认消息
     */
    @StreamListener(MessageConstant.IMS_CONFIRM_INPUT)
    @Description("消费异步确认消息")
    public void receiveAsyncConfirm(@Valid AsyncConfirmMsg msg) {
        log.warn("receiveAsyncConfirm msg -> {}", JsonUtil.toJson(msg));
        CredentialHeader credentialHeaderAndDetail = credentialWrapperManager
            .getCredentialHeaderAndDetail(msg.getIdempotentId());
        inventoryCredentialService.directHandleCredential(credentialHeaderAndDetail);
    }
}
