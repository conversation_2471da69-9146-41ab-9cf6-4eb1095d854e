package com.ddmc.ims.message.inventory;

import com.ddmc.ims.message.inventory.dto.AsyncConfirmMsg;
import com.ddmc.multi.cloud.mq.MsgShardIdSetter;
import com.ddmc.multi.cloud.mq.MsgShardType;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@EnableBinding({AsyncConfirmMsgProcessor.class})
public class AsyncConfirmMsgProducer {

    @Resource
    private AsyncConfirmMsgProcessor asyncConfirmMsgProcessor;

    @MsgShardIdSetter(shardType = MsgShardType.SINGLE_SHARD, argIndex = 0, argProperty = "warehouseId")
    public boolean sendAsyncConfirm(AsyncConfirmMsg msg) {
        return asyncConfirmMsgProcessor.imsConfirmOutput()
            .send(MessageBuilder.withPayload(msg).build());
    }
}
