package com.ddmc.ims.message.alert;

import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ratelimit.MqGroupConstants;
import com.ddmc.ratelimit.ScmRateLimit;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.support.MessageBuilder;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableBinding(AlertMsgProcessor.class)
public class WxAlertMsgProducer {

    @Resource
    private AlertMsgProcessor alertMsgProcessor;

    /**
     * mq发送超时时间，防止一直阻塞
     */
    @Value("${rabbitmq.send-timeout:10000}")
    private Long rabbitmqSendTimeout;

    @Resource
    private LocalParamService localParamService;

    /**
     * 消息告警下发消息(common接) false为关闭 true为开启
     */
    @Value("${switch.scmAlertMsgSendMessage:true}")
    private boolean switchScmAlertMsgSendMessage;

    public boolean getBaseSwitchScmAlertMsgSendMessage() {
        return localParamService
            .getBooleanValue(LocalParamsConstants.BASE_SWITCH_SCM_ALERT_MSG_SEND_MESSAGE, switchScmAlertMsgSendMessage);
    }

    /**
     * 消息告警
     *
     * @param msg 消息内容
     * @return 结果
     */
    @ScmRateLimit(rateLimiterName = "scmAlertMsg", rateLimiterGroupName = MqGroupConstants.PMS_ROCKET_MQ, requestPerSecond = 100)
    public boolean alertMsgOutput(String msg) {
        if (!getBaseSwitchScmAlertMsgSendMessage()) {
            return false;
        }
        log.info("scmAlertMsgOutput send mq param={}", msg);
        boolean result = alertMsgProcessor.scmAlertMsgOutput()
            .send(MessageBuilder.withPayload(msg).build(), rabbitmqSendTimeout);
        log.info("scmAlertMsgOutput send mq result={}", result);
        return result;
    }

}
