package com.ddmc.ims.event.handler;


import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.event.entity.inventory.TransferInStoreAllocChangeEvent;
import com.google.common.eventbus.Subscribe;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TransferInStoreAllocChangeHandler {

    @Resource
    private DbEventStorage dbEventStorage;


    @Subscribe
    public void syncTransferInStoreAllocChangeEvent(TransferInStoreAllocChangeEvent event) {
        log.info("TransferInStoreAllocChangeEvent start, event is:{}", JsonUtils.toJson(event));
        dbEventStorage.completeAndAsyncTriggerSubEvent(event);
    }


}
