package com.ddmc.ims.event.producer;

import com.ddmc.ims.common.enums.common.EventExecuteServiceNameEnum;
import com.ddmc.ims.common.enums.common.EventKeyTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.event.entity.inventory.SnapshotCredentialDiffEvent;
import java.util.Collections;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SnapshotCredentialDiffProducer {

    @Resource
    private DbEventStorage dbEventStorage;

    public void sendSnapshotCredentialDiffEvent(CredentialHeader header) {


        if (OrderOperateTypeEnum.IN_STOCK.getCode().equals(header.getOrderOperateType())
            || OrderTypeEnum.UN_MANUFACTURE_MATERIAL_INBOUND.getCode().equals(header.getOrderType())
            || OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode().equals(header.getOrderOperateType())
            || OrderTypeEnum.REVERSE_MANUFACTURE.getCode().equals(header.getOrderType())) {
            SnapshotCredentialDiffEvent event = new SnapshotCredentialDiffEvent();
            event.setCredentialId(header.getId());
            event.setEventExecuteServiceName(EventExecuteServiceNameEnum.SNAPSHOT_INVENTORY_DIFF);
            event.setKeyType(EventKeyTypeEnum.SNAPSHOT_INVENTORY_DIFF.getCode());
            event.setKeyId(header.getId().toString());
            // 保存事件
            dbEventStorage.saveAndAsyncTriggerParentEvent(Collections.singletonList(event));
        }

    }


}
