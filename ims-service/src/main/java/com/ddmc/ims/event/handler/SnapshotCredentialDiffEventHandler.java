package com.ddmc.ims.event.handler;

import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.event.entity.inventory.SnapshotCredentialDiffEvent;
import com.ddmc.ims.manager.inventory.SnapshotCredentialDiffManager;
import com.google.common.eventbus.Subscribe;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SnapshotCredentialDiffEventHandler {

    @Resource
    private DbEventStorage dbEventStorage;

    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;
    @Resource
    private SnapshotCredentialDiffManager snapshotCredentialDiffManager;



    @Subscribe
    public void syncSnapshotInventoryDiffEvent(SnapshotCredentialDiffEvent event) {
        log.info("SnapshotInventoryDiffEvent start, event is:{}", JsonUtils.toJson(event));
        CredentialHeader credentialHeader = credentialHeaderMapper.selectById(event.getCredentialId());
        if (Objects.isNull(credentialHeader)) {
            throw new ImsBusinessException("凭证信息为空");
        }
        snapshotCredentialDiffManager.doSnapshotInventoryDiff(credentialHeader);
        dbEventStorage.completeAndAsyncTriggerSubEvent(event);
    }


}
