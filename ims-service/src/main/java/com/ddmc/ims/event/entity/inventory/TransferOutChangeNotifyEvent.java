package com.ddmc.ims.event.entity.inventory;

import com.ddmc.ims.dal.model.ims.EventId;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@Accessors(chain = true)
public class TransferOutChangeNotifyEvent extends EventId {

    /**
     * 凭证id
     */
    private Long credentialId;

    /**
     * 单号
     */
    private String orderNo;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 凭证明细中的skuId
     */
    private List<Long> skuIds;

    /**
     * 单据类型
     */
    private String orderType;

    /**
     * 单据操作类型
     */
    private Integer orderOperateType;
}
