package com.ddmc.ims.event.handler;

import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.event.entity.inventory.SendAsyncConfirmEvent;
import com.ddmc.ims.message.inventory.AsyncConfirmMsgProducer;
import com.ddmc.ims.message.inventory.dto.AsyncConfirmMsg;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class AsyncConfirmEventHandler {

    @Resource
    private DbEventStorage dbEventStorage;
    @Resource
    private AsyncConfirmMsgProducer asyncConfirmMsgProducer;


    @Subscribe
    public void handleSendAsyncConfirmEvent(SendAsyncConfirmEvent event) {
        log.info("handleSendAsyncConfirmEvent start, event is:{}", JsonUtils.toJson(event));
        AsyncConfirmMsg msg = new AsyncConfirmMsg();
        msg.setIdempotentId(event.getIdempotentId());
        msg.setWarehouseId(event.getWarehouseId());
        boolean success = asyncConfirmMsgProducer.sendAsyncConfirm(msg);
        if (success) {
            dbEventStorage.completeAndAsyncTriggerSubEvent(event);
        } else {
            throw new ImsBusinessException("handleSendAsyncConfirmEvent error");
        }
    }

}
