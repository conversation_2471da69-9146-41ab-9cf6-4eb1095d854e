package com.ddmc.ims.event.producer;

import com.ddmc.ims.common.enums.common.EventKeyTypeEnum;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.event.entity.inventory.SendAsyncConfirmEvent;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;

@Component
@Slf4j
public class AsyncConfirmEventProducer {

    @Resource
    private DbEventStorage dbEventStorage;

    @Resource
    private LocalParamService localParamService;

    public void postSendAsyncConfirmEvent(String idempotentId, Long warehouseId) {
        SendAsyncConfirmEvent event = new SendAsyncConfirmEvent();
        event.setKeyType(EventKeyTypeEnum.SEND_ASYNC_CONFIRM.getCode());
        event.setKeyId(idempotentId);
        event.setIdempotentId(idempotentId);
        event.setWarehouseId(warehouseId);
        // 保存事件
        dbEventStorage.saveAndAsyncTriggerParentEvent(Collections.singletonList(event));
    }
}
