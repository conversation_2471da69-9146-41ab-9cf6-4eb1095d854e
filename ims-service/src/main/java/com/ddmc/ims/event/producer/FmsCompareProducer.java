package com.ddmc.ims.event.producer;

import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.enums.common.EventKeyTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.event.entity.inventory.FmsCompareEvent;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import java.util.Collections;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FmsCompareProducer {

    @Resource
    private DbEventStorage dbEventStorage;

    @Resource
    private LocalParamService localParamService;

    public boolean isNotifyFms(){
        return localParamService.getBooleanValue(LocalParamsConstants.SNAPSHOT_NOTIFY_FMS,true);
    }

    public void sendFmsCompareEvent(Date date) {
        if(!isNotifyFms()){
            log.info("不通知财务");
            return;
        }

        FmsCompareEvent event = new FmsCompareEvent();
        event.setDayEndTime(date);
        event.setKeyType(EventKeyTypeEnum.FMS_COMPARE_NOTIFY.getCode());
        event.setKeyId(ThreadLocalDateUtils.formatYmd(date));
        // 保存事件
        dbEventStorage.saveAndAsyncTriggerParentEvent(Collections.singletonList(event));


    }


}
