package com.ddmc.ims.event.handler;

import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.event.entity.inventory.FmsCompareEvent;
import com.ddmc.ims.manager.FmsManager;
import com.google.common.eventbus.Subscribe;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FmsCompareEventHandler {

    @Resource
    private DbEventStorage dbEventStorage;
    @Resource
    private FmsManager fmsManager;





    @Subscribe
    public void fmsCompareEvent(FmsCompareEvent event) {
        log.info("fmsCompareEvent start, event is:{}", JsonUtils.toJson(event));
        fmsManager.dailySettlement(ThreadLocalDateUtils.formatYmd(event.getDayEndTime()));
        dbEventStorage.completeAndAsyncTriggerSubEvent(event);
    }


}
