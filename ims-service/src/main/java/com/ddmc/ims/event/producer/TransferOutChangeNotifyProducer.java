package com.ddmc.ims.event.producer;

import com.ddmc.ims.common.enums.common.EventExecuteServiceNameEnum;
import com.ddmc.ims.common.enums.common.EventKeyTypeEnum;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.event.entity.inventory.TransferOutChangeNotifyEvent;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TransferOutChangeNotifyProducer {

    @Resource
    private DbEventStorage dbEventStorage;

    public void postTransferOutChangeNotifyEvent(CredentialHeader header, List<CredentialDetail> details) {
        TransferOutChangeNotifyEvent event = new TransferOutChangeNotifyEvent();
        event.setCredentialId(header.getId());
        event.setKeyType(EventKeyTypeEnum.TRANSFER_OUT_CHANGE_NOTIFY.getCode());
        event.setKeyId(String.valueOf(header.getId()));
        event.setEventExecuteServiceName(EventExecuteServiceNameEnum.TRANSFER_OUT_CHANGE_NOTIFY);
        event.setOrderNo(header.getOrderNo());
        event.setOrderSource(header.getOrderSource());
        event.setOrderType(header.getOrderType());
        event.setOrderOperateType(header.getOrderOperateType());
        List<Long> skuIds = Optional.ofNullable(details).orElse(Collections.emptyList())
            .stream().map(CredentialDetail::getSkuId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        event.setSkuIds(skuIds);
        dbEventStorage.saveAndAsyncTriggerParentEvent(Lists.newArrayList(event));
    }
}
