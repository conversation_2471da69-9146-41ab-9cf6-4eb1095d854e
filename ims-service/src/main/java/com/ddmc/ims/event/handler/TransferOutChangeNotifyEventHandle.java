package com.ddmc.ims.event.handler;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.TransferOrderNoAndSource;
import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.enums.ims.DeliveryModeEnum;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.Warehouse;
import com.ddmc.ims.event.entity.inventory.TransferOutChangeNotifyEvent;
import com.ddmc.ims.manager.imsAdmin.BookingInventoryManager;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.google.common.eventbus.Subscribe;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TransferOutChangeNotifyEventHandle {

    @Resource
    private DbEventStorage dbEventStorage;

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private BookingInventoryManager bookingInventoryManager;

    @Resource
    private LocalParamService localParamService;

    @Resource
    private WarehouseService warehouseService;

    @Subscribe
    public void syncTransferOutChangeNotifyEvent(TransferOutChangeNotifyEvent event) {
        log.info("TransferOutChangeNotifyEvent start, event is:{}", JsonUtils.toJson(event));
        //查询usageDetail 发布do、取消、关闭、出库、出库完成、收工
        doNotifyTransferOutEvent(event);

        dbEventStorage.completeAndAsyncTriggerSubEvent(event);
    }

    private void doNotifyTransferOutEvent(TransferOutChangeNotifyEvent event) {
        var transferOrderNoAndSource = new TransferOrderNoAndSource(event.getOrderSource(), event.getOrderNo());
        TransferIntransitInventory one;
        List<Long> skuIds = event.getSkuIds();
        if (CollectionUtils.isEmpty(skuIds)) {
            List<TransferIntransitInventory> transferIntransitInventories = transferIntransitInventoryMapper
                .selectTransferIntransitInventory(transferOrderNoAndSource);
            if (CollectionUtils.isEmpty(transferIntransitInventories)) {
                log.warn("[doNotifyTransferOutEvent] 凭证:{}找不到相关的品，不处理", event.getCredentialId());
                return ;
            }
            skuIds = transferIntransitInventories.stream()
                .map(TransferIntransitInventory::getSkuId).distinct().collect(Collectors.toList());
            one = transferIntransitInventories.get(0);
        } else {
            one = transferIntransitInventoryMapper
                .selectOneTransferIntransitInventory(transferOrderNoAndSource);
        }
        if (Objects.isNull(one)) {
            log.warn("[doNotifyTransferOutEvent] 凭证:{}调拨在途明细为空，不处理", event.getCredentialId());
            return ;
        }
        if (!DeliveryModeEnum.ONE_DELIVERY.getCode().equals(one.getDeliveryMode())) {
            log.info("[doNotifyTransferOutEvent] 凭证:{}不是一配，不处理", event.getCredentialId());
            return;
        }
        Warehouse warehouse = warehouseService.getWarehouse(one.getToWarehouseId());
        if (Objects.nonNull(warehouse) && !warehouse.isSaleWarehouse()) {
            log.info("[doNotifyTransferOutEvent] 凭证:{}入库仓不是前置仓，不处理", event.getCredentialId());
            return;
        }
        LogicInventoryLocation fromLocation = new LogicInventoryLocation(one.getFromWarehouseId(),
            one.getFromCargoOwnerId(), one.getFromLogicInventoryLocationCode());

        LogicInventoryLocation toLocation = new LogicInventoryLocation(one.getToWarehouseId(),
            one.getToCargoOwnerId(), one.getToLogicInventoryLocationCode());
        bookingInventoryManager.notifyTransferOutEvent(fromLocation, toLocation, one.getExpectOutTime(), one.getDeliveryMode(),
            skuIds, getBookingChangeTriggerType(event.getOrderType(), event.getOrderOperateType()));
    }

    private String getBookingChangeTriggerType(String orderType, Integer orderOperateType) {
        var pair = ImmutablePair.of(orderType, orderOperateType);
        String bookingChangeTriggerType = CacheConfig.BOOKING_CHANGE_TRIGGER_TYPE.getIfPresent(pair);
        if (Objects.nonNull(bookingChangeTriggerType)) {
            return bookingChangeTriggerType;
        }
        refreshBookingChangeTriggerTypeCache();
        return CacheConfig.BOOKING_CHANGE_TRIGGER_TYPE.getIfPresent(pair);
    }


    private void refreshBookingChangeTriggerTypeCache() {
        String configStr = localParamService.getStringValue(LocalParamsConstants.BOOKING_CHANGE_TRIGGER_TYPE, "[]");
        List<BookingChangeTriggerTypeConfig> bookingChangeTriggerTypeConfigs = JsonUtils.parseList(configStr,
            BookingChangeTriggerTypeConfig.class);
        bookingChangeTriggerTypeConfigs.forEach(b ->
            CacheConfig.BOOKING_CHANGE_TRIGGER_TYPE.put(ImmutablePair.of(b.getOrderType(), b.getOrderOperateType()), b.getBookingChangeTriggerType()));
    }

    @Data
    public static class BookingChangeTriggerTypeConfig{
        private String orderType;

        private Integer orderOperateType;

        private String bookingChangeTriggerType;
    }
}
