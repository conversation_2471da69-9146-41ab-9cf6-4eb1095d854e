package com.ddmc.ims.event.entity.inventory;

import com.ddmc.ims.dal.model.ims.EventId;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class TransferInStoreAllocChangeEvent extends EventId {

    /**
     * 履约日期
     */
    private Date deliverDate;

    /**
     * 转锁明细
     */
    private List<TransferInStoreAllocChangeEventDetail> details;

    @Data
    public static class TransferInStoreAllocChangeEventDetail implements Serializable  {

        /**
         * 逻辑库位编码
         */
        private String logicInventoryLocationCode;

        /**
         * 仓库id
         */
        private Long warehouseId;

        /**
         * 货主id
         */
        private Long cargoOwnerId;

        /**
         * 货品id
         */
        private Long skuId;
    }



}
