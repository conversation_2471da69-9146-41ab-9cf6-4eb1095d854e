package com.ddmc.ims.bo.credential;

import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 凭证用途详情信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UsageDetailBo {

    /**
     * 凭证头id
     */
    private Long credentialHeaderId;

    /**
     * 凭证明细id
     */
    private Long credentialDetailId;

    /**
     * 来源逻辑库位编码
     */
    private String fromLogicLocationCode;

    /**
     * 来源货主id
     */
    private Long fromCargoOwnerId;

    /**
     * 来源仓库id
     */
    private Long fromWarehouseId;

    /**
     * 目标逻辑库位编码
     */
    private String toLogicLocationCode;

    /**
     * 目标货主id
     */
    private Long toCargoOwnerId;

    /**
     * 目标仓库id
     */
    private Long toWarehouseId;

    /**
     * 货品id
     */
    private Long skuId;
    /**
     * 批次id
     */
    private String lotId;

    /**
     * 数量，保留3位小数
     */
    private BigDecimal qty;

    /**
     * 库存需求日期时间
     */
    private Date demand;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 库存状态:0-未知，1-可用，2-不可用
     */
    private InventoryStatusEnum inventoryStatus;

    /**
     * 入库库存状态:0-未知，1-可用，2-不可用
     */
    private InventoryStatusEnum toInventoryStatus;

    /**
     * 库存用途
     */
    private String usageCode;

    /**
     * 单据标记：预售-presale,非预售-none
     */
    private String orderTag;


    /**
     * 目标库存用途
     */
    private String toUsageCode;


    /**
     * 命令类型
     */
    private String commandType;

    /**
     * 是否今日上架
     */
    private Boolean todaySale;

    /**
     * 修改需求时间
     */
    private Date toDemand;
}
