package com.ddmc.ims.bo.inventory;

import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import lombok.Builder;
import lombok.Getter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Builder
@Getter
public class InventoryChangeItem{

    /**
     * 库存操作类型，库存增加、库存减少
     */
    @NotNull
    private final InventoryOperateTypeEnum inventoryOperateTypeEnum;

    /**
     * 库存作业类型，可作业、不可作业
     */
    @NotNull
    private final InventoryWorkTypeEnum inventoryWorkTypeEnum;

    /**
     * 货品id
     */
    @NotNull
    private final Long skuId;

    /**
     * 批次id
     */
    private final String lotId;

    /**
     * 用途
     */
    private final String usage;

    /**
     * 变更数量
     */
    @NotNull
    @Min(0)
    private final BigDecimal qty;
}
