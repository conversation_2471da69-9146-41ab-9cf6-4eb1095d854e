package com.ddmc.ims.bo.inventory;

import java.util.Objects;
import lombok.Getter;

@Getter
public class UsageCodeSkuId {

    private final String usageCode;

    private final Long skuId;

    public UsageCodeSkuId(String usageCode, Long skuId) {
        this.usageCode = usageCode;
        this.skuId = skuId;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof UsageCodeSkuId)) {
            return false;
        }
        UsageCodeSkuId that = (UsageCodeSkuId) o;
        return Objects.equals(usageCode, that.usageCode) && Objects.equals(skuId, that.skuId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(usageCode, skuId);
    }


}
