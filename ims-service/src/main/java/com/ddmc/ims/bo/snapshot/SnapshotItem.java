package com.ddmc.ims.bo.snapshot;

import java.util.Objects;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class SnapshotItem {

    /**
     * 货品id
     */
    private final Long skuId;

    /**
     * 批次id
     */
    private final String lotId;

    /**
     * 仓库id
     */
    private final Long warehouseId;

    /**
     * 逻辑库位编码
     */
    private final String logicLocationCode;

    /**
     * 货主id
     */
    private final Long cargoOwnerId;

    public SnapshotItem(Long skuId, String lotId, Long warehouseId, String logicLocationCode, Long cargoOwnerId) {
        this.skuId = skuId;
        this.lotId = lotId;
        this.warehouseId = warehouseId;
        this.logicLocationCode = logicLocationCode;
        this.cargoOwnerId = cargoOwnerId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SnapshotItem)) {
            return false;
        }
        SnapshotItem that = (SnapshotItem) o;
        return Objects.equals(skuId, that.skuId) && Objects.equals(lotId, that.lotId)
            && Objects.equals(warehouseId, that.warehouseId) && Objects
            .equals(logicLocationCode, that.logicLocationCode) && Objects.equals(cargoOwnerId, that.cargoOwnerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuId, lotId, warehouseId, logicLocationCode, cargoOwnerId);
    }
}
