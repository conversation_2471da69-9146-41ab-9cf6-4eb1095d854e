package com.ddmc.ims.bo.command;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@SuperBuilder
@Getter
@NoArgsConstructor
public class SkuInventoryCommand {

   @NotNull
   protected LogicInventoryLocation fromLocation;

   /**
    * 出库库存状态:0-未知，1-可用，2-不可用
    */
   private InventoryStatusEnum fromInventoryStatus;


   /**
    * 入库库存状态:0-未知，1-可用，2-不可用
    */
   private InventoryStatusEnum toInventoryStatus;


   /**
    * 单据标记：预售-presale,非预售-none
    */
   private String orderTag;

   /**
    * 单个命令包含多个逻辑库位时使用
    * @see CommandTypeEnum
    */
   protected LogicInventoryLocation toLocation;

   @NotNull
   protected Long skuId;

   @NotNull
   protected CommandTypeEnum commandType;

   /**
    * 用途优先级，批量逗号隔开
    */
   private String usageCode;


   /**
    * 用途优先级，批量逗号隔开
    */
   private String toUsageCode;

   public InventoryWorkTypeEnum getFromInventoryWorkTypeEnum() {
      return converterToInventoryWorkTypeEnum(this.fromInventoryStatus);
   }

   public InventoryWorkTypeEnum getToInventoryWorkTypeEnum() {
      return converterToInventoryWorkTypeEnum(this.toInventoryStatus);
   }

   private InventoryWorkTypeEnum converterToInventoryWorkTypeEnum(InventoryStatusEnum inventoryStatusEnum) {
      if (InventoryStatusEnum.NOT_AVAILABLE.equals(inventoryStatusEnum)) {
         return InventoryWorkTypeEnum.FROZEN;
      }  else {
         return InventoryWorkTypeEnum.FREE;
      }
   }
}
