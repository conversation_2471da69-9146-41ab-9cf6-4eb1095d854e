package com.ddmc.ims.bo.inventory;

import java.util.Objects;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class TransferInTransitItem {

    /**
     * 出库库位货主
     */
    private final Long fromCargoOwnerId;

    /**
     * 出库仓库id
     */
    private final Long fromWarehouseId;

    /**
     * 出库库位编码
     */
    private final String fromLogicInventoryLocationCode;


    /**
     * 入库库位货主
     */
    private final Long toCargoOwnerId;

    /**
     * 入库逻辑库位编码
     */
    private final String toLogicInventoryLocationCode;

    /**
     * 入库仓库id
     */
    private final Long toWarehouseId;

    /**
     * skuId
     */
    private final Long skuId;


    /**
     * 用途优先级
     */
    private final String usageCode;

    /**
     * 用途优先级
     */
    private final String toUsageCode;

    /**
     * 批次id
     */
    private final String lotId;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TransferInTransitItem)) {
            return false;
        }
        TransferInTransitItem condition = (TransferInTransitItem) o;
        return Objects
            .equals(fromCargoOwnerId, condition.fromCargoOwnerId) && Objects
            .equals(fromWarehouseId, condition.fromWarehouseId) && Objects
            .equals(fromLogicInventoryLocationCode, condition.fromLogicInventoryLocationCode) && Objects
            .equals(toCargoOwnerId, condition.toCargoOwnerId) && Objects
            .equals(toLogicInventoryLocationCode, condition.toLogicInventoryLocationCode) && Objects
            .equals(toWarehouseId, condition.toWarehouseId) && Objects.equals(skuId, condition.skuId) && Objects
            .equals(usageCode, condition.usageCode) && Objects
            .equals(toUsageCode, condition.toUsageCode) && Objects
            .equals(lotId, condition.lotId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fromCargoOwnerId, fromWarehouseId, fromLogicInventoryLocationCode, toCargoOwnerId,
            toLogicInventoryLocationCode, toWarehouseId, skuId, usageCode, toUsageCode, lotId);
    }


}
