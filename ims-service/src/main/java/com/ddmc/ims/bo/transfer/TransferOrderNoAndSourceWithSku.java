package com.ddmc.ims.bo.transfer;

import com.ddmc.ims.common.bo.TransferOrderNoAndSource;
import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class TransferOrderNoAndSourceWithSku  {

    protected final TransferOrderNoAndSource transferOrderNoAndSource;

    protected final Long skuId;

    public TransferOrderNoAndSourceWithSku(TransferOrderNoAndSource transferOrderNoAndSource, Long skuId) {
        this.transferOrderNoAndSource = transferOrderNoAndSource;
        this.skuId = skuId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TransferOrderNoAndSourceWithSku)) {
            return false;
        }
        TransferOrderNoAndSourceWithSku that = (TransferOrderNoAndSourceWithSku) o;
        return Objects.equals(transferOrderNoAndSource, that.transferOrderNoAndSource) && Objects
            .equals(skuId, that.skuId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(transferOrderNoAndSource, skuId);
    }
}
