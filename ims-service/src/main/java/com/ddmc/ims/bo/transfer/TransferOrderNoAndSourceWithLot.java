package com.ddmc.ims.bo.transfer;

import com.ddmc.ims.common.bo.TransferOrderNoAndSource;
import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class TransferOrderNoAndSourceWithLot extends TransferOrderNoAndSourceWithSku{

    private final String lotId;

    public TransferOrderNoAndSourceWithLot(TransferOrderNoAndSource transferOrderNoAndSource, Long skuId, String lotId) {
        super(transferOrderNoAndSource, skuId);
        this.lotId = lotId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TransferOrderNoAndSourceWithLot)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        TransferOrderNoAndSourceWithLot that = (TransferOrderNoAndSourceWithLot) o;
        return Objects.equals(lotId, that.lotId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), lotId);
    }
}
