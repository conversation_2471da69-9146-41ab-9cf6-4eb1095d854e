package com.ddmc.ims.bo.command;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@SuperBuilder
@Getter
@NoArgsConstructor
public class OutBoundInventoryCommand extends SkuInventoryCommand {

    private BigDecimal qty;
    private String lotId;
    private String orderSource;
    private String orderNo;
    private String orderType;
    private Date expectOutTime;
    private String exeOrderNo;
    private String exeOrderSource;

}
