package com.ddmc.ims.bo.snapshot;

/**
 * <AUTHOR>
 */

import java.util.Objects;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class SnapshotTransferItem {

    /**
     * 货品id
     */
    private final Long skuId;


    /**
     * 仓库id
     */
    private final Long fromWarehouseId;

    /**
     * 逻辑库位编码
     */
    private final String fromLogicLocationCode;

    /**
     * 货主id
     */
    private final Long fromCargoOwnerId;

    /**
     * 仓库id
     */
    private final Long toWarehouseId;

    /**
     * 逻辑库位编码
     */
    private final String toLogicLocationCode;

    /**
     * 货主id
     */
    private final Long toCargoOwnerId;

    public SnapshotTransferItem(Long skuId, Long fromWarehouseId, String fromLogicLocationCode, Long fromCargoOwnerId,
        Long toWarehouseId, String toLogicLocationCode, Long toCargoOwnerId) {
        this.skuId = skuId;
        this.fromWarehouseId = fromWarehouseId;
        this.fromLogicLocationCode = fromLogicLocationCode;
        this.fromCargoOwnerId = fromCargoOwnerId;
        this.toWarehouseId = toWarehouseId;
        this.toLogicLocationCode = toLogicLocationCode;
        this.toCargoOwnerId = toCargoOwnerId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SnapshotTransferItem)) {
            return false;
        }
        SnapshotTransferItem that = (SnapshotTransferItem) o;
        return Objects.equals(skuId, that.skuId) && Objects.equals(fromWarehouseId,
            that.fromWarehouseId) && Objects.equals(fromLogicLocationCode, that.fromLogicLocationCode)
            && Objects.equals(fromCargoOwnerId, that.fromCargoOwnerId) && Objects.equals(toWarehouseId,
            that.toWarehouseId) && Objects.equals(toLogicLocationCode, that.toLogicLocationCode)
            && Objects.equals(toCargoOwnerId, that.toCargoOwnerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuId, fromWarehouseId, fromLogicLocationCode, fromCargoOwnerId, toWarehouseId,
            toLogicLocationCode, toCargoOwnerId);
    }
}
