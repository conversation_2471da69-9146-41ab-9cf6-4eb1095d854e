package com.ddmc.ims.bo.command;

import com.ddmc.ims.common.enums.ims.DeliveryModeEnum;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@SuperBuilder
@Getter
@NoArgsConstructor
public class TransferIntransitInventoryCommand extends SkuInventoryCommand{

    private String orderSource;
    private String orderNo;
    private BigDecimal qty;
    private DeliveryModeEnum deliveryMode;
    private Date expectOutTime;
    private Date expectInTime;
    private String lotId;
    private Integer orderOperateType;
    private String exeOrderNo;
    private String exeOrderSource;
    private Date businessTime;
    private boolean todaySale;

}
