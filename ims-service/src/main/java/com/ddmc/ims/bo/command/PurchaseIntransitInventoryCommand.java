package com.ddmc.ims.bo.command;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@SuperBuilder
@Getter
@NoArgsConstructor
public class PurchaseIntransitInventoryCommand extends SkuInventoryCommand {

    private String orderSource;
    private String orderNo;
    private Date expectArriveTime;
    private BigDecimal qty;
    private String lotId;
    /**
     * 单据标记：预售-presale,非预售-none
     */
    private String orderTag;
}
