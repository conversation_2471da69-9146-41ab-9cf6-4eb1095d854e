package com.ddmc.ims.bo.inventory;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 单逻辑库位内的库存增加和减少
 * <AUTHOR>
 */
@Data
public class InventoryChange {

    /**
     * 逻辑库位
     */
    @NotNull
    private LogicInventoryLocation logicInventoryLocation;



    /**
     * 逻辑库位变动明细
     */
    @NotEmpty
    private List<InventoryChangeItem> inventoryChangeItemList;
}
