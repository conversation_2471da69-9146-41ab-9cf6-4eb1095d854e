package com.ddmc.ims.bo.snapshot;

import java.util.Objects;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class SnapshotUsageItem {

    /**
     * 货品id
     */
    private final Long skuId;

    /**
     * 用途维度
     */
    private final String usage;

    /**
     * 仓库id
     */
    private final Long warehouseId;

    /**
     * 逻辑库位编码
     */
    private final String logicLocationCode;

    /**
     * 货主id
     */
    private final Long cargoOwnerId;

    public SnapshotUsageItem(Long skuId, String usage, Long warehouseId, String logicLocationCode, Long cargoOwnerId) {
        this.skuId = skuId;
        this.usage = usage;
        this.warehouseId = warehouseId;
        this.logicLocationCode = logicLocationCode;
        this.cargoOwnerId = cargoOwnerId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SnapshotUsageItem)) {
            return false;
        }
        SnapshotUsageItem that = (SnapshotUsageItem) o;
        return Objects.equals(skuId, that.skuId) && Objects.equals(usage, that.usage)
            && Objects.equals(warehouseId, that.warehouseId) && Objects
            .equals(logicLocationCode, that.logicLocationCode) && Objects.equals(cargoOwnerId, that.cargoOwnerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuId, usage, warehouseId, logicLocationCode, cargoOwnerId);
    }
}
