package com.ddmc.ims.bo.snapshot;

import java.util.Objects;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class SnapshotInOutItem {

    /**
     * 货品id
     */
    private final Long skuId;


    /**
     * 仓库id
     */
    private final Long warehouseId;



    public SnapshotInOutItem(Long skuId, Long warehouseId) {
        this.skuId = skuId;
        this.warehouseId = warehouseId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SnapshotInOutItem)) {
            return false;
        }
        SnapshotInOutItem that = (SnapshotInOutItem) o;
        return Objects.equals(skuId, that.skuId)
            && Objects.equals(warehouseId, that.warehouseId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuId, warehouseId);
    }
}
