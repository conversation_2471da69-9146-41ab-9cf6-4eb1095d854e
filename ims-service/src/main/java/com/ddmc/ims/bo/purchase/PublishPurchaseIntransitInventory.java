package com.ddmc.ims.bo.purchase;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 采购在途
 *
 * <AUTHOR>
 */
@Data
public class PublishPurchaseIntransitInventory {

    /**
     * 逻辑库位
     */
    @NotNull
    private LogicInventoryLocation logicInventoryLocation;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 采购单号
     */
    private String orderNo;

    /**
     * 期望到货时间
     */
    private Date expectArriveTime;

    /**
     * 采购在途明细
     */
    private List<PurchaseIntransitInventoryItem> inventoryTransferItems;
}
