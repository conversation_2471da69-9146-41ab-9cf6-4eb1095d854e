package com.ddmc.ims.bo.inventory;

import java.util.Date;
import java.util.Objects;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class TransferManufactureDateItem {

    /**
     * 出库库位货主
     */
    private final Long fromCargoOwnerId;

    /**
     * 出库仓库id
     */
    private final Long fromWarehouseId;

    /**
     * 出库库位编码
     */
    private final String fromLogicInventoryLocationCode;


    /**
     * 入库库位货主
     */
    private final Long toCargoOwnerId;

    /**
     * 入库逻辑库位编码
     */
    private final String toLogicInventoryLocationCode;

    /**
     * 入库仓库id
     */
    private final Long toWarehouseId;

    /**
     * skuId
     */
    private final Long skuId;


    /**
     * 用途优先级
     */
    private final String usageCode;


    /**
     * 生产日期
     */
    private final Date manufactureDate;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TransferManufactureDateItem)) {
            return false;
        }
        TransferManufactureDateItem that = (TransferManufactureDateItem) o;
        return Objects.equals(fromCargoOwnerId, that.fromCargoOwnerId) && Objects
            .equals(fromWarehouseId, that.fromWarehouseId) && Objects
            .equals(fromLogicInventoryLocationCode, that.fromLogicInventoryLocationCode) && Objects
            .equals(toCargoOwnerId, that.toCargoOwnerId) && Objects
            .equals(toLogicInventoryLocationCode, that.toLogicInventoryLocationCode) && Objects
            .equals(toWarehouseId, that.toWarehouseId) && Objects.equals(skuId, that.skuId) && Objects
            .equals(usageCode, that.usageCode) && Objects.equals(manufactureDate, that.manufactureDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fromCargoOwnerId, fromWarehouseId, fromLogicInventoryLocationCode, toCargoOwnerId,
            toLogicInventoryLocationCode, toWarehouseId, skuId, usageCode, manufactureDate);
    }
}
