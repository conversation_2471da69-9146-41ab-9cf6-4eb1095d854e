package com.ddmc.ims.controller.monitor;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/monitor")
public class WarmUpController {

    @Resource
    private WarehouseMapper warehouseMapper;

    /**
     * 启动预热
     */
    @GetMapping(value = "/warmUp")
    public ResponseBaseVo<Boolean> warmUp() {
        warehouseMapper.selectById(1L);
        return ResponseBaseVo.ok(true);
    }
}