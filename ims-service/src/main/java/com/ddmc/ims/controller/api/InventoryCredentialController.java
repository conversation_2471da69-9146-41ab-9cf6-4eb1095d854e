package com.ddmc.ims.controller.api;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.service.credential.InventoryCredentialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "库存操作凭证")
@RestController
@RequestMapping("/api/inventory")
public class InventoryCredentialController {

    @Resource
    private InventoryCredentialService inventoryCredentialService;


    @SuppressWarnings("unchecked")
    @PostMapping("/confirm")
    @ApiOperation("凭证确认")
    public ResponseBaseVo<Void> confirm(
        @Validated @RequestBody CredentialTryConfirmRequest request) {
        inventoryCredentialService.confirm(request.getIdempotentId());
        return ResponseBaseVo.ok();
    }
    @SuppressWarnings("unchecked")
    @PostMapping("/cancel")
    @ApiOperation("凭证取消")
    public ResponseBaseVo<Void> cancel(
        @Validated @RequestBody CredentialTryConfirmRequest request) {
        inventoryCredentialService.cancel(request.getIdempotentId());
        return ResponseBaseVo.ok();
    }

}
