package com.ddmc.ims.controller.monitor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.commons.util.InetUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping(value = "/monitor/base")
public class BaseMonitorController {

    @Value("${spring.application.name}")
    private String serviceName;
    @Autowired
    private DiscoveryClient discoveryClient;

    @Autowired
    private InetUtils inetUtils;

    /**
     * 校验是否启动
     */
    @GetMapping(value = "/checkStartUp")
    public ResponseEntity<Map<String, String>> checkStartUp() {
        List<ServiceInstance> sis = discoveryClient.getInstances(serviceName);
        String ipAddress = inetUtils.findFirstNonLoopbackHostInfo().getIpAddress();
        Map<String, String> registerStatus = new HashMap<>();
        if (!CollectionUtils.isEmpty(sis)) {
            for (ServiceInstance instance : sis) {
                String host = instance.getHost();
                if (StringUtils.equals(host, ipAddress)) {
                    registerStatus.put("registered", "true");
                    return new ResponseEntity<>(registerStatus, HttpStatus.OK);
                }
            }
        }
        registerStatus.put("registered", "false");
        return new ResponseEntity<>(registerStatus, HttpStatus.SERVICE_UNAVAILABLE);
    }
}