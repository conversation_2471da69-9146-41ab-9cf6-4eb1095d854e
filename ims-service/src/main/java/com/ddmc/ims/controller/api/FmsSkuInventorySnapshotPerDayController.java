package com.ddmc.ims.controller.api;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.constant.DataSourceConstants;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.dynamic.DataSourceSwitch;
import com.ddmc.ims.dal.model.monitor.FmsSkuInventorySnapshotPerDay;
import com.ddmc.ims.request.snopshot.DayEndTimeNoticeRequest;
import com.ddmc.ims.request.snopshot.FmsSnapshotCountRequest;
import com.ddmc.ims.request.snopshot.FmsSnapshotQueryRequest;
import com.ddmc.ims.response.inventory.FmsSnapshotInitResponse;
import com.ddmc.ims.response.inventory.FmsSnapshotResponse;
import com.ddmc.ims.service.snapshot.FmsSkuInventorySnapshotPerDayService;
import com.ddmc.ims.service.snapshot.SnapshotTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "财务快照接口")
@RestController
@RequestMapping("/api/fmsSnapshot")
public class FmsSkuInventorySnapshotPerDayController {


    @Resource
    private FmsSkuInventorySnapshotPerDayService fmsSkuInventorySnapshotPerDayService;
    @Resource
    private SnapshotTaskService snapshotTaskService;


    @PostMapping("/dayEndTimeNotice")
    @ApiOperation("归结日期通知")
    public ResponseBaseVo<Void> dayEndTimeNotice(
        @Validated @RequestBody DayEndTimeNoticeRequest request) {
        log.info("归结日期通知,{}", JsonUtil.toJson(request));
        snapshotTaskService.dayEndTimeNotice(request.getDayEndTime());
        return ResponseBaseVo.ok();
    }

    @PostMapping("/queryByWarehouseAndSnapshotDate")
    @ApiOperation("查询财务快照")
    @DataSourceSwitch(DataSourceConstants.SLAVE_SHARDING_DATASOURCE)
    public ResponseBaseVo<List<FmsSnapshotResponse>> queryByWarehouseAndSnapshotDate(
        @Validated @RequestBody FmsSnapshotQueryRequest request) {
        List<FmsSkuInventorySnapshotPerDay> fmsSkuInventorySnapshotPerDays = fmsSkuInventorySnapshotPerDayService
            .queryByWarehouseAndSnapshotDate(request.getWarehouseId(), request.getSnapshotDate(), request.getSkuId(),
                request.getLimit());
        List<FmsSnapshotResponse> responseList = fmsSkuInventorySnapshotPerDays.stream()
            .map(t -> {
                FmsSnapshotResponse response = new FmsSnapshotResponse();
                response.setSkuId(t.getSkuId());
                response.setSnapshotDate(t.getSnapshotDate());
                response.setWarehouseId(t.getWarehouseId());
                response.setInQty(t.getInQty());
                response.setQty(t.getQty());
                response.setOutQty(t.getOutQty());
                response.setProcessingQty(t.getProcessingQty());
                response.setTransferIntransitQty(t.getTransferIntransitQty());
                response.setPurchasePartInQty(t.getPurchasePartInQty());
                response.setTransferPartInQty(t.getTransferPartInQty());
                response.setReverseProcessingQty(t.getReverseProcessingQty());
                response.setOtherDiffQty(t.getOtherDiffQty());
                return response;
            }).collect(Collectors.toList());
        return ResponseBaseVo.ok(responseList);
    }

    @PostMapping("/queryInitByWarehouseAndSnapshotDate")
    @ApiOperation("查询初始化财务快照")
    public ResponseBaseVo<List<FmsSnapshotInitResponse>> queryInitByWarehouseAndSnapshotDate(
        @Validated @RequestBody FmsSnapshotQueryRequest request) {
        return ResponseBaseVo.ok(Collections.emptyList());
    }


    @PostMapping("/countByWarehouseAndSnapshotDate")
    @ApiOperation("查询财务快照数量")
    public ResponseBaseVo<Integer> countByWarehouseAndSnapshotDate(
        @Validated @RequestBody FmsSnapshotCountRequest request) {
        return ResponseBaseVo.ok(fmsSkuInventorySnapshotPerDayService
            .countByWarehouseAndSnapshotDate(request.getWarehouseId(), request.getSnapshotDate()));
    }

    @PostMapping("/compareResult")
    @ApiOperation("财务比对结果通知")
    public ResponseBaseVo<Void> compareResult(
        @Validated @RequestBody DayEndTimeNoticeRequest request) {
        log.info("财务比对结果通知,{}", JsonUtil.toJson(request));
        snapshotTaskService.compareResult(request.getDayEndTime(), request.getStatus());
        return ResponseBaseVo.ok();
    }

}
