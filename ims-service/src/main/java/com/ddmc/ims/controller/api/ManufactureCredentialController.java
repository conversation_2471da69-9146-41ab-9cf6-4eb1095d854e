package com.ddmc.ims.controller.api;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.request.credential.oc.ManufactureCredentialRequest;
import com.ddmc.ims.service.credential.ManufactureInventoryCredentialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(tags = "库存操作凭证-制造单")
@RestController
@RequestMapping("/api/inventory")
public class ManufactureCredentialController {


    @Resource
    private ManufactureInventoryCredentialService manufactureInventoryCredentialService;

    @PostMapping("/tryManufacture")
    @ApiOperation("入库操作")
    public ResponseBaseVo<Void> tryManufacture(
        @Validated @RequestBody ManufactureCredentialRequest request) {
        manufactureInventoryCredentialService.tryManufacture(request);
        return ResponseBaseVo.ok();
    }

}
