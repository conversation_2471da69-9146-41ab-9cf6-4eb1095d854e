package com.ddmc.ims.controller.api;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.SnapshotCredentialDiff;
import com.ddmc.ims.request.credential.oc.IdempotentAndBusinessTimeRequest;
import com.ddmc.utils.json.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "库存操作凭证")
@RestController
@RequestMapping("/api/inventory")
public class FixInventoryCredentialController {

    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;
    @Resource
    private SnapshotCredentialDiffMapper snapshotCredentialDiffMapper;


    @PostMapping("/changeBusinessTime")
    @ApiOperation("修改业务时间")
    public ResponseBaseVo<Void> changeBusinessTime(
        @Validated @RequestBody IdempotentAndBusinessTimeRequest request) {
        CredentialHeader header = credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId());
        if(Objects.isNull(header)){
            return ResponseBaseVo.fail(-1,"数据不存在");
        }
        header.setBusinessTime(request.getBusinessTime());
        credentialHeaderMapper.updateById(header);
        return ResponseBaseVo.ok();
    }

    @PostMapping("/deleteCredentialDiff")
    @ApiOperation("删除差异表信息")
    public ResponseBaseVo<Void> deleteCredentialDiff(
        @Validated @RequestBody List<Long> ids) {
        List<SnapshotCredentialDiff> snapshotCredentialDiffs = snapshotCredentialDiffMapper.selectBatchIds(ids);
        log.info("删除差异信息 {}", JsonUtil.toJSON(snapshotCredentialDiffs));
        snapshotCredentialDiffMapper.deleteBatchIds(ids);
        return ResponseBaseVo.ok();
    }
}
