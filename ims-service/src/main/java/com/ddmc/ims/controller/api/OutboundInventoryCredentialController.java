package com.ddmc.ims.controller.api;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;
import com.ddmc.ims.service.credential.OutboundInventoryCredentialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "库存操作凭证")
@RestController
@RequestMapping("/api/inventory")
public class OutboundInventoryCredentialController {

    @Resource
    private OutboundInventoryCredentialService outboundInventoryCredentialService;

    @PostMapping("/tryOutbound")
    @ApiOperation("出库操作")
    public ResponseBaseVo<Void> tryOutbound(
        @Validated @RequestBody OutboundCredentialRequest request) {
        outboundInventoryCredentialService.tryOutbound(request);
        return ResponseBaseVo.ok();
    }

    @PostMapping("/tryPublishOutbound")
    @ApiOperation("申请出库操作")
    public ResponseBaseVo<Void> tryPublishOutbound(
        @Validated @RequestBody OutboundCredentialRequest request) {
        outboundInventoryCredentialService.tryPublishOutbound(request);
        return ResponseBaseVo.ok();
    }

    @PostMapping("/tryTransferReject")
    @ApiOperation("调拨拒收")
    public ResponseBaseVo<Void> tryTransferReject(
        @Validated @RequestBody OutboundCredentialRequest request) {
        outboundInventoryCredentialService.tryTransferReject(request);
        return ResponseBaseVo.ok();
    }


    @PostMapping("/tryModifyOutboundPlanQty")
    @ApiOperation("修改出库计划数量操作")
    public ResponseBaseVo<Void> tryModifyOutboundPlanQty(
        @Validated @RequestBody OutboundCredentialRequest request) {
        outboundInventoryCredentialService.tryModifyOutboundPlanQty(request);
        return ResponseBaseVo.ok();
    }

    @PostMapping("/tryCancelOutbound")
    @ApiOperation("取消出库申请操作")
    public ResponseBaseVo<Void> tryCancelOutbound(
        @Validated @RequestBody OutboundCredentialRequest request) {
        outboundInventoryCredentialService.tryCancelOutbound(request);
        return ResponseBaseVo.ok();
    }


    @PostMapping("/tryFinishOutbound")
    @ApiOperation("出库完成操作")
    public ResponseBaseVo<Void> tryFinishOutbound(
        @Validated @RequestBody OutboundCredentialRequest request) {
        outboundInventoryCredentialService.tryFinishOutbound(request);
        return ResponseBaseVo.ok();
    }

    @PostMapping("/tryModifyExpectOutTime")
    @ApiOperation("修改出库日期操作")
    public ResponseBaseVo<Void> tryModifyExpectOutTime(
        @Validated @RequestBody OutboundCredentialRequest request) {
        outboundInventoryCredentialService.tryModifyExpectOutTime(request);
        return ResponseBaseVo.ok();
    }
}
