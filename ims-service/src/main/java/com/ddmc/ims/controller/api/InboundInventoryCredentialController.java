package com.ddmc.ims.controller.api;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import com.ddmc.ims.service.credential.InboundInventoryCredentialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "库存操作凭证")
@RestController
@SuppressWarnings("unchecked")
@RequestMapping("/api/inventory")
public class InboundInventoryCredentialController {

    @Resource
    private InboundInventoryCredentialService inboundInventoryCredentialService;

    @PostMapping("/tryInbound")
    @ApiOperation("入库操作")
    public ResponseBaseVo<Void> tryInbound(
        @Validated @RequestBody InboundCredentialRequest request) {
        inboundInventoryCredentialService.tryInbound(request);
        return ResponseBaseVo.ok();
    }


    @PostMapping("/tryPublishInbound")
    @ApiOperation("发布入库操作")
    public ResponseBaseVo<Void> tryPublishInbound(
        @Validated @RequestBody InboundCredentialRequest request) {
        inboundInventoryCredentialService.tryPublishInbound(request);
        return ResponseBaseVo.ok();
    }

    @PostMapping("/tryModifyExpectArriveTime")
    @ApiOperation("修改到货日期操作")
    public ResponseBaseVo<Void> tryModifyExpectArriveTime(
        @Validated @RequestBody InboundCredentialRequest request) {
        inboundInventoryCredentialService.tryModifyExpectArriveTime(request);
        return ResponseBaseVo.ok();
    }

    @PostMapping("/tryCancelInbound")
    @ApiOperation("取消入库申请")
    public ResponseBaseVo<Void> tryCancelInbound(
        @Validated @RequestBody InboundCredentialRequest request) {
        inboundInventoryCredentialService.tryCancelInbound(request);
        return ResponseBaseVo.ok();
    }

    @PostMapping("/tryFinishInbound")
    @ApiOperation("完成入库操作")
    public ResponseBaseVo<Void> tryFinishInbound(
        @Validated @RequestBody InboundCredentialRequest request) {
        inboundInventoryCredentialService.tryFinishInbound(request);
        return ResponseBaseVo.ok();
    }

    @PostMapping("/tryCleanPlanIn")
    @ApiOperation("清理采购在途数据")
    public ResponseBaseVo<Void> tryCleanPlanIn(
        @Validated @RequestBody InboundCredentialRequest request) {
        inboundInventoryCredentialService.tryCleanPlanIn(request);
        return ResponseBaseVo.ok();
    }
}
