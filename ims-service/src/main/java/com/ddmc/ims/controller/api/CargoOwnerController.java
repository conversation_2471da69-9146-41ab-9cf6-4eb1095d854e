package com.ddmc.ims.controller.api;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.gateway.bg.client.annotation.HttpApi;
import com.ddmc.gateway.bg.client.annotation.HttpApiGroup;
import com.ddmc.ims.response.inventory.CargoOwnerResponse;
import com.ddmc.ims.service.inventoryconfig.CargoOwnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "货主信息")
@HttpApiGroup("货主信息")
@RestController
@RequestMapping("/api/cargoOwner")
public class CargoOwnerController {

    @Resource
    private CargoOwnerService cargoOwnerService;

    @PostMapping("/queryCargoOwnerList")
    @ApiOperation("获取所有的货主")
    @HttpApi("获取所有的货主")
    public ResponseBaseVo<List<CargoOwnerResponse>> queryCargoOwnerList() {
        return ResponseBaseVo.ok(cargoOwnerService.queryCargoOwnerList());
    }
}
