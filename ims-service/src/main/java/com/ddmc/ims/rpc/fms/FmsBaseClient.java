package com.ddmc.ims.rpc.fms;

import com.ddmc.ims.config.feign.CommonFeignConfig;
import com.ddmc.ims.rpc.fms.dto.FmsBaseResponseVo;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsDto;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 财务接口
 */
@FeignClient(name = "fmsbase-service", contextId = "fmsBaseClient",
    fallbackFactory = FmsBaseClientFallbackFactory.class, configuration = CommonFeignConfig.class)
public interface FmsBaseClient {

    /**
     * 查询法人公司信息
     *
     * @param request 请求参数
     * @return 返回数据
     */
    @ResponseBody
    @PostMapping(value = "/manage/corporation/getCorporations")
    FmsBaseResponseVo<GetCorporationsDto> getCorporations(@RequestBody GetCorporationsRequest request);
}
