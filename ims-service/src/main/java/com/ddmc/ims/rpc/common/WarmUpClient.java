package com.ddmc.ims.rpc.common;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.config.feign.CommonFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = "scm-ims-service", contextId = "warmUpClient", url = "http://localhost:8080",
    configuration = CommonFeignConfig.class, fallbackFactory = WarmUpFallbackFactory.class)
public interface WarmUpClient {

    /**
     * 启动预热
     *
     * @return 返回数据
     */
    @GetMapping(value = "/monitor/warmUp")
    ResponseBaseVo<Boolean> warmUp();
}

