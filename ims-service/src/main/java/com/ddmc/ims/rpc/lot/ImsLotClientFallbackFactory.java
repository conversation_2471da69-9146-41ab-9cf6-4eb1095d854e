package com.ddmc.ims.rpc.lot;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.lot.client.response.LotDTO;
import com.ddmc.lot.request.ScanLotReq;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * lot-service
 */
@Slf4j
@Component
public class ImsLotClientFallbackFactory implements FallbackFactory<ImsLotClient> {

    @Override
    public ImsLotClient create(Throwable cause) {
        log.error("ImsLotClientFallbackFactory Error", cause);
        return new ImsLotClient() {
            @Override
            public ResponseBaseVo<Map<String, LotDTO>> multiGet(List<String> lotIds) {
                return ResponseBaseVo.fail(-1, "批量查询批次信息请求异常 lot-service list error:" + cause, null);
            }

            @Override
            public ResponseBaseVo<LotDTO> get(String lotId) {
                return ResponseBaseVo.fail(-1, "查询批次请求异常 lot-service list error:" + cause, null);
            }

            @Override
            public ResponseBaseVo<List<LotDTO>> scanLot(ScanLotReq req) {
                return ResponseBaseVo.fail(-1, "遍历批次信息请求异常 lot-service list error:" + cause, null);
            }
        };
    }
}
