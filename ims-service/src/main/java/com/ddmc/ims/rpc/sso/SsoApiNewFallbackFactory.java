package com.ddmc.ims.rpc.sso;

import com.ddmc.duc.req.LoginReq;
import com.ddmc.duc.req.LogoutReq;
import com.ddmc.duc.req.ModifyPasswordReq;
import com.ddmc.duc.req.ResetPasswordReq;
import com.ddmc.duc.req.SmsReq;
import com.ddmc.duc.req.UserRegisterReq;
import com.ddmc.duc.req.UserUpdateReq;
import com.ddmc.duc.req.WxLoginReq;
import com.ddmc.duc.resp.BasicUser;
import com.ddmc.duc.resp.LoginResp;
import com.ddmc.duc.resp.LogoutResp;
import com.ddmc.duc.resp.MenuResp;
import com.ddmc.duc.resp.ProductGroupResp;
import com.ddmc.duc.resp.RoleIdNameResp;
import com.ddmc.duc.resp.UserAppAuthResp;
import com.ddmc.duc.resp.UserDepartmentResp;
import com.ddmc.duc.vo.BaseResponseVo;
import com.ddmc.duc.vo.PageResponseVo;
import com.ddmc.duc.vo.user.BaseMeta;
import feign.hystrix.FallbackFactory;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.Max;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SsoApiNewFallbackFactory implements FallbackFactory<SsoApiNewClient> {

    @Override
    public SsoApiNewClient create(Throwable cause) {
        log.error("sso请求异常：", cause);

        return new SsoApiNewClient() {
            @Override
            public BaseResponseVo<BasicUser> getUserById(String userId) {
                BaseResponseVo<BasicUser> responseVo = new BaseResponseVo<>();
                responseVo.setCode(500);
                responseVo.setSuccess(false);
                responseVo.setMessage("duc-service服务请求异常 getUserById Error getUserById");
                return responseVo;
            }

            @Override
            public BaseResponseVo<List<BasicUser>> getUserByIds(
                @Max(value = 200L, message = "单次最大查询{value}条") Collection<String> userIds) {
                return null;
            }

            @Override
            public PageResponseVo<List<BasicUser>> pageGetUserByRoleIds(Collection<String> roleIds, int pageNo,
                int pageSize) {
                return null;
            }

            @Override
            public PageResponseVo<List<BasicUser>> pageGetAppUsers(int pageNo, int pageSize) {
                return null;
            }

            @Override
            public PageResponseVo<List<BasicUser>> getAppUserByKeyword(String keyword) {
                return null;
            }

            @Override
            public PageResponseVo<List<BasicUser>> getUserByKeyword(String keyword) {
                return null;
            }

            @Override
            public BaseResponseVo<List<BasicUser>> getUserList(long updateFrom, long updateTo, int pageNo,
                int pageSize) {
                return null;
            }

            @Override
            public BaseResponseVo<List<RoleIdNameResp>> getUserAppRoles(String userId) {
                BaseResponseVo<List<RoleIdNameResp>> responseVo = new BaseResponseVo<>();
                responseVo.setCode(500);
                responseVo.setSuccess(false);
                responseVo.setMessage("duc-service服务请求异常 getUserAppRoles Error getUserAppRoles");
                return responseVo;
            }

            @Override
            public BaseResponseVo<Map<Integer, List<RoleIdNameResp>>> getUserAppRoles(List<Integer> appIds,
                String userId) {
                BaseResponseVo<Map<Integer, List<RoleIdNameResp>>> responseVo = new BaseResponseVo<>();
                responseVo.setCode(500);
                responseVo.setSuccess(false);
                responseVo.setMessage("duc-service服务请求异常 getUserAppRoles Error getUserAppRoles");
                return responseVo;
            }

            @Override
            public BaseResponseVo<Map<String, List<RoleIdNameResp>>> batchGetUserAppRoles(Collection<String> userIds) {
                return null;
            }

            @Override
            public BaseResponseVo<List<UserAppAuthResp>> getUserApps(String userId) {
                return null;
            }

            @Override
            public BaseResponseVo<List<UserDepartmentResp>> getUserDepartment(Collection<String> jobNumbers) {
                return null;
            }

            @Override
            public BaseResponseVo<BasicUser> getUserByEmail(String email) {
                return null;
            }

            @Override
            public BaseResponseVo<List<BasicUser>> batchGetUserByEmail(Collection<String> emails) {
                return null;
            }

            @Override
            public BaseResponseVo<BasicUser> getUserByMobile(String mobile) {
                return null;
            }

            @Override
            public BaseResponseVo<List<BasicUser>> batchGetUserByMobile(Collection<String> mobiles) {
                return null;
            }

            @Override
            public BaseResponseVo<BasicUser> getUserByJobNumber(String jobNumber) {
                return null;
            }

            @Override
            public BaseResponseVo<List<BasicUser>> batchGetUserByJobNumber(Collection<String> jobNumbers) {
                return null;
            }

            @Override
            public BaseResponseVo<List<RoleIdNameResp>> getAppRoles() {
                return null;
            }

            @Override
            public BaseResponseVo<List<MenuResp>> getMenus(String userId) {
                return null;
            }

            @Override
            public BaseResponseVo<T> getSms(SmsReq smsReq) {
                return null;
            }

            @Override
            public BaseResponseVo<T> modifyPassword(ModifyPasswordReq modifyPasswordReq) {
                return null;
            }

            @Override
            public BaseResponseVo<T> resetPassword(ResetPasswordReq resetPasswordReq) {
                return null;
            }

            @Override
            public BaseResponseVo<LoginResp> login(LoginReq loginReq) {
                return null;
            }

            @Override
            public BaseResponseVo<LoginResp> wxLogin(WxLoginReq wxLoginReq) {
                return null;
            }

            @Override
            public BaseResponseVo<LogoutResp> logout(LogoutReq logoutReq) {
                return null;
            }

            @Override
            public BaseResponseVo<String> updateUserInfo(UserUpdateReq userUpdateReq) {
                return null;
            }

            @Override
            public BaseResponseVo<String> userRegister(UserRegisterReq userRegisterReq) {
                return null;
            }

            @Override
            public BaseResponseVo<List<ProductGroupResp>> getProductGroupList() {
                BaseResponseVo<List<ProductGroupResp>> responseVo = new BaseResponseVo<>();
                responseVo.setCode(500);
                responseVo.setSuccess(false);
                responseVo.setMessage("duc-service服务请求异常 SsoApiNewClient Error getProductGroupList");
                return responseVo;
            }

            @Override
            public BaseResponseVo<List<BaseMeta>> getCity() {
                BaseResponseVo<List<BaseMeta>> responseVo = new BaseResponseVo<>();
                responseVo.setCode(500);
                responseVo.setSuccess(false);
                responseVo.setMessage("duc-service服务请求异常 SsoApiNewClient Error getCity");
                return responseVo;
            }
        };
    }
}
