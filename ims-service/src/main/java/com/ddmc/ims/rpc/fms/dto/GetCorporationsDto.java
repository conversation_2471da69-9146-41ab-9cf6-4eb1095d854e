package com.ddmc.ims.rpc.fms.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询结算主体响应信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetCorporationsDto {

    private Integer current;

    private Integer size;

    private Integer total;

    private Integer pages;

    private Integer startIndex;

    private List<CorporationInfo> records;

    @Data
    public static class CorporationInfo {

        /**
         * 公司名称
         */
        private String corporationName;

        /**
         * 公司编码
         */
        private String corporationCode;
    }
}
