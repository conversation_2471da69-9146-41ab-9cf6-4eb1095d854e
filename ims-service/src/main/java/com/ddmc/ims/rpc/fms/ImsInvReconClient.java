package com.ddmc.ims.rpc.fms;

import com.ddmc.fms.inv.client.InvReconClient;
import com.ddmc.ims.config.feign.CommonFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "fms-inv-service", path = "/manage/recon", contextId = "imsInvReconClient",
    fallbackFactory = ImsInvReconClientFallbackFactory.class, configuration = CommonFeignConfig.class)
public interface ImsInvReconClient extends InvReconClient {

}
