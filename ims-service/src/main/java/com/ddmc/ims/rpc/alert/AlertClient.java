package com.ddmc.ims.rpc.alert;

import com.ddmc.ims.config.feign.CommonFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * 请求运维监控告警服务
 */
@FeignClient(name = "AlertClient", url = "${wechat.robot.url}",
    fallbackFactory = com.ddmc.ims.rpc.alert.AlertClientFallbackFactory.class, configuration = CommonFeignConfig.class)
public interface AlertClient {

    @PostMapping(value = "/send")
    @ResponseBody
    AlertResponse alert(@RequestParam("key") String key, @RequestBody Map<String, Object> jo);
}
