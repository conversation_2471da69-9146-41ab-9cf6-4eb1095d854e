package com.ddmc.ims.rpc.fms.dto;

import lombok.Data;

/**
 * 返回字段定义
 */
@Data
public class FmsBaseResponseVo<T> {

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 错误消息
     */
    private String msg;


    private boolean success;

    private String timestamp;

    private T data;

    public static <T> FmsBaseResponseVo<T> fail(Integer code, String msg) {
        FmsBaseResponseVo<T> fmsBaseResponseVo = new FmsBaseResponseVo<>();
        fmsBaseResponseVo.setCode(code);
        fmsBaseResponseVo.setMsg(msg);
        return fmsBaseResponseVo;
    }
}
