package com.ddmc.ims.rpc.lot;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.config.feign.CommonFeignConfig;
import com.ddmc.lot.client.response.LotDTO;
import com.ddmc.lot.request.ScanLotReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(name = "lot-service", contextId = "lotClient",
    configuration = CommonFeignConfig.class, fallbackFactory = ImsLotClientFallbackFactory.class)
public interface ImsLotClient {

    @PostMapping("/service/multiGet")
    ResponseBaseVo<Map<String,LotDTO>> multiGet(@RequestBody List<String> lotIds);

    @PostMapping("/service/get")
    ResponseBaseVo<LotDTO> get(@RequestBody String lotId);

    @PostMapping("/service/scanLot")
    ResponseBaseVo<List<LotDTO>> scanLot(@RequestBody @Validated ScanLotReq req);

}
