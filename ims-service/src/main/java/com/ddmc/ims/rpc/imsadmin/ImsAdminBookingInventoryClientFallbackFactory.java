package com.ddmc.ims.rpc.imsadmin;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.sale.request.TransferOutChangeEventRequest;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ImsAdminBookingInventoryClientFallbackFactory implements FallbackFactory<ImsAdminBookingInventoryClient> {

    @Override
    public ImsAdminBookingInventoryClient create(Throwable cause) {
        log.error("ImsConfigInventoryUsageClient req error", cause);
        return new ImsAdminBookingInventoryClient() {

            @Override
            public ResponseBaseVo<Boolean> notifyTransferOutEvent(
                TransferOutChangeEventRequest transferOutChangeEventRequest) {
                return error();
            }

            @SuppressWarnings("unchecked")
            private <T> ResponseBaseVo<T> error() {
                return ResponseBaseVo.fail(Integer.valueOf("-1"), "ims-config请求异常 ImsAdminBookingInventoryClient error");
            }
        };
    }
}
