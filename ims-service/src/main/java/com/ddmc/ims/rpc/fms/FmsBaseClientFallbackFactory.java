package com.ddmc.ims.rpc.fms;

import com.ddmc.ims.rpc.fms.dto.FmsBaseResponseVo;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsDto;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsRequest;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * FMS
 */
@Slf4j
@Component
public class FmsBaseClientFallbackFactory implements FallbackFactory<FmsBaseClient> {

    @Override
    public FmsBaseClient create(Throwable cause) {
        log.error("FmsBaseClientFallbackFactory Error", cause);
        return new FmsBaseClient() {
            @Override
            public FmsBaseResponseVo<GetCorporationsDto> getCorporations(GetCorporationsRequest request) {
                return error();
            }

            private <T> FmsBaseResponseVo<T> error() {
                return FmsBaseResponseVo.fail(-1, "fms请求异常 fms-service error:" + cause);
            }
        };
    }
}
