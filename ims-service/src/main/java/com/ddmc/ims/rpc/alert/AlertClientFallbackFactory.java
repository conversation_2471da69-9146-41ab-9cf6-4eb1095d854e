package com.ddmc.ims.rpc.alert;

import com.ddmc.ims.common.errorcode.CommonErrorCode;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class AlertClientFallbackFactory implements FallbackFactory<AlertClient> {

    @Override
    public AlertClient create(Throwable cause) {
        log.error("AlertClient req error", cause);
        return (key, jo) -> {
            AlertResponse response = new AlertResponse();
            response.setErrcode(CommonErrorCode.SYS_ERR.getCode());
            response.setErrmsg("告警失败，Fallback");
            return response;
        };


    }
}