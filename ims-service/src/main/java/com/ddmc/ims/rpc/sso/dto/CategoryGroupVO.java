package com.ddmc.ims.rpc.sso.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "负责组数据列表")
public class CategoryGroupVO {

    @ApiModelProperty(value = "负责组id")
    private String id;

    @ApiModelProperty(value = "负责组名称")
    private String name;

}