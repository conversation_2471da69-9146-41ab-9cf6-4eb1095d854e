package com.ddmc.ims.rpc.pes;

import com.ddmc.ims.config.feign.CommonFeignConfig;
import com.ddmc.pes.client.PesClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "${pes.service.name:sku-api-service}", contextId = "ImsPesExpireInfoClient", configuration = CommonFeignConfig.class)
@Qualifier
public interface PesExpireInfoClient extends PesClient {

}
