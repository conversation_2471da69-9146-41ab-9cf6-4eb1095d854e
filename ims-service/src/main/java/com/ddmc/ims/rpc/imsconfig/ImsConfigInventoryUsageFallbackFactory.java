package com.ddmc.ims.rpc.imsconfig;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.imsconfig.client.response.InventoryLocationUsageResponse;
import feign.hystrix.FallbackFactory;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ImsConfigInventoryUsageFallbackFactory implements FallbackFactory<ImsConfigInventoryUsageClient> {

    @Override
    public ImsConfigInventoryUsageClient create(Throwable cause) {
        log.error("ImsConfigInventoryUsageClient req error", cause);
        return new ImsConfigInventoryUsageClient() {

            @Override
            public ResponseBaseVo<List<String>> transferScene() {
                return error();
            }

            @Override
            public ResponseBaseVo<Set<String>> getLogicLocationNoReal() {
                return error();
            }

            @Override
            public ResponseBaseVo<List<InventoryLocationUsageResponse>> getLogicLocationAndUsage() {
                return error();
            }

            @Override
            public ResponseBaseVo<List<InventoryLocationUsageResponse>> getLogicLocationByRealLocation() {
                return error();
            }

            @SuppressWarnings("unchecked")
            private <T> ResponseBaseVo<T> error() {
                return ResponseBaseVo.fail(Integer.valueOf("-1"), "ims-config请求异常 ImsConfigInventoryUsageClient error");
            }
        };
    }
}
