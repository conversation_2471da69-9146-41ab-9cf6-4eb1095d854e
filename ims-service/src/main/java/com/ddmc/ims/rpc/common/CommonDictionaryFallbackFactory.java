package com.ddmc.ims.rpc.common;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.pms.common.client.request.dictionary.SystemDictionaryRequest;
import com.ddmc.pms.common.client.response.dictionary.SystemDictionaryVO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CommonDictionaryFallbackFactory implements FallbackFactory<CommonDictionaryClient> {

    @Override
    public CommonDictionaryClient create(Throwable cause) {
        log.error("CommonDictionaryClient req error", cause);


        return new CommonDictionaryClient() {
            @Override
            public ResponseBaseVo<List<SystemDictionaryVO>> getSystemDictionaryList(String sysDictTypeCode) {
                return error();
            }

            @Override
            public ResponseBaseVo<Map<String, List<SystemDictionaryVO>>> getSystemDictionaryListByCodes(
                List<String> sysDictTypeCodes) {
                return error();
            }

            @Override
            public ResponseBaseVo<SystemDictionaryVO> get(SystemDictionaryRequest request) {
                return error();
            }

            @SuppressWarnings("unchecked")
            private <T> ResponseBaseVo<T> error() {
                return ResponseBaseVo.fail(Integer.valueOf("-1"), "pms-common请求异常 CommonDictionaryClient error");
            }
        };
    }
}
