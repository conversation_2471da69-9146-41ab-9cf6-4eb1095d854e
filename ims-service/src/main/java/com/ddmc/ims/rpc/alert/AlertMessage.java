package com.ddmc.ims.rpc.alert;

import lombok.Data;

@Data
public class AlertMessage {

    private String id;

    private String service;

    private com.ddmc.ims.rpc.alert.AlertEnvironment environment;

    private String message;

    private com.ddmc.ims.rpc.alert.AlertLevel level;

    @Override
    public String toString() {
        return "[" + environment.name() + " - " + service + "] "
            + level.name() + "\n"
            + getId() + "\n"
            + message;
    }
}

