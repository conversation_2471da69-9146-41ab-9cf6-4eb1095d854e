package com.ddmc.ims.rpc.imsconfig;

import com.ddmc.ims.config.feign.CommonFeignConfig;
import com.ddmc.imsconfig.client.InventoryUsageClient;
import org.springframework.cloud.openfeign.FeignClient;


/**
 * <AUTHOR>
 */
@FeignClient(name = "scm-ims-config-service", contextId = "imsConfigInventoryUsageClient",
    configuration = CommonFeignConfig.class, fallbackFactory = ImsConfigInventoryUsageFallbackFactory.class)
public interface ImsConfigInventoryUsageClient extends InventoryUsageClient {

}
