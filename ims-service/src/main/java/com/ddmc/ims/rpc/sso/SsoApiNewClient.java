package com.ddmc.ims.rpc.sso;

import com.ddmc.duc.client.SsoApiClient;
import com.ddmc.ims.config.feign.SsoNewFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 *
 * 新增使用调用接口，自行到 SsoApiNewFallbackFactory.class 配置对应接口的fallback返回规则
 */
@FeignClient(name = "duc-service", contextId = "ssoApiNewClient",
    configuration = SsoNewFeignConfig.class, fallbackFactory = SsoApiNewFallbackFactory.class)
public interface SsoApiNewClient extends SsoApiClient {

}
