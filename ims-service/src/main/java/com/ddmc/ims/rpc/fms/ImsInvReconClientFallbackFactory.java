package com.ddmc.ims.rpc.fms;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.fms.inv.dto.recon.InvDailySettlementDTO;
import feign.hystrix.FallbackFactory;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ImsInvReconClientFallbackFactory implements FallbackFactory<ImsInvReconClient> {

    @Override
    public ImsInvReconClient create(Throwable cause) {
        log.error("ImsInvReconClient Error", cause);
        return new ImsInvReconClient() {
            @Override
            public ResponseBaseVo<Void> dailySettlement(@Valid InvDailySettlementDTO dto) {
                return error();
            }

            private <T> ResponseBaseVo<T> error() {
                return ResponseBaseVo.fail(-1, "fms请求异常 ImsInvReconClient error:" + cause);
            }
        };
    }
}
