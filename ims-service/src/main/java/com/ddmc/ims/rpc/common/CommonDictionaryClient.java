package com.ddmc.ims.rpc.common;

import com.ddmc.ims.config.feign.CommonFeignConfig;
import com.ddmc.pms.common.client.DictionaryClient;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "pms-com-service", contextId = "commonDictionaryClient",
    configuration = CommonFeignConfig.class, fallbackFactory = CommonDictionaryFallbackFactory.class)
public interface CommonDictionaryClient extends DictionaryClient {
}
