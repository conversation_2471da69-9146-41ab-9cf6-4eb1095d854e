package com.ddmc.ims.rpc.common;

import com.ddmc.core.view.compat.ResponseBaseVo;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WarmUpFallbackFactory implements FallbackFactory<WarmUpClient> {

    @Override
    public WarmUpClient create(Throwable cause) {
        log.error("WarmUpClient req error", cause);

        return () -> ResponseBaseVo.fail(Integer.valueOf("-1"), "scm-ims-service请求异常 warmUp error");
    }
}
