package com.ddmc.ims.rpc.imsadmin;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.sale.request.TransferOutChangeEventRequest;
import com.ddmc.multi.cloud.rpc.RpcShardIdSetter;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        name = "scm-ims-sale-service",
        contextId = "bookingInventoryClient",
        fallbackFactory = ImsAdminBookingInventoryClientFallbackFactory.class
)
public interface ImsAdminBookingInventoryClient {


    @RpcShardIdSetter(argIndex = 0, argProperty = "toWarehouseId")
    @PostMapping({"/api/bookingInventory/notifyTransferOutEvent"})
    @ApiOperation("查询库存")
    ResponseBaseVo<Boolean> notifyTransferOutEvent(@RequestBody @Validated TransferOutChangeEventRequest transferOutChangeEventRequest);
}
