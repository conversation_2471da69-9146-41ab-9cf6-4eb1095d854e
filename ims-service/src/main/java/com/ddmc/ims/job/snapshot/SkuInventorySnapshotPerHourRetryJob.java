package com.ddmc.ims.job.snapshot;

import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.service.snapshot.SkuInventorySnapshotPerHourService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 逻辑库存快照，小时快照重试job
 */
@Slf4j
@Component
public class SkuInventorySnapshotPerHourRetryJob extends AbstractJobHandler {

    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;

    @Resource
    private SkuInventorySnapshotPerHourService snapshotPerHourService;

    @Resource
    private SkuInventorySnapshotPerHourMapper skuInventorySnapshotPerHourMapper;


    @XxlJob("skuInventorySnapshotPerHourRetryJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Date nowHourDate;
        if (StringUtils.isNotBlank(param)) {
            nowHourDate = ThreadLocalDateUtils.parseYmdhms(param);
        } else {
            nowHourDate = DateUtil.getNowMinusHourDate(1);
        }
        List<SnapshotTask> snapshotTasks = snapshotTaskMapper
            .selectBySnapshotTimeAndSnapshotTypeAndStatus(nowHourDate, SnapshotTypeEnum.INVENTORY_HOUR,
                SnapshotStatusEnum.PROCESSING);

        if (CollectionUtils.isEmpty(snapshotTasks)) {
            return ReturnT.SUCCESS;
        }

        snapshotTasks.forEach(t -> {
            skuInventorySnapshotPerHourMapper
                .deleteByWarehouseIdAndSnapshotDateTime(t.getWarehouseId(), t.getSnapshotTime());
            snapshotPerHourService.handlePerHourSnapshotTask(t);
        });
        return ReturnT.SUCCESS;
    }
}
