package com.ddmc.ims.job.delete;

import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.monitor.FmsSkuInventorySnapshotPerDayMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dto.WarehouseIdAndSnapshotDateDto;
import com.ddmc.ims.job.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeleteFmsSnapshotTaskJob extends AbstractJobHandler {

    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;

    @Resource
    private FmsSkuInventorySnapshotPerDayMapper fmsSkuInventorySnapshotPerDayMapper;
    @Resource
    private FmsSnapshotNotifyMapper fmsSnapshotNotifyMapper;
    @Resource
    private WarehouseMapper warehouseMapper;


    @XxlJob("deleteFmsSnapshotTaskJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("删除快照" + param);
        Date snapshotDate = DateUtils.truncate(DateUtils.addDays(CurrentDateUtil.newDate(), -1), Calendar.DATE);
        List<Long> warehouseIds = Collections.emptyList();
        if (StringUtils.isNotBlank(param)) {
            WarehouseIdAndSnapshotDateDto warehouseIdAndSnapshotDateDto = JsonUtil
                .fromJson(param, WarehouseIdAndSnapshotDateDto.class);
            assert warehouseIdAndSnapshotDateDto != null;
            snapshotDate = warehouseIdAndSnapshotDateDto.getSnapshotDate();
            assert snapshotDate != null;
            warehouseIds = warehouseIdAndSnapshotDateDto.getWarehouseIds();
        }
        if (CollectionUtils.isEmpty(warehouseIds)) {
            warehouseIds = warehouseMapper.selectAllId();
        }

        Date finalSnapshotDate = snapshotDate;
        warehouseIds.forEach(warehouseId -> {
            snapshotTaskMapper.deleteByWarehouseIdInAndSnapshotTimeAndSnapshotType(Collections.singleton(warehouseId),
                finalSnapshotDate, SnapshotTypeEnum.FMS);
            fmsSkuInventorySnapshotPerDayMapper.deleteByWarehouseAndSnapshotDate(warehouseId, finalSnapshotDate);
        });

        fmsSnapshotNotifyMapper.deleteByDayEndDate(snapshotDate);

        return ReturnT.SUCCESS;
    }


}


