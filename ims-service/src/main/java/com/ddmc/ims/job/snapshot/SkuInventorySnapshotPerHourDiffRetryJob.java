package com.ddmc.ims.job.snapshot;

import com.ddmc.ims.bo.snapshot.SnapshotWarehouseSkuItem;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.dal.condition.SnapshotFixCheckItem;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourDiffMapper;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHourDiff;
import com.ddmc.ims.job.AbstractJobHandler;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 逻辑库存快照比对修复重试job
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SkuInventorySnapshotPerHourDiffRetryJob extends AbstractJobHandler {

    @Resource
    private SkuInventorySnapshotPerHourDiffMapper skuInventorySnapshotPerHourDiffMapper;

    @Resource
    private SkuInventorySnapshotPerHourFixCheckJob skuInventorySnapshotPerHourFixCheckJob;

    @Resource
    private SkuInventorySnapshotPerHourDiffRetryJob selfProxy;


    @XxlJob("SkuInventorySnapshotPerHourDiffRetryJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        List<Long> warehouseIds;
        if (StringUtils.isNotEmpty(param)) {
            warehouseIds = Arrays.stream(param.split(",")).map(Long::valueOf).distinct().collect(Collectors.toList());
        } else {
            warehouseIds = skuInventorySnapshotPerHourDiffMapper.selectDistinctWarehouseId();
        }

        AtomicBoolean needAlert = new AtomicBoolean(false);
        warehouseIds.forEach(warehouseId -> {
            List<SkuInventorySnapshotPerHourDiff> diffs = skuInventorySnapshotPerHourDiffMapper
                .selectByWarehouseId(warehouseId);

            if (CollectionUtils.isEmpty(diffs)) {
                return;
            }
            SkuInventorySnapshotPerHourDiff diff = diffs.get(0);
            needAlert.set(diff.getTriggerCount() > 5);
            List<Long> skuIds = diffs.stream().map(SkuInventorySnapshotPerHourDiff::getSkuId).distinct()
                .collect(Collectors.toList());
            SnapshotWarehouseSkuItem item = new SnapshotWarehouseSkuItem(warehouseId, skuIds);

            if (isNeedSnapshotRetryFixData()) {
                skuInventorySnapshotPerHourFixCheckJob
                    .handleSingleInventoryItem(diff.getCredentialStartDate(),
                        skuInventorySnapshotPerHourFixCheckJob.updateOrInsert(), item);
            }
            List<SnapshotFixCheckItem> currentDiff = skuInventorySnapshotPerHourFixCheckJob
                .getSnapshotFixCheckItems(diff.getCredentialStartDate(), item);
            selfProxy.saveDiff(diff, currentDiff);

        });

        return ReturnT.SUCCESS;
    }


    private boolean isNeedSnapshotRetryFixData() {
        return localParamService.getBooleanValue("ims.isNeedSnapshotRetryFixData", true);
    }

    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void saveDiff(SkuInventorySnapshotPerHourDiff diff, List<SnapshotFixCheckItem> currentDiffs) {
        skuInventorySnapshotPerHourDiffMapper.deleteByWarehouseId(diff.getWarehouseId());
        if (CollectionUtils.isEmpty(currentDiffs)) {
            return;
        }
        List<SkuInventorySnapshotPerHourDiff> diffs = Lists.newArrayListWithExpectedSize(currentDiffs.size());
        currentDiffs.forEach(currentDiff -> {
            SkuInventorySnapshotPerHourDiff item = new SkuInventorySnapshotPerHourDiff();
            item.setSkuId(currentDiff.getSkuId());
            item.setWarehouseId(currentDiff.getWarehouseId());
            item.setLogicLocationCode(currentDiff.getLogicInventoryLocationCode());
            item.setCargoOwnerId(currentDiff.getCargoOwnerId());
            item.setLotId(currentDiff.getLotId());
            item.setSnapshotId(Objects.isNull(currentDiff.getPerHourId()) ? 0L : currentDiff.getPerHourId());
            item.setSnapshotQty(currentDiff.getSnapShotQty());
            item.setCredentialQty(currentDiff.getCredentialQty());
            item.setInventoryQty(currentDiff.getInventoryQty());
            item.setTriggerCount(diff.getTriggerCount() + 1);
            item.setCredentialStartDate(diff.getCredentialStartDate());
            item.setCredentialEndDate(diff.getCredentialEndDate());
            diffs.add(item);
        });

        Lists.partition(diffs, CommonConstants.BATCH_INSERT_DB_100)
            .forEach(subList -> skuInventorySnapshotPerHourDiffMapper.insertList(subList));
    }

}
