package com.ddmc.ims.job.event;

import com.ddmc.ims.common.constant.EventBusConstants;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.event.EventService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Event事件自动补偿执行（查询出30分钟还未处理的事件）
 */
@Component
@Slf4j
public class EventAutoExecutorJob extends AbstractJobHandler {

    @Resource
    private EventService eventService;
    @Resource
    private DbEventStorage dbEventStorage;
    @Resource
    private AlertService alertService;
    @Value("${eventBus.auto.maxCount:5}")
    private Integer autoMaxCount;
    @Value("${eventAutoExecutorEndTime:30}")
    private Integer eventAutoExecutorEndTime;

    @Value("${EventAutoExecutorJob.limit:500}")
    private Integer eventAutoExecutorJobLimit;

    @XxlJob("EventAutoExecutorJob")
    @Override
    public ReturnT<String> execute(String param) {
        //查询出所有未处理的事件
        ListEventCondition condition = new ListEventCondition();
        condition.setCreateTimeEnd(DateUtils.addMinutes(CurrentDateUtil.newDate(), -eventAutoExecutorEndTime));
        condition.setStatusList(EventBusConstants.WAIT_STATUS_LIST);
        condition.setTriggerCountEnd(autoMaxCount);
        condition.setLimit(eventAutoExecutorJobLimit);
        List<EventEntity> eventEntityList = eventService.listByCondition(condition);
        if (Objects.isNull(eventEntityList)) {
            eventEntityList = Collections.emptyList();
        }
        //过滤延时队列
        eventEntityList = eventEntityList.stream().filter(o -> !o.isDelayEvent()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(eventEntityList)) {
            return ReturnT.SUCCESS;
        }
        //查询出所有未处理的父事件
        Set<Long> parentEventIdList = eventEntityList.stream().map(EventEntity::getParentId)
            .collect(Collectors.toSet());
        ListEventCondition condition2 = new ListEventCondition();
        condition2.setEventIdList(parentEventIdList);
        condition2.setStatusList(EventBusConstants.WAIT_STATUS_LIST);
        List<EventEntity> waitConsumedParentList = eventService.listByCondition(condition2);
        List<EventEntity> needEventEntityList = eventEntityList;
        if (CollectionUtils.isNotEmpty(waitConsumedParentList)) {
            //排除父事件未处理的子事件
            Set<Long> waitConsumedParentIds = waitConsumedParentList.stream().map(EventEntity::getId)
                .collect(Collectors.toSet());
            needEventEntityList = eventEntityList.stream()
                .filter(o -> !waitConsumedParentIds.contains(o.getParentId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(needEventEntityList)) {
            log.info("EventAutoExecutorJob 排除父事件未处理的子事件后无事件需要处理, eventEntityList={},waitConsumedParentList={}",
                JsonUtils.toJson(eventEntityList), JsonUtils.toJson(waitConsumedParentList));
            alertService.alertWarning("EventAutoExecutorJob 排除父事件未处理的子事件后无事件需要处理", "请尽快排查");
            return ReturnT.FAIL;
        }
        //执行事件
        dbEventStorage.executeEvent(eventEntityList);
        return ReturnT.SUCCESS;
    }
}
