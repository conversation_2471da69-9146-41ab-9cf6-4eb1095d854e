package com.ddmc.ims.job;

import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import java.lang.reflect.Method;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractJobHandler extends IJobHandler {

    @Resource
    private AlertService alertService;

    @Resource
    protected LocalParamService localParamService;

    protected String jobName;

    protected void alert(String message, AlertLevel level) {
        alertService.alert("定时任务[ " + getJobName() + "]", message, level);
    }

    /**
     * @return 当前执行的job的名称
     */
    protected String getJobName() {
        if (jobName != null) {
            return jobName;
        }
        try {
            Method m = this.getClass().getMethod("execute", String.class);
            jobName = m.getAnnotation(XxlJob.class).value();
        } catch (Exception e) {
            log.error("getJobName error", e);
            jobName = "unknown";
        }
        return jobName;
    }


    /**
     * 获取该节点分片后的仓库id列表
     *
     * @return result
     */
    protected List<Long> getShardingWarehouseIds(List<Long> warehouseIds) {
        // 分片参数
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(), shardingVO.getTotal());
        log.info("[" + getJobName() + "] 分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(),
            shardingVO.getTotal());
        return warehouseIds.stream().filter(t -> t % shardingVO.getTotal() == shardingVO.getIndex())
            .collect(Collectors.toList());
    }
}
