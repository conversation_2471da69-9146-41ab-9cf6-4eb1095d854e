package com.ddmc.ims.job.fms;

import com.ddmc.fms.inv.enums.DsStatus;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.SystemTypeEnum;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.monitor.FmsSkuInventorySnapshotPerDayMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.WarehouseIdAndSnapshotDateDto;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.FmsSnapshotNotifyService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class FmsSnapshotPerDayRetryJob extends AbstractJobHandler {


    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;

    @Resource
    private FmsSnapshotNotifyMapper fmsSnapshotNotifyMapper;

    @Resource
    private AlertService alertService;

    @Resource
    private WarehouseMapper warehouseMapper;
    @Resource
    private FmsSnapshotNotifyService fmsSnapshotNotifyService;
    @Resource
    private FmsSkuInventorySnapshotPerDayMapper fmsSkuInventorySnapshotPerDayMapper;
    @Resource
    private FmsSnapshotPerDayJob fmsSnapshotPerDayJob;

    @XxlJob("fmsSnapshotPerDayRetryJob")
    @Override
    public ReturnT<String> execute(String param) {

        WarehouseIdAndSnapshotDateDto dto = JsonUtil
            .fromJson(param, WarehouseIdAndSnapshotDateDto.class);
        if (Objects.isNull(dto)) {
            return ReturnT.SUCCESS;
        }
        FmsSnapshotNotify fmsSnapshotNotify = fmsSnapshotNotifyMapper.selectByDayEndDate(dto.getSnapshotDate());
        if (Objects.nonNull(fmsSnapshotNotify)) {
            fmsSnapshotNotify.setStatus(DsStatus.SYNC_SUCC.getValue());
        } else {
            fmsSnapshotNotify = new FmsSnapshotNotify();
            fmsSnapshotNotify.setDayEndDate(dto.getSnapshotDate());
            fmsSnapshotNotify.setSource(SystemTypeEnum.SYSTEM.getDesc());
            fmsSnapshotNotify.setStatus(DsStatus.RECON_READY.getValue());
            fmsSnapshotNotifyMapper.insert(fmsSnapshotNotify);
        }
        dto.getWarehouseIds().forEach(warehouseId -> fmsSkuInventorySnapshotPerDayMapper
            .deleteByWarehouseAndSnapshotDate(warehouseId, dto.getSnapshotDate()));

        snapshotTaskMapper
            .deleteByWarehouseIdInAndSnapshotTimeAndSnapshotType(dto.getWarehouseIds(), dto.getSnapshotDate(),
                SnapshotTypeEnum.FMS);
        List<SnapshotTask> taskList = dto.getWarehouseIds().stream()
            .map(warehouseId -> buildTask(warehouseId, dto.getSnapshotDate())).collect(Collectors.toList());
        Lists.partition(taskList, CommonConstants.BATCH_INSERT_DB_100).forEach(snapshotTaskMapper::batchInsert);
        fmsSnapshotPerDayJob.processSnapshotTasks(taskList);
        return ReturnT.SUCCESS;
    }

    private SnapshotTask buildTask(Long warehouseId, Date snapshotDate) {
        SnapshotTask task = new SnapshotTask();
        task.setSnapshotType(SnapshotTypeEnum.FMS);
        task.setSnapshotTime(snapshotDate);
        task.setStatus(SnapshotStatusEnum.INIT);
        task.setWarehouseId(warehouseId);
        return task;
    }


}
