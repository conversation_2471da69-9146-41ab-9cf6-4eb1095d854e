package com.ddmc.ims.job;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.config.RateLimiterConfig;
import com.ddmc.ims.dal.mapper.ims.InventoryLotInfoMapper;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.manager.lot.LotManager;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Slf4j
@Component
public class InitLotScmLotNoJob extends AbstractJobHandler{

    @Resource
    private AlertService alertService;

    @Resource(name = "lotInitExecutor")
    private ExecutorService executorService;

    @Resource
    private LotManager lotManager;
    @Resource
    private RateLimiterConfig rateLimiterConfig;

    @Resource
    private InventoryLotInfoMapper inventoryLotInfoMapper;

    @Value("${ims.InitScmLotNoJob.stop:false}")
    private boolean stop;

    @Value("#{${ims.InitScmLotNoJob.lastPrimaryKeyIdMaps:{}}}")
    private Map<Integer, Long> map;

    @Value("#{'${ims.InitScmLotNoJob.shardingTableIndexList:0}'.split(',')}")
    private List<Integer> shardingTableIndexList;

    @XxlJob("InitScmLotNoJob")
    @Override
    public ReturnT<String> execute(String param) {
        log.info("[InitScmLotNoJob] param -> {}", param);
        //查询远程的仓库信息
        List<Future<?>> futures = shardingTableIndexList.stream()
            .map(t -> executorService.submit(() -> intLotByShardingTableIndex(t))).collect(Collectors.toList());

        futures.forEach(f -> {
            try {
                f.get();
            } catch (InterruptedException | ExecutionException interruptedException) {
                log.error("[InitLotJob] 初始化批次信息异常", interruptedException);
                alertService.alert("初始化批次信息异常",
                    "异常信息" + interruptedException.getMessage(), AlertLevel.WARNING);
                Thread.currentThread().interrupt();
            }
        });
        log.info("[InitScmLotNoJob]完成");
        return ReturnT.SUCCESS;
    }

    private void intLotByShardingTableIndex(Integer shardingTableIndex) {
        Long lastPrimaryKeyId = map.getOrDefault(shardingTableIndex, 0L);
        List<InventoryLotInfo> inventoryLotInfo = inventoryLotInfoMapper.getInventoryLotInfoByTableIndex(shardingTableIndex.longValue(),
            lastPrimaryKeyId, 1000);
        while (CollectionUtils.isNotEmpty(inventoryLotInfo)) {
            List<String> lotIds = inventoryLotInfo.stream().map(InventoryLotInfo::getLotId).distinct().collect(Collectors.toList());

            List<InventoryLotInfo> remoteLotInfo = Collections.emptyList();
            try {
                remoteLotInfo = Lists.partition(lotIds, CommonConstants.BATCH_SELECT_DB_200).stream()
                    .map(li -> lotManager.batchGet(li, false)).flatMap(List::stream).collect(Collectors.toList());
            } catch (Exception e) {
                log.warn("查询批次失败，跳过当前批次", e);
            }

            Map<String, String> scmLotNoMap = remoteLotInfo.stream().collect(Collectors.toMap(InventoryLotInfo::getLotId, InventoryLotInfo::getScmLotNo));
            //开启下次查询
            List<InventoryLotInfo> hasScmLotNo = Lists.newArrayList();
            List<InventoryLotInfo> noScmLotNo = Lists.newArrayList();
            inventoryLotInfo.forEach(lot -> {
                if (scmLotNoMap.containsKey(lot.getLotId())) {
                    lot.setScmLotNo(scmLotNoMap.get(lot.getLotId()));
                    hasScmLotNo.add(lot);
                } else {
                    noScmLotNo.add(lot);
                }
            });
            if (CollectionUtils.isNotEmpty(hasScmLotNo)) {
                Lists.partition(hasScmLotNo, CommonConstants.BATCH_UPDATE_DB_300)
                    .forEach(in -> inventoryLotInfoMapper.batchUpdateByTableIndex(shardingTableIndex.longValue(), in));
            }
            if (CollectionUtils.isNotEmpty(noScmLotNo)) {
                log.warn("[InitScmLotNoJob]批次查询不到scmLotNo->{}", noScmLotNo.stream().map(InventoryLotInfo::getLotId).collect(Collectors.toList()));
            }
            lastPrimaryKeyId = inventoryLotInfo.get(inventoryLotInfo.size() - 1).getId();
            inventoryLotInfo = inventoryLotInfoMapper.getInventoryLotInfoByTableIndex(shardingTableIndex.longValue(),
                lastPrimaryKeyId, 1000);

            if (stop) {
                return;
            }
            //控制qps
            rateLimiterConfig.getRateLimiterByKey(RateLimiterConfig.INIT_LOT_INFO_LIMIT).acquire();
        }
    }
}
