package com.ddmc.ims.job.delete;

import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 逻辑库存InOut快照删除job
 */
@Slf4j
@Component
public class DeleteSkuInventoryInOutSnapshotJob extends AbstractDeleteJobHandler {

    @Resource
    private SkuInventoryInOutSnapshotMapper skuInventoryInOutSnapshotMapper;

    @XxlJob("DeleteSkuInventoryInOutSnapshotJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        handleDelete("DeleteSkuInventoryInOutSnapshotJob");
        return ReturnT.SUCCESS;
    }


    @Override
    protected Long getMaxId() {
        Long configMaxId = localParamService.getLongValue("ims.DeleteSkuInventoryInOutSnapshotJobMaxId");
        if (Objects.nonNull(configMaxId)) {
            return configMaxId;
        }

        return skuInventoryInOutSnapshotMapper.selectOneIdByCreateTimeBefore(getMaxDeleteDate());

    }


    private Date getMaxDeleteDate() {
        Integer intValue = localParamService
            .getIntValue("ims.DeleteSkuInventoryInOutSnapshotJobDeleteTimeInterval", 15);
        return DateUtil.getPlusStartOfDay(CurrentDateUtil.newDate(), -intValue);
    }

    @Override
    protected boolean getInterrupt() {
        return localParamService.getBooleanValue("ims.DeleteSkuInventoryInOutSnapshotJobInterrupt", false);
    }

    @Override
    protected Long getSleep() {
        return localParamService.getLongValue("ims.DeleteSkuInventoryInOutSnapshotJobSleep", 10L);
    }

    @Override
    protected Long deleteByMaxId(Long maxId) {
        return skuInventoryInOutSnapshotMapper.deleteByMaxId(maxId);
    }

}
