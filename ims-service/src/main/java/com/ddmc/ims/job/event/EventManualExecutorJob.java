package com.ddmc.ims.job.event;

import com.ddmc.ims.common.constant.EventBusConstants;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.event.EventService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * Event事件手动执行
 */
@Component
@Slf4j
public class EventManualExecutorJob extends AbstractJobHandler {

    @Resource
    private EventService eventService;
    @Resource
    private DbEventStorage dbEventStorage;
    @Resource
    private AlertService alertService;

    @XxlJob("EventManualExecutorJob")
    @Override
    public ReturnT<String> execute(String param) {
        List<Long> eventIdList = JsonUtils.parseList(param, Long.class);
        ListEventCondition condition = new ListEventCondition();
        condition.setEventIdList(eventIdList);
        condition.setStatusList(EventBusConstants.WAIT_STATUS_LIST);
        List<EventEntity> eventEntityList = eventService.listByCondition(condition);
        if (CollectionUtils.isEmpty(eventEntityList)) {
            log.info("EventManualExecutorJob 无需要处理的事件,param={}", param);
            return ReturnT.SUCCESS;
        }
        //判断是否有未执行的父类事件
        Set<Long> parentEventIdList = eventEntityList.stream().map(EventEntity::getParentId)
            .collect(Collectors.toSet());
        condition.setEventIdList(parentEventIdList);
        condition.setStatusList(EventBusConstants.WAIT_STATUS_LIST);
        List<EventEntity> waitConsumedParentList = eventService.listByCondition(condition);
        if (CollectionUtils.isNotEmpty(waitConsumedParentList)) {
            String waitConsumedParentIds = JsonUtils
                .toJson(waitConsumedParentList.stream().map(EventEntity::getId).collect(Collectors.toList()));
            log.info("EventManualExecutorJob 存在未处理父类事件,param={},waitConsumedParentIds={}"
                , param, waitConsumedParentIds);
            alertService.alertWarning("EventManualExecutorJob 存在未处理父类事件", waitConsumedParentIds);
            return ReturnT.FAIL;
        }
        dbEventStorage.executeEvent(eventEntityList);
        return ReturnT.SUCCESS;
    }

}
