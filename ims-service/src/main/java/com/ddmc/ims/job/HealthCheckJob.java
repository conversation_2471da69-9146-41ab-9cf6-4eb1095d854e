package com.ddmc.ims.job;

import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.config.SpringProfileConfig;
import com.ddmc.ims.config.redis.RedisConfig;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 验证job是否在正常触发。超过1分钟未触发则告警
 */
@Slf4j
@Component
public class HealthCheckJob extends AbstractJobHandler {

    @Resource
    private RedisTemplate<String, Long> redisTemplate;

    @Resource
    private SpringProfileConfig profileConfig;

    private static final String REDIS_KEY = RedisConfig.PREFIX + "job:HealthCheckJob";
    private static final long REDIS_CACHE_DAY = 1;

    //每两分钟检测一次
    private static final long CHECK_PERIOD_MILLIS = 2 * 60 * 1000L;

    @PostConstruct
    public void postConstruct() {
        if (profileConfig.isPe()) {
            Timer timer = new Timer(true);
            long delayMillis = 60 * 1000L;
            timer.schedule(new MyTimerTask(), delayMillis, CHECK_PERIOD_MILLIS);
        }
    }

    /**
     * 检测最后一次触发时间，如果超过2分钟未触发则发送告警。
     */
    public class MyTimerTask extends TimerTask {

        public void run() {
            Long lastRunAt = redisTemplate.opsForValue().get(REDIS_KEY);
            if (lastRunAt == null) {
                redisTemplate.opsForValue()
                    .setIfAbsent(REDIS_KEY, CurrentDateUtil.currentTimeMillis(), REDIS_CACHE_DAY, TimeUnit.DAYS);
                return;
            }
            if (CurrentDateUtil.currentTimeMillis() - lastRunAt > CHECK_PERIOD_MILLIS) {
                alert("定时任务未正常触发，请检查xxl-job服务", AlertLevel.WARNING);
            }
        }
    }

    /**
     * 每分钟触发一次，记录最后触发时间
     */
    @XxlJob("HealthCheckJob")
    @Override
    public ReturnT<String> execute(String param) {
        redisTemplate.opsForValue().set(REDIS_KEY, CurrentDateUtil.currentTimeMillis(), REDIS_CACHE_DAY, TimeUnit.DAYS);
        return ReturnT.SUCCESS;
    }
}
