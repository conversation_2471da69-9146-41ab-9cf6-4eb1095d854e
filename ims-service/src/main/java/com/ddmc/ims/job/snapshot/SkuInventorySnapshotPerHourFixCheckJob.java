package com.ddmc.ims.job.snapshot;

import com.ddmc.ims.bo.snapshot.SnapshotItem;
import com.ddmc.ims.bo.snapshot.SnapshotWarehouseSkuItem;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.SnapshotConverter;
import com.ddmc.ims.dal.condition.SnapshotFixCheckItem;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuLotInventoryMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourDiffMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHourDiff;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.springframework.stereotype.Component;

/**
 * 逻辑库存快照比对修复job
 *
 * 灰度分两个Job执行期
 * 1：SkuInventorySnapshotPerHourFixCheckJob：逻辑库存快照比对修复job自动分片
 * 2：SkuInventorySnapshotPerHourFixCheckJob：逻辑库存快照比对修复job手动修复
 *
 * 开关检查项：
 * 1:ims.snapshotPerHourFixCheckInterrupt。生产为false关闭。如果在执行过程中出现问题设置为true。
 * 2:ims.snapshotPerHourFixCheckWarehouseId。指定执行的仓库id，不指定则为全仓分片
 * 3:ims.snapshotPerHourFixCheckStartDate。快照时间与凭证检查的开始时间，不指定则为当天零点快照
 * 4:ims.snapshotPerHourFixCheckEndDate。凭证检查截止时间，不指定则为当天最晚时间
 * 5:ims.snapshotPerHourFixEnable。是否开启修复，不开启则保存到SkuInventorySnapshotPerHourDiff表，由SkuInventorySnapshotPerHourDiffRetryJob重试
 * 6:ims.snapshotPerHourFixUpdate。在5开启后，则要再次检查该次修改为更新还是做插入。true为更新，fasle为插入
 * 7:ims.snapshotPerHourFixWarehouseAndSku。指定需要执行的仓与品
 *
 *
 * 分片自动执行job速查：
 * 1.ims.snapshotPerHourFixCheckInterrupt = false
 * 2.ims.snapshotPerHourFixCheckWarehouseId =
 * 3.ims.snapshotPerHourFixCheckStartDate =
 * 4.ims.snapshotPerHourFixCheckEndDate =
 * 5.ims.snapshotPerHourFixEnable = false
 * 6.ims.snapshotPerHourFixUpdate = true
 * 7.ims.snapshotPerHourFixWarehouseAndSku =
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SkuInventorySnapshotPerHourFixCheckJob extends AbstractJobHandler {

    @Resource
    private WarehouseSkuLotInventoryMapper warehouseSkuLotInventoryMapper;

    @Resource
    private CredentialWrapperManager credentialWrapperManager;

    @Resource
    private SkuInventorySnapshotPerHourMapper skuInventorySnapshotPerHourMapper;

    @Resource
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;

    @Resource
    private CommandManager commandManager;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private SkuInventorySnapshotPerHourDiffMapper skuInventorySnapshotPerHourDiffMapper;

    @Resource
    private LocalParamConfig localParamConfig;


    private boolean getInterrupt() {
        return localParamService.getBooleanValue("ims.snapshotPerHourFixCheckInterrupt", false);
    }


    private List<Long> getFixWarehouseId() {
        List<Long> fixWarehouseIds = localParamService
            .getLongListValue("ims.snapshotPerHourFixCheckWarehouseId", Collections.emptyList());

        if (CollectionUtils.isEmpty(fixWarehouseIds)) {
            // 分片参数
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();

            if (Objects.isNull(shardingVO)) {
                return Collections.emptyList();
            }
            XxlJobLogger.log("分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(), shardingVO.getTotal());
            log.info("[SkuInventorySnapshotPerHourFixCheckJob] 分片参数：当前分片序号 = {}, 总分片数 = {}",
                shardingVO.getIndex(),
                shardingVO.getTotal());
            return warehouseService.getAllWarehouseIds().stream()
                .filter(t -> t % shardingVO.getTotal() == shardingVO.getIndex())
                .collect(Collectors.toList());
        }

        return fixWarehouseIds;
    }

    private Date getFixCheckStartDate() {

        String startDateStr = localParamService.getStringValue("ims.snapshotPerHourFixCheckStartDate");
        if (StringUtils.isNotBlank(startDateStr)) {
            return ThreadLocalDateUtils.parseYmdhms(startDateStr);
        }
        return DateUtil.getPlusStartOfDay(CurrentDateUtil.newDate(), 0);
    }

    private boolean enableFix() {
        return localParamService.getBooleanValue("ims.snapshotPerHourFixEnable", false);
    }


    public boolean updateOrInsert() {
        return localParamService.getBooleanValue("ims.snapshotPerHourFixUpdate", false);
    }


    private String getWarehouseAndSku() {
        return localParamService.getStringValue("ims.snapshotPerHourFixWarehouseAndSku", "");
    }


    @XxlJob("SkuInventorySnapshotPerHourFixCheckJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {

        List<SnapshotWarehouseSkuItem> items = new ArrayList<>();
        if (StringUtils.isBlank(param)) {
            param = getWarehouseAndSku();
        }
        if (StringUtils.isNotBlank(param)) {
            items = JsonUtil
                .fromJson(param, new TypeReference<List<SnapshotWarehouseSkuItem>>() {
                });
        } else {
            List<Long> fixWarehouseId = getFixWarehouseId();
            List<SnapshotWarehouseSkuItem> finalItems = items;
            fixWarehouseId.forEach(w -> {
                SnapshotWarehouseSkuItem item = new SnapshotWarehouseSkuItem();
                item.setWarehouseId(w);
                item.setSkuIds(new ArrayList<>());
                finalItems.add(item);
            });
        }

        if (CollectionUtils.isEmpty(items)) {
            return ReturnT.FAIL;
        }

        log.info("[SkuInventorySnapshotPerHourFixCheckJob] items:{}", JsonUtil.toJson(items));
        Date fixCheckStartDate = getFixCheckStartDate();
        boolean updateOrInsert = updateOrInsert();
        List<SnapshotWarehouseSkuItem> responseList = Lists.newArrayList();
        //仓+sku
        items.forEach(
            item -> {
                if (CollectionUtils.isEmpty(item.getSkuIds())) {
                    List<Long> skuIds = warehouseSkuInventoryMapper.selectSkuIdByWarehouseId(item.getWarehouseId());
                    if (CollectionUtils.isEmpty(skuIds)) {
                        return;
                    }
                    item.setSkuIds(skuIds);
                }
                List<SnapshotWarehouseSkuItem> snapshotWarehouseSkuItems = handleSingleInventoryItem(fixCheckStartDate,
                    updateOrInsert, item);
                if (CollectionUtils.isNotEmpty(snapshotWarehouseSkuItems)) {
                    responseList.addAll(snapshotWarehouseSkuItems);
                }
            });
        log.info("[SkuInventorySnapshotPerHourFixCheckJob] [比对结果] {}", JsonUtil.toJson(responseList));
        log.info("[SkuInventorySnapshotPerHourFixCheckJob] 比对结束");

        return ReturnT.SUCCESS;

    }

    public List<SnapshotWarehouseSkuItem> handleSingleInventoryItem(Date fixCheckStartDate,
        boolean updateOrInsert, SnapshotWarehouseSkuItem item) {
        log.info("[SkuInventorySnapshotPerHourFixCheckJob] start item:{}", JsonUtil.toJson(item));
        //快照数量
        Map<SnapshotItem, SkuInventorySnapshotPerHour> perHourMap = getSnapshotItemSkuInventorySnapshotPerHourMap(
            fixCheckStartDate, item);

        Date fixCheckEndDate = CurrentDateUtil.newDate();

        List<SnapshotFixCheckItem> filterItems = getSnapshotFixCheckItems(
            fixCheckStartDate, fixCheckEndDate, item, perHourMap);

        if (CollectionUtils.isNotEmpty(filterItems)) {
            fixOrSaveDiff(fixCheckStartDate, fixCheckEndDate, updateOrInsert, perHourMap, filterItems);
        }

        List<SnapshotWarehouseSkuItem> responseList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(filterItems)) {
            Map<Long, Set<Long>> warehouseSkuMap = filterItems.stream().collect(Collectors
                .groupingBy(SnapshotFixCheckItem::getWarehouseId,
                    Collectors.mapping(SnapshotFixCheckItem::getSkuId, Collectors.toSet())));
            warehouseSkuMap.forEach((k, v) -> {
                if (CollectionUtils.isNotEmpty(v)) {
                    SnapshotWarehouseSkuItem item1 = new SnapshotWarehouseSkuItem(k, Lists.newArrayList(v));
                    responseList.add(item1);
                }
            });
        }
        return responseList;
    }


    public List<SnapshotFixCheckItem> getSnapshotFixCheckItems(Date fixCheckStartDate,
        SnapshotWarehouseSkuItem item) {
        //快照数量
        Map<SnapshotItem, SkuInventorySnapshotPerHour> perHourMap = getSnapshotItemSkuInventorySnapshotPerHourMap(
            fixCheckStartDate, item);

        Date fixCheckEndDate = CurrentDateUtil.newDate();

        return getSnapshotFixCheckItems(fixCheckStartDate, fixCheckEndDate, item, perHourMap);
    }

    private List<SnapshotFixCheckItem> getSnapshotFixCheckItems(Date fixCheckStartDate, Date fixCheckEndDate,
        SnapshotWarehouseSkuItem item, Map<SnapshotItem, SkuInventorySnapshotPerHour> perHourMap) {

        //仓库+sku+lot维度的库存信息
        List<WarehouseSkuLotInventory> warehouseSkuLotInventories = Lists
            .partition(item.getSkuIds(), CommonConstants.BATCH_SELECT_DB_200).stream()
            .map(t -> getWarehouseSkuLotInventories(item.getWarehouseId(), t)).flatMap(List::stream).collect(
                Collectors.toList());
        //仓库凭证信息
        List<CredentialHeader> credentialHeaders = credentialWrapperManager
            .getCredentialHeaders(item.getWarehouseId(), fixCheckStartDate, fixCheckEndDate);

        List<SnapshotFixCheckItem> snapshotFixCheckItems = new ArrayList<>();

        //在批次库存修改之前的凭证信息
        Map<SnapshotItem, CommandInventoryNumDto> commandInventoryNumDtoMap = getSnapshotItemCommandInventoryNumDtoMap(
            credentialHeaders);
        Map<SnapshotItem, BigDecimal> warehouseSkuLotInventoryMap = warehouseSkuLotInventories.stream()
            .collect(Collectors.toMap(this::getSnapShotItem, WarehouseSkuLotInventory::getFreeQty, (k1, k2) -> k1));

        Set<SnapshotItem> allSnapshotItemSet = Sets.newHashSetWithExpectedSize(
            perHourMap.size() + commandInventoryNumDtoMap.size() + warehouseSkuLotInventoryMap.size());
        allSnapshotItemSet.addAll(perHourMap.keySet());
        allSnapshotItemSet.addAll(warehouseSkuLotInventoryMap.keySet());
        allSnapshotItemSet.forEach(snapShotItem -> handleSingleInventory(snapShotItem,
            warehouseSkuLotInventoryMap.getOrDefault(snapShotItem, BigDecimal.ZERO),
            snapshotFixCheckItems, perHourMap.get(snapShotItem), commandInventoryNumDtoMap.get(snapShotItem)));

        return snapshotFixCheckItems.stream().filter(t ->
            t.getInventoryQty().signum() != 0 || t.getCredentialQty().signum() != 0
                || t.getSnapShotQty().signum() != 0).collect(Collectors.toList());
    }

    private void fixOrSaveDiff(Date fixCheckStartDate, Date fixCheckEndDate, boolean updateOrInsert,
        Map<SnapshotItem, SkuInventorySnapshotPerHour> perHourMap, List<SnapshotFixCheckItem> filterItems) {
        filterItems.forEach(t -> t.setFixQty(t.getInventoryQty().subtract(t.getCredentialQty())));
        log.info("[SkuInventorySnapshotPerHourFixCheckJob] [存在差异] snapshotFixCheckItems:{}",
            JsonUtil.toJson(filterItems));
        if (enableFix()) {
            fixPerHourData(fixCheckStartDate, updateOrInsert, perHourMap, filterItems);
        } else {
            saveFixCheckDiff(fixCheckStartDate, fixCheckEndDate, filterItems);
        }
    }


    private void saveFixCheckDiff(Date fixCheckStartDate, Date fixCheckEndDate,
        List<SnapshotFixCheckItem> fixCheckItems) {
        if (CollectionUtils.isEmpty(fixCheckItems)) {
            return;
        }
        List<SkuInventorySnapshotPerHourDiff> diffs = Lists.newArrayListWithExpectedSize(fixCheckItems.size());
        fixCheckItems.forEach(t -> {
            SkuInventorySnapshotPerHourDiff diff = new SkuInventorySnapshotPerHourDiff();
            diff.setSkuId(t.getSkuId());
            diff.setWarehouseId(t.getWarehouseId());
            diff.setLogicLocationCode(t.getLogicInventoryLocationCode());
            diff.setCargoOwnerId(t.getCargoOwnerId());
            diff.setLotId(t.getLotId());
            diff.setSnapshotId(Objects.isNull(t.getPerHourId()) ? 0L : t.getPerHourId());
            diff.setSnapshotQty(t.getSnapShotQty());
            diff.setCredentialQty(t.getCredentialQty());
            diff.setInventoryQty(t.getInventoryQty());
            diff.setTriggerCount(0);
            diff.setCredentialStartDate(fixCheckStartDate);
            diff.setCredentialEndDate(fixCheckEndDate);
            diffs.add(diff);
        });

        Lists.partition(diffs, CommonConstants.BATCH_INSERT_DB_100)
            .forEach(subList -> skuInventorySnapshotPerHourDiffMapper.insertList(subList));
    }

    private void fixPerHourData(Date fixCheckStartDate, boolean updateOrInsert,
        Map<SnapshotItem, SkuInventorySnapshotPerHour> perHourMap, List<SnapshotFixCheckItem> filterItems) {
        if (updateOrInsert) {
            filterItems = filterItems.stream().filter(t -> Objects.nonNull(t.getPerHourId()))
                .collect(Collectors.toList());
            Lists.partition(filterItems, CommonConstants.BATCH_UPDATE_DB_100).forEach(
                subList -> skuInventorySnapshotPerHourMapper.updateByFixItem(subList, fixCheckStartDate));
        } else {
            List<SkuInventorySnapshotPerHour> needInsertList = new ArrayList<>();
            filterItems.forEach(t -> {
                SnapshotItem snapShotItem = getSnapShotItem(t);
                SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = perHourMap.get(snapShotItem);
                if (Objects.isNull(skuInventorySnapshotPerHour)) {
                    SkuInventorySnapshotPerHour newPerHour = new SkuInventorySnapshotPerHour();
                    newPerHour.setSkuId(snapShotItem.getSkuId());
                    newPerHour.setWarehouseId(snapShotItem.getWarehouseId());
                    newPerHour.setLogicLocationCode(snapShotItem.getLogicLocationCode());
                    newPerHour.setCargoOwnerId(snapShotItem.getCargoOwnerId());
                    newPerHour.setLotId(snapShotItem.getLotId());
                    newPerHour.setFreeQty(t.getFixQty());
                    newPerHour.setFrozenQty(new BigDecimal("0"));
                    newPerHour.setTransferIntransitQty(new BigDecimal("0"));
                    newPerHour.setSnapshotDateTime(fixCheckStartDate);
                    needInsertList.add(newPerHour);
                }
            });

            if (CollectionUtils.isNotEmpty(needInsertList)) {
                Lists.partition(needInsertList, CommonConstants.BATCH_INSERT_DB_100)
                    .forEach(subList -> skuInventorySnapshotPerHourMapper.insertList(subList));
            }
        }
    }


    private void handleSingleInventory(SnapshotItem snapshotItem, BigDecimal inventoryQty,
        List<SnapshotFixCheckItem> snapshotFixCheckItems, SkuInventorySnapshotPerHour perHour,
        CommandInventoryNumDto command) {
        BigDecimal commandQty = Objects.isNull(command) ? BigDecimal.ZERO : command.getFreeQty();
        BigDecimal perHourQty = Objects.isNull(perHour) ? BigDecimal.ZERO : perHour.getFreeQty();

        if (perHourQty.add(commandQty).compareTo(inventoryQty) != 0) {
            log.info(
                "[SkuInventorySnapshotPerHourFixCheckJob] [差异] [批次库存差异] warehouseId:{},skuId:{},lotId:{},credentialQty:{},snapshotQty:{},inventoryQty:{},snapshotItem{}",
                snapshotItem.getWarehouseId(), snapshotItem.getSkuId(),
                snapshotItem.getLotId(), commandQty, perHourQty, inventoryQty,JsonUtil.toJson(snapshotItem));
            SnapshotFixCheckItem snapshotFixCheckItem = getSnapshotFixCheckItem(snapshotItem, inventoryQty, perHourQty,
                commandQty);
            if (Objects.nonNull(perHour)) {
                snapshotFixCheckItem.setPerHourId(perHour.getId());
            }
            snapshotFixCheckItems.add(snapshotFixCheckItem);
        }
        interrupt();
    }

    private Map<SnapshotItem, SkuInventorySnapshotPerHour> getSnapshotItemSkuInventorySnapshotPerHourMap(
        Date fixCheckStartDate, SnapshotWarehouseSkuItem item) {
        List<SkuInventorySnapshotPerHour> snapshotPerHourList = skuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(fixCheckStartDate,
                item.getWarehouseId(), item.getSkuIds());

        return snapshotPerHourList.stream()
            .collect(Collectors.toMap(SnapshotConverter::getSnapshotItem, Function.identity(), (n1, n2) -> n1));
    }

    private Map<SnapshotItem, CommandInventoryNumDto> getSnapshotItemCommandInventoryNumDtoMap(
        List<CredentialHeader> credentialHeaders) {

        List<CommandInventoryNumDto> commandInventoryNumList = commandManager
            .getCommandInventoryNumList(credentialHeaders);
        //凭证变动的数量
        return commandInventoryNumList.stream()
            .collect(
                Collectors.toMap(SnapshotConverter::getSnapshotItemByCommandDto, Function.identity(),
                    (existingItem, newItem) -> {
                        existingItem.setFreeQty(existingItem.getFreeQty().add(newItem.getFreeQty()));
                        existingItem.setFrozenQty(existingItem.getFrozenQty().add(newItem.getFrozenQty()));
                        existingItem.setTransferIntransitQty(
                            existingItem.getTransferIntransitQty().add(newItem.getTransferIntransitQty()));
                        return existingItem;
                    }
                ));
    }

    private List<WarehouseSkuLotInventory> getWarehouseSkuLotInventories(Long warehouseId, List<Long> skuIds) {
        List<String> allCodes = localParamConfig.allLocationSortCodes();
        return warehouseSkuLotInventoryMapper
            .getByWarehouseIdList(warehouseId, allCodes, skuIds);
    }

    private SnapshotItem getSnapShotItem(WarehouseSkuLotInventory w) {
        return new SnapshotItem(w.getSkuId(), w.getLotId(), w.getWarehouseId(), w.getLogicInventoryLocationCode(),
            w.getCargoOwnerId());
    }

    private SnapshotItem getSnapShotItem(SnapshotFixCheckItem s) {
        return new SnapshotItem(s.getSkuId(), s.getLotId(), s.getWarehouseId(), s.getLogicInventoryLocationCode(),
            s.getCargoOwnerId());
    }


    private SnapshotFixCheckItem getSnapshotFixCheckItem(SnapshotItem snapshotItem, BigDecimal inventoryQty,
        BigDecimal snapshotQty,
        BigDecimal credentialQty) {
        return new SnapshotFixCheckItem(snapshotItem.getSkuId(), snapshotItem.getWarehouseId(), snapshotItem.getLotId(),
            inventoryQty, snapshotQty,
            credentialQty, snapshotItem.getCargoOwnerId(), snapshotItem.getLogicLocationCode());
    }


    private void interrupt() {
        boolean interrupt = getInterrupt();
        if (interrupt) {
            throw new ImsBusinessException("手动中断");
        }
    }
}
