package com.ddmc.ims.job.snapshot;

import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.ddmc.ims.job.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 逻辑库存快照清理job
 */
@Slf4j
@Component
public class SkuInventorySnapshotUsagePerHourClearByIdJob extends AbstractJobHandler {


    @Resource
    private SkuInventorySnapshotUsagePerHourMapper skuInventorySnapshotUsagePerHourMapper;

    private Long getMinId() {
        return localParamService.getLongValue("ims.SkuInventorySnapUsagePerHourDeleteId", 316662591L);
    }

    private boolean getInterrupt() {
        return localParamService.getBooleanValue(LocalParamsConstants.STOCK_TRANSACTION_LOG_REPLAY_INTERRUPT, false);
    }

    private Long getSleep() {
        return localParamService.getLongValue(LocalParamsConstants.STOCK_TRANSACTION_LOG_REPLAY_SLEEP, 100L);
    }

    @XxlJob("SkuInventorySnapshotUsagePerHourClearByIdJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {

        Long minId = getMinId();
        Long total = 0L;
        Long effectNum = skuInventorySnapshotUsagePerHourMapper.deleteByMinId(minId);
        total = total + effectNum;
        while (effectNum > 0) {
            sleep();
            interrupt();
            effectNum = skuInventorySnapshotUsagePerHourMapper.deleteByMinId(minId);
            total = total + effectNum;
            log.info("[SkuInventorySnapshotUsagePerHourClearByIdJob] current deleteNum:{}", total);
        }
        log.info("[SkuInventorySnapshotUsagePerHourClearByIdJob] end");
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    private void sleep() {
        Long sleep = getSleep();
        Thread.sleep(sleep);
    }


    private void interrupt() {
        boolean interrupt = getInterrupt();
        if (interrupt) {
            throw new ImsBusinessException("手动中断");
        }
    }


}
