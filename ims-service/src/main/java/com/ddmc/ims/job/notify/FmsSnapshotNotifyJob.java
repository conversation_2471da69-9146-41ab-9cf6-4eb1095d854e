package com.ddmc.ims.job.notify;

import com.ddmc.fms.inv.enums.DsStatus;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FmsSnapshotNotifyJob extends AbstractJobHandler {

    @Resource
    private FmsSnapshotNotifyMapper fmsSnapshotNotifyMapper;
    @Resource
    private AlertService alertService;

    @XxlJob("fmsSnapshotNotifyJob")
    @Override
    public ReturnT<String> execute(String param) {

        log.info("[fmsSnapshotNotifyJob] param -> {}", param);
        Date snapshotDate = DateUtils.truncate(DateUtils.addDays(CurrentDateUtil.newDate(), -1), Calendar.DATE);
        FmsSnapshotNotify fmsSnapshotNotify = fmsSnapshotNotifyMapper.selectByDayEndDate(snapshotDate);
        if(Objects.isNull(fmsSnapshotNotify)){
            alertService.alert("财务快照异常","财务快照通知未生成", AlertLevel.WARNING);
            return ReturnT.FAIL;
        }
        if(DsStatus.SYNC_SUCC.getValue().equals(fmsSnapshotNotify.getStatus()) ){
            alertService.alert("财务快照异常","当日财务快照生成失败", AlertLevel.WARNING);
            return ReturnT.FAIL;
        }
        log.info("[fmsSnapshotNotifyJob]完成");
        return ReturnT.SUCCESS;
    }
}
