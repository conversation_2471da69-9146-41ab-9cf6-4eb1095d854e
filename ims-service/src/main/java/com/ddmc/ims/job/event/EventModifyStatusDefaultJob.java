package com.ddmc.ims.job.event;

import com.ddmc.ims.common.enums.common.EventEntityStatusEnum;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.event.EventService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 修改事件状态为未执行
 */
@Component
@Slf4j
public class EventModifyStatusDefaultJob extends AbstractJobHandler {

    @Resource
    private EventService eventService;
    @Resource
    private AlertService alertService;

    @XxlJob("EventModifyStatusDefaultJob")
    @Override
    public ReturnT<String> execute(String param) {
        List<Long> eventIdList = JsonUtils.parseList(param, Long.class);
        int count = eventService.updateStatus(eventIdList, EventEntityStatusEnum.DEFAULT.getCode());
        log.info("[修改事件状态为未执行],成功 {}条", count);
        return ReturnT.SUCCESS;
    }
}
