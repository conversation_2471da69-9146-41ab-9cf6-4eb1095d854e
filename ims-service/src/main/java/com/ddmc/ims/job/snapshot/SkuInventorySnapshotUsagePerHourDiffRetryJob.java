package com.ddmc.ims.job.snapshot;

import com.ddmc.ims.bo.snapshot.SnapshotWarehouseSkuItem;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.constant.DataSourceConstants;
import com.ddmc.ims.config.dynamic.DataSourceSwitch;
import com.ddmc.ims.dal.condition.SnapshotFixUsageCheckItem;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourDiffMapper;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHourDiff;
import com.ddmc.ims.job.AbstractJobHandler;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class SkuInventorySnapshotUsagePerHourDiffRetryJob extends AbstractJobHandler {

    @Resource
    private SkuInventorySnapshotUsagePerHourDiffMapper skuInventorySnapshotUsagePerHourDiffMapper;

    @Resource
    private SkuInventorySnapshotUsagePerHourFixCheckJob skuInventorySnapshotUsagePerHourFixCheckJob;


    @Resource
    private SkuInventorySnapshotUsagePerHourDiffRetryJob selfProxy;

    @DataSourceSwitch(DataSourceConstants.SLAVE_SHARDING_DATASOURCE)
    @XxlJob("SkuInventorySnapshotUsagePerHourDiffRetryJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        List<Long> warehouseIds = skuInventorySnapshotUsagePerHourDiffMapper.selectDistinctWarehouseId();

        warehouseIds.forEach(warehouseId -> {
            List<SkuInventorySnapshotUsagePerHourDiff> diffs = skuInventorySnapshotUsagePerHourDiffMapper
                .selectByWarehouseId(warehouseId);

            if (CollectionUtils.isEmpty(diffs)) {
                return;
            }
            SkuInventorySnapshotUsagePerHourDiff diff = diffs.get(0);
            List<Long> skuIds = diffs.stream().map(SkuInventorySnapshotUsagePerHourDiff::getSkuId).distinct()
                .collect(Collectors.toList());
            SnapshotWarehouseSkuItem item = new SnapshotWarehouseSkuItem(warehouseId, skuIds);
            List<SnapshotFixUsageCheckItem> currentDiff = skuInventorySnapshotUsagePerHourFixCheckJob
                .getSnapshotFixCheckItems(diff.getCredentialStartDate(), item);
            selfProxy.saveDiff(diff, currentDiff);

        });


        return ReturnT.SUCCESS;
    }

    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void saveDiff(SkuInventorySnapshotUsagePerHourDiff diff, List<SnapshotFixUsageCheckItem> currentDiffs) {
        skuInventorySnapshotUsagePerHourDiffMapper.deleteByWarehouseId(diff.getWarehouseId());
        if (CollectionUtils.isEmpty(currentDiffs)) {
            return;
        }
        List<SkuInventorySnapshotUsagePerHourDiff> diffs = Lists.newArrayListWithExpectedSize(currentDiffs.size());
        currentDiffs.forEach(currentDiff -> {
            SkuInventorySnapshotUsagePerHourDiff item = new SkuInventorySnapshotUsagePerHourDiff();
            item.setSkuId(currentDiff.getSkuId());
            item.setWarehouseId(currentDiff.getWarehouseId());
            item.setLogicLocationCode(currentDiff.getLogicInventoryLocationCode());
            item.setCargoOwnerId(currentDiff.getCargoOwnerId());
            item.setUsageCode(currentDiff.getUsageCode());
            item.setSnapshotId(Objects.isNull(currentDiff.getPerHourId()) ? 0L : currentDiff.getPerHourId());
            item.setSnapshotQty(currentDiff.getSnapShotQty());
            item.setCredentialQty(currentDiff.getCredentialQty());
            item.setInventoryQty(currentDiff.getInventoryQty());
            item.setTriggerCount(diff.getTriggerCount() + 1);
            item.setCredentialStartDate(diff.getCredentialStartDate());
            item.setCredentialEndDate(diff.getCredentialEndDate());
            diffs.add(item);
        });

        Lists.partition(diffs, CommonConstants.BATCH_INSERT_DB_100)
            .forEach(subList -> skuInventorySnapshotUsagePerHourDiffMapper.insertList(subList));
    }
}
