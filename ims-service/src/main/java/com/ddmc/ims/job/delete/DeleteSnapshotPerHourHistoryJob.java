package com.ddmc.ims.job.delete;

import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 逻辑库存快照删除job
 */
@Slf4j
@Component
public class DeleteSnapshotPerHourHistoryJob extends AbstractDeleteJobHandler {


    @Resource
    private SkuInventorySnapshotPerHourMapper skuInventorySnapshotPerHourMapper;


    @XxlJob("DeleteSnapshotPerHourHistoryJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        handleDelete("DeleteSnapshotPerHourHistoryJob");
        return ReturnT.SUCCESS;
    }


    @Override
    public Long getMaxId() {
        Long configMaxId = localParamService.getLongValue("ims.DeleteSnapshotPerHourHistoryJobMaxId");
        if (Objects.nonNull(configMaxId)) {
            return configMaxId;
        }

        return skuInventorySnapshotPerHourMapper.selectOneIdByCreateTimeBefore(getMaxDeleteDate());
    }


    private Date getMaxDeleteDate() {
        Integer intValue = localParamService.getIntValue("ims.DeleteSnapshotPerHourHistoryJobDeleteTimeInterval", 15);
        return DateUtil.getPlusStartOfDay(CurrentDateUtil.newDate(), -intValue);
    }

    @Override
    public boolean getInterrupt() {
        return localParamService.getBooleanValue("ims.DeleteSnapshotPerHourHistoryJobInterrupt", false);
    }

    @Override
    public Long getSleep() {
        return localParamService.getLongValue("ims.DeleteSnapshotPerHourHistoryJobSleep", 10L);
    }

    @Override
    protected Long deleteByMaxId(Long maxId) {
        return skuInventorySnapshotPerHourMapper.deleteByMaxId(maxId);
    }


}
