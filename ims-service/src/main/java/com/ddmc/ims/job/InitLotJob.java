package com.ddmc.ims.job;

import com.ddmc.ims.config.RateLimiterConfig;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.manager.lot.LotManager;
import com.ddmc.ims.message.lot.LotProducer;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.lot.InventoryLotInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Slf4j
@Component
public class InitLotJob extends AbstractJobHandler{

    @Resource
    private AlertService alertService;

    @Resource(name = "lotInitExecutor")
    private ExecutorService executorService;

    @Resource
    private LotManager lotManager;

    @Resource
    private InventoryLotInfoService inventoryLotInfoService;

    @Resource
    private RateLimiterConfig rateLimiterConfig;

    @Resource
    private LotProducer lotProducer;


    @Value("#{${ims.lastPrimaryKeyIdMaps:{}}}")
    private Map<Integer, Long> map;

    @Value("#{'${ims.shardingTableIndexList:0}'.split(',')}")
    private List<Integer> shardingTableIndexList;

    @XxlJob("InitLotJob")
    @Override
    public ReturnT<String> execute(String param) {
        log.info("[InitLotJob] param -> {}", param);
        //查询远程的仓库信息
        List<Future<?>> futures = shardingTableIndexList.stream()
            .map(t -> executorService.submit(() -> intLotByShardingTableIndex(t))).collect(Collectors.toList());

        futures.forEach(f -> {
            try {
                f.get();
            } catch (InterruptedException | ExecutionException interruptedException) {
                log.error("[InitLotJob] 初始化批次信息异常", interruptedException);
                alertService.alert("初始化批次信息异常",
                    "异常信息" + interruptedException.getMessage(), AlertLevel.WARNING);
                Thread.currentThread().interrupt();
            }
        });
        log.info("[InitLotJob]完成");
        return ReturnT.SUCCESS;
    }

    private void intLotByShardingTableIndex(Integer shardingTableIndex) {
        Long lastPrimaryKeyId = map.getOrDefault(shardingTableIndex, 0L);
        ImmutablePair<List<InventoryLotInfo>, Long> result = lotManager.scanLot(shardingTableIndex, lastPrimaryKeyId, true);
        while (CollectionUtils.isNotEmpty(result.getLeft())) {
            //没有临期日期的数据requene
            requeneLotWithoutUnsaleDate(result.getLeft());

            //处理数据库
            inventoryLotInfoService.replaceInventoryLotInfoList(result.getLeft());

            //开启下次查询
            lastPrimaryKeyId = result.getRight();
            if (Objects.isNull(lastPrimaryKeyId)) {
                return;
            }
            result = lotManager.scanLot(shardingTableIndex, lastPrimaryKeyId, true);
            //控制qps
            rateLimiterConfig.getRateLimiterByKey(RateLimiterConfig.INIT_LOT_INFO_LIMIT).acquire();
        }
    }

    private void requeneLotWithoutUnsaleDate(List<InventoryLotInfo> inventoryLotInfos) {
        List<String> lotIds = inventoryLotInfos.stream().filter(t -> Objects.isNull(t.getUnsalableDate()))
            .map(InventoryLotInfo::getLotId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(lotIds)) {
            return;
        }
        log.warn("requeneLotWithoutUnsaleDate -> {}", lotIds);
    }
}
