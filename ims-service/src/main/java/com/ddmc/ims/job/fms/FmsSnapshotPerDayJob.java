package com.ddmc.ims.job.fms;

import com.ddmc.fms.inv.enums.DsStatus;
import com.ddmc.ims.common.constant.DataSourceConstants;
import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.dynamic.DataSourceSwitch;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.WarehouseIdAndSnapshotDateDto;
import com.ddmc.ims.event.producer.FmsCompareProducer;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.FmsSkuInventorySnapshotPerDayService;
import com.ddmc.ims.service.snapshot.FmsSnapshotNotifyService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class FmsSnapshotPerDayJob extends AbstractJobHandler {


    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;

    @Resource
    private FmsSkuInventorySnapshotPerDayService fmsSkuInventorySnapshotPerDayService;

    @Resource
    private AlertService alertService;

    @Resource
    private WarehouseMapper warehouseMapper;
    @Resource
    private FmsCompareProducer fmsCompareProducer;
    @Resource
    private FmsSnapshotNotifyMapper fmsSnapshotNotifyMapper;
    @Resource
    private FmsSnapshotNotifyService fmsSnapshotNotifyService;

    @Resource
    private LocalParamConfig localParamConfig;

    private boolean isStop() {
        return localParamService.getBooleanValue(LocalParamsConstants.FMS_SNAPSHOT_STOP,
            false);
    }

    @DataSourceSwitch(DataSourceConstants.SLAVE_SHARDING_DATASOURCE)
    @XxlJob("fmsSnapshotPerDayJob")
    @Override
    public ReturnT<String> execute(String param) {

        Date snapshotDate = DateUtils.truncate(DateUtils.addDays(CurrentDateUtil.newDate(), -1), Calendar.DATE);
        List<Long> warehouseIds = Collections.emptyList();
        if (StringUtils.isNotBlank(param)) {
            WarehouseIdAndSnapshotDateDto warehouseIdAndSnapshotDateDto = JsonUtil
                .fromJson(param, WarehouseIdAndSnapshotDateDto.class);
            assert warehouseIdAndSnapshotDateDto != null;
            snapshotDate = warehouseIdAndSnapshotDateDto.getSnapshotDate();
            assert snapshotDate != null;
            warehouseIds = warehouseIdAndSnapshotDateDto.getWarehouseIds();
        }
        if(CollectionUtils.isEmpty(warehouseIds)){
            warehouseIds = warehouseMapper.selectAllId();
        }
        warehouseIds = warehouseIds.stream().filter(w -> !localParamConfig.excludeWarehouseForSnapshot(w))
            .collect(Collectors.toList());

        FmsSnapshotNotify fmsSnapshotNotify = fmsSnapshotNotifyMapper.selectByDayEndDate(snapshotDate);
        if (Objects.isNull(fmsSnapshotNotify)) {
            return ReturnT.SUCCESS;
        }
        if (!DsStatus.SYNC_SUCC.getValue().equals(fmsSnapshotNotify.getStatus())) {
            return ReturnT.SUCCESS;
        }

        // 分片参数
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(), shardingVO.getTotal());
        log.info("[skuInventorySnapshotPerDayJob] 分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(),
            shardingVO.getTotal());

        List<Long> shardingWarehouseIds = warehouseIds.stream()
            .filter(t -> t % shardingVO.getTotal() == shardingVO.getIndex())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shardingWarehouseIds)) {
            log.info("[retryFmsSnapshotPerDayJob] shardingWarehouseIds为空，跳过此次job");
            return ReturnT.SUCCESS;
        }
        List<SnapshotTask> snapshotTasks = snapshotTaskMapper.selectBySnapshotTypeAndSnapshotTimeAndStatus(
            SnapshotTypeEnum.FMS.getCode(), snapshotDate, shardingWarehouseIds, SnapshotStatusEnum.INIT.getCode());
        if (CollectionUtils.isEmpty(snapshotTasks)) {
            checkAndSendFms(snapshotDate);
            return ReturnT.SUCCESS;
        }
        List<Long> errorWarehouseIds = processSnapshotTasks(snapshotTasks);
        log.error("生成财务快照完成,异常数量{},异常数据{}", errorWarehouseIds.size(), JsonUtil.toJson(errorWarehouseIds));
        if (CollectionUtils.isNotEmpty(errorWarehouseIds)) {
            alertService.alert("生成财务快照完成", "异常数量" + errorWarehouseIds.size(), AlertLevel.WARNING);
        }

        return ReturnT.SUCCESS;
    }

    public List<Long> processSnapshotTasks(List<SnapshotTask> snapshotTasks) {
        List<Long> errorWarehouseIds = Lists.newArrayList();
        snapshotTasks.forEach(t -> {
            try {
                if (isStop()) {
                    return;
                }
                Integer count = snapshotTaskMapper
                    .updateStatusById(t.getId(), SnapshotStatusEnum.PROCESSING, SnapshotStatusEnum.INIT);
                if (count == 0) {
                    return;
                }
                fmsSkuInventorySnapshotPerDayService
                    .dealEndFmsSkuInventorySnapshot(t.getId(), t.getWarehouseId(), t.getSnapshotTime());
            } catch (Exception e) {
                log.error("生成财务快照异常,入参{}", JsonUtil.toJson(t), e);
                snapshotTaskMapper.updateStatusById(t.getId(), SnapshotStatusEnum.INIT, SnapshotStatusEnum.PROCESSING);
                errorWarehouseIds.add(t.getWarehouseId());
            }
        });
        if (CollectionUtils.isEmpty(errorWarehouseIds)) {
            checkAndSendFms(snapshotTasks.get(0).getSnapshotTime());
        }
        return errorWarehouseIds;
    }


    private void checkAndSendFms(Date dayEndDate) {
        SnapshotTask snapshotTask = snapshotTaskMapper.selectOneBySnapshotTypeAndSnapshotTimeAndStatus(
            SnapshotTypeEnum.FMS, dayEndDate,
            Lists.newArrayList(SnapshotStatusEnum.INIT, SnapshotStatusEnum.PROCESSING));
        if (Objects.isNull(snapshotTask)) {
            fmsSnapshotNotifyService.notifyFmsCheck(dayEndDate);
        }
    }


}
