package com.ddmc.ims.job.init;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.dal.mapper.ims.CredentialDetailMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialUseageDetailMapper;
import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeleteCredentialHeadJob extends AbstractJobHandler {

    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;

    @Resource
    private CredentialDetailMapper credentialDetailMapper;

    @Resource
    private SnapshotCredentialDiffMapper snapshotCredentialDiffMapper;

    @Resource
    private CredentialUseageDetailMapper credentialUseageDetailMapper;

    @Resource
    private DeleteCredentialHeadJob selfProxy;

    @Resource
    private LocalParamService localParamService;

    @XxlJob("deleteCredentialHeadJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Long maxId;
        if (StringUtils.isEmpty(param)) {
            maxId = getMaxId();
            if (Objects.isNull(maxId)) {
                alert("查询最大max失败，请调整" + LocalParamsConstants.DELETE_CREDENTIAL_QUERY_MAX_ID_COUNT + "值", AlertLevel.INFO);
                return ReturnT.FAIL;
            }
        } else {
            maxId = Long.parseLong(param);
        }

        log.info("[deleteCredentialHeadJob] start");
        List<Long> headIds;
        while (CollectionUtils.isNotEmpty(headIds = credentialHeaderMapper.selectIdByMaxIdLimit100(maxId))) {
            if (isStop()) {
                return ReturnT.SUCCESS;
            }

            log.info("[deleteCredentialHeadJob] current maxId:{}", maxId);

            maxId = headIds.stream().sorted().collect(Collectors.toList()).get(0);

            Set<Long> existHeadIds = snapshotCredentialDiffMapper.selectCredentialIdByCredentialIdIn(headIds);
            headIds = headIds.stream().filter(t -> !existHeadIds.contains(t)).collect(Collectors.toList());

            selfProxy.deleteCredentailIds(headIds);

            doDelay();

        }

        return ReturnT.SUCCESS;
    }

    /**
     * 查询在截止时间前最大的凭证id，从截止时间往前推移，每次推移1min，知道查询最大次数或查询到最大id
     * @return null 查询次数达到最大次数，依然没有查询到最大id
     */
    public Long getMaxId() {
        Date endDateTime = DateUtils.addMinutes(getMaxDeleteDate(), 1);
        Date startDateTime;
        for (int i = 0; i < localParamService.getIntValue(LocalParamsConstants.DELETE_CREDENTIAL_QUERY_MAX_ID_COUNT, 60); i++) {
            endDateTime = DateUtils.addMinutes(endDateTime, -1);
            startDateTime = DateUtils.addMinutes(endDateTime, -1);
            Long maxId = credentialHeaderMapper.selectMaxIdByCreateTime(startDateTime, endDateTime);
            if (Objects.nonNull(maxId)) {
                return maxId;
            }
        }
        return null;
    }

    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER)
    public void deleteCredentailIds(List<Long> headerIds) {
        if (CollectionUtils.isNotEmpty(headerIds)) {
            int batchSize = localParamService.getIntValue(LocalParamsConstants.DELETE_CREDENTIAL_BATCH_SIZE, 20);
            Lists.partition(headerIds, batchSize).forEach(subList -> {
                credentialHeaderMapper.deleteBatchIds(subList);
                credentialDetailMapper.deleteByHeaderIds(subList);
                credentialUseageDetailMapper.deleteByCredentialHeaderIds(subList);
            });
        }
    }

    private Date getMaxDeleteDate() {
        Integer intValue = localParamService
            .getIntValue("ims.DeleteCredentialHeadJobTimeInterval", 15);
        return DateUtil.getPlusStartOfDay(CurrentDateUtil.newDate(), -intValue);
    }


    private boolean isStop() {
        return localParamService.getBooleanValue(LocalParamsConstants.DELETE_CREDENTIAL_HEAD,
            false);
    }

    private Long getSleep() {
        return localParamService.getLongValue("ims.DeleteCredentialHeadJobSleep", 100L);
    }

    private void doDelay() {
        try {
            Thread.sleep(getSleep());
        } catch (Exception e) {
            log.warn("延时被中断", e);
            Thread.currentThread().interrupt();
        }
    }
}


