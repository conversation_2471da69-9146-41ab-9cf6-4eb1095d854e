package com.ddmc.ims.job.event;

import com.ddmc.ims.common.enums.common.EventEntityStatusEnum;
import com.ddmc.ims.common.enums.common.EventKeyTypeEnum;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.event.EventService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Event事件自动补偿执行（查询出30分钟还未处理的事件）
 */
@Component
@Slf4j
public class TransferOutChangeEventAutoExecutorJob extends AbstractJobHandler {

    @Resource
    private EventService eventService;
    @Resource
    private DbEventStorage dbEventStorage;
    @Resource
    private AlertService alertService;
    @Value("${eventBus.auto.maxCount:5}")
    private Integer autoMaxCount;
    @Value("${TransferOutChangeEventAutoExecutorEndTime:10}")
    private Integer eventAutoExecutorEndTime;

    @Value("${TransferOutChangeEventAutoExecutorEndTime.range:30}")
    private Integer range;

    @Value("${TransferOutChangeEventAutoExecutorJob.limit:2000}")
    private Integer eventAutoExecutorJobLimit;

    @XxlJob("TransferOutChangeEventAutoExecutorJob")
    @Override
    public ReturnT<String> execute(String param) {
        //查询出所有未处理的事件
        ListEventCondition condition = new ListEventCondition();
        condition.setCreateTimeEnd(DateUtils.addMinutes(new Date(), -eventAutoExecutorEndTime));
        List<Integer> status = Lists.newArrayList();
        status.add(EventEntityStatusEnum.PROCESSING.getCode());
        condition.setStatusList(status);
        condition.setTriggerCountEnd(autoMaxCount);
        condition.setLimit(eventAutoExecutorJobLimit);
        List<EventEntity> eventEntityList = eventService.listByCondition(condition);
        if (Objects.isNull(eventEntityList)) {
            eventEntityList = Collections.emptyList();
        }
        //过滤延时队列
        eventEntityList = eventEntityList.stream().filter(o -> !o.isDelayEvent())
            .filter(o -> EventKeyTypeEnum.TRANSFER_OUT_CHANGE_NOTIFY.getCode().equalsIgnoreCase(o.getKeyType()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(eventEntityList)) {
            return ReturnT.SUCCESS;
        }
        //执行事件
        dbEventStorage.executeProcessingEvent(eventEntityList);
        return ReturnT.SUCCESS;
    }
}
