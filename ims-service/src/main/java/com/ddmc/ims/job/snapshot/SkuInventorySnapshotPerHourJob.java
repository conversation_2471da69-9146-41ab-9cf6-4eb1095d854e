package com.ddmc.ims.job.snapshot;

import com.ddmc.ims.common.constant.DataSourceConstants;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.dynamic.DataSourceSwitch;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.SkuInventorySnapshotPerHourService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 逻辑库存快照，每小时执行，按仓分片广播
 */
@Slf4j
@Component
public class SkuInventorySnapshotPerHourJob extends AbstractJobHandler {

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private LocalParamConfig localParamConfig;

    @Resource
    private SkuInventorySnapshotPerHourService skuInventorySnapshotPerHourService;

    @Resource
    private AlertService alertService;


    @DataSourceSwitch(DataSourceConstants.SLAVE_SHARDING_DATASOURCE)
    @XxlJob("skuInventorySnapshotPerHourJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        //手工处理，灰度单节点job执行
        if (StringUtils.isNotBlank(param)) {
            return handleManual(param);
        }

        List<Long> shardingWarehouseIds = getShardingWarehouseIds(warehouseService.getAllWarehouseIds());
        shardingWarehouseIds = shardingWarehouseIds.stream().filter(w -> !localParamConfig.excludeWarehouseForSnapshot(w))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shardingWarehouseIds)) {
            log.info("[SkuInventorySnapshotPerHourJob] shardingWarehouseIds为空，跳过此次job");
            return ReturnT.SUCCESS;
        }

        List<Long> handleErrWarehouseIds = Collections.synchronizedList(Lists.newArrayList());
        Date snapShotPerHourDate = getSnapShotPerHourDate();
        shardingWarehouseIds.forEach(wi -> {
            try {
                skuInventorySnapshotPerHourService
                    .handleWarehouseSnapshotPerHour(snapShotPerHourDate, Lists.newArrayList(wi));
            } catch (Exception e) {
                handleErrWarehouseIds.add(wi);
                log.error("[SkuInventorySnapshotPerHourJob] 执行处理失败", e);
            }
        });
        if (CollectionUtils.isNotEmpty(handleErrWarehouseIds)) {
            alertService.alert("SkuInventorySnapshotPerHourJob", "以下仓库生产快照失败:" + JsonUtil.toJson(handleErrWarehouseIds), AlertLevel.CRITICAL);
        }
        return ReturnT.SUCCESS;
    }



    /**
     * 人工处理
     * @param param param
     * @return result
     */
    private ReturnT<String> handleManual(String param) {
        List<Long> ids = Arrays.stream(param.split(",")).map(Long::parseLong).collect(Collectors.toList());

        Date snapShotPerHourDate = getSnapShotPerHourDate();
        skuInventorySnapshotPerHourService.clearSnapshotPerHour(ids, snapShotPerHourDate);
        if (localParamConfig.isOnlyClearSnapshotInfo()) {
            log.info("[SkuInventorySnapshotPerHourJob] clear done");
            return ReturnT.SUCCESS;
        }

        skuInventorySnapshotPerHourService
            .handleWarehouseSnapshotPerHour(snapShotPerHourDate, ids);
        return ReturnT.SUCCESS;
    }


    /**
     * 获取快照时间
     * @return 如果自定义配置有值则取配置值，如果没有则取当前时间-1
     */
    private Date getSnapShotPerHourDate() {
        Date snapShotPerHourDate = localParamConfig.getSnapShotPerHourDate();
        if (snapShotPerHourDate == null) {
            snapShotPerHourDate = DateUtil.getNowMinusHourDate(0);
        }
        return snapShotPerHourDate;
    }

}
