package com.ddmc.ims.job.event;

import com.ddmc.ims.common.enums.common.EventKeyTypeEnum;
import com.ddmc.ims.dal.mapper.ims.EventMapper;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时清理已完成事件历史数据
 */
@Component
@Slf4j
public class TransferOutEventClearJobHandler extends IJobHandler {

    @Resource
    private EventMapper eventMapper;

    @Value("${isStopEventClearJob:false}")
    private boolean isStopEventClearJob;

    @Value("${eventClearLimit:1000}")
    private Integer eventClearLimit;

    @Value("${eventDeleteCount:1000}")
    private Integer eventDeleteCount;

    @Value("${eventDeleteDelay:true}")
    private boolean delay;

    @Value("${eventDeleteStop:false}")
    private boolean stop;

    @Value("${eventDeleteDelayTime:20}")
    private Integer eventDeleteDelayTime;

    @Override
    @XxlJob("TransferOutEventClearJobHandler")
    public ReturnT<String> execute(String param) {
        Date minDate = DateUtils.truncate(new Date(), Calendar.DATE);
        int count = 0;
        List<EventEntity> greaterEventEntity = eventMapper.getProcessing();
        while (CollectionUtils.isNotEmpty(greaterEventEntity) && count < eventDeleteCount) {
            count++;
            List<Long> needDeleteIds = greaterEventEntity.stream().filter(t -> t.getStatus().equals(2)
                && t.getCreateTime().before(minDate)
                && t.getKeyType().equals(EventKeyTypeEnum.TRANSFER_OUT_CHANGE_NOTIFY.getCode()))
                .map(EventEntity::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                eventMapper.deleteByIds(needDeleteIds);
            } else {
                break;
            }
            greaterEventEntity = eventMapper.getProcessing();

            sleep();

            if (stop) {
                log.warn("CleanEventEntityJobHandler is stop!");
                break;
            }
        }
        return ReturnT.SUCCESS;
    }



    private void sleep() {
        if (!delay) {
            return;
        }
        try {
            Thread.sleep(eventDeleteDelayTime);
        } catch (InterruptedException interruptedException) {
            Thread.currentThread().interrupt();
        }
    }

}
