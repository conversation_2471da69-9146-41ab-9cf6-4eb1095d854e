package com.ddmc.ims.job.delete;

import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.dal.mapper.ims.CredentialDetailMapper;
import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.job.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeleteSnapshotCredentialDiffJob extends AbstractJobHandler {

    @Resource
    private SnapshotCredentialDiffMapper snapshotCredentialDiffMapper;

    @Resource
    private CredentialDetailMapper credentialDetailMapper;


    @XxlJob("deleteSnapshotCredentialDiffJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("删除差异数据" + param);
        Date maxDeleteDate = getMaxDeleteDate();
        int count = snapshotCredentialDiffMapper.deleteByMaxIdLimit500(maxDeleteDate);
        while (count > 0) {
            if(isStop()){
                return ReturnT.SUCCESS;
            }
            doDelay();
            count =  snapshotCredentialDiffMapper.deleteByMaxIdLimit500(maxDeleteDate);
        }

        return ReturnT.SUCCESS;
    }


    private boolean isStop() {
        return localParamService.getBooleanValue("ims.deleteSnapshotCredentialDiff",
            false);
    }

    private long getDelaySecond() {
        return localParamService.getLongValue("ims.deleteSnapshotCredentialDiffDelaySecond",
            40L);
    }

    private void doDelay() {
        long delayTime = (long) (Math.random() * getDelaySecond()) + 1;
        try {
            Thread.sleep(delayTime);
        } catch (Exception e) {
            log.warn("延时被中断", e);
            Thread.currentThread().interrupt();
        }
    }

    private Date getMaxDeleteDate() {
        Integer intValue = localParamService
            .getIntValue("ims.DeleteSnapshotCredentialDiffJobDeleteTimeInterval", 15);
        if(intValue < 5){
            throw new ImsBusinessException("不能删除五天内数据");
        }
        return DateUtil.getPlusStartOfDay(CurrentDateUtil.newDate(), -intValue);
    }
}


