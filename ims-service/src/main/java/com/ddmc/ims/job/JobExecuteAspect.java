package com.ddmc.ims.job;

import com.ddmc.ims.common.util.ThreadLocalContextHelper;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.google.common.base.Stopwatch;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 监控job执行耗时，对job异常进行发送告警
 */
@Component
@Aspect
@Order
@Slf4j
public class JobExecuteAspect {

    @Resource
    private AlertService alertService;

    private static final String REQUEST_ID = "requestId";

    @Around("@annotation(com.xxl.job.core.handler.annotation.XxlJob)")
    public Object aroundJob(ProceedingJoinPoint joinPoint) throws Throwable {
        MDC.put(REQUEST_ID, UUID.randomUUID().toString().replace("-", ""));
        Stopwatch stopwatch = Stopwatch.createStarted();
        ThreadLocalContextHelper.clearAll();
        String jobName = getJobName(joinPoint);
        try {
            return joinPoint.proceed();
        } catch (Throwable t) {
            alertService.alert("定时任务[ " + jobName + "]", t.getMessage(), AlertLevel.CRITICAL);
            throw t;
        } finally {
            ThreadLocalContextHelper.clearAll();
            log.info("job: {} cost: {}ms", jobName, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            MDC.clear();
        }
    }

    private String getJobName(ProceedingJoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method targetMethod = methodSignature.getMethod();
        return targetMethod.getAnnotation(XxlJob.class).value();
    }
}
