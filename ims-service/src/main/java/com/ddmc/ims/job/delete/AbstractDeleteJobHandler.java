package com.ddmc.ims.job.delete;

import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.job.AbstractJobHandler;
import java.util.Objects;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractDeleteJobHandler extends AbstractJobHandler {

    /**
     * 获取最大id
     *
     * @return id
     */
    protected abstract Long getMaxId();


    /**
     * 判断是否中断
     *
     * @return result
     */
    protected abstract boolean getInterrupt();

    /**
     * 间隔
     *
     * @return result
     */
    protected abstract Long getSleep();

    /**
     * 执行删除
     *
     * @param maxId 最大id
     * @return 生效个数
     */
    protected abstract Long deleteByMaxId(Long maxId);


    public void handleDelete(String jobHandleName) {
        Long maxId = getMaxId();
        if (Objects.isNull(maxId)) {
            log.info("[DeleteJobHandler] current jobHandle:{} maxIdIsNull", jobHandleName);
            return;
        }

        Long total = 0L;
        Long effectNum = deleteByMaxId(maxId);
        total = total + effectNum;
        while (effectNum > 0) {
            sleep();
            interrupt();
            effectNum = deleteByMaxId(maxId);
            total = total + effectNum;
            log.info("[DeleteJobHandler] current jobHandle:{} deleteNum:{}", jobHandleName, total);
        }
        log.info("[DeleteJobHandler] jobHandle:{} end", jobHandleName);
    }

    @SneakyThrows
    private void sleep() {
        Long sleep = getSleep();
        Thread.sleep(sleep);
    }

    private void interrupt() {
        boolean interrupt = getInterrupt();
        if (interrupt) {
            throw new ImsBusinessException("手动中断");
        }
    }
}