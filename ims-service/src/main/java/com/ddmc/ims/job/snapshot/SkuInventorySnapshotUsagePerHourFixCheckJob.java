package com.ddmc.ims.job.snapshot;

import com.ddmc.ims.bo.snapshot.SnapshotUsageItem;
import com.ddmc.ims.bo.snapshot.SnapshotWarehouseSkuItem;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.constant.DataSourceConstants;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.config.dynamic.DataSourceSwitch;
import com.ddmc.ims.config.sharding.ShardingProperties;
import com.ddmc.ims.converter.SnapshotConverter;
import com.ddmc.ims.dal.condition.SnapshotFixUsageCheckItem;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourDiffMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHour;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHourDiff;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.dto.InventoryNumDto;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 逻辑库存快照比对修复job
 *
 * 灰度分两个Job执行期
 * 1：SkuInventorySnapshotUsagePerHourFixCheckJob：逻辑库存快照比对修复job自动分片
 * 2：SkuInventorySnapshotUsagePerHourFixCheckJob：逻辑库存快照比对修复job手动修复
 *
 * 开关检查项：
 * 1:ims.snapshotPerHourFixCheckInterrupt。生产为false关闭。如果在执行过程中出现问题设置为true。
 * 2:ims.snapshotPerHourFixCheckWarehouseId。指定执行的仓库id，不指定则为全仓分片
 * 3:ims.snapshotPerHourFixCheckStartDate。快照时间与凭证检查的开始时间，不指定则为当天零点快照
 * 4:ims.snapshotPerHourFixCheckEndDate。凭证检查截止时间，不指定则为当天最晚时间
 * 5:ims.snapshotPerHourFixEnable。是否开启修复，不开启则保存到SkuInventorySnapshotPerHourDiff表，由SkuInventorySnapshotPerHourDiffRetryJob重试
 * 6:ims.snapshotPerHourFixUpdate。在5开启后，则要再次检查该次修改为更新还是做插入。true为更新，fasle为插入
 * 7:ims.snapshotPerHourFixWarehouseAndSku。指定需要执行的仓与品
 *
 *
 * 分片自动执行job速查：
 * 1.ims.snapshotPerHourFixCheckInterrupt = false
 * 2.ims.snapshotPerHourFixCheckWarehouseId =
 * 3.ims.snapshotPerHourFixCheckStartDate =
 * 4.ims.snapshotPerHourFixCheckEndDate =
 * 5.ims.snapshotPerHourFixEnable = false
 * 6.ims.snapshotPerHourFixUpdate = true
 * 7.ims.snapshotPerHourFixWarehouseAndSku =
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SkuInventorySnapshotUsagePerHourFixCheckJob extends AbstractJobHandler {

    @Resource
    private CredentialWrapperManager credentialWrapperManager;

    @Resource
    private SkuInventorySnapshotUsagePerHourMapper skuInventorySnapshotUsagePerHourMapper;

    @Resource
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;

    @Resource
    private CommandManager commandManager;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private SkuInventorySnapshotUsagePerHourDiffMapper skuInventorySnapshotUsagePerHourDiffMapper;


    @Resource
    private ShardingProperties shardingProperties;

    private boolean getInterrupt() {
        return localParamService.getBooleanValue("ims.snapshotUsagePerHourFixCheckInterrupt", false);
    }


    private List<Long> getFixWarehouseId() {
        List<Long> fixWarehouseIds = localParamService
            .getLongListValue("ims.snapshotUsagePerHourFixCheckWarehouseId", Collections.emptyList());

        if (CollectionUtils.isEmpty(fixWarehouseIds)) {
            // 分片参数
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();

            if (Objects.isNull(shardingVO)) {
                return Collections.emptyList();
            }
            XxlJobLogger.log("分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(), shardingVO.getTotal());
            log.info("[SkuInventorySnapshotUsagePerHourFixCheckJob] 分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(),
                shardingVO.getTotal());
            return warehouseService.getAllWarehouseIds().stream()
                .filter(t -> t % shardingVO.getTotal() == shardingVO.getIndex())
                .collect(Collectors.toList());
        }

        return fixWarehouseIds;
    }

    private Date getFixCheckStartDate() {

        String startDateStr = localParamService.getStringValue("ims.snapshotUsagePerHourFixCheckStartDate");
        if (StringUtils.isNotBlank(startDateStr)) {
            return ThreadLocalDateUtils.parseYmdhms(startDateStr);
        }
        return DateUtil.getPlusStartOfDay(CurrentDateUtil.newDate(), 0);
    }

    private boolean enableFix() {
        return localParamService.getBooleanValue("ims.snapshotUsagePerHourFixEnable", false);
    }


    private boolean updateOrInsert() {
        return localParamService.getBooleanValue("ims.snapshotUsagePerHourFixUpdate", false);
    }


    private String getWarehouseAndSku() {
        return localParamService.getStringValue("ims.snapshotUsagePerHourFixWarehouseAndSku", "");
    }

    @DataSourceSwitch(DataSourceConstants.SLAVE_SHARDING_DATASOURCE)
    @XxlJob("skuInventorySnapshotUsagePerHourFixCheckJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {

        List<SnapshotWarehouseSkuItem> items = new ArrayList<>();
        if (StringUtils.isBlank(param)) {
            param = getWarehouseAndSku();
        }
        if (StringUtils.isNotBlank(param)) {
            items = JsonUtil
                .fromJson(param, new TypeReference<List<SnapshotWarehouseSkuItem>>() {
                });
        } else {
            List<Long> fixWarehouseId = getFixWarehouseId();
            List<SnapshotWarehouseSkuItem> finalItems = items;
            fixWarehouseId.forEach(w -> {
                SnapshotWarehouseSkuItem item = new SnapshotWarehouseSkuItem();
                item.setWarehouseId(w);
                item.setSkuIds(new ArrayList<>());
                finalItems.add(item);
            });
        }

        if (CollectionUtils.isEmpty(items)) {
            return ReturnT.FAIL;
        }

        log.info("[SkuInventorySnapshotUsagePerHourFixCheckJob] items:{}", JsonUtil.toJson(items));
        Date fixCheckStartDate = getFixCheckStartDate();
        boolean updateOrInsert = updateOrInsert();
        List<SnapshotWarehouseSkuItem> responseList = Lists.newArrayList();
        //仓+sku
        items.forEach(
            item -> {
                if (CollectionUtils.isEmpty(item.getSkuIds())) {
                    List<Long> skuIds = warehouseSkuInventoryMapper.selectSkuIdByWarehouseId(item.getWarehouseId());
                    if (CollectionUtils.isEmpty(skuIds)) {
                        return;
                    }
                    item.setSkuIds(skuIds);
                }
                log.info("[SkuInventorySnapshotUsagePerHourFixCheckJob] 仓库:{}", item.getWarehouseId());
                List<SnapshotWarehouseSkuItem> snapshotWarehouseSkuItems = handleSingleInventoryItem(fixCheckStartDate,
                    updateOrInsert, item);
                if (CollectionUtils.isNotEmpty(snapshotWarehouseSkuItems)) {
                    responseList.addAll(snapshotWarehouseSkuItems);
                }
            });
        log.info("[SkuInventorySnapshotUsagePerHourFixCheckJob] [比对结果] {}", JsonUtil.toJson(responseList));
        log.info("[SkuInventorySnapshotUsagePerHourFixCheckJob] 比对结束");

        return ReturnT.SUCCESS;

    }

    private List<SnapshotWarehouseSkuItem> handleSingleInventoryItem(Date fixCheckStartDate,
        boolean updateOrInsert, SnapshotWarehouseSkuItem item) {
        log.info("[SkuInventorySnapshotUsagePerHourFixCheckJob] fixCheckStartDate:{} item:{}",
            JsonUtil.toJson(fixCheckStartDate), JsonUtil.toJson(item));
        //快照数量
        Map<SnapshotUsageItem, SkuInventorySnapshotUsagePerHour> perHourMap = getSnapshotItemSkuInventorySnapshotPerHourMap(
            fixCheckStartDate, item);

        Date fixCheckEndDate = CurrentDateUtil.newDate();

        List<SnapshotFixUsageCheckItem> filterItems = getSnapshotFixCheckItems(
            fixCheckStartDate, fixCheckEndDate, item, perHourMap);

        if (CollectionUtils.isNotEmpty(filterItems)) {
            fixOrSaveDiff(fixCheckStartDate, fixCheckEndDate, updateOrInsert, perHourMap, filterItems);
        }

        List<SnapshotWarehouseSkuItem> responseList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(filterItems)) {
            Map<Long, Set<Long>> warehouseSkuMap = filterItems.stream().collect(Collectors
                .groupingBy(SnapshotFixUsageCheckItem::getWarehouseId,
                    Collectors.mapping(SnapshotFixUsageCheckItem::getSkuId, Collectors.toSet())));
            log.info("[SkuInventorySnapshotUsagePerHourFixCheckJob] 差异数据:{}", JsonUtil.toJson(filterItems));
            warehouseSkuMap.forEach((k, v) -> {
                if (CollectionUtils.isNotEmpty(v)) {
                    SnapshotWarehouseSkuItem item1 = new SnapshotWarehouseSkuItem(k, Lists.newArrayList(v));
                    responseList.add(item1);
                }
            });
        }
        return responseList;
    }

    public List<SnapshotFixUsageCheckItem> getSnapshotFixCheckItems(Date fixCheckStartDate,
        SnapshotWarehouseSkuItem item) {
        //快照数量
        Map<SnapshotUsageItem, SkuInventorySnapshotUsagePerHour> perHourMap = getSnapshotItemSkuInventorySnapshotPerHourMap(
            fixCheckStartDate, item);

        Date fixCheckEndDate = CurrentDateUtil.newDate();

        return getSnapshotFixCheckItems(fixCheckStartDate, fixCheckEndDate, item, perHourMap);
    }


    private List<SnapshotFixUsageCheckItem> getSnapshotFixCheckItems(Date fixCheckStartDate, Date fixCheckEndDate,
        SnapshotWarehouseSkuItem item, Map<SnapshotUsageItem, SkuInventorySnapshotUsagePerHour> perHourMap) {

        //仓库+sku+lot维度的库存信息
        List<WarehouseSkuInventory> warehouseSkuLotInventories = getWarehouseSkuLotInventories(item);
        //仓库凭证信息
        List<CredentialHeader> credentialHeaders = credentialWrapperManager
            .getCredentialHeaders(item.getWarehouseId(), fixCheckStartDate, fixCheckEndDate);

        List<SnapshotFixUsageCheckItem> snapshotFixCheckItems = new ArrayList<>();

        //在批次库存修改之前的凭证信息
        Map<SnapshotUsageItem, InventoryNumDto> commandInventoryNumDtoMap = getSnapshotItemCommandInventoryNumDtoMap(
            credentialHeaders);

        //逐个处理批次库存
        warehouseSkuLotInventories.forEach(
            warehouseSkuLotInventory -> handleSingleInventory(warehouseSkuLotInventory,
                snapshotFixCheckItems, perHourMap, commandInventoryNumDtoMap));

        return snapshotFixCheckItems.stream().filter(t ->
            t.getInventoryQty().signum() != 0 || t.getCredentialQty().signum() != 0
                || t.getSnapShotQty().signum() != 0).collect(Collectors.toList());
    }

    private void fixOrSaveDiff(Date fixCheckStartDate, Date fixCheckEndDate, boolean updateOrInsert,
        Map<SnapshotUsageItem, SkuInventorySnapshotUsagePerHour> perHourMap,
        List<SnapshotFixUsageCheckItem> filterItems) {
        filterItems.forEach(t -> t.setFixQty(t.getInventoryQty().subtract(t.getCredentialQty())));
        log.info("[SkuInventorySnapshotUsagePerHourFixCheckJob] [存在差异] snapshotFixCheckItems:{}",
            JsonUtil.toJson(filterItems));
        if (enableFix()) {
            fixPerHourData(fixCheckStartDate, updateOrInsert, perHourMap, filterItems);
        } else {
            saveFixCheckDiff(fixCheckStartDate, fixCheckEndDate, filterItems);
        }
    }


    private void saveFixCheckDiff(Date fixCheckStartDate, Date fixCheckEndDate,
        List<SnapshotFixUsageCheckItem> fixCheckItems) {
        if (CollectionUtils.isEmpty(fixCheckItems)) {
            return;
        }
        List<SkuInventorySnapshotUsagePerHourDiff> diffs = Lists.newArrayListWithExpectedSize(fixCheckItems.size());
        fixCheckItems.forEach(t -> {
            SkuInventorySnapshotUsagePerHourDiff diff = new SkuInventorySnapshotUsagePerHourDiff();
            diff.setSkuId(t.getSkuId());
            diff.setWarehouseId(t.getWarehouseId());
            diff.setLogicLocationCode(t.getLogicInventoryLocationCode());
            diff.setCargoOwnerId(t.getCargoOwnerId());
            diff.setUsageCode(t.getUsageCode());
            diff.setSnapshotId(Objects.isNull(t.getPerHourId()) ? 0L : t.getPerHourId());
            diff.setSnapshotQty(t.getSnapShotQty());
            diff.setCredentialQty(t.getCredentialQty());
            diff.setInventoryQty(t.getInventoryQty());
            diff.setTriggerCount(0);
            diff.setCredentialStartDate(fixCheckStartDate);
            diff.setCredentialEndDate(fixCheckEndDate);
            diffs.add(diff);
        });

        Lists.partition(diffs, CommonConstants.BATCH_INSERT_DB_100)
            .forEach(skuInventorySnapshotUsagePerHourDiffMapper::insertList);
    }

    private void fixPerHourData(Date fixCheckStartDate, boolean updateOrInsert,
        Map<SnapshotUsageItem, SkuInventorySnapshotUsagePerHour> perHourMap,
        List<SnapshotFixUsageCheckItem> filterItems) {
        if (updateOrInsert) {
            filterItems = filterItems.stream().filter(t -> Objects.nonNull(t.getPerHourId()))
                .collect(Collectors.toList());
            Lists.partition(filterItems, CommonConstants.BATCH_UPDATE_DB_100).forEach(
                subList -> skuInventorySnapshotUsagePerHourMapper.updateByFixItem(subList, fixCheckStartDate));
        } else {
            List<SkuInventorySnapshotUsagePerHour> needInsertList = new ArrayList<>();
            filterItems.forEach(t -> {
                SnapshotUsageItem snapShotItem = getSnapShotItem(t);
                SkuInventorySnapshotUsagePerHour skuInventorySnapshotPerHour = perHourMap.get(snapShotItem);
                if (Objects.isNull(skuInventorySnapshotPerHour)) {
                    SkuInventorySnapshotUsagePerHour newPerHour = new SkuInventorySnapshotUsagePerHour();
                    newPerHour.setSkuId(snapShotItem.getSkuId());
                    newPerHour.setWarehouseId(snapShotItem.getWarehouseId());
                    newPerHour.setLogicLocationCode(snapShotItem.getLogicLocationCode());
                    newPerHour.setCargoOwnerId(snapShotItem.getCargoOwnerId());
                    newPerHour.setUsageCode(snapShotItem.getUsage());
                    newPerHour.setFreeQty(t.getFixQty());
                    newPerHour.setFrozenQty(new BigDecimal("0"));
                    newPerHour.setTransferIntransitQty(new BigDecimal("0"));
                    newPerHour.setSnapshotDateTime(fixCheckStartDate);
                    needInsertList.add(newPerHour);
                }
            });

            if (CollectionUtils.isNotEmpty(needInsertList)) {
                Lists.partition(needInsertList, CommonConstants.BATCH_INSERT_DB_100)
                    .forEach(skuInventorySnapshotUsagePerHourMapper::insertList);
            }
        }
    }


    private void handleSingleInventory(WarehouseSkuInventory inventory,
        List<SnapshotFixUsageCheckItem> snapshotFixCheckItems,
        Map<SnapshotUsageItem, SkuInventorySnapshotUsagePerHour> perHourMap,
        Map<SnapshotUsageItem, InventoryNumDto> commandInventoryNumDtoMap) {

        SnapshotUsageItem snapShotItem = getSnapShotItem(inventory);
        InventoryNumDto command = commandInventoryNumDtoMap.get(snapShotItem);
        SkuInventorySnapshotUsagePerHour perHour = perHourMap.get(snapShotItem);
        BigDecimal inventoryQty = inventory.getFreeQty();
        BigDecimal commandQty = Objects.isNull(command) ? BigDecimal.ZERO : command.getFreeQty();
        BigDecimal perHourQty = Objects.isNull(perHour) ? BigDecimal.ZERO : perHour.getFreeQty();

        if (perHourQty.add(commandQty).compareTo(inventoryQty) != 0) {
            log.info(
                "[SkuInventorySnapshotUsagePerHourFixCheckJob] [差异] [批次库存差异] warehouseId:{},skuId:{},usageCode:{},credentialQty:{},snapshotQty:{},inventoryQty:{}",
                inventory.getWarehouseId(), inventory.getSkuId(),
                inventory.getUsageCode(), commandQty, perHourQty, inventoryQty);
            SnapshotFixUsageCheckItem snapshotFixCheckItem = getSnapshotFixCheckItem(inventory, perHourQty, commandQty);
            if (Objects.nonNull(perHour)) {
                snapshotFixCheckItem.setPerHourId(perHour.getId());
            }
            snapshotFixCheckItems.add(snapshotFixCheckItem);
        }
        interrupt();
    }

    private Map<SnapshotUsageItem, SkuInventorySnapshotUsagePerHour> getSnapshotItemSkuInventorySnapshotPerHourMap(
        Date fixCheckStartDate, SnapshotWarehouseSkuItem item) {
        List<SkuInventorySnapshotUsagePerHour> snapshotPerHourList = Lists
            .newArrayListWithExpectedSize(item.getSkuIds().size());
        Lists.partition(item.getSkuIds(), CommonConstants.BATCH_SELECT_DB_50)
            .forEach(subList -> snapshotPerHourList.addAll(skuInventorySnapshotUsagePerHourMapper
                .selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(fixCheckStartDate,
                    item.getWarehouseId(), subList)));

        return snapshotPerHourList.stream()
            .collect(Collectors.toMap(SnapshotConverter::getSnapshotUsageItem, Function.identity(), (n1, n2) -> n1));
    }

    private Map<SnapshotUsageItem, InventoryNumDto> getSnapshotItemCommandInventoryNumDtoMap(
        List<CredentialHeader> credentialHeaders) {

        List<CommandInventoryNumDto> commandInventoryNumList = commandManager
            .getCommandInventoryNumList(credentialHeaders);

        //凭证变动的数量
        return commandInventoryNumList.stream()
            .collect(
                Collectors
                    .toMap(SnapshotConverter::getSnapshotUsageItemByCommandDto, SnapshotConverter::getInventoryNumDto,
                        (existingItem, newItem) -> {
                            existingItem.setFreeQty(existingItem.getFreeQty().add(newItem.getFreeQty()));
                            existingItem.setFrozenQty(existingItem.getFrozenQty().add(newItem.getFrozenQty()));
                            existingItem.setTransferIntransitQty(
                                existingItem.getTransferIntransitQty().add(newItem.getTransferIntransitQty()));
                            return existingItem;
                        }
                    ));
    }


    private List<WarehouseSkuInventory> getWarehouseSkuLotInventories(SnapshotWarehouseSkuItem item) {
        List<String> allLocationSortCodes = allLocationSortCodes();
        List<WarehouseSkuInventory> all = Lists.newArrayListWithExpectedSize(item.getSkuIds().size());
        Lists.partition(item.getSkuIds(), CommonConstants.BATCH_SELECT_DB_200)
            .forEach(subList -> all.addAll(warehouseSkuInventoryMapper
                .getByWarehouseIdList(item.getWarehouseId(), allLocationSortCodes, subList)));

        return all;
    }

    private SnapshotUsageItem getSnapShotItem(WarehouseSkuInventory w) {
        return new SnapshotUsageItem(w.getSkuId(), w.getUsageCode(), w.getWarehouseId(),
            w.getLogicInventoryLocationCode(),
            w.getCargoOwnerId());
    }

    private SnapshotUsageItem getSnapShotItem(SnapshotFixUsageCheckItem s) {
        return new SnapshotUsageItem(s.getSkuId(), s.getUsageCode(), s.getWarehouseId(),
            s.getLogicInventoryLocationCode(),
            s.getCargoOwnerId());
    }


    private SnapshotFixUsageCheckItem getSnapshotFixCheckItem(WarehouseSkuInventory w, BigDecimal snapshotQty,
        BigDecimal crdentailQty) {
        return new SnapshotFixUsageCheckItem(w.getSkuId(), w.getWarehouseId(), w.getUsageCode(), w.getFreeQty(),
            snapshotQty,
            crdentailQty, w.getCargoOwnerId(), w.getLogicInventoryLocationCode());
    }


    private void interrupt() {
        boolean interrupt = getInterrupt();
        if (interrupt) {
            throw new ImsBusinessException("手动中断");
        }
    }

    public List<String> allLocationSortCodes() {
        return shardingProperties.getLogicInventoryLocationCodeRouteMap().keySet().stream().sorted()
            .collect(Collectors.toList());
    }

}
