package com.ddmc.ims.job.delete.snapshot;

import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.SkuInventorySnapshotPerHourService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 逻辑库存快照清理job
 */
@Slf4j
@Component
public class SkuInventorySnapshotPerHourClearJob extends AbstractJobHandler {

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private SkuInventorySnapshotPerHourService skuInventorySnapshotPerHourService;

    @Resource
    private AlertService alertService;


    private boolean getInterrupt() {
        return localParamService.getBooleanValue(LocalParamsConstants.STOCK_TRANSACTION_LOG_REPLAY_INTERRUPT, false);
    }

    private Long getSleep() {
        return localParamService.getLongValue(LocalParamsConstants.STOCK_TRANSACTION_LOG_REPLAY_SLEEP, 100L);
    }

    private List<Long> getClearWarehouseIds() {
        return localParamService.getLongListValue(LocalParamsConstants.SKU_INVENTORY_SNAPSHOT_CLEAR_WAREHOUSE,
            Collections.emptyList());
    }


    @XxlJob("SkuInventorySnapshotPerHourClearJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {

        if (StringUtils.isBlank(param)) {
            log.error("[SkuInventorySnapshotPerHourClearJob] param为空");
            return ReturnT.FAIL;
        }
        //手工处理，灰度单节点job执行
        try {

            List<Long> clearWarehouseIds;
            List<Long> paramConfigWarehouseIds = getClearWarehouseIds();
            if (CollectionUtils.isNotEmpty(paramConfigWarehouseIds)) {
                clearWarehouseIds = paramConfigWarehouseIds;
            } else {
                clearWarehouseIds = warehouseService.getAllWarehouseIds();
            }

            Date nowHourDate = ThreadLocalDateUtils.parseYmdhms(param);
            Lists.partition(clearWarehouseIds, 10)
                .forEach(subList -> {
                    log.info("[SkuInventorySnapshotPerHourClearJob] currentList:{}", JsonUtil.toJson(subList));
                    skuInventorySnapshotPerHourService.clearSnapshotPerHour(subList, nowHourDate);
                    sleep();
                    interrupt();
                });
        } catch (Exception e) {
            log.error("[SkuInventorySnapshotPerHourClearJob] 执行处理失败", e);
            alertService.alert("SkuInventorySnapshotPerHourClearJob", "任务执行失败:" + e.getMessage(), AlertLevel.CRITICAL);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }


    @SneakyThrows
    private void sleep() {
        Long sleep = getSleep();
        Thread.sleep(sleep);
    }


    private void interrupt() {
        boolean interrupt = getInterrupt();
        if (interrupt) {
            throw new ImsBusinessException("异动重放中断");
        }
    }

}
