package com.ddmc.ims.job.delete;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.job.AbstractJobHandler;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class DeleteSkuInventorySnapshotPerHourJob extends AbstractJobHandler {


    @Resource
    private SkuInventorySnapshotPerHourMapper skuInventorySnapshotPerHourMapper;


    private void delay() {
        try {
            Thread.sleep(delayOfMillis());
        } catch (InterruptedException interruptedException) {
            log.info("[延时失败]", interruptedException);
            Thread.currentThread().interrupt();
        }
    }

    @XxlJob("deleteSkuInventorySnapshotPerHourJob")
    @Override
    public ReturnT<String> execute(String param) {
        log.info("--- deleteSkuInventorySnapshotPerHourJob start,param:{} ---", param);
        //查询最大最小id
        Date endTime = DateUtils.addDays(CurrentDateUtil.newDate(), -1 * offsetDate());
        Long minId = 0L;
        int loopCount = 0;
        List<SkuInventorySnapshotPerHour> headers = skuInventorySnapshotPerHourMapper.getByMinId(
            minId - 1, 1000);
        while (CollectionUtils.isNotEmpty(headers) && loopCount < 10000) {
            if (stop()) {
                return ReturnT.FAIL;
            }

            if (isDelay()) {
                delay();
            }

            minId = headers.get(headers.size() - 1).getId();
            List<SkuInventorySnapshotPerHour> endTimeBeforeList = headers.stream()
                .filter(h -> h.getCreateTime().before(endTime)
                && h.getSnapshotDateTime().before(endTime))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(endTimeBeforeList)) {
                break;
            }
            deleteList(endTimeBeforeList);
            headers = skuInventorySnapshotPerHourMapper.getByMinId(minId, 1000);
            loopCount++;
        }
        return ReturnT.SUCCESS;
    }

    private void deleteList(List<SkuInventorySnapshotPerHour> endTimeBeforeList) {
        List<Long> ids = endTimeBeforeList.stream().map(SkuInventorySnapshotPerHour::getId)
            .collect(Collectors.toList());
        Lists.partition(ids, CommonConstants.BATCH_INSERT_DB_100)
                .forEach(skuInventorySnapshotPerHourMapper::deleteBatchIds);
    }





    private boolean stop() {
        return localParamService.getBooleanValue("ims.deleteSkuInventorySnapshotPerHourJob.stop",
            false);
    }

    private int offsetDate() {
        return localParamService.getIntValue("ims.deleteSkuInventorySnapshotPerHourJob.offsetDate",
            5);
    }

    private int delayOfMillis() {
        return localParamService.getIntValue("ims.deleteSkuInventorySnapshotPerHourJob.delayOfMillis",
            100);
    }

    private boolean isDelay() {
        return localParamService.getBooleanValue("ims.deleteSkuInventorySnapshotPerHourJob.delay",
            true);
    }
}
