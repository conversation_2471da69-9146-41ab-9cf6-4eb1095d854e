package com.ddmc.ims.job.snapshot;

import com.ddmc.ims.common.constant.DataSourceConstants;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.dynamic.DataSourceSwitch;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dto.WarehouseIdAndSnapshotDateDto;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.service.snapshot.SkuTransferInventorySnapshotPerHourService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 逻辑库存快照，每小时执行，按仓分片广播
 */
@Slf4j
@Component
public class SkuTransferInventorySnapshotPerHourJob extends AbstractJobHandler {

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private LocalParamConfig localParamConfig;

    @Resource
    private SkuTransferInventorySnapshotPerHourService skuTransferInventorySnapshotPerHourService;



    @DataSourceSwitch(DataSourceConstants.SLAVE_SHARDING_DATASOURCE)
    @XxlJob("skuTransferInventorySnapshotPerHourJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Date snapshotDate = DateUtils.truncate(CurrentDateUtil.newDate(), Calendar.DATE);
        List<Long> warehouseIds = Collections.emptyList();
        if (StringUtils.isNotBlank(param)) {
            WarehouseIdAndSnapshotDateDto warehouseIdAndSnapshotDateDto = JsonUtil
                .fromJson(param, WarehouseIdAndSnapshotDateDto.class);
            assert warehouseIdAndSnapshotDateDto != null;
            snapshotDate = warehouseIdAndSnapshotDateDto.getSnapshotDate();
            assert snapshotDate != null;
            warehouseIds = warehouseIdAndSnapshotDateDto.getWarehouseIds();
        }
        if (CollectionUtils.isEmpty(warehouseIds)) {
            warehouseIds = warehouseService.getAllWarehouseIds();
        }

        List<Long> shardingWarehouseIds = getShardingWarehouseIds(warehouseIds);
        shardingWarehouseIds = shardingWarehouseIds.stream()
            .filter(w -> !localParamConfig.excludeWarehouseForSnapshot(w))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shardingWarehouseIds)) {
            log.info("[skuTransferInventorySnapshotPerHourJob] shardingWarehouseIds为空，跳过此次job");
            return ReturnT.SUCCESS;
        }

        List<Long> handleErrWarehouseIds = Collections.synchronizedList(Lists.newArrayList());
        Date finalSnapshotDate = snapshotDate;
        shardingWarehouseIds.forEach(warehouseId -> {
            try {
                skuTransferInventorySnapshotPerHourService
                    .handleWarehouseSnapshotPerHour(finalSnapshotDate, warehouseId);
            } catch (Exception e) {
                handleErrWarehouseIds.add(warehouseId);
                log.error("[skuTransferInventorySnapshotPerHourJob] 执行处理失败", e);
            }
        });
        return ReturnT.SUCCESS;
    }


}
