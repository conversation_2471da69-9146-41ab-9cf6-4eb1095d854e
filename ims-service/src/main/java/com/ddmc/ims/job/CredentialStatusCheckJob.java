package com.ddmc.ims.job;

import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.manager.ocs.InventoryCredentialManager;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.credential.InventoryCredentialService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CredentialStatusCheckJob extends AbstractJobHandler{

    @Resource
    private AlertService alertService;

    @Resource
    private WarehouseMapper warehouseMapper;

    @Resource
    private CredentialHeaderMapper credentialHeaderMapper;

    @Resource
    private InventoryCredentialManager inventoryCredentialManager;

    @Resource
    private InventoryCredentialService inventoryCredentialService;

    private int getCredentialStatusCheckOffsetOnSecond() {
        return localParamService.getIntValue(LocalParamsConstants.CREDENTIAL_STATUS_CHECK_OFFSET_ON_SECOND, 300);
    }

    private int getCredentialStatusCheckRangeOnSecond() {
        return localParamService.getIntValue(LocalParamsConstants.CREDENTIAL_STATUS_CHECK_RANGE_ON_SECOND, 600);
    }

    private int getCredentialStatusCheckLimit() {
        return localParamService.getIntValue(LocalParamsConstants.CREDENTIAL_STATUS_CHECK_LIMIT, 500);
    }

    @XxlJob("CredentialStatusCheckJob")
    @Override
    public ReturnT<String> execute(String param) {
        log.info("[CredentialStatusCheckJob] param -> {}", param);
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("CredentialStatusCheckJob 分片参数：当前分片序号 = {}, 总分片数 = {}",
            shardingVO.getIndex(), shardingVO.getTotal());

        Date endTime = DateUtils.addSeconds(CurrentDateUtil.newDate(), -1 * getCredentialStatusCheckOffsetOnSecond());
        Date startTime = DateUtils.addSeconds(endTime, -1 * getCredentialStatusCheckRangeOnSecond());

        List<CredentialHeader> credentialHeaders = credentialHeaderMapper
            .getInitCredentialHeaders(startTime, endTime, getCredentialStatusCheckLimit())
            .stream().filter(h -> h.getId() % shardingVO.getTotal() == shardingVO.getIndex()).collect(Collectors.toList());

        handleCredentials(credentialHeaders);
        log.info("[CredentialStatusCheckJob]完成");
        return ReturnT.SUCCESS;
    }

    private void handleCredentials(List<CredentialHeader> credentialHeaders) {
        credentialHeaders.forEach(h -> {
                try {
                    handleSingleCredential(h);
                } catch (Exception e) {
                    log.warn("校验库存凭证状态异常->{}", h.getIdempotentId(), e);
                    alertService.alert("[校验库存凭证状态异常]", "校验凭证" + h.getIdempotentId() + "的状态异常" +
                        e.getMessage(), AlertLevel.CRITICAL);
                }
            }
        );
    }

    private void handleSingleCredential(CredentialHeader h) {
        Boolean exists = inventoryCredentialManager.checkInventoryCredentialExists(h.getIdempotentId());
        if (Objects.isNull(exists) || Boolean.FALSE.equals(exists)) {
            inventoryCredentialService.cancel(h.getIdempotentId());
        } else {
            inventoryCredentialService.confirm(h.getIdempotentId());
        }
    }
}
