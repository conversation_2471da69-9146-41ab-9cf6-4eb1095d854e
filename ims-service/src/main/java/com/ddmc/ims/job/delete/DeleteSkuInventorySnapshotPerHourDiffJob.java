package com.ddmc.ims.job.delete;

import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourDiffMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 逻辑库存快照差异job
 */
@Slf4j
@Component
public class DeleteSkuInventorySnapshotPerHourDiffJob extends AbstractDeleteJobHandler {

    @Resource
    private SkuInventorySnapshotPerHourDiffMapper skuInventorySnapshotPerHourDiffMapper;

    @XxlJob("DeleteSkuInventorySnapshotPerHourDiffJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        handleDelete("DeleteSkuInventorySnapshotPerHourDiffJob");
        return ReturnT.SUCCESS;
    }

    @Override
    protected Long getMaxId() {
        return localParamService.getLongValue("ims.DeleteSkuInventorySnapshotPerHourDiffJobMaxId", 0L);
    }


    @Override
    public boolean getInterrupt() {
        return localParamService.getBooleanValue("ims.DeleteSkuInventorySnapshotPerHourDiffJobInterrupt", false);
    }

    @Override
    public Long getSleep() {
        return localParamService.getLongValue("ims.DeleteSkuInventorySnapshotPerHourDiffJobSleep", 50L);
    }

    @Override
    protected Long deleteByMaxId(Long maxId) {
        return skuInventorySnapshotPerHourDiffMapper.deleteByMaxId(maxId);
    }


}
