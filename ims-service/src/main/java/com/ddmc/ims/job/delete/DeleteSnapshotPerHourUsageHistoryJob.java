package com.ddmc.ims.job.delete;

import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 逻辑库存用途维度快照删除job
 */
@Slf4j
@Component
public class DeleteSnapshotPerHourUsageHistoryJob extends AbstractDeleteJobHandler {


    @Resource
    private SkuInventorySnapshotUsagePerHourMapper skuInventorySnapshotUsagePerHourMapper;


    @XxlJob("DeleteSnapshotPerHourUsageHistoryJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        handleDelete("DeleteSnapshotPerHourUsageHistoryJob");
        return ReturnT.SUCCESS;
    }


    @Override
    public Long getMaxId() {
        Long configMaxId = localParamService.getLongValue("ims.DeleteSnapshotPerHourUsageHistoryJobMaxId");
        if (Objects.nonNull(configMaxId)) {
            return configMaxId;
        }

        return skuInventorySnapshotUsagePerHourMapper.selectOneIdByCreateTimeBefore(getMaxDeleteDate());
    }


    private Date getMaxDeleteDate() {
        Integer intValue = localParamService.getIntValue("ims.DeleteSnapshotPerHourUsageHistoryJobDeleteTimeInterval", 15);
        return DateUtil.getPlusStartOfDay(new Date(), -intValue);
    }

    @Override
    public boolean getInterrupt() {
        return localParamService.getBooleanValue("ims.DeleteSnapshotPerHourUsageHistoryJobInterrupt", false);
    }

    @Override
    public Long getSleep() {
        return localParamService.getLongValue("ims.DeleteSnapshotPerHourUsageHistoryJobSleep", 100L);
    }

    @Override
    protected Long deleteByMaxId(Long maxId) {
        return skuInventorySnapshotUsagePerHourMapper.deleteByMaxId(maxId);
    }


}
