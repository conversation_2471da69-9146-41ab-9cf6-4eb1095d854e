package com.ddmc.ims.job.fms;

import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.service.snapshot.SnapshotTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class InitFmsTaskJob extends AbstractJobHandler {
    @Resource
    private SnapshotTaskService snapshotTaskService;

    @XxlJob("initFmsTaskJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("生成财务快照" + param);
        Date snapshotDate = DateUtils.truncate(DateUtils.addDays(CurrentDateUtil.newDate(), -1), Calendar.DATE);
        if (StringUtils.isNotBlank(param)) {
            snapshotDate = ThreadLocalDateUtils.parseYmd(param);
        }
        if(Objects.isNull(snapshotDate)){
            return ReturnT.SUCCESS;
        }
        snapshotTaskService.dayEndTimeNoticeJob(snapshotDate);
        return ReturnT.SUCCESS;
    }
}
