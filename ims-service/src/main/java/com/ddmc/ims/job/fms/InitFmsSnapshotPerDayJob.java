package com.ddmc.ims.job.fms;

import com.ddmc.fms.inv.enums.DsStatus;
import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.dto.WarehouseIdAndSnapshotDateDto;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.FmsSkuInventorySnapshotPerDayService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class InitFmsSnapshotPerDayJob extends AbstractJobHandler {


    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;

    @Resource
    private FmsSkuInventorySnapshotPerDayService fmsSkuInventorySnapshotPerDayService;
    @Resource
    private AlertService alertService;
    @Resource
    private WarehouseMapper warehouseMapper;
    @Resource
    private FmsSnapshotNotifyMapper fmsSnapshotNotifyMapper;
    @Resource
    private LocalParamConfig localParamConfig;
    private boolean isStop() {
        return localParamService.getBooleanValue(LocalParamsConstants.FMS_SNAPSHOT_STOP,
            false);
    }


    @XxlJob("initFmsSnapshotPerDayJob")
    @Override
    public ReturnT<String> execute(String param) {
        // 分片参数
        ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
        XxlJobLogger.log("分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(), shardingVO.getTotal());
        log.info("[skuInventorySnapshotPerDayJob] 分片参数：当前分片序号 = {}, 总分片数 = {}", shardingVO.getIndex(),
            shardingVO.getTotal());

        Date snapshotDate = DateUtils.truncate(DateUtils.addDays(CurrentDateUtil.newDate(), -1), Calendar.DATE);
        List<Long> warehouseIds = Collections.emptyList();
        if (StringUtils.isNotBlank(param)) {
            WarehouseIdAndSnapshotDateDto warehouseIdAndSnapshotDateDto = JsonUtil
                .fromJson(param, WarehouseIdAndSnapshotDateDto.class);
            assert warehouseIdAndSnapshotDateDto != null;
            snapshotDate = warehouseIdAndSnapshotDateDto.getSnapshotDate();
            assert snapshotDate != null;
            warehouseIds = warehouseIdAndSnapshotDateDto.getWarehouseIds();
        }
        if(CollectionUtils.isEmpty(warehouseIds)){
            warehouseIds = warehouseMapper.selectAllId();
        }
        warehouseIds = warehouseIds.stream().filter(w -> !localParamConfig.excludeWarehouseForSnapshot(w))
            .collect(Collectors.toList());
        List<Long> shardingWarehouseIds = warehouseIds.stream().filter(t -> t %  shardingVO.getTotal() == shardingVO.getIndex())
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shardingWarehouseIds)) {
            log.info("[retryFmsSnapshotPerDayJob] shardingWarehouseIds为空，跳过此次job");
            return ReturnT.SUCCESS;
        }

        List<Long> errorWarehouseIds = Lists.newArrayList();
        Date finalSnapshotDate = snapshotDate;
        shardingWarehouseIds.forEach(t -> {
            try {
                if(isStop()){
                    return;
                }
                fmsSkuInventorySnapshotPerDayService
                    .initEndFmsSkuInventorySnapshot( t, finalSnapshotDate);
            } catch (Exception e) {
                log.error("生成财务快照异常,入参{}", JsonUtil.toJson(t), e);
                errorWarehouseIds.add(t);
            }
        });

        log.error("生成财务快照完成,异常数量{},异常数据{}", errorWarehouseIds.size(), JsonUtil.toJson(errorWarehouseIds));
        if (CollectionUtils.isNotEmpty(errorWarehouseIds)) {
            alertService.alert("生成财务快照完成", "异常数量" + errorWarehouseIds.size(), AlertLevel.WARNING);
        }
        saveFmsNotify(snapshotDate);

        return ReturnT.SUCCESS;
    }

    private void saveFmsNotify(Date snapshotDate) {
        FmsSnapshotNotify fmsSnapshotNotify = fmsSnapshotNotifyMapper.selectByDayEndDate(snapshotDate);
        if(Objects.isNull(fmsSnapshotNotify)){
            fmsSnapshotNotify = new FmsSnapshotNotify();
            fmsSnapshotNotify.setDayEndDate(snapshotDate);
            fmsSnapshotNotify.setStatus(DsStatus.RECON_READY.getValue());
            fmsSnapshotNotifyMapper.insert(fmsSnapshotNotify);
        }
    }


}
