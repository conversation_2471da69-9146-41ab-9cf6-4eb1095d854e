package com.ddmc.ims.job.delete.snapshot;

import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper;
import com.ddmc.ims.job.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 逻辑库存快照InOut数据清理job
 */
@Slf4j
@Component
public class SkuInventorySnapshotInOutClearByIdJob extends AbstractJobHandler {




    @Resource
    private SkuInventoryInOutSnapshotMapper skuInventoryInOutSnapshotMapper;

    private Long getMinId() {
        return localParamService.getLongValue("ims.SkuInventorySnapInOutDeleteId", 63179361L);
    }

    private boolean getInterrupt() {
        return localParamService.getBooleanValue(LocalParamsConstants.STOCK_TRANSACTION_LOG_REPLAY_INTERRUPT, false);
    }

    private Long getSleep() {
        return localParamService.getLongValue(LocalParamsConstants.STOCK_TRANSACTION_LOG_REPLAY_SLEEP, 100L);
    }

    @XxlJob("SkuInventorySnapshotInOutClearByIdJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {

        Long minId = getMinId();
        Long total = 0L;
        Long effectNum = skuInventoryInOutSnapshotMapper.deleteByMinId(minId);
        total = total + effectNum;
        while (effectNum > 0) {
            sleep();
            interrupt();
            effectNum = skuInventoryInOutSnapshotMapper.deleteByMinId(minId);
            total = total + effectNum;
            log.info("[SkuInventorySnapshotInOutClearByIdJob] current deleteNum:{}", total);
        }
        log.info("[SkuInventorySnapshotInOutClearByIdJob] end");
        return ReturnT.SUCCESS;
    }

    @SneakyThrows
    private void sleep() {
        Long sleep = getSleep();
        Thread.sleep(sleep);
    }


    private void interrupt() {
        boolean interrupt = getInterrupt();
        if (interrupt) {
            throw new ImsBusinessException("手动中断");
        }
    }
}
