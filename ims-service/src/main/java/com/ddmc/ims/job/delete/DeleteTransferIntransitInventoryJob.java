package com.ddmc.ims.job.delete;

import com.ddmc.ims.common.bo.TransferOrderNoAndSource;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.manager.inventory.TransferInTransitManager;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class DeleteTransferIntransitInventoryJob extends AbstractJobHandler {


    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;
    @Resource
    private TransferInTransitManager transferInTransitManager;



    @XxlJob("deleteTransferIntransitInventoryJob")
    @Override
    public ReturnT<String> execute(String param) {
        log.info("--- deleteTransferIntransitInventoryJob start,param:{} ---", param);
        //查询最大最小id
        Date endTime = DateUtils.addDays(CurrentDateUtil.newDate(), -1 * offsetDate());
        Long minId = 0L;
        int loopCount = 0;
        List<TransferIntransitInventory> headers = transferIntransitInventoryMapper.getByMinId(minId - 1, 1000);
        while (CollectionUtils.isNotEmpty(headers) && loopCount < 10000) {
            if (stop()) {
                return ReturnT.FAIL;
            }

            minId = headers.get(headers.size() - 1).getId();
            List<TransferIntransitInventory> endTimeBeforeList = headers.stream()
                .filter(h -> h.getExpectOutTime().before(endTime) && h.getCreateTime().before(endTime))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(endTimeBeforeList)) {
                break;
            }
            List<TransferOrderNoAndSource> transferOrderNoAndSources = endTimeBeforeList.stream()
                .map(t -> new TransferOrderNoAndSource(t.getOrderSource(), t.getOrderNo())).distinct()
                .collect(Collectors.toList());
            transferOrderNoAndSources.forEach(orderNoAndSource -> transferInTransitManager.deleteTransferInventory(orderNoAndSource, endTime));
            headers = transferIntransitInventoryMapper.getByMinId(minId, 1000);
            loopCount++;
        }
        return ReturnT.SUCCESS;
    }




    private boolean stop() {
        return localParamService.getBooleanValue("ims.deleteTransferIntransitInventoryJob.stop", false);
    }

    private int offsetDate() {
        return localParamService.getIntValue("ims.deleteTransferIntransitInventoryJob.offsetDate", 15);
    }



}
