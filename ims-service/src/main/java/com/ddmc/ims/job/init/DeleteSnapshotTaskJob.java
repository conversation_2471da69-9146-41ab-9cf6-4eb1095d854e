package com.ddmc.ims.job.init;

import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.dal.mapper.ims.CredentialDetailMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.job.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeleteSnapshotTaskJob extends AbstractJobHandler {

    @Resource
    private SnapshotTaskMapper snapshotTaskMapper;

    @Resource
    private CredentialDetailMapper credentialDetailMapper;


    @XxlJob("deleteSnapshotTaskJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("删除快照" + param);

        if (StringUtils.isNotBlank(param)) {
            Date snapshotDate = ThreadLocalDateUtils.parseYmdhms(param);
            snapshotTaskMapper.deleteBySnapshotTimeAndSnapshotType(snapshotDate, SnapshotTypeEnum.INVENTORY_HOUR);
        }
        return ReturnT.SUCCESS;
    }


}


