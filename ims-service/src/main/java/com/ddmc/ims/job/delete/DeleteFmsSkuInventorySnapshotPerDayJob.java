package com.ddmc.ims.job.delete;

import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.dal.mapper.monitor.FmsSkuInventorySnapshotPerDayMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 逻辑库存InOut快照删除job
 */
@Slf4j
@Component
public class DeleteFmsSkuInventorySnapshotPerDayJob extends AbstractDeleteJobHandler {


    @Resource
    private FmsSkuInventorySnapshotPerDayMapper fmsSkuInventorySnapshotPerDayMapper;

    @XxlJob("DeleteFmsSkuInventorySnapshotPerDayJob")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        handleDelete("DeleteFmsSkuInventorySnapshotPerDayJob");
        return ReturnT.SUCCESS;
    }


    @Override
    protected Long getMaxId() {
        Long configMaxId = localParamService.getLongValue("ims.DeleteFmsSkuInventorySnapshotPerDayJobMaxId");
        if (Objects.nonNull(configMaxId)) {
            return configMaxId;
        }

        return fmsSkuInventorySnapshotPerDayMapper.selectOneIdByCreateTimeBefore(getMaxDeleteDate());

    }


    private Date getMaxDeleteDate() {
        Integer intValue = localParamService
            .getIntValue("ims.DeleteFmsSkuInventorySnapshotPerDayJobDeleteTimeInterval", 15);
        return DateUtil.getPlusStartOfDay(CurrentDateUtil.newDate(), -intValue);
    }

    @Override
    protected boolean getInterrupt() {
        return localParamService.getBooleanValue("ims.DeleteFmsSkuInventorySnapshotPerDayJobInterrupt", false);
    }

    @Override
    protected Long getSleep() {
        return localParamService.getLongValue("ims.DeleteFmsSkuInventorySnapshotPerDayJobSleep", 10L);
    }

    @Override
    protected Long deleteByMaxId(Long maxId) {
        return fmsSkuInventorySnapshotPerDayMapper.deleteByMaxId(maxId);
    }

}
