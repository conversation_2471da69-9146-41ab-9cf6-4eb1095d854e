package com.ddmc.ims.job.event;

import com.ddmc.ims.common.constant.EventBusConstants;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.dto.EventTimeoutWarningCountDto;
import com.ddmc.ims.job.AbstractJobHandler;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.event.EventService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Event事件超时未处理告警
 */
@Component
@Slf4j
public class EventTimeoutWarningJob extends AbstractJobHandler {

    @Resource
    private EventService eventService;
    @Resource
    private AlertService alertService;
    @Value("${eventTimeoutWarning:10}")
    private Integer eventTimeoutWarning;
    //事件执行只要执行了指定次数就不告警配置
    @Value("${eventTimeoutWarningCount}")
    private String eventTimeoutWarningCount;


    @XxlJob("EventTimeoutWarningJob")
    @Override
    public ReturnT<String> execute(String param) {
        log.info("EventTimeoutWarningJob start");
        ListEventCondition condition = new ListEventCondition();
        condition.setStatusList(EventBusConstants.WAIT_STATUS_LIST_MANUAL);
        condition.setCreateTimeEnd(DateUtils.addMinutes(CurrentDateUtil.newDate(), -eventTimeoutWarning));
        List<EventEntity> list = eventService.listByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> eventIds = new ArrayList<>();
            if (StringUtils.isNotBlank(eventTimeoutWarningCount)) {
                List<EventTimeoutWarningCountDto> countList = JsonUtils
                    .parseList(eventTimeoutWarningCount, EventTimeoutWarningCountDto.class);
                Map<String, Integer> countMap = countList.stream().collect(
                    Collectors.toMap(EventTimeoutWarningCountDto::getKeyType,
                        EventTimeoutWarningCountDto::getStopWarningTriggerCount));
                for (EventEntity eventEntity : list) {
                    Integer stopWarningTriggerCount = countMap.get(eventEntity.getKeyType());
                    if (Objects.isNull(stopWarningTriggerCount)
                        || eventEntity.getTriggerCount().compareTo(stopWarningTriggerCount) < 0) {
                        eventIds.add(eventEntity.getId());
                    }
                }
            } else {
                eventIds = list.stream().map(EventEntity::getId).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(eventIds)) {
                alertService.alertWarning("存在超" + eventTimeoutWarning + "分钟未处理Event事件",
                    "共计" + eventIds.size() + "条, 详情如下: " + JsonUtil.toJson(eventIds));
            }
        }
        log.info("EventTimeoutWarningJob end");
        return ReturnT.SUCCESS;
    }

}
