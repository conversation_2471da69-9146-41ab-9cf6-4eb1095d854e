package com.ddmc.ims.metrics;

import com.ddmc.ims.config.param.LocalParamConfig;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import com.google.common.collect.RangeMap;
import com.google.common.collect.TreeRangeMap;
import java.util.Date;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Metrics 打点
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DocMetricsService {

    public static final String TIME_LESS_0 = "-1";


    /**
     * IMS-单据超时预警
     */
    public static final String IMS_BIZ_DOC_OVERDUE_HANDLE = "ims_biz_doc_overdue_handle";

    @Resource
    private LocalParamConfig localParamConfig;

    public static final RangeMap<Long, String> rangeMap = TreeRangeMap.create();

    static {
        rangeMap.put(Range.closed(11L, 15L), String.valueOf(11));
        rangeMap.put(Range.closed(15L, 20L), String.valueOf(15));
        rangeMap.put(Range.closed(20L, 30L), String.valueOf(20));
        rangeMap.put(Range.closed(30L, 60L), String.valueOf(30));
        rangeMap.put(Range.closed(60L, 120L), String.valueOf(60));
        rangeMap.put(Range.closed(120L, 300L), String.valueOf(120));
        rangeMap.put(Range.atLeast(300L), String.valueOf(300));
    }

    /**
     * IMS-单据超时处理
     *
     * @param orderType 单据类型
     * @param operateType 单据操作
     * @param businessTime 业务时间
     */

    public void metricDelayTime(String orderType, Integer operateType, @NotNull Date businessTime) {
        try {
            // 超期时间
            long overdueMillisTime = System.currentTimeMillis() - businessTime.getTime();
            final Map<String, String> map = Maps.newHashMap();
            map.put("orderType", orderType);
            map.put("operateType", String.valueOf(operateType));
            map.put("delayMillis", formatDelayTime(overdueMillisTime));
            MetricTraceUtils.recordInvokeCountMetric(IMS_BIZ_DOC_OVERDUE_HANDLE, map, 1);
        } catch (Exception ex) {
            log.error("[DocMetricsService#metricOverdue] metric doc:{}!", orderType, ex);
        }
    }

    private String formatDelayTime(long overdueMillisTime) {
        long delaySeconds = overdueMillisTime / 1000;
        if (delaySeconds < 0) {
            return TIME_LESS_0;
        }
        if (delaySeconds <= 10) {
            return String.valueOf(delaySeconds);
        }
        return rangeMap.get(delaySeconds);


    }

}
