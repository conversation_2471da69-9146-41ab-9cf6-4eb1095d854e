package com.ddmc.ims.metrics;


import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.common.AttributesBuilder;
import com.csoss.monitor.api.metrics.Counter;
import com.csoss.monitor.api.metrics.Metrics;
import java.util.Map;
import javax.validation.constraints.NotNull;
import org.apache.commons.collections4.MapUtils;

/**
 * Metrics Trace Utils
 *
 * <AUTHOR>
 **/
public final class MetricTraceUtils {

    private MetricTraceUtils() {
    }

    public static void recordInvokeCountMetric(@NotNull String metricName, Map<String, String> attributes, int value) {
        Counter counter = Metrics
            .newCounter(metricName)
            .build();
        if (MapUtils.isNotEmpty(attributes)) {
            AttributesBuilder attributesBuilder = Attributes.builder();
            attributes.forEach((k, v) -> attributesBuilder.put(AttributeKey.stringKey(k), v));
            counter.bind(attributesBuilder.build()).add(value);
        } else {
            counter.add(value);
        }
    }


}

