package com.ddmc.ims.config.sharding.strategy;

import com.ddmc.ims.common.util.ShardingDbUtil;
import com.ddmc.ims.config.sharding.ShardingProperties;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * <AUTHOR>
 * @description: warehouse_sku_inventory分表规则
 * @date 2021/7/26
 */
@Component
public class WarehouseSkuInventoryAlgorithm implements PreciseShardingAlgorithm<String> {

    @Resource
    private ShardingProperties shardingProperties;


    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> shardingValue) {
        String logicInventoryLocationCode = shardingValue.getValue();
        Integer tableIndex = shardingProperties.getLogicInventoryLocationCodeRouteMap().get(logicInventoryLocationCode);
        return ShardingDbUtil.getActualTableName(shardingValue.getLogicTableName(), tableIndex.toString());
    }
}
