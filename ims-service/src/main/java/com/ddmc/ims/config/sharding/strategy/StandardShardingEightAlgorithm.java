package com.ddmc.ims.config.sharding.strategy;

import com.ddmc.ims.config.sharding.ShardingProperties;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 * @description: 分片算法,根据商品id分表
 * @date 2021/7/26
 */
@Component
public class StandardShardingEightAlgorithm extends BaseShardingAlgorithm<Long> {

    @Override
    public String getActualTableIndex(Long shardingValue, Integer tableNum) {
        return String.valueOf((Math.abs(shardingValue.intValue())) % tableNum);
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> shardingValue) {
        return doSharding(shardingValue, ShardingProperties.TABLE_NUM_8);
    }
}
