package com.ddmc.ims.config;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class SpringProfileConfig {

    private static final String DEFAULT = "default";
    private static final String DEV = "DEV";
    private static final String PE = "PE";
    //压力测试环境
    private static final String UA = "UA";

    private static final String SHT4PE = "SHT4PE";

    private static final String SHT5PE = "SHT5PE";

    private static final String SHHW1PE = "SHHW1PE";

    private static final String EAST1GRAY = "EAST1GRAY";

    private static final String EAST1PE = "EAST1PE";

    private static final List<String> TEST_LIST = new ArrayList<>();
    static {
        TEST_LIST.add("TE");
        TEST_LIST.add("RE");
        TEST_LIST.add("XE");
        TEST_LIST.add("ME");
        TEST_LIST.add("YE");
        TEST_LIST.add("UA");
    }

    @Value("${spring.profiles.active:DEV}")
    private String activeProfile;

    private boolean devFlag;

    private boolean peFlag;

    private boolean testFlag;

    @PostConstruct
    public void init() {
        devFlag = activeProfile.equalsIgnoreCase(DEV) || activeProfile.equalsIgnoreCase(DEFAULT)
            || activeProfile.equalsIgnoreCase(UA);
        testFlag = TEST_LIST.contains(activeProfile.toUpperCase());
        peFlag = activeProfile.equalsIgnoreCase(PE) || activeProfile.equalsIgnoreCase(SHT4PE)
        || activeProfile.equalsIgnoreCase(SHT5PE) || activeProfile.equalsIgnoreCase(SHHW1PE)
            || activeProfile.equalsIgnoreCase(EAST1GRAY) || activeProfile.equalsIgnoreCase(EAST1PE);
    }

    /**
     * @return 是否开发环境
     */
    public boolean isDev() {
        return devFlag;
    }

    /**
     * @return 是否生产环境
     */
    public boolean isPe() {
        return peFlag;
    }

    /**
     * @return 当前配置的Profile
     */
    public String getProfile() {
        return activeProfile;
    }

    /**
     * @return 是否测试环境
     */
    public boolean isTest() {
        return testFlag;
    }
}
