package com.ddmc.ims.config.datasource;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.jdbc.pool.config.apollo.ConfigBuilder;
import com.ddmc.jdbc.pool.datasource.DdmcDataSource;
import java.util.Map;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

/**
 * scm_ims_monitor数据源配置
 */
@Slf4j
@Configuration
@MapperScan(basePackages = {"com.ddmc.ims.dal.mapper.monitor"}, sqlSessionTemplateRef = "imsMonitorSqlSessionTemplate")
public class ImsMonitorDaoConfiguration {

    @Value("${ims.monitor.datasource.resourceName:scm_ims_monitor_group}")
    private String imsMonitorResourceName;

    @Value("${ims.monitor.datasource.maxPoolSize:30}")
    private int maxPoolSize;

    @Value("${ims.monitor.datasource.minPoolSize:1}")
    private int minPoolSize;

    @Bean(name = "imsMonitorDataSource")
    public DataSource getDataSource() {
        DdmcDataSource ds = new DdmcDataSource();
        Map<String, String> config = ConfigBuilder.newInstance()
            .resourceName(imsMonitorResourceName)
            .bindMaster(false)
            .maxPoolSize(maxPoolSize)
            .minPoolSize(minPoolSize)
            .extraJDBCParams(
                "useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull&serverTimezone=GMT%2B8")
            .build();
        ds.setConfigServiceMeta(config);
        ds.init();
        return ds;
    }

    @Bean(name = "imsMonitorSqlSessionFactory")
    public SqlSessionFactory imsMonitorSqlSessionFactory(@Qualifier("imsMonitorDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:sqlmap/monitor/*.xml"));
        bean.setTypeHandlersPackage("com.ddmc.ims.config.ibatis");
        return bean.getObject();
    }

    @Bean("imsMonitorSqlSessionTemplate")
    public SqlSessionTemplate imsMonitorSqlSessionTemplate(
        @Qualifier("imsMonitorSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }


    @Bean(CommonConstants.IMS_MONITOR_DB_TRANSACTION_MANAGER)
    public DataSourceTransactionManager transactionManager(@Qualifier("imsMonitorDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
