package com.ddmc.ims.config.eventbus;

import com.ddmc.ims.common.enums.common.EventExecuteServiceNameEnum;
import com.ddmc.ims.common.util.JsonUtils;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import lombok.extern.slf4j.Slf4j;

import java.util.EnumMap;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.ThreadPoolExecutor.DiscardPolicy;
import java.util.concurrent.TimeUnit;

/**
 * 事件执行器定义
 */
@Slf4j
public class EventBusFactory {

    //异步执行器
    private static final EventBus asyncEventBus;
    //同步执行器
    private static final EventBus syncEventBus = new EventBus("sync_event_bus");
    //商品来源业务独立异步执行器
    private static final EventBus skuSourceAsyncEventBus;
    //独立执行器map 执行器名称 -> 执行器
    private static final Map<EventExecuteServiceNameEnum, EventBus> eventBusMap = new EnumMap<>(EventExecuteServiceNameEnum.class);

    static {
        BlockingQueue<Runnable> blockingQueue = new LinkedBlockingQueue<>(2000);
        MonitoredThreadPoolExecutor threadPoolExecutor = new MonitoredThreadPoolExecutor(10,
            10,
            60,
            TimeUnit.SECONDS,
            blockingQueue,
            new CallerRunsPolicy());
        //线程池拒绝策略：由调用方线程执行
        threadPoolExecutor.setPoolName("async_event_bus");
        asyncEventBus = new AsyncEventBus(threadPoolExecutor,
            (exception, context) ->
                log.error(
                    "event bus subscriber handle error, subscribe:{}, method:{}, event:{}, error:",
                    context.getSubscriber().getClass().getSimpleName(),
                    context.getSubscriberMethod().getName(),
                    JsonUtils.toJson(context.getEvent()),
                    exception));

        //商品来源计算线程池
        BlockingQueue<Runnable> skuSourceBlockingQueue = new LinkedBlockingQueue<>(10000);
        MonitoredThreadPoolExecutor skuSourceThreadPoolExecutor = new MonitoredThreadPoolExecutor(2,
            2,
            60,
            TimeUnit.SECONDS,
            skuSourceBlockingQueue,
            new DiscardPolicy());
        //拒绝策略：直接静默抛弃  由定时器去补偿执行
        skuSourceThreadPoolExecutor.setPoolName("skuSource_async_event_bus");
        skuSourceAsyncEventBus = new AsyncEventBus(skuSourceThreadPoolExecutor,
            (exception, context) ->
                log.error(
                    "skuSource event bus subscriber handle error, subscribe:{}, method:{}, event:{}, error:",
                    context.getSubscriber().getClass().getSimpleName(),
                    context.getSubscriberMethod().getName(),
                    JsonUtils.toJson(context.getEvent()),
                    exception));


        eventBusMap.put(EventExecuteServiceNameEnum.SNAPSHOT_INVENTORY_DIFF, buildEventBus(10, 10,
                60, 2000, new CallerRunsPolicy(), EventExecuteServiceNameEnum.SNAPSHOT_INVENTORY_DIFF.name()));

        eventBusMap.put(EventExecuteServiceNameEnum.TRANSFER_OUT_CHANGE_NOTIFY, buildEventBus(20, 20,
            60, 2000, new DiscardPolicy(), EventExecuteServiceNameEnum.TRANSFER_OUT_CHANGE_NOTIFY.name()));

    }

    /**
     * 构建事件总线
     * @param corePoolSize 核心线程数
     * @param maxPoolSize 最大线程数
     * @param keepAliveTime 线程最大空闲时间，单位为秒
     * @param maxTaskNum 任务队列最大任务数量
     * @param rejectedExecutionHandler 拒绝策略
     * @param poolName 线程池名称
     * @return 事件总线
     */
    private static EventBus buildEventBus(int corePoolSize, int maxPoolSize, int keepAliveTime,
                                          int maxTaskNum, RejectedExecutionHandler rejectedExecutionHandler, String poolName) {
        BlockingQueue<Runnable> blockingQueue = new LinkedBlockingQueue<>(maxTaskNum);
        MonitoredThreadPoolExecutor threadPoolExecutor = new MonitoredThreadPoolExecutor(corePoolSize,
            maxPoolSize,
            keepAliveTime,
            TimeUnit.SECONDS,
            blockingQueue,
            rejectedExecutionHandler);
        //拒绝策略：直接静默抛弃  由定时器去补偿执行
        threadPoolExecutor.setPoolName(poolName);
        return new AsyncEventBus(threadPoolExecutor,
            (exception, context) ->
                log.error(
                    "skuSource event bus subscriber handle error, subscribe:{}, method:{}, event:{}, error:",
                    context.getSubscriber().getClass().getSimpleName(),
                    context.getSubscriberMethod().getName(),
                    JsonUtils.toJson(context.getEvent()),
                    exception));
    }

    private EventBusFactory() {
    }

    public static EventBus getSyncEventBus() {
        return syncEventBus;
    }

    public static EventBus getAsyncEventBus() {
        return asyncEventBus;
    }

    public static EventBus getSkuSourceAsyncEventBus() {
        return skuSourceAsyncEventBus;
    }

    public static EventBus getAsyncEventBusByEventExecuteServiceName(EventExecuteServiceNameEnum eventExecuteServiceNameEnum) {
        return eventBusMap.get(eventExecuteServiceNameEnum);
    }
}
