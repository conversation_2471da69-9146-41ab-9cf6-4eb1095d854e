package com.ddmc.ims.config.auditlog;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 安全日志请求
 */
@Data
public class AuditLogRequest {

    /**
     * 操作时间 符合ISO8601标准 例如：2021-04-26T11:18:20+08:00
     */
    @JsonProperty("op_time")
    private String opTime;

    /**
     * 操作类型 操作类型，增删改查 CRUDC: 增R: 查U: 改D: 删根据操作自由拼接四个大写字母 例如：CR
     */
    @JsonProperty("op_type")
    private String opType;

    /**
     * 操作模块 操作当前系统中的那个模块，含子模块。父子模块之间用"-"连接。 模块自行定义，能表明操作了哪个模块即可。比如登入、登入等功能，需要明确表达出这个意思 例如：权限中心-用户管理
     */
    @JsonProperty("sys_module")
    private String sysModule;

    /**
     * 操作对象 操作对象无特定格式要求如果是查询，则存储查询的条件例如是登录（实际是查询）， 则传输登录的用户名等。例如删除了用户，则传输被删除的用户名。 例如：username=test
     */
    @JsonProperty("op_target")
    private String opTarget;

    /**
     * 操作人邮箱 例如：<EMAIL>
     */
    @JsonProperty("op_mail")
    private String opMail;

    /**
     * 操作人账户名 例如：赵建军/zhaojianjun/赵建军的工号
     */
    @JsonProperty("op_user_name")
    private String opUserName;

    /**
     * 操作人类型 操作人账号属于哪个渠道 例如：员工/供应商/外包，可自行定义能表明即可
     */
    @JsonProperty("op_user_type")
    private String opUserType;

    /**
     * 操作结果 描述操作结果。是否操作成功、返回条数等。 格式为"1,-1"，两个数字中间由英文半角逗号分隔。 这里第一个“1”代表成功（失败则为0），第二个“-1”代表没有条数信息，
     * 如果操作有条数信息返回（例如查询），则返回条目数。 例如 “0,-1“ 代表操作失败。例如“1,100”代表操作成功，返回100条数据。 例如“1,-1”代表操作成功，但没有返回数据条数信息。 例如：1,-1
     */
    @JsonProperty("op_result")
    private String opResult;

    /**
     * 原始请求包 原始数据包请求（get or post） 例如：list?page=1&rows=300&keyword=&mobile=&job_number=&sort=create_time&order=desc&role_id=&department_id=&status=3
     */
    @JsonProperty("orig_req")
    private String origReq;

    /**
     * user_agent 传操作人的操作浏览器UA 例如：Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like
     * Gecko) Version/14.0.2 Safari/605.1.15
     */
    @JsonProperty("op_ua")
    private String opUa;

    /**
     * 来源ip 传操作人IP，如内网操作则传内网IP 例如：*******
     */
    @JsonProperty("op_ip")
    private String opIp;

    /**
     * 其他 日志系统未定义但需要保存的其他信息
     */
    private String others;


}
