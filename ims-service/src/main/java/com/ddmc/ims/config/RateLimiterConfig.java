package com.ddmc.ims.config;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 通用常量类
 * @date 2022/4/2 22:26
 */

@Component
@Slf4j
public class RateLimiterConfig implements InitializingBean {

    private static final String RATE_LIMIT_RULE_CONFIG = "rate.limit.rule.config";


    public static final String INIT_LOT_INFO_LIMIT = "ims.initLotInfoLimit";

    private final ConcurrentHashMap<String, RateLimiter> rateLimiterMapping = new ConcurrentHashMap<>();

    private static final double DEFAULT_RATE_LIMIT = 200;

    /**
     * 默认限流配置
     */
    private String rateLimitDefaultConfig = "[{\"key\":\"sku.db.limit\",\"rateLimit\":200},{\"key\":\"sku.region.db.limit\",\"rateLimit\":200},{\"key\":\"sku.store.db.limit\",\"rateLimit\":200},{\"key\":\"product.supply.chain.db.request.limit\",\"rateLimit\":200}]";

    /**
     * 默认限流配置
     */
    @Value("${rate.limit.rule.config:[]}")
    private String rateLimitRuleConfig;

    @ApolloConfigChangeListener
    public void mqSwitchRuleOnChangeListener(ConfigChangeEvent changeEvent) {
        boolean isChanged = changeEvent.isChanged(RATE_LIMIT_RULE_CONFIG);
        if (isChanged) {
            ConfigChange change = changeEvent.getChange(RATE_LIMIT_RULE_CONFIG);
            log.info(
                    "mqSwitchRule apollo change - key: {}, oldValue: {}, newValue: {}, changeType: {}",
                    change.getPropertyName(), change.getOldValue(),
                    change.getNewValue(), change.getChangeType());
            this.rateLimitRuleConfig = change.getNewValue();
            setRateLimitRule();
        }
    }


    @Override
    public void afterPropertiesSet() {
        setRateLimitRule();
    }

    private void setRateLimitRule(){
        List<RateLimitRule> rateLimitRuleList;
        if (StringUtils.isNotEmpty(rateLimitRuleConfig)) {
            rateLimitRuleList = JSON.parseArray(rateLimitRuleConfig, RateLimitRule.class);
        }else {
            rateLimitRuleList = JSON.parseArray(rateLimitDefaultConfig, RateLimitRule.class);
        }
        if (CollectionUtils.isEmpty(rateLimitRuleList)) {
            return;
        }
        for (RateLimitRule rateLimitRule : rateLimitRuleList) {
            String key = rateLimitRule.getKey();
            Long rateLimit = rateLimitRule.getRateLimit();
            if (StringUtils.isNotEmpty(key) && rateLimit != null) {
                RateLimiter tempRateLimiter = rateLimiterMapping.get(key);
                if (tempRateLimiter == null) {
                    RateLimiter rateLimiter = RateLimiter.create(rateLimit);
                    rateLimiterMapping.put(key, rateLimiter);
                }else {
                    tempRateLimiter.setRate(rateLimit);
                }
            }
        }
    }

    public RateLimiter getRateLimiterByKey(String key){
        RateLimiter rateLimiter = rateLimiterMapping.get(key);
        if (rateLimiter == null) {
            log.error("not config rate limit for key: {}", key);
            return RateLimiter.create(DEFAULT_RATE_LIMIT);
        }
        return rateLimiter;
    }
}
