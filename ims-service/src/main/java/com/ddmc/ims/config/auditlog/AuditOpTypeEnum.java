package com.ddmc.ims.config.auditlog;

/**
 * 安全日志操作类型
 */
public enum AuditOpTypeEnum {

    CREATE("C", "增加"),
    READ("R", "查询"),
    UPDATE("U", "更新"),
    DELETE("D", "删除"),
    ;

    private final String code;
    private final String desc;

    AuditOpTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
