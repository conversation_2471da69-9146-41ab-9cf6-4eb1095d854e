package com.ddmc.ims.config.sharding;

import com.ddmc.ims.config.sharding.strategy.StandardShardingEightAlgorithm;
import com.ddmc.ims.config.sharding.strategy.StandardShardingFourAlgorithm;
import com.ddmc.ims.config.sharding.strategy.WarehouseSkuInventoryAlgorithm;
import com.ddmc.ims.config.sharding.strategy.WarehouseSkuLotInventoryComplexAlgorithm;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.config.sharding.ShardingRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.TableRuleConfiguration;
import org.apache.shardingsphere.api.config.sharding.strategy.ComplexShardingStrategyConfiguration;
import org.apache.shardingsphere.api.config.sharding.strategy.StandardShardingStrategyConfiguration;
import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;

/**
 * 分表分片数据源配置：提供精确路由配置.
 */
@Slf4j
@Configuration
public class ShardingConfiguration {

    @Resource
    private ShardingProperties shardingProperties;

    @Resource
    private StandardShardingFourAlgorithm standardShardingFourAlgorithm;

    @Resource
    private StandardShardingEightAlgorithm standardShardingEightAlgorithm;

    @Resource
    private WarehouseSkuInventoryAlgorithm warehouseSkuInventoryAlgorithm;

    @Resource
    private WarehouseSkuLotInventoryComplexAlgorithm warehouseSkuLotInventoryComplexAlgorithm;

    /**
     * 创建分表分片规则
     */
    public ShardingRuleConfiguration createShardingRule() {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        // 批次信息分片规则
        shardingRuleConfig.getTableRuleConfigs().add(skuInventoryInfoTableRuleConfiguration());

        //在库库存分表规则
        shardingRuleConfig.getTableRuleConfigs().add(warehouseSkuInventoryTableRuleConfiguration());

        //在库批次库存分表规则
        shardingRuleConfig.getTableRuleConfigs().add(warehouseSkuLotInventoryTableRuleConfiguration());
        return shardingRuleConfig;
    }

    /**
     * 货品批次信息分片规则.
     */
    private TableRuleConfiguration skuInventoryInfoTableRuleConfiguration() {
        return getStandardTableRuleConfiguration(ShardingProperties.INVENTORY_LOT_INFO,
            ShardingProperties.INVENTORY_LOT_INFO_SHARDING_KEY, ShardingProperties.TABLE_NUM_8, standardShardingEightAlgorithm);
    }

    /**
     * 在库库存分片规则
     * @return 规则配置
     */
    private TableRuleConfiguration warehouseSkuInventoryTableRuleConfiguration() {
        return getStandardTableRuleConfiguration(ShardingProperties.WAREHOUSE_SKU_INVENTORY,
            ShardingProperties.WAREHOUSE_SKU_INVENTORY_SHARDING_KEY, ShardingProperties.DATABASE_NUM_2, warehouseSkuInventoryAlgorithm);
    }

    /**
     * 在库批次库存分片规则
     * @return 规则配置
     */
    private TableRuleConfiguration warehouseSkuLotInventoryTableRuleConfiguration() {
        return getComplexTableRuleConfiguration(ShardingProperties.WAREHOUSE_SKU_LOT_INVENTORY,
            Lists.newArrayList(ShardingProperties.WAREHOUSE_SKU_LOT_INVENTORY_FIRST_SHARDING_KEY, ShardingProperties.WAREHOUSE_SKU_LOT_INVENTORY_SECOND_SHARDING_KEY),
            ShardingProperties.TABLE_NUM_8, warehouseSkuLotInventoryComplexAlgorithm);
    }



    /**
     * 获取标准分片配置：只能有一个分片键.
     *
     * @param logicalTableName 逻辑表名
     * @param shardingColumn 分片列
     * @param tableNum 分表数量
     * @param shardingAlgorithm 分片算法
     */
    private TableRuleConfiguration getStandardTableRuleConfiguration(String logicalTableName, String shardingColumn,
        int tableNum, PreciseShardingAlgorithm<?> shardingAlgorithm) {
        String dataNodeList = getDoDataNodes(ShardingProperties.DATABASE_NAME, logicalTableName, tableNum);
        TableRuleConfiguration tableRuleConfiguration = new TableRuleConfiguration(logicalTableName, dataNodeList);
        // 创建分片策略
        StandardShardingStrategyConfiguration strategyConfiguration = new StandardShardingStrategyConfiguration(
            shardingColumn, shardingAlgorithm);
        // 设置表分片策略
        tableRuleConfiguration.setTableShardingStrategyConfig(strategyConfiguration);
        return tableRuleConfiguration;
    }

    /**
     * 获取复杂分片配置：有多个个分片键.
     *
     * @param logicalTableName 逻辑表名
     * @param shardingColumns 分片列
     * @param tableNum 分表数量
     * @param shardingAlgorithm 分片算法
     */
    private TableRuleConfiguration getComplexTableRuleConfiguration(String logicalTableName, List<String> shardingColumns,
                                                                     int tableNum, ComplexKeysShardingAlgorithm<?> shardingAlgorithm) {
        String dataNodeList = getDoDataNodes(ShardingProperties.DATABASE_NAME, logicalTableName, tableNum);
        TableRuleConfiguration tableRuleConfiguration = new TableRuleConfiguration(logicalTableName, dataNodeList);
        // 创建分片策略
        ComplexShardingStrategyConfiguration strategyConfiguration = new ComplexShardingStrategyConfiguration(
            String.join(",",shardingColumns), shardingAlgorithm);
        // 设置表分片策略
        tableRuleConfiguration.setTableShardingStrategyConfig(strategyConfiguration);
        return tableRuleConfiguration;
    }

    /**
     * 获取数据结点表达式：sharding-sphere 规则，不要随便修改
     *
     * @param databaseName 逻辑库名
     * @param tableName 逻辑表名
     * @param tableNum 每个库的表个数
     */
    private String getDoDataNodes(String databaseName, String tableName, int tableNum) {
        String dataNodes;
        if (tableNum > 0) {
            dataNodes = String
                .format("%s.%s_${0..%d}", databaseName, tableName, tableNum - 1);
        } else {
            dataNodes = String.format("%s.%s", databaseName, tableName);
        }
        return dataNodes;
    }
}