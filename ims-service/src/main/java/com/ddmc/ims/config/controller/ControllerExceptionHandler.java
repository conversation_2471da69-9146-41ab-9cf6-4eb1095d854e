package com.ddmc.ims.config.controller;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.exception.ImsRemoteInvocationException;
import com.ddmc.ims.common.exception.RedisLockTimeOutException;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
public class ControllerExceptionHandler {

    @ExceptionHandler(RuntimeException.class)
    @SuppressWarnings("unchecked")
    public ResponseEntity<ResponseBaseVo<Object>> handleEx(RuntimeException e) {
        log.error("[SYS_ERR] error:{}", e.getMessage(), e);
        //设置打点信息
        addRequestException("RuntimeException");
        return ResponseEntity
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON_UTF8)
            .body(
                ResponseBaseVo.fail(CommonErrorCode.SYS_ERR.getErrorCode(), e.getMessage())
            );
    }

    /**
     * 断言异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @SuppressWarnings("unchecked")
    public ResponseEntity<ResponseBaseVo<Object>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("[Illegal_Param] - error:{}", e.getMessage(), e);
        //设置打点信息
        addRequestException("IllegalArgumentException");
        return ResponseEntity
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON_UTF8)
            .body(
                ResponseBaseVo.fail(
                    CommonErrorCode.ILLEGAL_PARAMETER.getErrorCode(),
                    e.getMessage())
            );
    }

    /**
     * hibernate 原生校验异常抓捕
     */
    @ExceptionHandler({ValidationException.class})
    @SuppressWarnings("unchecked")
    public ResponseEntity<ResponseBaseVo<Object>> handleValidationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        String message = constraintViolations.stream()
            .map(ConstraintViolation::getMessageTemplate)
            .collect(Collectors.joining(","));

        log.error("[Validation_Error] error:{}", message, e);
        //设置打点信息
        addRequestException("ConstraintViolationException");

        return ResponseEntity
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON_UTF8)
            .body(ResponseBaseVo.fail(CommonErrorCode.ILLEGAL_PARAMETER.getCode(), message));
    }

    /**
     * controller层参数校验抓捕
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @SuppressWarnings("unchecked")
    public ResponseEntity<ResponseBaseVo<Object>> handleSpringMVCValidation(MethodArgumentNotValidException e) {
        StringBuilder errorMessage = new StringBuilder();

        for (ObjectError error : e.getBindingResult().getAllErrors()) {
            if (errorMessage.length() > 0) {
                errorMessage.append(",");
            }
            if (error instanceof FieldError) {
                FieldError fieldError = (FieldError) error;
                errorMessage.append(fieldError.getDefaultMessage()).append(";");
            } else {
                errorMessage.append(error.getDefaultMessage()).append(";");
            }
        }

        errorMessage.deleteCharAt(errorMessage.length() - 1);

        log.error("[Validation_Error] error:{}", errorMessage, e);
        //设置打点信息
        addRequestException("MethodArgumentNotValidException");

        return ResponseEntity
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON_UTF8)
            .body(
                ResponseBaseVo.fail(CommonErrorCode.ILLEGAL_PARAMETER.getErrorCode(), errorMessage.toString())
            );
    }

    /**
     * controller层参数缺少异常拦截
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @SuppressWarnings("unchecked")
    public ResponseEntity<ResponseBaseVo<Object>> handleMissingServletRequestParameterException(
        MissingServletRequestParameterException e) {
        StringBuilder errorMessage = new StringBuilder();
        if (!Strings.isNullOrEmpty(e.getParameterName())) {
            errorMessage.append(String.format(CommonErrorCode.ILLEGAL_PARAMETER.getMsg(), e.getParameterName()));
            errorMessage.append("，缺少参数：").append(e.getParameterName());
        } else {
            errorMessage.append("缺少参数");
        }

        log.error("[Parameter_Absent_Error] error:{}", errorMessage, e);
        //设置打点信息
        addRequestException("MissingServletRequestParameterException");

        return ResponseEntity
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON_UTF8)
            .body(
                ResponseBaseVo.fail(CommonErrorCode.ILLEGAL_PARAMETER.getErrorCode(), errorMessage.toString())
            );
    }

    /**
     * 参数不能解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @SuppressWarnings("unchecked")
    public ResponseEntity<ResponseBaseVo<Object>> handleHttpMessageNotReadableException(
        HttpMessageNotReadableException e) {
        log.error("[Params_Parse_Error] error:{}", e.toString(), e);
        //设置打点信息
        addRequestException("HttpMessageNotReadableException");
        return ResponseEntity
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON_UTF8)
            .body(
                ResponseBaseVo.fail(CommonErrorCode.ILLEGAL_PARAMETER.getErrorCode(), "参数格式不正确")
            );
    }

    /**
     * 业务异常抓捕
     */
    @ExceptionHandler({ImsBusinessException.class})
    @SuppressWarnings("unchecked")
    public ResponseEntity<ResponseBaseVo<Object>> handleToException(ImsBusinessException e) {
        log.warn("[scm-ims-service_业务异常] error:{}", e.getMessage(), e);
        //设置打点信息
        addRequestException("SoBusinessException");
        return ResponseEntity
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON_UTF8)
            .body(ResponseBaseVo.fail(e.getCodeMsg()));
    }

    /**
     * 远程调用异常抓捕
     */
    @ExceptionHandler({ImsRemoteInvocationException.class})
    @SuppressWarnings("unchecked")
    public ResponseEntity<ResponseBaseVo<Object>> handleToRemoteInvocationException(ImsRemoteInvocationException e) {
        log.error("[RemoteInvocationException] error:{}", e.getMessage(), e);
        //设置打点信息
        addRequestException("SoRemoteInvocationException");
        return ResponseEntity
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON_UTF8)
            .body(ResponseBaseVo.fail(e.getCodeMsg()));
    }


    /**
     * 锁等待超时
     */
    @ExceptionHandler(RedisLockTimeOutException.class)
    @SuppressWarnings("unchecked")
    public ResponseEntity<ResponseBaseVo<Object>> handleRedisLockTimeOutException(RedisLockTimeOutException e) {
        log.error("Redis lock timeout ", e);
        //设置打点信息
        addRequestException("RedisLockTimeOutException");
        return ResponseEntity
            .status(HttpStatus.OK)
            .contentType(MediaType.APPLICATION_JSON_UTF8)
            .body(
                ResponseBaseVo.fail(CommonErrorCode.ACTION_TOO_FAST.getErrorCode(),
                    CommonErrorCode.ACTION_TOO_FAST.getMsg())
            );
    }

    private void addRequestException(String message) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(requestAttributes)) {
            return;
        }
        ((ServletRequestAttributes) requestAttributes).getRequest().setAttribute("ErrorMsg", message);
    }
}
