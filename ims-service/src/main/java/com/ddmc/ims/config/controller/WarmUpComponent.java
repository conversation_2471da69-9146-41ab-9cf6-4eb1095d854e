package com.ddmc.ims.config.controller;

import com.ddmc.ims.manager.ImsConfigRemoteManager;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.rpc.common.WarmUpClient;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.soa.warmup.AbstractWarmUpComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class WarmUpComponent extends AbstractWarmUpComponent {

    @Resource
    private WarmUpClient warmUpClient;

    @Resource
    private AlertService alertService;

    @Resource
    private ImsConfigRemoteManager imsConfigRemoteManager;

    @Override
    public void warmUp() throws Exception {
        //刷新逻辑库位编码
        imsConfigRemoteManager.warmUp();

        //启动
        log.info("start warm up service");
        try {
            warmUpClient.warmUp();
        } catch (Exception e) {
            log.warn("接口预热->", e);
            alertService.alert("接口预热", "接口预热:" + e.getMessage(),
                AlertLevel.CRITICAL);
        }
        log.info("end warm up service");
    }

}
