package com.ddmc.ims.config.controller;

import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.metrics.Counter;
import com.csoss.monitor.api.metrics.Metrics;
import java.io.IOException;
import java.util.Objects;
import java.util.UUID;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * 请求Rquest
 */
@Slf4j
@Component
@WebFilter(filterName = "requestIdFilter", urlPatterns = "/**")
public class RequestIdFilter implements Filter {

    private static final String HEADER_REQUEST_ID = "Request-Id";

    private static final String HEADER_X_REQUEST_ID = "X-Request-Id";

    private static final String REQUEST_ID = "requestId";

    private final Counter responseSuccessRateCounter = Metrics.newCounter("url_response_success_rate").build();

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        try {
            //获取并设置RequestId
            MDC.put(REQUEST_ID, this.getRequestId(httpServletRequest));
            chain.doFilter(httpServletRequest, httpServletResponse);
            statisticsResponse(httpServletRequest);
        } finally {
            MDC.clear();
        }
    }

    /**
     * 获取请求RequestId
     *
     * @param httpServletRequest 请求
     * @return requestId
     */
    private String getRequestId(HttpServletRequest httpServletRequest) {
        String requestId = httpServletRequest.getHeader(HEADER_REQUEST_ID);
        if (StringUtils.isBlank(requestId)) {
            requestId = httpServletRequest.getHeader(REQUEST_ID);
        }

        if (StringUtils.isBlank(requestId)) {
            requestId = httpServletRequest.getHeader(HEADER_X_REQUEST_ID);
        }

        if (StringUtils.isBlank(requestId)) {
            requestId = UUID.randomUUID().toString().replace("-", "");
        }
        return requestId;
    }

    private void statisticsResponse(HttpServletRequest httpServletRequest) {
        if (Objects.isNull(httpServletRequest)) {
            return;
        }
        try {
            String requestURI = httpServletRequest.getRequestURI();
            String errorMsg = (String) httpServletRequest.getAttribute("ErrorMsg");
            String type = StringUtils.isNotBlank(errorMsg) ? "fail" : "success";
            Attributes attributes = Attributes.of(AttributeKey.stringKey("url"), requestURI,
                AttributeKey.stringKey("type"), type, AttributeKey.stringKey("errorMsg"), errorMsg);
            responseSuccessRateCounter.add(1L, attributes);
        } catch (Exception e) {
            log.warn("url_response_success_rate error [{}]", e.getMessage());
        }
    }

}
