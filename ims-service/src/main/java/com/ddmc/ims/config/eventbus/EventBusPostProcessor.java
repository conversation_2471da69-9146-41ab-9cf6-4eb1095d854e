package com.ddmc.ims.config.eventbus;

import com.ddmc.ims.common.enums.common.EventExecuteServiceNameEnum;
import com.google.common.collect.HashMultiset;
import com.google.common.collect.Multiset;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.util.Arrays;
import java.util.Objects;

@Component
@Slf4j
public class EventBusPostProcessor implements BeanPostProcessor {

    private final Multiset<String> multiset = HashMultiset.create();

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) {
        ReflectionUtils.doWithMethods(bean.getClass(), method -> {
            multiset.add(beanName);
            if (multiset.count(beanName) == 1) {
                // register subscriber
                EventBusFactory.getSyncEventBus().register(bean);
                EventBusFactory.getAsyncEventBus().register(bean);
                EventBusFactory.getSkuSourceAsyncEventBus().register(bean);
                //注册监听
                Arrays.stream(EventExecuteServiceNameEnum.values()).map(EventBusFactory::getAsyncEventBusByEventExecuteServiceName)
                    .filter(Objects::nonNull).forEach(t -> t.register(bean));
                log.info("EventBus#registerBean:" + beanName);
            }
        }, method -> method.isAnnotationPresent(Subscribe.class));
        return bean;
    }
}
