package com.ddmc.ims.config.cache;

import com.ddmc.ims.common.bo.BizOrderOperatorCommandConfig;
import com.ddmc.ims.common.bo.ManageCategory.ProductAdmPurchaseCategory;
import com.ddmc.ims.dal.model.ims.Warehouse;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsDto.CorporationInfo;
import com.ddmc.ims.rpc.sso.dto.CategoryGroupVO;
import com.ddmc.ims.rpc.sso.dto.SsoCity;
import com.ddmc.pms.common.client.response.dictionary.SystemDictionaryVO;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 配置项目中使用到的缓存
 */
public class CacheConfig {

    public static final String KEY_ALL = "all";

    private CacheConfig() {
    }

    /**
     * 结算主体缓存
     */
    public static final Cache<String, List<CorporationInfo>> FMS_CORPORATION_INFO = CacheBuilder
        .newBuilder()
        .maximumSize(5)
        .expireAfterWrite(3, TimeUnit.MINUTES)
        .build();

    /**
     * 城市信息缓存
     */
    public static final Cache<String, List<SsoCity>> SSO_CITY_LIST = CacheBuilder
        .newBuilder()
        .maximumSize(50)
        .expireAfterWrite(10, TimeUnit.MINUTES)
        .build();

    /**
     * 字典缓存，key为：sysDictTypeCode
     */
    public static final Cache<String, List<SystemDictionaryVO>> COMMON_DICTIONARY_CACHE = CacheBuilder.newBuilder()
        .maximumSize(20)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();

    /**
     * 单据类型转凭证操作命令map
     */
    public static final Cache<String, Map<String, BizOrderOperatorCommandConfig>> BIZ_ORDER_OPERATOR_COMMAND = CacheBuilder.newBuilder()
        .maximumSize(1)
        .expireAfterWrite(5, TimeUnit.SECONDS)
        .build();

    /**
     * 单据类型转凭证操作命令map_transfer-兼容
     */
    public static final Cache<String, Map<String, BizOrderOperatorCommandConfig>> BIZ_ORDER_OPERATOR_COMMAND_TRANSFER_COMPATIBLE = CacheBuilder.newBuilder()
        .maximumSize(1)
        .expireAfterWrite(5, TimeUnit.SECONDS)
        .build();


    public static final Cache<String, Map<String, String>> DEFAULT_LOCATION_USAGE_CODE_CONFIG = CacheBuilder.newBuilder()
        .maximumSize(1)
        .expireAfterWrite(5, TimeUnit.SECONDS)
        .build();

    /**
     * 所有采购分类
     */
    public static final Cache<String, List<ProductAdmPurchaseCategory>> CATEGORY_ALL = CacheBuilder.newBuilder()
        .maximumSize(5000)
        .expireAfterWrite(20, TimeUnit.MINUTES)
        .build();

    /**
     * 采购负责组 key为id
     */
    public static final Cache<String, CategoryGroupVO> CATEGORY_GROUP_CACHE = CacheBuilder.newBuilder()
        .maximumSize(5000)
        .expireAfterWrite(10, TimeUnit.MINUTES)
        .build();


    public static final Cache<Long, Warehouse> WAREHOUSE = CacheBuilder.newBuilder()
        .weakKeys()
        .maximumSize(6000)
        .expireAfterWrite(10, TimeUnit.MINUTES)
        .build();

    /**
     * 调拨用途场景数据，key为：all,
     */
    public static final Cache<String, Map<String,Integer>> TRANSFER_SCENE = CacheBuilder.newBuilder()
        .maximumSize(1)
        .expireAfterWrite(10, TimeUnit.MINUTES)
        .build();


    /**
     * 单据类型 + 操作对应的BookingChangeTriggerType
     */
    public static final Cache<ImmutablePair<String, Integer>, String> BOOKING_CHANGE_TRIGGER_TYPE = CacheBuilder.newBuilder()
        .maximumSize(100)
        .expireAfterWrite(40, TimeUnit.MINUTES)
        .build();

    /**
     * 单据延时时间配置
     */
    public static final Cache<String, Map<String, Long>> BIZ_ORDER_OPERATOR_DELAY_METRICS = CacheBuilder.newBuilder()
        .maximumSize(1)
        .expireAfterWrite(5, TimeUnit.SECONDS)
        .build();
}
