package com.ddmc.ims.config.eventbus;

import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.dal.model.ims.EventId;

import java.util.Date;
import java.util.List;

/**
 * 事件接口定义
 */
public interface EventStorage {

    /**
     * 根据搜索条件查询事件数据
     *
     * @param condition 查询条件
     * @return 事件集合
     */
    List<EventEntity> get(ListEventCondition condition);

    /**
     * 根据事件id查询事件
     *
     * @param id 事件id
     * @return 事件
     */
    Object get(Long id);

    /**
     * 查询未处理事件
     *
     * @param ttl 等待了多长时间（单位：秒）
     * @param maxTriggerCount 最多执行次数（小于）
     * @param limit 查询的条数
     * @return 事件集合
     */
    List<EventId> get(Integer ttl, Integer maxTriggerCount, Integer limit);

    /**
     * 查询未处理且（执行次数大于等于triggerCount 或者 创建时间小于createTimeEnd）事件集合
     *
     * @param triggerCount 执行次数大于等于triggerCount
     * @param createTimeEnd 创建时间小于createTimeEnd
     */
    List<EventEntity> listUnhandled(Integer triggerCount, Date createTimeEnd);

    /**
     * 保存事件
     *
     * @param event 事件
     */
    <T extends EventId> int save(T event);

    /**
     * 批量保存事件
     *
     * @param eventList 事件集合
     */
    <T extends EventId> int save(List<T> eventList);

    /**
     * 批量保存事件 并且 在事务commit之后异步执行父事件
     *
     * @param eventList 事件集合
     */
    <T extends EventId> int saveAndAsyncTriggerParentEvent(List<T> eventList);

    /**
     * 完成事件
     *
     * @param event 事件
     */
    void complete(EventId event);

    /**
     * 完成事件 并且 在事务commit之后异步执行子事件
     */
    void completeAndAsyncTriggerSubEvent(EventId event);

    /**
     * 事件执行次数加1
     *
     * @param eventId 事件id
     */
    void plusTriggerCount(EventId eventId);

    /**
     * 事件执行次数加1且状态修改为执行中
     *
     * @param eventId 事件id
     * @return true成功 false失败
     */
    boolean plusTriggerCountAndStatusProcessing(EventId eventId);

    /**
     * 事件执行次数加1且状态修改为执行中
     *
     * @param eventIdList 事件id
     */
    void batchPlusTriggerCountAndStatusProcessing(List<Long> eventIdList);

    /**
     * 根据事件名称获取事件Class
     *
     * @param eventTypeName 事件名字
     * @return 事件Class
     */
    Class<?> getClazz(String eventTypeName);

    /**
     * 执行event事件
     */
    void executeEvent(List<EventEntity> eventEntityList);

    /**
     * 查询当前数据库时间，精确到毫秒级别
     * @return 当前时间
     */
    Date selectNowMilliSecond();

    /**
     * 保存延时时间
     * @param eventList 事件列表
     * @param <T> 事件类型
     * @return 保存的事件数量
     */
    <T extends EventId> int saveDelayEvent(List<T> eventList);

    void executeProcessingEvent(List<EventEntity> eventEntityList);

}
