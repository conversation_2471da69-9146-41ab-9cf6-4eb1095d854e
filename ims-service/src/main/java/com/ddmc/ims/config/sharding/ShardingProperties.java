package com.ddmc.ims.config.sharding;

import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description: 据库分片相关常量定义
 * @date 2020/7/16
 */
@Data
@Configuration
@Slf4j
public class ShardingProperties {

    /**
     * 逻辑库名.
     */
    public static final String DATABASE_NAME = "ims";

    /**
     * 物理库个数.
     */
    public static final int DATABASE_NUM = 1;

    /**
     * 每个物理库的物理表个数为2.
     */
    public static final int DATABASE_NUM_2 = 2;

    /**
     * 每个物理库的物理表个数为32
     */
    public static final int TABLE_NUM_32 = 32;

    /**
     * 每个物理库的物理表个数为12（按月分表）
     */
    public static final int TABLE_NUM_12 = 12;

    /**
     * 4张表
     */
    public static final int TABLE_NUM_4 = 4;

    /**
     * 8张表
     */
    public static final int TABLE_NUM_8 = 8;

    /**
     * 批次信息表名
     */
    public static final String INVENTORY_LOT_INFO = "inventory_lot_info";

    /**
     * 在库库存表名
     */
    public static final String WAREHOUSE_SKU_INVENTORY = "warehouse_sku_inventory";


    /**
     * 在库批次库存表名
     */
    public static final String WAREHOUSE_SKU_LOT_INVENTORY = "warehouse_sku_lot_inventory";

    /**
     * 在库库存表名分片字段
     */
    public static final String WAREHOUSE_SKU_INVENTORY_SHARDING_KEY = "logic_inventory_location_code";

    /**
     * 在库批次库存表名分片字段
     */
    public static final String WAREHOUSE_SKU_LOT_INVENTORY_FIRST_SHARDING_KEY = "logic_inventory_location_code";

    /**
     * 在库批次库存表名分片字段
     */
    public static final String WAREHOUSE_SKU_LOT_INVENTORY_SECOND_SHARDING_KEY = "warehouse_id";

    /**
     * 批次信息表分片字段
     */
    public static final String INVENTORY_LOT_INFO_SHARDING_KEY = "warehouse_id";

    private static final String APOLLO_LOCATION_CODE_ROUTE = "ims.logicInventoryLocationCodeRouteMap";

    @Value("${ims.logicInventoryLocationCodeRouteMap:{}}")
    private String logicInventoryLocationCodeRouteMapStr;

    private Map<String, Integer> logicInventoryLocationCodeRouteMap;

    @PostConstruct
    private void init() {
        logicInventoryLocationCodeRouteMap = JsonUtil.fromJson(logicInventoryLocationCodeRouteMapStr, new TypeReference<HashMap<String, Integer>>(){});
        if (Objects.isNull(logicInventoryLocationCodeRouteMap) || logicInventoryLocationCodeRouteMap.isEmpty()) {
            throw new ImsBusinessException(CommonErrorCode.CONFIG_WAREHOUSE_SKU_INVENTORY_SHADING_ERR);
        }
    }

    /**
     * 监听日志开关，若日志开关发生变更。则重新计数
     *
     * @param configChangeEvent apollo配置变化事件
     */
    @ApolloConfigChangeListener
    private void rateLimiterChange(ConfigChangeEvent configChangeEvent) {
        if (Objects.isNull(configChangeEvent)) {
            return;
        }
        if (configChangeEvent.isChanged(APOLLO_LOCATION_CODE_ROUTE)) {
            ConfigChange configChange = configChangeEvent.getChange(APOLLO_LOCATION_CODE_ROUTE);
            String newValue = configChange.getNewValue();
            log.info("key->{},newValue is -> {}, oldValue is -> {}", APOLLO_LOCATION_CODE_ROUTE,
                configChange.getNewValue(), configChange.getOldValue());

            if (StringUtils.isNotBlank(newValue)) {
                logicInventoryLocationCodeRouteMap = JsonUtil.fromJson(logicInventoryLocationCodeRouteMapStr, new TypeReference<HashMap<String, Integer>>(){});
            } else {
                log.warn("new value ->{} is not number", newValue);
            }
        }
    }
}
