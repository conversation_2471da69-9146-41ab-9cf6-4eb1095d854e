package com.ddmc.ims.config.sso;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * sso token与appId
 */
@Configuration
public class SsoAuthConfiguration {

    @Value("${sso.app.secret}")
    private String ssoToken;

    @Value("${sso.app.id}")
    private String appId;


    public String getSsoToken() {
        return ssoToken;
    }

    public String getAppId() {
        return appId;
    }

}
