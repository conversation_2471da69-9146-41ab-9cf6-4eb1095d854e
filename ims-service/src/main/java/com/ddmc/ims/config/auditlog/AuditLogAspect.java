package com.ddmc.ims.config.auditlog;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.gateway.bg.client.annotation.HttpApi;
import com.ddmc.gateway.bg.client.annotation.HttpApiGroup;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.RequestIpUtils;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.common.util.user.CurrentUser;
import com.ddmc.ims.common.util.user.UserContextUtils;
import com.ddmc.ims.service.common.AlertService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.InputStreamSource;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * 安全日志上报切面
 */
@Component
@Order(Integer.MAX_VALUE - 100)
@Aspect
@Slf4j
public class AuditLogAspect {

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final String DATE_FORMAT_ISO8601 = "yyyy-MM-dd'T'HH:mm:ssXXX";

    @Value("${switch.openAuditLog:false}")
    private boolean openAuditLog;
    @Resource
    private AlertService alertService;
    @Resource
    private AuditLogKafkaProducer auditLogKafkaProducer;
    @Resource(name = "auditLogKafkaExecutor")
    private ExecutorService auditLogKafkaExecutor;

    @Around("@annotation(AuditLog)")
    public Object processAround(ProceedingJoinPoint pjp) throws Throwable {
        //是否开启安全日志上报 true上报 false不上报
        if (!openAuditLog) {
            log.info("AuditLogAspect switch.openAuditLog is false");
            return pjp.proceed();
        }
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(requestAttributes)) {
            log.error("AuditLogAspect RequestAttributes is null");
            return pjp.proceed();
        }
        HttpServletRequest request = (HttpServletRequest) requestAttributes
            .resolveReference(RequestAttributes.REFERENCE_REQUEST);
        if (Objects.isNull(request)) {
            log.error("AuditLogAspect HttpServletRequest is null");
            return pjp.proceed();
        }
        String httpMethod = request.getMethod();
        Object result = null;
        try {
            result = pjp.proceed();
            return result;
        } finally {
            try {
                //封装日志数据
                AuditLogRequest auditLogRequest = getAuditLogRequest(httpMethod, result, request, pjp);
                //线程池调用kafka发送消息
                auditLogKafkaExecutor.execute(() -> auditLogKafkaProducer.sendMessage(auditLogRequest));
            } catch (Exception e) {
                log.error("组装安全上报日志异常", e);
                alertService.alertWarning("组装安全上报日志异常", e.getMessage());
            }
        }

    }

    /**
     * 封装安全日志数据
     */
    private AuditLogRequest getAuditLogRequest(String httpMethod, Object result, HttpServletRequest request,
        ProceedingJoinPoint pjp) {
        AuditLogRequest auditLogRequest = new AuditLogRequest();
        //操作时间
        auditLogRequest.setOpTime(ThreadLocalDateUtils.format(CurrentDateUtil.newDate(), DATE_FORMAT_ISO8601));
        CurrentUser currentUser = UserContextUtils.get();
        if (Objects.nonNull(currentUser)) {
            //邮箱
            auditLogRequest.setOpMail(currentUser.getEmail());
            //用户名
            auditLogRequest.setOpUserName(currentUser.getUserName());
        }
        //用户类型
        auditLogRequest.setOpUserType("员工");
        //请求参数
        String requestStr = getRequestParams(httpMethod, request, pjp);
        auditLogRequest.setOrigReq(requestStr);
        //ua
        auditLogRequest.setOpUa(request.getHeader("user-agent"));
        //ip
        auditLogRequest.setOpIp(RequestIpUtils.getIPAddress(request));
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        //操作类型
        AuditLog auditLog = method.getAnnotation(AuditLog.class);
        if (Objects.nonNull(auditLog)) {
            AuditOpTypeEnum[] auditOpTypeEnums = auditLog.opType();
            StringBuilder opType = new StringBuilder();
            for (AuditOpTypeEnum auditOpTypeEnum : auditOpTypeEnums) {
                opType.append(auditOpTypeEnum.getCode());
            }
            auditLogRequest.setOpType(opType.toString());
        }
        //操作模块
        HttpApi httpApi = method.getAnnotation(HttpApi.class);
        HttpApiGroup httpApiGroup = method.getDeclaringClass().getAnnotation(HttpApiGroup.class);
        if (Objects.nonNull(httpApi) && Objects.nonNull(httpApiGroup)) {
            auditLogRequest.setSysModule(httpApiGroup.value() + httpApi.value());
        }
        //操作对象
        auditLogRequest.setOpTarget(requestStr);
        //操作结果
        Boolean success = Boolean.FALSE;
        if (Objects.nonNull(result)) {
            String jsonStr = JsonUtil.toJson(result);
            ResponseBaseVo<?> responseBaseVo = JsonUtil.fromJson(jsonStr, ResponseBaseVo.class);
            success = Objects.nonNull(responseBaseVo) && responseBaseVo.isSuccess();
        }
        String opRes = Boolean.TRUE.equals(success) ? "1" : "0";
        auditLogRequest.setOpResult(opRes + ",-1");
        auditLogRequest.setOthers("");
        return auditLogRequest;
    }

    /**
     * 获取请求参数
     */
    private String getRequestParams(String httpMethod, HttpServletRequest request,
        ProceedingJoinPoint pjp) {
        if ("GET".equals(httpMethod)) {
            Object paramMap = MAPPER.valueToTree(request.getParameterMap());
            if (Objects.nonNull(paramMap)) {
                return paramMap.toString();
            }
        } else {
            Object[] args = pjp.getArgs();
            Map<String, Object> paramsMap = new HashMap<>();
            for (int i = 0; i < args.length; i++) {
                if (isIgnoreArgType(args[i])) {
                    continue;
                }
                paramsMap.put("parameters." + i, args[i]);
            }
            return JsonUtil.toJson(paramsMap);
        }
        return "";
    }

    private boolean isIgnoreArgType(Object arg) {
        return arg instanceof ServletRequest || arg instanceof ServletResponse
            || arg instanceof HttpSession || arg instanceof Reader
            || arg instanceof InputStream || arg instanceof InputStreamSource
            || arg instanceof OutputStream || arg instanceof File;
    }
}
