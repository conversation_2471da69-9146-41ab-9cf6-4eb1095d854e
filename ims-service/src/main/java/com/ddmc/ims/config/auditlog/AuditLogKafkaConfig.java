package com.ddmc.ims.config.auditlog;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

/**
 * 安全日志上报kafka
 */
@Data
@EnableKafka
@Configuration
@ConfigurationProperties(prefix = "auditlog.kafka")
public class AuditLogKafkaConfig {

    private String servers;
    private Integer retries;
    private Integer batchsize;
    private Integer lingerMs;
    private Integer bufferMemory;

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> props = new HashMap<>();
        //请求servers
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, servers);
        //重试次数
        props.put(ProducerConfig.RETRIES_CONFIG, retries);
        //批量大小
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, batchsize);
        //提交延时
        props.put(ProducerConfig.LINGER_MS_CONFIG, lingerMs);
        //Kafka提供的序列化和反序列化类
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return new DefaultKafkaProducerFactory<>(props);
    }

}
