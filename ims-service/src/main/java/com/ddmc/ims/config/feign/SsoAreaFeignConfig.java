package com.ddmc.ims.config.feign;

import feign.Logger;
import feign.codec.Decoder;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;
import org.springframework.cloud.openfeign.FeignLoggerFactory;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

/**
 * sso
 * <AUTHOR>
 */
public class SsoAreaFeignConfig extends FeignClientsConfiguration {


    /**
     * 日志输出格式.
     */
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }


    /**
     * 请求响应日志.
     */
    @Override
    public FeignLoggerFactory feignLoggerFactory() {
        return new InfoFeignLoggerFactory();
    }

    @Override
    @Bean
    public Decoder feignDecoder() {
        TextPlainToJsonMessageConverter messageConverter = new TextPlainToJsonMessageConverter();
        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(messageConverter);
        return new SpringDecoder(objectFactory);
    }

    public static class TextPlainToJsonMessageConverter extends MappingJackson2HttpMessageConverter {

        /**
         * 转换
         */
        public TextPlainToJsonMessageConverter() {
            List<MediaType> mediaTypes = new ArrayList<>();
            mediaTypes.add(MediaType.TEXT_HTML);
            mediaTypes.add(MediaType.TEXT_PLAIN);
            setSupportedMediaTypes(mediaTypes);
        }
    }

}
