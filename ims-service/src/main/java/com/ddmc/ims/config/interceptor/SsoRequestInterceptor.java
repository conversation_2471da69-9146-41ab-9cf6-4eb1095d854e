package com.ddmc.ims.config.interceptor;

import com.ddmc.ims.common.util.SignUtils;
import com.ddmc.ims.config.sso.SsoAuthConfiguration;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Resource;

/**
 *
 */
public class SsoRequestInterceptor implements RequestInterceptor {

    private static final String SIGN_FIELD = "sign";
    private static final String APP_ID_FIELD = "app_id";

    @Resource
    private SsoAuthConfiguration ssoAuthConfiguration;

    @Override
    public void apply(RequestTemplate template) {
        Map<String, Collection<String>> queryParamMap = template.queries();
        Map<String, String> requestParamMap = new HashMap<>();
        for (Map.Entry<String, Collection<String>> paramEntry : queryParamMap.entrySet()) {
            requestParamMap.put(paramEntry.getKey(), Optional.of(paramEntry.getValue()).map(v -> v.iterator().next())
                .orElse(""));
        }
        requestParamMap.put(APP_ID_FIELD, ssoAuthConfiguration.getAppId());
        String sign = SignUtils.signForSSO(requestParamMap, ssoAuthConfiguration.getSsoToken());

        // api请求需要添加sign
        requestParamMap.forEach(template::query);
        template.query(SIGN_FIELD, sign);
    }

}
