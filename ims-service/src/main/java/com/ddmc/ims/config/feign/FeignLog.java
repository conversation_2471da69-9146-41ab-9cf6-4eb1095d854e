package com.ddmc.ims.config.feign;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Feign日志定义
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeignLog {

    //请求标识
    private String configKey;

    //请求方法 get or post
    private String requestHttpMethod;

    //请求地址
    private String requestUrl;

    //请求时间
    private String requestTime;

    //请求参数
    private String requestParams;

    //响应码
    private String responseHttpCode;

    //响应耗时
    private String responseElapsedTime;

    //响应参数
    private String responseParams;

    //响应是否正常 true正常 false 异常
    private Boolean responseSuccess;

    //响应原因
    private String responseReason;

}
