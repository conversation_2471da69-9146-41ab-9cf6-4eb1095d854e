package com.ddmc.ims.config.dynamic;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标注在方法上，表示该方法下的查询是主库还是从库，需与 {@link DataSourceSwitchAspect}配合使用
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface DataSourceSwitch {

    String value() default "SLAVE_SHARDING_DATASOURCE";
}