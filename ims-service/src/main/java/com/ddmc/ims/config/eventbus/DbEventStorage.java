package com.ddmc.ims.config.eventbus;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.constant.EventBusConstants;
import com.ddmc.ims.common.enums.common.EventEntityStatusEnum;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.JsonUtils;
import com.ddmc.ims.common.util.UuidUtils;
import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.mapper.ims.EventMapper;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.dal.model.ims.EventId;
import com.ddmc.ims.service.common.AlertService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 基于DB的事件执行实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DbEventStorage implements EventStorage {

    private static final LoadingCache<String, Class<?>> typeCache =
        CacheBuilder.newBuilder()
            .weakKeys()
            .build(new CacheLoader<String, Class<?>>() {
                @Override
                public Class<?> load(String key) throws ClassNotFoundException {
                    return Class.forName(key);
                }
            });
    @Resource
    private EventMapper eventMapper;
    @Resource
    private AlertService alertService;

    @Resource(name = "updateEventStatusExecutor")
    private ExecutorService executorService;


    private static final String ALTER_TITLE = "保存eventBus异常";
    private static final String ALTER_MESSAGE = "保存eventBus异常, 请及时处理, logId =";

    @Override
    public List<EventEntity> get(ListEventCondition condition) {
        return eventMapper.listByCondition(condition);
    }

    @Override
    public EventId get(Long id) {
        ListEventCondition condition = new ListEventCondition();
        condition.setEventId(id);
        condition.setStatus(EventEntityStatusEnum.DEFAULT.getCode());
        List<EventEntity> entities = eventMapper.listByCondition(condition);
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }
        Class<?> type = getClazz(entities.get(0).getType());
        EventId eventId = (EventId) JsonUtils.parse(entities.get(0).getEvent(), type);
        eventId.setId(id);
        return eventId;
    }

    @Override
    public List<EventId> get(Integer ttl, Integer maxTriggerCount, Integer limit) {

        ListEventCondition condition = new ListEventCondition();
        condition.setStatus(EventEntityStatusEnum.DEFAULT.getCode());
        condition.setTriggerCountEnd(maxTriggerCount);
        condition.setCreateTimeStart(new Date(CurrentDateUtil.currentTimeMillis() - ttl * 1000));
        condition.setLimit(limit);

        List<EventEntity> entities = eventMapper.listByCondition(condition);
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }

        List<EventId> result = new ArrayList<>(entities.size());

        for (EventEntity entity : entities) {
            Class<?> type = getClazz(entity.getType());

            EventId event = (EventId) JsonUtils.parse(entity.getEvent(), type);
            event.setId(entity.getId());
            result.add(event);
        }

        return result;
    }

    @Override
    public List<EventEntity> listUnhandled(Integer triggerCount, Date createTimeEnd) {
        return eventMapper.listUnHandled(triggerCount, createTimeEnd);
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public <T extends EventId> int save(T event) {
        return save(Collections.singletonList(event));
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public <T extends EventId> int save(List<T> eventList) {
        int totalCount = 0;
        List<List<T>> list = Lists.partition(eventList, 100);
        for (List<T> list1 : list) {
            totalCount = totalCount + saveBatch(list1);
        }
        return totalCount;
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public <T extends EventId> int saveAndAsyncTriggerParentEvent(List<T> eventList) {
        //保存处理事件
        int count;
        try {
            count = save(eventList);
            if (count != eventList.size()) {
                String logId = UuidUtils.getUuid();
                log.error("eventBus save count error, logId={}, count={}, eventList={}", logId, count,
                    JsonUtil.toJson(eventList));
                alertService.alertWarning(ALTER_TITLE, ALTER_MESSAGE + logId);
                throw new ImsBusinessException(CommonErrorCode.EVENT_BUS_SAVE_ERROR);
            }
        } catch (Exception e) {
            String logId = UuidUtils.getUuid();
            log.error("eventBus save error, logId={}, eventList={},e={}", logId, JsonUtil.toJson(eventList), e.getMessage());
            alertService.alertWarning(ALTER_TITLE, ALTER_MESSAGE + logId);
            throw new ImsBusinessException(CommonErrorCode.EVENT_BUS_SAVE_ERROR);
        }
        //处理事件保存完成提交事务之后
        TransactionSynchronizationManager.registerSynchronization(
            new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    executorSubmitEvent(eventList);
                }


            });
        return count;
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public <T extends EventId> int saveDelayEvent(List<T> eventList) {
        //保存处理事件
        int count;
        try {
            count = save(eventList);
            if (count != eventList.size()) {
                String logId = UuidUtils.getUuid();
                log.error("eventBus save count error, logId={}, count={}, eventList={}", logId, count,
                    JsonUtil.toJson(eventList));
                alertService.alertWarning(ALTER_TITLE, ALTER_MESSAGE + logId);
                throw new ImsBusinessException(CommonErrorCode.EVENT_BUS_SAVE_ERROR);
            }
        } catch (Exception e) {
            String logId = UuidUtils.getUuid();
            log.error("eventBus save error, logId={}, eventList={}", logId, JsonUtil.toJson(eventList), e);
            alertService.alertWarning(ALTER_TITLE, ALTER_MESSAGE + logId);
            throw new ImsBusinessException(CommonErrorCode.EVENT_BUS_SAVE_ERROR);
        }
        return count;
    }

    private <T extends EventId> void executorSubmitEvent(List<T> eventList) {
        executorService.submit(() -> {
            //取出无父级事件 加入线程池异步执行
            List<T> parentEvent = eventList.stream().filter(o -> o.getParentId() == 0)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(parentEvent)) {
                //将事件设置为处理中
                Lists.partition(parentEvent, 200).forEach(batch -> batchPlusTriggerCountAndStatusProcessing(
                    batch.stream().map(EventId::getId).collect(Collectors.toList())));
                parentEvent.forEach(evt -> {
                    //商品来源业务指定独立线程池
                    if (Boolean.TRUE.equals(evt.getSkuSourceThreadPool())) {
                        EventBusFactory.getSkuSourceAsyncEventBus().post(evt);
                    } else if (Objects.nonNull(evt.getEventExecuteServiceName())
                        && Objects.nonNull(EventBusFactory.getAsyncEventBusByEventExecuteServiceName(evt.getEventExecuteServiceName()))){
                        EventBusFactory.getAsyncEventBusByEventExecuteServiceName(evt.getEventExecuteServiceName()).post(evt);
                    } else {
                        EventBusFactory.getAsyncEventBus().post(evt);
                    }
                    log.info("event bus post event success eventId :{}", evt.getId());
                });
            }
        });
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void complete(EventId event) {
        //修改事件状态 为 已完成
        int updateResult = eventMapper.update(event.getId(),
            EventEntityStatusEnum.CONSUMED.getCode(),
            EventEntityStatusEnum.PROCESSING.getCode());
        if (updateResult != 1) {
            log.error("eventBus complete error,event={}", JsonUtil.toJson(event));
            alertService.alertWarning("完成eventBus异常", "完成eventBus异常, 请及时处理, eventId = " + event.getId());
            throw new ImsBusinessException(CommonErrorCode.EVENT_BUS_COMPLETE_ERROR);
        }
    }

    @Override
    @Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void completeAndAsyncTriggerSubEvent(EventId event) {
        //完成事件
        this.complete(event);
        //查询出是否有未处理的子事件
        ListEventCondition condition = new ListEventCondition();
        condition.setParentEventId(event.getId());
        condition.setStatusList(EventBusConstants.WAIT_STATUS_LIST);
        List<EventEntity> entities = eventMapper.listByCondition(condition);
        //存在未处理子事件 在事务提交之后 异步执行子事件
        if (CollectionUtils.isNotEmpty(entities)) {
            TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        executorService.submit(() -> executeEvent(entities));
                    }
                });
        }
    }

    private <T extends EventId> int saveBatch(List<T> eventList) {
        Date currentTime = CurrentDateUtil.newDate();
        List<EventEntity> entities = eventList.stream().map(t -> {
            if (Objects.isNull(t.getEarliestExeDate())) {
                t.setEarliestExeDate(currentTime);
            }
            log.info("event json:{}", JsonUtils.toJson(t));
            return new EventEntity()
                .setId(t.getId())
                .setParentId(t.getParentId())
                .setKeyType(StringUtils.isNotBlank(t.getKeyType()) ? t.getKeyType() : "")
                .setKeyId(StringUtils.isNotBlank(t.getKeyId()) ? t.getKeyId() : "")
                .setType(t.getClass().getTypeName())
                .setEvent(JsonUtils.toJson(t))
                .setStatus(EventEntityStatusEnum.DEFAULT.getCode())
                .setTriggerCount(0)
                .setCreateTime(currentTime)
                .setUpdateTime(currentTime)
                .setEarliestExeDate(t.getEarliestExeDate());
        }).collect(Collectors.toList());
        int count = eventMapper.insertBatch(entities);
        for(int i =0; i<eventList.size(); i++){
            eventList.get(i).setId(entities.get(i).getId());
        }
        return count;
    }

    @Override
    public void plusTriggerCount(EventId eventId) {
        eventMapper.plusTriggerCount(eventId.getId());
    }

    @Override
    public boolean plusTriggerCountAndStatusProcessing(EventId eventId) {
        return eventMapper.plusTriggerCountAndStatusProcessing(eventId.getId()) > 0;
    }

    @Override
    public void batchPlusTriggerCountAndStatusProcessing(List<Long> eventIdList) {
        eventMapper.batchPlusTriggerCountAndStatusProcessing(eventIdList);
    }

    @Override
    public Class<?> getClazz(String eventTypeName) {
        try {
            return typeCache.get(eventTypeName);
        } catch (ExecutionException e) {
            log.error("DbEventStorage getClazz error", e);
        }
        return null;
    }

    @Override
    public void executeEvent(List<EventEntity> eventEntityList) {
        for (EventEntity entity : eventEntityList) {
            Class<?> type = getClazz(entity.getType());
            EventId eventId = (EventId) JsonUtils.parse(entity.getEvent(), type);
            eventId.setId(entity.getId());
            if (plusTriggerCountAndStatusProcessing(eventId)) {
                log.info("event bus post event success eventId :{}", eventId.getId());
                //是否商品来源指定线程池处理
                if (Boolean.TRUE.equals(eventId.getSkuSourceThreadPool())) {
                    EventBusFactory.getSkuSourceAsyncEventBus().post(eventId);
                } else if (Objects.nonNull(eventId.getEventExecuteServiceName())
                    && Objects.nonNull(EventBusFactory.getAsyncEventBusByEventExecuteServiceName(eventId.getEventExecuteServiceName()))){
                    EventBusFactory.getAsyncEventBusByEventExecuteServiceName(eventId.getEventExecuteServiceName()).post(eventId);
                } else {
                    EventBusFactory.getAsyncEventBus().post(eventId);
                }
            }
        }
    }

    @Override
    public void executeProcessingEvent(List<EventEntity> eventEntityList) {
        for (EventEntity entity : eventEntityList) {
            Class<?> type = getClazz(entity.getType());
            EventId eventId = (EventId) JsonUtils.parse(entity.getEvent(), type);
            eventId.setId(entity.getId());
            log.info("event bus post event success eventId :{}", eventId.getId());
            //是否商品来源指定线程池处理
            EventEntity e = eventMapper.getById(eventId.getId());
            if (Objects.isNull(e) || !EventEntityStatusEnum.PROCESSING.getCode().equals(e.getStatus())) {
                log.info("[executeProcessingEvent] event is null or not processing :{}", eventId.getId());
                continue;
            }
            if (Boolean.TRUE.equals(eventId.getSkuSourceThreadPool())) {
                EventBusFactory.getSkuSourceAsyncEventBus().post(eventId);
            } else if (Objects.nonNull(eventId.getEventExecuteServiceName())
                && Objects.nonNull(EventBusFactory.getAsyncEventBusByEventExecuteServiceName(eventId.getEventExecuteServiceName()))){
                EventBusFactory.getAsyncEventBusByEventExecuteServiceName(eventId.getEventExecuteServiceName()).post(eventId);
            } else {
                EventBusFactory.getAsyncEventBus().post(eventId);
            }
        }
    }

    @Override
    public Date selectNowMilliSecond() {
        return eventMapper.selectNowMilliSecond();
    }
}
