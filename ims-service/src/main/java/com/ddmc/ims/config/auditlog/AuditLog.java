package com.ddmc.ims.config.auditlog;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 上报系统操作记录至安全部门
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuditLog {

    /**
     * 操作类型，增删改查 CRUDC: 增R: 查U: 改D: 删根据操作自由拼接四个大写字母
     *
     * @return 例如：CR
     */
    AuditOpTypeEnum[] opType();

}
