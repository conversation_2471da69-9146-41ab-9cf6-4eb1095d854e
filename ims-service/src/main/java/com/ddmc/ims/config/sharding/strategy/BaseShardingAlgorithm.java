package com.ddmc.ims.config.sharding.strategy;

import com.ddmc.ims.common.util.ShardingDbUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;


@Slf4j
public abstract class BaseShardingAlgorithm<T extends Comparable<?>> implements PreciseShardingAlgorithm<T> {


    /**
     * 获取表索引
     * @param shardingValue 分片列值
     * @param tableNum 分表数量
     * @return 表索引
     */
    public abstract String getActualTableIndex(T shardingValue,Integer tableNum);

    /**
     * 获取物理表名
     * @param preciseShardingValue
     * @param tableNum 分表数量
     * @return 物理表名
     */
    protected String doSharding(PreciseShardingValue<T> preciseShardingValue,int tableNum) {
        String targetTable;
        if (tableNum <= 1) {
            targetTable = preciseShardingValue.getLogicTableName();
        } else {
            String tableIndex = getActualTableIndex(preciseShardingValue.getValue(),tableNum);
            targetTable = ShardingDbUtil.getActualTableName(preciseShardingValue.getLogicTableName(),tableIndex);
        }

        if (log.isDebugEnabled()) {
            log.debug("sharding table: {}", targetTable);
        }
        return targetTable;
    }
}
