package com.ddmc.ims.config.dynamic;

public class DynamicDataSourceHolder {

    private static ThreadLocal<String> currentDataSourceThreadLocal = new ThreadLocal<>();

    public static void setCurrentDataSource(String dataSourceName) {
        currentDataSourceThreadLocal.set(dataSourceName);
    }

    public static String getCurrentDataSource() {
        return currentDataSourceThreadLocal.get();
    }

    public static void clearCurrentDataSource() {
        currentDataSourceThreadLocal.remove();
    }

}
