package com.ddmc.ims.config.executor;

import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.internal.Instrumentation;
import com.csoss.monitor.api.metrics.Gauge;
import com.csoss.monitor.api.metrics.Metrics;
import com.csoss.monitor.api.metrics.MetricsBinder;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class ExecutorMetricsConfig {

    private static final MetricsBinder METRICS_BINDER = Metrics.bindInstrumentationName(Instrumentation.CUSTOM);
    /**
     * 线程池核心线程数
     */
    private static final Gauge poolCoreSize = METRICS_BINDER.newGauge("custom_thread_pool_core_size").build();
    /**
     * 线程池历史峰值线程数
     */
    private static final Gauge poolLargestSize = METRICS_BINDER.newGauge("custom_thread_pool_largest_size").build();
    /**
     * 线程池容量(最大线程数)
     */
    private static final Gauge poolMaxSize = METRICS_BINDER.newGauge("custom_thread_pool_max_size").build();
    /**
     * 线程池活跃线程数
     */
    private static final Gauge poolActiveSize = METRICS_BINDER.newGauge("custom_thread_pool_active_size").build();
    /**
     * 线程池运行中的线程数
     */
    private static final Gauge poolThreadSize = METRICS_BINDER.newGauge("custom_thread_pool_thread_count").build();
    /**
     * 线程池积压任务数 注意如果阻塞队列使用无界队列这里不能直接取size
     */
    private static final Gauge poolQueueSize = METRICS_BINDER.newGauge("custom_thread_pool_queue_size").build();

    private final ScheduledExecutorService scheduledExecutor = Executors.newSingleThreadScheduledExecutor();

    private final Map<String, ThreadPoolExecutor> myThreadPoolExecutorMap = new ConcurrentHashMap<>();

    @Value("${thread.metrics.enable:true}")
    private Boolean enableThreadPoolMetrics;
    /**
     * 监控上报Thread Pool 状态信息
     */
    private final Runnable monitor = () -> {
        if (Boolean.FALSE.equals(enableThreadPoolMetrics)) {
            return;
        }

        //自定义线程池统计
        try {
            myThreadPoolMonitor();
        } catch (Exception e) {
            log.warn("ExecutorMetricsConfig Warn", e);
        }

    };

    /**
     * 自定义线程池监控
     */
    private void myThreadPoolMonitor() {
        if (MapUtils.isEmpty(myThreadPoolExecutorMap)) {
            return;
        }
        for (Entry<String, ThreadPoolExecutor> entry : myThreadPoolExecutorMap.entrySet()) {
            String poolName = entry.getKey();
            ThreadPoolExecutor executor = entry.getValue();
            Attributes poolNameAttr = Attributes.of(AttributeKey.stringKey("poolName"), poolName);
            //线程池核心线程数
            poolCoreSize.value(executor.getCorePoolSize(), poolNameAttr);
            //线程池历史峰值线程数
            poolLargestSize.value(executor.getLargestPoolSize(), poolNameAttr);
            //线程池容量(最大线程数)
            poolMaxSize.value(executor.getMaximumPoolSize(), poolNameAttr);
            //线程池活跃线程数
            poolActiveSize.value(executor.getActiveCount(), poolNameAttr);
            //线程池运行中的线程数
            poolThreadSize.value(executor.getPoolSize(), poolNameAttr);
            //线程池积压任务数 注意如果阻塞队列使用无界队列这里不能直接取size
            poolQueueSize.value(executor.getQueue().size(), poolNameAttr);
        }
    }

    @PostConstruct
    public void startExecutorMetrics() {
        scheduledExecutor.scheduleWithFixedDelay(monitor, 0, 5, TimeUnit.SECONDS);
    }

    @Around("@annotation(com.ddmc.ims.config.executor.ExecutorMetricsConfig.ThreadPoolMetrics)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        Object returnObj = joinPoint.proceed(joinPoint.getArgs());
        if (!(returnObj instanceof ThreadPoolExecutor)) {
            return returnObj;
        }
        String poolName;
        ThreadPoolMetrics threadPoolMetrics = method.getDeclaredAnnotation(ThreadPoolMetrics.class);
        poolName = threadPoolMetrics.poolName();
        if (StringUtils.isEmpty(poolName)) {
            poolName = joinPoint.getThis().getClass().getSimpleName() + "#" + method.getName();
        }
        myThreadPoolExecutorMap.put(poolName, (ThreadPoolExecutor) returnObj);
        return returnObj;
    }

    @Documented
    @Target({ElementType.METHOD})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface ThreadPoolMetrics {

        /**
         * 线程池名称
         */
        String poolName() default "";
    }
}
