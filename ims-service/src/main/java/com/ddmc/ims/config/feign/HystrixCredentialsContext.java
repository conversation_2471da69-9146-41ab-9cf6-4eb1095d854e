package com.ddmc.ims.config.feign;

import com.netflix.hystrix.HystrixInvokable;
import com.netflix.hystrix.exception.HystrixRuntimeException.FailureType;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestContext;
import com.netflix.hystrix.strategy.concurrency.HystrixRequestVariableDefault;
import com.netflix.hystrix.strategy.executionhook.HystrixCommandExecutionHook;
import java.util.Map;
import org.apache.commons.collections.MapUtils;
import org.slf4j.MDC;

/**
 * Feign全局变量
 */
public class HystrixCredentialsContext extends HystrixCommandExecutionHook {


    private static final HystrixRequestVariableDefault<Map<String, String>> HYSTRIX_REQUEST_VARIABLE_DEFAULT =
        new HystrixRequestVariableDefault<>();

    @Override
    public <T> void onStart(HystrixInvokable<T> commandInstance) {
        setMDCContext();
    }

    @Override
    public <T> void onThreadStart(HystrixInvokable<T> commandInstance) {
        Map<String, String> map = HYSTRIX_REQUEST_VARIABLE_DEFAULT.get();
        if (MapUtils.isNotEmpty(map)) {
            MDC.setContextMap(map);
        }
    }

    @Override
    public <T> Exception onError(HystrixInvokable<T> commandInstance, FailureType failureType, Exception e) {
        clearLogContext();
        return e;
    }

    @Override
    public <T> void onSuccess(HystrixInvokable<T> commandInstance) {
        clearLogContext();
    }


    /**
     * 主线程同步变量到子线程 MDC
     */
    private void setMDCContext() {
        if (!HystrixRequestContext.isCurrentThreadInitialized()) {
            HystrixRequestContext.initializeContext();
        }
        HYSTRIX_REQUEST_VARIABLE_DEFAULT.set(MDC.getCopyOfContextMap());
    }

    /**
     * 清理
     */
    private void clearLogContext() {
        if (HystrixRequestContext.isCurrentThreadInitialized()) {
            HystrixRequestContext.getContextForCurrentThread().shutdown();
        }
        MDC.clear();
    }
}
