package com.ddmc.ims.config.feign;

import static feign.Util.UTF_8;
import static feign.Util.decodeOrDefault;

import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.utils.json.JsonUtil;
import feign.Request;
import feign.Response;
import feign.Util;
import java.io.IOException;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;

/**
 * copy from fdc
 */
public class BriefFeignLogger extends feign.Logger {

    private static final String GSQ_FEIGN_LOG = "====== SCM-BASE Feign Log -> %s";

    private final Logger logger;

    public BriefFeignLogger(Logger logger) {
        this.logger = logger;
    }

    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        if (logLevel.ordinal() == Level.NONE.ordinal()) {
            return;
        }

        String bodyText = null;
        if (request.requestBody() != null && logLevel.ordinal() >= Level.FULL.ordinal()) {
            bodyText = request.requestBody().asString();
        }
        if (logger.isInfoEnabled()) {
            try {
                //开启日志上下文
                FeignLogContext.start();
                FeignLog feignLog = new FeignLog();
                //请求标识
                feignLog.setConfigKey(configKey);
                //请求时间
                feignLog.setRequestTime(ThreadLocalDateUtils.formatYmdhms(CurrentDateUtil.newDate()));
                //请求方式
                feignLog.setRequestHttpMethod(request.httpMethod().name());
                //请求地址
                feignLog.setRequestUrl(request.url());
                //请求参数
                feignLog.setRequestParams(bodyText != null ? bodyText : "Binary data");
                FeignLogContext.setFeignLog(feignLog);
            } catch (Exception e) {
                logger.error("feign log request error,configKey:{}", configKey, e);
            }
        }
    }

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime)
        throws IOException {
        if (logLevel.ordinal() == Level.NONE.ordinal()) {
            return response;
        }

        String reason = response.reason() != null && logLevel.compareTo(Level.NONE) > 0 ? " " + response.reason() : "";
        int status = response.status();
        String responseBody = "";
        byte[] bodyData = null;
        // HTTP 204（无内容）服务器成功处理了请求，但没有返回任何内容。
        // HTTP 205（重置内容）服务器成功处理了请求，但没有返回任何内容
        boolean responseSuccess =
            response.body() != null && !(status == HttpStatus.SC_NO_CONTENT || status == HttpStatus.SC_RESET_CONTENT);
        if (responseSuccess) {
            bodyData = Util.toByteArray(response.body().asInputStream());
            responseBody = decodeOrDefault(bodyData, UTF_8, "Binary data");
        }
        if (logger.isInfoEnabled()) {
            try {
                FeignLog feignLog = FeignLogContext.getFeignLog();
                //响应是否正常
                feignLog.setResponseSuccess(responseSuccess);
                //响应码
                feignLog.setResponseHttpCode(String.valueOf(status));
                //响应时间
                feignLog.setResponseElapsedTime(String.valueOf(elapsedTime));
                //响应参数
                feignLog.setResponseParams(responseBody);
                //响应原因
                feignLog.setResponseReason(reason);
                //记录日志
                logger.info(String.format(GSQ_FEIGN_LOG, JsonUtil.toJSON(feignLog)));
                //关闭日志上下文
                FeignLogContext.end();
            } catch (Exception e) {
                logger.error("feign log response error,configKey:{}", configKey, e);
            }
        }
        return responseSuccess ? response.toBuilder().body(bodyData).build() : response;
    }

    @Override
    protected IOException logIOException(String configKey, Level logLevel, IOException ioe, long elapsedTime) {
        if (logger.isInfoEnabled()) {
            try {
                FeignLog feignLog = FeignLogContext.getFeignLog();
                //响应是否正常
                feignLog.setResponseSuccess(false);
                //响应时间
                feignLog.setResponseElapsedTime(String.valueOf(elapsedTime));
                //响应原因
                feignLog.setResponseReason(ioe.getMessage());
                //记录日志
                logger.info(String.format(GSQ_FEIGN_LOG, JsonUtil.toJSON(feignLog)));
                //关闭日志上下文
                FeignLogContext.end();
            } catch (Exception e) {
                logger.error("feign log logIOException error,configKey:{}", configKey, e);
            }
        }
        return super.logIOException(configKey, logLevel, ioe, elapsedTime);
    }

    private String getLogText(String configKey, String format) {
        return methodTag(configKey) + format;
    }

    @Override
    protected void log(String configKey, String format, Object... args) {
        if (logger.isInfoEnabled()) {
            logger.info(String.format(getLogText(configKey, format), args));
        }
    }
}