package com.ddmc.ims.config.redis;

import com.ddmc.ims.common.exception.RedisLockTimeOutException;
import csoss.daas.redis.client.lock.DdmcLock;
import csoss.daas.redis.client.lock.LockManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
@Aspect
@Order(Integer.MAX_VALUE - 100)
@Slf4j
public class LockAspect {

    @Autowired
    private LockManager lockManager;

    private String getLockName(String key) {
        String lockName = com.ddmc.ims.config.redis.RedisConfig.PREFIX + "lock:" + key;
        log.debug("lock name: {}", lockName);
        return lockName;
    }

    private String getArgValue(ProceedingJoinPoint pjp, int argIndex, String... argProperties)
        throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        if (argIndex == -1) {
            return "";
        }
        Object arg = pjp.getArgs()[argIndex];
        if (arg == null || ArrayUtils.isEmpty(argProperties)) {
            return Optional.ofNullable(arg).orElse("").toString();
        }
        StringBuilder argValue = new StringBuilder();
        for (String argProperty : argProperties) {
            String getMethod = "get" + StringUtils.capitalize(argProperty);
            Method method = arg.getClass().getMethod(getMethod);
            Object value = method.invoke(arg);
            argValue.append("_").append(Optional.ofNullable(value).orElse("").toString());
        }
        return argValue.substring(1);
    }

    @Around("@annotation(com.ddmc.ims.config.redis.Lock)")
    public Object processAround(ProceedingJoinPoint pjp) throws Throwable {
        String methodName = pjp.getSignature().getName();
        Class<?> classTarget = pjp.getTarget().getClass();
        Class<?>[] parameterTypes = ((MethodSignature) pjp.getSignature()).getParameterTypes();
        Method objMethod = classTarget.getMethod(methodName, parameterTypes);
        com.ddmc.ims.config.redis.Lock lock = objMethod.getDeclaredAnnotation(com.ddmc.ims.config.redis.Lock.class);
        String lockName = lock.name() + ":" + getArgValue(pjp, lock.argIndex(), lock.argProperty());

        DdmcLock rLock = lockManager.create(getLockName(lockName));
        if (!rLock.tryLock(lock.waitTime(), lock.leaseTime(), TimeUnit.MILLISECONDS)) {
            throw new RedisLockTimeOutException(
                "acquire redis lock[" + lockName + "] timeout!! waitTime: " + lock.waitTime() + "ms");
        }
        try {
            return pjp.proceed();
        } finally {
            rLock.unlock();
        }
    }
}
