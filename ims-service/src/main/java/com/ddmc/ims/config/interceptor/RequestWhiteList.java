package com.ddmc.ims.config.interceptor;

import com.google.common.collect.Lists;

import java.util.List;

public class RequestWhiteList {

    private static final List<String> WHITE_LIST = Lists.newArrayList(
        //权限抓取
        "/monitor/base/checkStartUp",
        "/monitor/warmUp",
        "/manage/privilege/collect",
        "/error",
        "/v2/api-docs.json",
        ".xlsx",
        "swagger-ui.html",
        "/webjars/springfox-swagger-ui",
        "/swagger-resources/configuration/ui",
        "/druid/index.html"
    );

    public static boolean matches(String uri) {
        for (String white : WHITE_LIST) {
            if (uri.endsWith(white) || uri.startsWith(white)) {
                return true;
            }
        }
        return false;
    }

    private RequestWhiteList() {
    }
}
