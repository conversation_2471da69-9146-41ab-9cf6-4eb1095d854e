package com.ddmc.ims.config.executor;

import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.metrics.Counter;
import com.csoss.monitor.api.metrics.Metrics;
import com.ddmc.ims.common.util.ThreadLocalContextHelper;
import com.ddmc.ims.common.util.ThreadMdcUtil;
import com.ddmc.ims.config.executor.ExecutorMetricsConfig.ThreadPoolMetrics;
import com.ddmc.soa.concurrent.WrappedExecutors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;
import java.util.concurrent.*;

@Slf4j
@Configuration
public class ExecutorConfig {

    /**
     * cpu密集型应用
     */
    public static final int CPU_DEFAULT_MAX_CONCURRENT = Runtime.getRuntime().availableProcessors() + 1;

    /**
     * io密集型应用
     */
    public static final int IO_DEFAULT_MAX_CONCURRENT = Runtime.getRuntime().availableProcessors() * 2 + 1;

    /**
     * 默认队列大小
     */
    public static final int DEFAULT_MAX_SIZE = 500;

    /**
     * 默认线程存活时间
     */
    public static final int DEFAULT_KEEP_ALIVE = 60;


    private static final Counter COUNTER = Metrics.newCounter("ThreadPoolRejectedEvent").build();

    @Bean("AlertExecutor")
    public ExecutorService alertExecutor() {
        return new MyThreadPoolExecutor(1, 2, DEFAULT_KEEP_ALIVE, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(50), new ThreadPoolExecutor.DiscardOldestPolicy());
    }

    /**
     * 安全日志上报线程池
     */
    @Bean("auditLogKafkaExecutor")
    @ThreadPoolMetrics(poolName = "auditLogKafkaExecutor")
    public ExecutorService auditLogKafkaExecutor() {
        return new MyThreadPoolExecutor(2, 5, DEFAULT_KEEP_ALIVE, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(5000), MyCallerRunsPolicy
            .getInstance(new ThreadPoolExecutor.AbortPolicy(), "auditLogKafkaExecutor"));
    }

    @Bean("lotInitExecutor")
    public ExecutorService lotInitExecutor() {
        return new MyThreadPoolExecutor(8, 8, DEFAULT_KEEP_ALIVE, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(50), new ThreadPoolExecutor.DiscardOldestPolicy());
    }

    @Bean("updateEventStatusExecutor")
    public ExecutorService updateEventStatusExecutor() {
        return new MyThreadPoolExecutor(8, 8, DEFAULT_KEEP_ALIVE, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(8000), (r, e) -> log.error("更新事件状态任务队列"));
    }

    @Bean("warehouseSkuInventoryQueryExecutor")
    public ExecutorService warehouseSkuInventoryQueryExecutor() {
        return new MyThreadPoolExecutor(8, 10, DEFAULT_KEEP_ALIVE, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100), new ThreadPoolExecutor.CallerRunsPolicy());
    }


    /**
     * 定时刷新调拨场景
     */
    @Bean("autoRefreshTransferSceneScheduledExecutor")
    @ThreadPoolMetrics(poolName = "autoRefreshTransferSceneScheduledExecutor")
    public ScheduledExecutorService paramAutoPushScheduledExecutor() {
        return new ScheduledThreadPoolExecutor(1,
            MyCallerRunsPolicy
                .getInstance(new ThreadPoolExecutor.AbortPolicy(), "autoRefreshTransferSceneScheduledExecutor"));
    }



    /**
     * 拒绝策略监控
     */
    @Slf4j
    private static class MyCallerRunsPolicy implements RejectedExecutionHandler {

        private final RejectedExecutionHandler handler;

        private final String threadPoolName;

        private MyCallerRunsPolicy(RejectedExecutionHandler handler, String threadPoolName) {
            this.handler = handler;
            this.threadPoolName = threadPoolName;
        }

        public static MyCallerRunsPolicy getInstance(RejectedExecutionHandler handler, String threadPoolName) {
            return new MyCallerRunsPolicy(handler, threadPoolName);
        }

        private void logEvent(String name) {
            try {
                COUNTER.once(Attributes.of(AttributeKey.stringKey("threadPoolName"), name));
            } catch (Exception e) {
                log.warn("logEvent error", e);
            }
        }

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            log.warn("【线程池触发拒绝策略】ThreadPoolName:【{}】Runnable【{}】handler【{}】", threadPoolName, r,
                handler.getClass().getName());
            logEvent(threadPoolName);
            handler.rejectedExecution(r, executor);
        }
    }

    private static class MyThreadPoolExecutor extends ThreadPoolExecutor {

        public MyThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
            BlockingQueue<Runnable> workQueue) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
        }

        public MyThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
            BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
        }

        public MyThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
            BlockingQueue<Runnable> workQueue, RejectedExecutionHandler handler) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
        }

        public MyThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
            BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        }


        @Override
        public void execute(Runnable command) {
            super.execute(WrappedExecutors.wrap(ThreadMdcUtil.wrap(command, MDC.getCopyOfContextMap())));
        }

        @Override
        public Future<?> submit(Runnable task) {
            return super.submit(WrappedExecutors.wrap(ThreadMdcUtil.wrap(task, MDC.getCopyOfContextMap())));
        }

        @Override
        public <T> Future<T> submit(Runnable task, T result) {
            return super.submit(WrappedExecutors.wrap(ThreadMdcUtil.wrap(task, MDC.getCopyOfContextMap())), result);
        }

        @Override
        public <T> Future<T> submit(Callable<T> task) {
            return super.submit(WrappedExecutors.wrap(ThreadMdcUtil.wrap(task, MDC.getCopyOfContextMap())));
        }

        @Override
        protected void afterExecute(Runnable r, Throwable t) {
            if (Objects.nonNull(t)) {
                String msg = String.format("线程：%s 执行错误：", Thread.currentThread().getName());
                log.error(msg, t);
            }
            super.afterExecute(r, t);
            ThreadLocalContextHelper.clearAll();
            MDC.clear();
        }
    }
}
