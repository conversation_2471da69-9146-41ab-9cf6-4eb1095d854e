package com.ddmc.ims.config.redis;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 通过给方法添加注解实现Redis分布式锁
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Lock {

    /**
     * @return lock的名称，可与 {@link #argIndex()},  {@link #argProperty()} 结合使用实现动态名称
     */
    String name();

    /**
     * @return 加锁超时时长，单位毫秒，默认35秒。超过35秒锁若还未释放锁则自动释放。
     */
    long leaseTime() default 35000L;

    /**
     * @return 最大等待时长，单位毫秒，默认35秒
     */
    long waitTime() default 35000L;

    /**
     * @return 取当前方法的第 {@link #argIndex()} 指定参数的哪些属性值与 {@link #name()} 共同组成分布式锁的名称。
     * <p>未指定时直接取 {@link #argIndex()} 指定参数的toString()获取</p>
     */
    String[] argProperty() default {};

    /**
     * @return 取当前方法的第几个参数以及其  {@link #argProperty()} 指定的属性值与 {@link #name()} 共同组成最后的分布式锁name。
     * <p>默认-1表示不取参数值</p>
     */
    int argIndex() default -1;
}
