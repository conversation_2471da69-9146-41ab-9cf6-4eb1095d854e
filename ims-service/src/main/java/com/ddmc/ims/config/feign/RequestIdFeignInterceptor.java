package com.ddmc.ims.config.feign;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

/**
 * RequestId Fegin配置类
 */
@Slf4j
@Component
@ConditionalOnClass(RequestInterceptor.class)
public class RequestIdFeignInterceptor implements RequestInterceptor {

    private static final String REQUEST_ID = "requestId";

    @Override
    public void apply(RequestTemplate template) {
        template.header(REQUEST_ID, MDC.get(REQUEST_ID));
    }
}
