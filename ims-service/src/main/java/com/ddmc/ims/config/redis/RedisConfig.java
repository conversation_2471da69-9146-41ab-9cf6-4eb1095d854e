package com.ddmc.ims.config.redis;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import csoss.daas.redis.client.builder.DdmcRedisClient;
import csoss.daas.redis.client.builder.DdmcRedisClientBuilder;
import csoss.daas.redis.client.lock.LockManager;
import csoss.daas.redis.core.aop.RedisAspect;
import lombok.Data;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

/**
 * redis配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.redis.single")
@Component
public class RedisConfig {

    /**
     * 所有key统一加前缀
     */
    public static final String PREFIX = "ScmImsService:";

    /**
     * redis密码
     */
    private String password;

    /**
     * redis 单节点ip
     */
    private String host;

    /**
     * redis单节点端口
     */
    private String port;

    /**
     * 最大连接数
     */
    private int maxActive;

    /**
     * redis资源码
     */
    private String resourceCode = "scm_ims_scm_ims_redis_group";

    @Bean
    public LockManager getLockManager(@Qualifier("ddmcRedisClient") DdmcRedisClient ddmcRedisClient) {
        return ddmcRedisClient.getLockManager();
    }


    @Bean
    public RedisSerializer<Object> redisSerializer() {
        ObjectMapper objectMapper = new ObjectMapper();
        //反序列化时候遇到不匹配的属性并不抛出异常
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //序列化时候遇到空对象不抛出异常
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        //反序列化的时候如果是无效子类型,不抛出异常
        objectMapper.configure(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE, false);
        //不使用默认的dateTime进行序列化,
        objectMapper.configure(SerializationFeature.WRITE_DATE_KEYS_AS_TIMESTAMPS, false);
        //使用JSR310提供的序列化类,里面包含了大量的JDK8时间序列化类
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
        return new GenericJackson2JsonRedisSerializer(objectMapper);
    }

    /**
     * Redis 新版 onepass Redis 监控SDK
     */
    @Bean
    public RedisAspect openTracingRedisAspect() {
        return new RedisAspect();
    }


    @Bean("ddmcRedisClient")
    public DdmcRedisClient createDdmcRedisClient() {
        return DdmcRedisClientBuilder.builder()
                .resourceCode(resourceCode)
                .build();
    }


    //获取redisfactory
    @Bean("redisConnectionFactory")
    public RedisConnectionFactory createHostRedisConnectionFactory(@Qualifier("ddmcRedisClient") DdmcRedisClient ddmcRedisClient) {
        return ddmcRedisClient.getRedisConnectionFactory();
    }

    @Bean("stringRedisTemplate")
    public StringRedisTemplate ddmcStringRedisTemplate(@Qualifier("ddmcRedisClient") DdmcRedisClient ddmcRedisClient) {
        return ddmcRedisClient.getStringRedisTemplate();
    }

    @Bean("redisTemplate")
    public RedisTemplate<String, Object> ddmcRedisTemplate(@Qualifier("ddmcRedisClient") DdmcRedisClient ddmcRedisClient,
                                                           RedisSerializer<Object> redisSerializer) {
        RedisTemplate redisTemplate = ddmcRedisClient.getRedisTemplate();
        RedisSerializer<?> stringRedisSerializer = RedisSerializer.string();
        // name-value结构序列化数据结构
        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setValueSerializer(redisSerializer);
        // hash数据结构序列化方式,必须这样否则存hash 就是基于jdk序列化的
        redisTemplate.setHashKeySerializer(stringRedisSerializer);
        redisTemplate.setHashValueSerializer(redisSerializer);
        // 启用默认序列化方式
        redisTemplate.setEnableDefaultSerializer(true);
        redisTemplate.setDefaultSerializer(redisSerializer);
        return redisTemplate;
    }
}
