package com.ddmc.ims.config.feign;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Feign日志上下文
 */
public class FeignLogContext {

    /**
     * ThreadLocal对象
     */
    private static final ThreadLocal<FeignLogContext> CONTEXT_THREAD_LOCAL = ThreadLocal.withInitial(FeignLogContext::new);
    //feign日志
    private static final String CONTEXT_KEY_FEIGN_LOG = "feignLog";
    /**
     * 存储对象的map
     */
    private Map<String, Object> map;

    /**
     * 获取当前上下文
     */
    public static FeignLogContext current() {
        return CONTEXT_THREAD_LOCAL.get();
    }

    /**
     * 启动上下文
     */
    public static void start() {
        FeignLogContext context = current();
        context.map = new LinkedHashMap<>();
    }

    /**
     * 关闭上下文
     */
    public static void end() {
        FeignLogContext context = current();
        context.map = null;
        CONTEXT_THREAD_LOCAL.remove();
    }

    public static FeignLog getFeignLog() {
        return (FeignLog) current().get(CONTEXT_KEY_FEIGN_LOG);
    }

    public static void setFeignLog(FeignLog feignLog) {
        current().put(CONTEXT_KEY_FEIGN_LOG, feignLog);
    }

    /**
     * 获取当前map
     */
    public Map<String, Object> map() {
        return this.map;
    }

    /**
     * 放到上下文中
     */
    public Object put(String key, Object value) {
        return Objects.nonNull(map) ? this.map.put(key, value) : null;
    }

    /**
     * 从上下文中删除
     */
    public Object remove(String key) {
        return Objects.nonNull(map) ? this.map.remove(key) : null;
    }

    /**
     * 从上下文中获取
     */
    public Object get(String key) {
        return Objects.nonNull(map) ? this.map.get(key) : null;
    }

}
