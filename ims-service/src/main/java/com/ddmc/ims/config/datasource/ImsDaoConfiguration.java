package com.ddmc.ims.config.datasource;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.constant.DataSourceConstants;
import com.ddmc.ims.config.dynamic.DynamicDataSource;
import com.ddmc.ims.config.sharding.ShardingConfiguration;
import com.ddmc.ims.config.sharding.ShardingProperties;
import com.ddmc.jdbc.pool.config.apollo.ConfigBuilder;
import com.ddmc.jdbc.pool.datasource.DdmcDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.shardingsphere.api.config.sharding.ShardingRuleConfiguration;
import org.apache.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * scm_ims主数据源配置
 */
@Slf4j
@Configuration
@MapperScan(basePackages = {"com.ddmc.ims.dal.mapper.ims"}, sqlSessionTemplateRef = "imsSqlSessionTemplate")
public class ImsDaoConfiguration {

    @Value("${ims.datasource.resourceName:scm_ims_group}")
    private String imsResourceName;

    @Value("${ims.datasource.maxPoolSize:30}")
    private int maxPoolSize;

    @Value("${ims.datasource.minPoolSize:1}")
    private int minPoolSize;

    @Bean(name = DataSourceConstants.IMS_DATA_SOURCE)
    public DataSource getDataSource() {
        DdmcDataSource ds = new DdmcDataSource();
        Map<String, String> config = ConfigBuilder.newInstance()
            .resourceName(imsResourceName)
            .bindMaster(true)
            .maxPoolSize(maxPoolSize)
            .minPoolSize(minPoolSize)
            .extraJDBCParams(
                "useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull&serverTimezone=GMT%2B8")
            .build();
        ds.setConfigServiceMeta(config);
        ds.init();
        return ds;
    }


    @Bean(name = DataSourceConstants.IMS_SLAVE_DATA_SOURCE)
    public DataSource getSlaveDataSource() {
        DdmcDataSource ds = new DdmcDataSource();
        Map<String, String> config = ConfigBuilder.newInstance()
            .resourceName(imsResourceName)
            .bindMaster(false)
            .maxPoolSize(maxPoolSize)
            .minPoolSize(minPoolSize)
            .extraJDBCParams(
                "useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull&serverTimezone=GMT%2B8")
            .build();
        ds.setConfigServiceMeta(config);
        ds.init();
        return ds;
    }


    /**
     * 分片数据源
     */
    @Bean(DataSourceConstants.SHARDING_DATASOURCE)
    public DataSource dataSource(
        @Qualifier(DataSourceConstants.IMS_DATA_SOURCE) DataSource imsDataSource,
        @Autowired ShardingConfiguration scmBaseShardingConfiguration
    ) throws SQLException {
        // 创建数据源，封装到数据源Map
        Map<String, DataSource> dataSourceMap = new HashMap<>(ShardingProperties.DATABASE_NUM, 1);
        dataSourceMap.put(ShardingProperties.DATABASE_NAME, imsDataSource);

        // 创建分表分片规则s
        ShardingRuleConfiguration shardingRuleConfig = scmBaseShardingConfiguration.createShardingRule();
        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, new Properties());
    }


    /**
     * 分片数据源
     */
    @Bean(DataSourceConstants.SLAVE_SHARDING_DATASOURCE)
    public DataSource slaveDataSource(
        @Qualifier(DataSourceConstants.IMS_SLAVE_DATA_SOURCE) DataSource imsSlaveDataSource,
        @Autowired ShardingConfiguration scmBaseShardingConfiguration
    ) throws SQLException {
        // 创建数据源，封装到数据源Map
        Map<String, DataSource> dataSourceMap = new HashMap<>(ShardingProperties.DATABASE_NUM, 1);
        dataSourceMap.put(ShardingProperties.DATABASE_NAME, imsSlaveDataSource);

        // 创建分表分片规则s
        ShardingRuleConfiguration shardingRuleConfig = scmBaseShardingConfiguration.createShardingRule();
        return ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, new Properties());
    }

    @Bean(CommonConstants.IMS_DB_TRANSACTION_MANAGER)
    public DataSourceTransactionManager transactionManager(
        @Qualifier(DataSourceConstants.DYNAMIC_DATASOURCE) DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = DataSourceConstants.DYNAMIC_DATASOURCE)
    public DataSource dynamicDataSource(
        @Qualifier(DataSourceConstants.SHARDING_DATASOURCE) DataSource shardingDataSource,
        @Qualifier(DataSourceConstants.SLAVE_SHARDING_DATASOURCE) DataSource slaveShardingDataSource
    ) {
        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        dynamicDataSource.setDefaultTargetDataSource(shardingDataSource);
        Map<Object, Object> targetDataSourceMap = new HashMap<>();
        targetDataSourceMap.put(DataSourceConstants.SHARDING_DATASOURCE, shardingDataSource);
        targetDataSourceMap.put(DataSourceConstants.SLAVE_SHARDING_DATASOURCE, slaveShardingDataSource);
        dynamicDataSource.setTargetDataSources(targetDataSourceMap);
        return dynamicDataSource;
    }


    @Bean(name = "imsSqlSessionFactory")
    public SqlSessionFactory imsSqlSessionFactory(
        @Qualifier(DataSourceConstants.DYNAMIC_DATASOURCE) DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:sqlmap/ims/*.xml"));
        bean.setTypeHandlersPackage("com.ddmc.ims.config.ibatis");
        return bean.getObject();
    }

    @Bean("imsSqlSessionTemplate")
    public SqlSessionTemplate imsSqlSessionTemplate(
        @Qualifier("imsSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }


}
