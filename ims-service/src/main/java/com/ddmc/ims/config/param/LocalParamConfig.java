package com.ddmc.ims.config.param;

import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.BizOrderOperatorCommandConfig;
import com.ddmc.ims.common.bo.DefaultLocationUsageCodeConfig;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.constant.LocalParamsConstants;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.PatternUtils;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.config.sharding.ShardingProperties;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.manager.conversion.CommandConversionFactory;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.fasterxml.jackson.core.type.TypeReference;
import java.text.ParseException;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LocalParamConfig {

    @Resource
    private LocalParamService localParamService;


    @Resource
    private ShardingProperties shardingProperties;


    /**
     * 逻辑库存快照是否只做清除
     *
     * @return result
     */
    public boolean isOnlyClearSnapshotInfo() {
        return localParamService.getBooleanValue(LocalParamsConstants.SNAPSHOT_PER_HOUR_ONLY_CLEAR, false);
    }

    /**
     * 逻辑库存小时快照sleep间隔时间
     *
     * @return result
     */
    public Long getSnapshotPerHourSleepInterval() {
        return localParamService.getLongValue(LocalParamsConstants.SNAPSHOT_PER_HOUR_SLEEP_INTERVAL, 100L);
    }


    /**
     * 是否中断逻辑库存小时快照
     *
     * @return result
     */
    public boolean isInterruptSnapshotPerHour() {
        return localParamService.getBooleanValue(LocalParamsConstants.IS_INTERRUPT_SNAPSHOT_PER_HOUR, false);
    }


    /**
     * 逻辑库存小时快照，自定义快照时间
     *
     * @return result
     */
    public Date getSnapShotPerHourDate() {
        try {
            return localParamService.getDateTimeValue(LocalParamsConstants.SNAPSHOT_PER_HOUR_DATE_TIME, null);
        } catch (ParseException e) {
            return null;
        }
    }




    /**
     * 通过单据类型获取凭证操作与命令
     *
     * @param bizOrderType bizOrderType
     * @param orderOperateType orderOperateType
     * @return result
     */
    public BizOrderOperatorCommandConfig getBizOrderOperatorCommandConfig(String bizOrderType,
        Integer orderOperateType) {
        Map<String, BizOrderOperatorCommandConfig> allConfig = CacheConfig.BIZ_ORDER_OPERATOR_COMMAND.getIfPresent(
            CacheConfig.KEY_ALL);

        if (MapUtils.isNotEmpty(allConfig)) {
            return allConfig.get(bizOrderType + "_" + orderOperateType);
        }

        String value = localParamService.getStringValue(LocalParamsConstants.BIZ_ORDER_OPERATOR_COMMAND_NEW,
            StringUtils.EMPTY);
        if (StringUtils.isBlank(value)) {
            return null;
        }

        List<BizOrderOperatorCommandConfig> bizOrderOperatorCommandConfigs = JsonUtil.parseList(value,
            BizOrderOperatorCommandConfig.class);
        allConfig = bizOrderOperatorCommandConfigs.stream().collect(
            Collectors.toMap(t -> t.getBizOrder() + "_" + t.getOrderOperateType(), Function.identity(),
                (n1, n2) -> n1));

        CacheConfig.BIZ_ORDER_OPERATOR_COMMAND.put(CacheConfig.KEY_ALL, allConfig);
        return allConfig.get(bizOrderType + "_" + orderOperateType);
    }


    /**
     * 通过单据类型获取凭证操作与命令-调拨兼容
     *
     * @param bizOrderType bizOrderType
     * @param orderOperateType orderOperateType
     * @return result
     */
    public BizOrderOperatorCommandConfig getBizOrderOperatorCommandConfigByTransferCompatible(String bizOrderType,
        Integer orderOperateType) {
        Map<String, BizOrderOperatorCommandConfig> allConfig = CacheConfig.BIZ_ORDER_OPERATOR_COMMAND_TRANSFER_COMPATIBLE.getIfPresent(
            CacheConfig.KEY_ALL);

        if (MapUtils.isNotEmpty(allConfig)) {
            return allConfig.get(bizOrderType + "_" + orderOperateType);
        }

        String value = localParamService.getStringValue(LocalParamsConstants.BIZ_ORDER_OPERATOR_COMMAND_COMPATIBLE,
            StringUtils.EMPTY);
        if (StringUtils.isBlank(value)) {
            return null;
        }

        List<BizOrderOperatorCommandConfig> bizOrderOperatorCommandConfigs = JsonUtil.parseList(value,
            BizOrderOperatorCommandConfig.class);
        allConfig = bizOrderOperatorCommandConfigs.stream().collect(
            Collectors.toMap(t -> t.getBizOrder() + "_" + t.getOrderOperateType(), Function.identity(),
                (n1, n2) -> n1));

        CacheConfig.BIZ_ORDER_OPERATOR_COMMAND_TRANSFER_COMPATIBLE.put(CacheConfig.KEY_ALL, allConfig);
        return allConfig.get(bizOrderType + "_" + orderOperateType);
    }


    public Map<String, String> getDefaultLocationUsageCodeMap() {
        Map<String, String> allConfig = CacheConfig.DEFAULT_LOCATION_USAGE_CODE_CONFIG.getIfPresent(
            CacheConfig.KEY_ALL);

        if (MapUtils.isNotEmpty(allConfig)) {
            return allConfig;
        }

        String value = localParamService.getStringValue(LocalParamsConstants.DEFAULT_LOCATION_USAGE_CODE_CONFIG,
            StringUtils.EMPTY);
        if (StringUtils.isBlank(value)) {
            return Collections.emptyMap();
        }

        List<DefaultLocationUsageCodeConfig> configs = JsonUtil.parseList(value, DefaultLocationUsageCodeConfig.class);
        allConfig = configs.stream().collect(Collectors.toMap(DefaultLocationUsageCodeConfig::getLocationCode,
            DefaultLocationUsageCodeConfig::getUsageCode, (n1, n2) -> n1));

        CacheConfig.DEFAULT_LOCATION_USAGE_CODE_CONFIG.put(CacheConfig.KEY_ALL, allConfig);
        return allConfig;
    }


    public boolean excludeWarehouseForSnapshot(Long warehouseId) {
        Set<Long> warehouseIds = localParamService.getLongSetValue(LocalParamsConstants.SNAPSHOT_EXCLUDE_WAREHOUSE_ID,
            Collections.emptySet());
        return warehouseIds.contains(warehouseId);
    }

    public boolean enableInsertUsageSnapshot() {
        return localParamService.getBooleanValue(LocalParamsConstants.ENABLE_INSERT_USAGE_SNAPSHOT, false);
    }

    /**
     * 过滤加工中逻辑库位
     */
    public List<String> filterProcessingLocationSortCodes() {
        return shardingProperties.getLogicInventoryLocationCodeRouteMap().keySet().stream()
            .filter(t -> !CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE.equals(t)).sorted()
            .collect(Collectors.toList());
    }

    /**
     * 所有逻辑库位
     */
    public List<String> allLocationSortCodes() {
        return shardingProperties.getLogicInventoryLocationCodeRouteMap().keySet().stream().sorted()
            .collect(Collectors.toList());
    }


    public List<CommandTypeEnum> getCommandTypeEnumList(CredentialHeader credentialHeader) {
        String orderType = credentialHeader.getOrderType();
        Integer orderOperateType = credentialHeader.getOrderOperateType();
        BizOrderOperatorCommandConfig bizOrderOperatorCommandConfig;
        //如果是调拨场景，且不是四要素的单子
        if (OrderTypeEnum.isTransfer(credentialHeader.getOrderType()) && !PatternUtils.isExactlyThreeHyphens(
            credentialHeader.getOrderNo())) {
            bizOrderOperatorCommandConfig = getBizOrderOperatorCommandConfigByTransferCompatible(orderType,
                orderOperateType);
        } else {
            bizOrderOperatorCommandConfig = getBizOrderOperatorCommandConfig(orderType, orderOperateType);
        }

        if (Objects.nonNull(bizOrderOperatorCommandConfig)) {
            return bizOrderOperatorCommandConfig.getCommands().stream().map(CommandTypeEnum::fromCode)
                .collect(Collectors.toList());
        }
        List<CommandTypeEnum> commandTypeEnumList = CommandConversionFactory.getCommandTypeEnum(orderOperateType);
        if (CollectionUtils.isEmpty(commandTypeEnumList)) {
            log.error("单据信息{}", JsonUtil.toJson(credentialHeader));
            throw new ImsBusinessException(CommonErrorCode.ERROR_BIZ_ORDER_TYPE);
        }
        return commandTypeEnumList;
    }

    public Integer getBusinessDateAheadEndDate() {
        return localParamService.getIntValue(LocalParamsConstants.FMS_BUSINESS_DATE_AHEAD_END_DATE, 1);
    }

    public boolean getEnableTransferScene() {
        return localParamService.getBooleanValue(LocalParamsConstants.ENABLE_TRANSFER_SCENE, false);
    }


    public List<TransferUsagePriority> getTransferInUsagePriority(String fromUsageCodes) {
        String transferInUsagePriorityStr = localParamService.getStringValue(
            LocalParamsConstants.TRANSFER_IN_USAGE_PRIORITY, "{}");
        HashMap<String, List<TransferUsagePriority>> transferInUsagePriorityMap = JsonUtil.fromJson(
            transferInUsagePriorityStr, new TypeReference<HashMap<String, List<TransferUsagePriority>>>() {
            });
        assert transferInUsagePriorityMap != null;
        return transferInUsagePriorityMap.getOrDefault(fromUsageCodes, Collections.emptyList());
    }


    public List<TransferUsagePriority> getTransferOutUsagePriority(String toUsageCodes) {
        String transferInUsagePriorityStr = localParamService.getStringValue(
            LocalParamsConstants.TRANSFER_OUT_USAGE_PRIORITY, "{}");
        HashMap<String, List<TransferUsagePriority>> transferInUsagePriorityMap = JsonUtil.fromJson(
            transferInUsagePriorityStr, new TypeReference<HashMap<String, List<TransferUsagePriority>>>() {
            });
        assert transferInUsagePriorityMap != null;
        return transferInUsagePriorityMap.getOrDefault(toUsageCodes, Collections.emptyList());
    }

    public List<String> getDefaultUsageForInbound(String logicInventoryLocationCode) {
        String transferInUsagePriorityStr = localParamService.getStringValue(LocalParamsConstants.INBOUND_DEFAULT_USAGE,
            "{}");
        HashMap<String, List<String>> transferInUsagePriorityMap = JsonUtil.fromJson(transferInUsagePriorityStr,
            new TypeReference<HashMap<String, List<String>>>() {
            });
        assert transferInUsagePriorityMap != null;
        return transferInUsagePriorityMap.getOrDefault(logicInventoryLocationCode, Collections.emptyList());
    }


    public List<String> getDefaultUsageForOutbound(String logicInventoryLocationCode) {
        String transferInUsagePriorityStr = localParamService.getStringValue(
            LocalParamsConstants.OUTBOUND_DEFAULT_USAGE, "{}");
        HashMap<String, List<String>> transferInUsagePriorityMap = JsonUtil.fromJson(transferInUsagePriorityStr,
            new TypeReference<HashMap<String, List<String>>>() {
            });
        assert transferInUsagePriorityMap != null;
        return transferInUsagePriorityMap.getOrDefault(logicInventoryLocationCode, Collections.emptyList());
    }

    public Integer getCredentialBusinessTimeDelay() {
        return localParamService.getIntValue(LocalParamsConstants.CREDENTIAL_BUSINESS_TIME_DELAY,30);
    }
    @Data
    public static class TransferUsagePriority {

        private String fromUsageCode;

        private String toUsageCode;
    }
}
