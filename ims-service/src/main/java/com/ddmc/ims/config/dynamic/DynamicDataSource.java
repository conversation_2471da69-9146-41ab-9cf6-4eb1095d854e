package com.ddmc.ims.config.dynamic;

import com.ddmc.ims.common.constant.DataSourceConstants;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

public class DynamicDataSource extends AbstractRoutingDataSource {

    @Override
    protected Object determineCurrentLookupKey() {
        String currentDatasource = DynamicDataSourceHolder.getCurrentDataSource();
        if(currentDatasource == null) {
            currentDatasource = DataSourceConstants.SHARDING_DATASOURCE;
        }
        return currentDatasource;
    }
}
