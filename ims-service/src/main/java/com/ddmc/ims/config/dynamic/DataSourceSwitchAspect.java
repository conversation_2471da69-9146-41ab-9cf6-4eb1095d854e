package com.ddmc.ims.config.dynamic;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Order(1)
public class DataSourceSwitchAspect {

    @Around("@annotation(dataSourceSwitch)")
    public Object switchDataSource(ProceedingJoinPoint joinPoint, DataSourceSwitch dataSourceSwitch) throws Throwable {
        String dataSource = dataSourceSwitch.value();
        try {
            DynamicDataSourceHolder.setCurrentDataSource(dataSource);
            return joinPoint.proceed();
        } finally {
            DynamicDataSourceHolder.clearCurrentDataSource();
        }
    }
}