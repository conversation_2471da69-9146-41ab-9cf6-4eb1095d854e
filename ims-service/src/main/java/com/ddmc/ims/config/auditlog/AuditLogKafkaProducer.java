package com.ddmc.ims.config.auditlog;

import javax.annotation.Resource;

import com.ddmc.ims.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

/**
 * 安全日志kafka消息生产类
 */
@Component
@Slf4j
public class AuditLogKafkaProducer {

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Value("${auditlog.kafka.topic}")
    private String topic;

    /**
     * 发送消息
     *
     * @param auditLogRequest 消息实例
     */
    public void sendMessage(AuditLogRequest auditLogRequest) {
        String message = JsonUtil.toJson(auditLogRequest);
        kafkaTemplate.send(topic, message);
        log.info("AuditLog send message to kafka ：{}", message);
    }

}
