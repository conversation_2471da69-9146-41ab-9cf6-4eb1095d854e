package com.ddmc.ims.config.ibatis;

import com.ddmc.ims.common.enums.IntegerEnumInterface;
import com.ddmc.ims.common.enums.StringEnumInterface;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.common.util.EnumUtils;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

@MappedTypes(value = {
    IntegerEnumInterface.class,
    StringEnumInterface.class,
    YesNoEnum.class
})
public class BaseEnumTypeHandler<E extends Enum<E>> extends BaseTypeHandler<E> {

    protected Class<E> type;

    public BaseEnumTypeHandler() {
    }

    public BaseEnumTypeHandler(Class<E> type) {
        this.type = type;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, E parameter, JdbcType jdbcType) throws SQLException {
        if (parameter instanceof IntegerEnumInterface) {
            ps.setInt(i, ((IntegerEnumInterface) parameter).getCode());
        } else if (parameter instanceof StringEnumInterface) {
            ps.setString(i, ((StringEnumInterface) parameter).getCode());
        } else {
            ps.setString(i, parameter.toString());
        }
    }

    @Override
    public E getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return get(rs.getString(columnName));
    }

    @Override
    public E getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return get(rs.getString(columnIndex));
    }

    @Override
    public E getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return get(cs.getString(columnIndex));
    }

    private E get(String v) {
        if (v == null) {
            return null;
        }
        return EnumUtils.getEnum(v, type);
    }
}

