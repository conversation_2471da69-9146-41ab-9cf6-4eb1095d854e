package com.ddmc.ims.config.interceptor;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.io.InputStreamSource;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.util.Objects;

@Aspect
@Component
@Slf4j
public class RequestCostInterceptor {

    @Around("execution(public * com.ddmc.ims.controller..*.*(..))")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        try {
            String appSource = getAppSource();
            log.info("服务层 request uri: {}, param: {}, sourceId: {}", getRequestUri(), getRequestArgs(pjp), appSource);
            Long receiveTime = CurrentDateUtil.currentTimeMillis();
            Object object = pjp.proceed();
            Long finishTime = CurrentDateUtil.currentTimeMillis();
            long cost = finishTime - receiveTime;
            if (object instanceof ResponseBaseVo) {
                ((ResponseBaseVo<?>) object).setExecTime(cost);
            }
            log.info("服务层 response uri: {}, result: {}, cost: {}", getRequestUri(), getResponseArgs(object), cost);
            return object;
        } catch (Exception e) {
            throw e;
        }


    }

    private String getAppSource() {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) Objects
                .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            String sourceId = request.getHeader("App-Source-Id");
            if (StringUtils.isBlank(sourceId)) {
                return StringUtils.EMPTY;
            }
            return sourceId;
        } catch (Exception e) {
            log.warn("getRequestUri error: {}", e.getMessage());
        }
        return StringUtils.EMPTY;
    }

    private String getRequestUri() {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) Objects
                .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            return request.getRequestURI();
        } catch (Exception e) {
            log.warn("getRequestUri error: {}", e.getMessage());
        }
        return "";
    }

    private String getRequestArgs(ProceedingJoinPoint pjp) {
        StringBuilder sb = new StringBuilder("[");
        Object[] args = pjp.getArgs();
        for (Object arg : args) {
            try {
                if (isIgnoreArgType(arg)) {
                    continue;
                }
                if (arg == null) {
                    sb.append("null");
                } else if (arg instanceof String) {
                    sb.append((String) arg);
                } else if (arg instanceof Number) {
                    sb.append(arg);
                } else {
                    sb.append(JsonUtil.toJson(arg));
                }
            } catch (Exception e) {
                log.warn("getRequestArgs error:", e);
            }
            sb.append(",");
        }

        if (sb.lastIndexOf(",") == sb.length() - 1) {
            sb.deleteCharAt(sb.length() - 1);
        }
        sb.append("]");
        return sb.toString();
    }

    private String getResponseArgs(Object object) {
        try {
            return JsonUtil.toJson(object);
        } catch (Exception e) {
            log.warn("getResponseArgs error:", e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 根据参数的类型判断该参数是否应该不在Sentry记录
     *
     * @param arg 参数
     * @return true表示需要忽略
     */
    private boolean isIgnoreArgType(Object arg) {
        return arg instanceof ServletRequest || arg instanceof ServletResponse
            || arg instanceof HttpSession || arg instanceof Reader
            || arg instanceof InputStream || arg instanceof InputStreamSource
            || arg instanceof OutputStream || arg instanceof File;
    }
}
