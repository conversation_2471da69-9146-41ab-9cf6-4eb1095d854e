package com.ddmc.ims.config.eventbus;

import com.ddmc.ims.common.util.CurrentDateUtil;
import com.ddmc.ims.common.util.ThreadMdcUtil;
import com.ddmc.soa.concurrent.WrappedExecutors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.*;

@Slf4j
public class MonitoredThreadPoolExecutor extends ThreadPoolExecutor {

    private final Map<String, Long> startTimes = new ConcurrentHashMap<>();

    private String poolName;

    public MonitoredThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
        BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    public MonitoredThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
        BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
    }

    public MonitoredThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
        BlockingQueue<Runnable> workQueue, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
    }

    public MonitoredThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
        BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

    public void setPoolName(String poolName) {
        this.poolName = poolName;
    }


    @Override
    public void execute(Runnable command) {
        super.execute(WrappedExecutors.wrap(ThreadMdcUtil.wrap(command, MDC.getCopyOfContextMap())));
    }

    @Override
    public Future<?> submit(Runnable task) {
        return super.submit(WrappedExecutors.wrap(ThreadMdcUtil.wrap(task, MDC.getCopyOfContextMap())));
    }

    @Override
    public <T> Future<T> submit(Runnable task, T result) {
        return super.submit(WrappedExecutors.wrap(ThreadMdcUtil.wrap(task, MDC.getCopyOfContextMap())), result);
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(WrappedExecutors.wrap(ThreadMdcUtil.wrap(task, MDC.getCopyOfContextMap())));
    }

    /**
     * 任务执行之后，计算任务结束时间
     */
    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        long startTime = startTimes.remove(String.valueOf(r.hashCode()));
        long finishTime = CurrentDateUtil.currentTimeMillis();
        long diff = finishTime - startTime;
        // 统计任务耗时、初始线程数、核心线程数、正在执行的任务数量、已完成任务数量、任务总数、队列里缓存的任务数量、池中存在的最大线程数、最大允许的线程数、线程空闲时间、线程池是否关闭、线程池是否终止
        log.info("{}-pool-monitor: Duration: {} ms, PoolSize: {}, CorePoolSize: {}, Active: {}, Completed: {}, "
                + "TotalTask: {}, Queue: {}, LargestPoolSize: {}, MaximumPoolSize: {},  KeepAliveTime: {}, isShutdown: {}"
                + ", isTerminated: {}",
            this.poolName,
            diff,
            this.getPoolSize(),
            this.getCorePoolSize(),
            this.getActiveCount(),
            this.getCompletedTaskCount(),
            this.getTaskCount(),
            this.getQueue().size(),
            this.getLargestPoolSize(),
            this.getMaximumPoolSize(),
            this.getKeepAliveTime(TimeUnit.MILLISECONDS),
            this.isShutdown(),
            this.isTerminated());
    }

    /**
     * 任务执行之前，记录任务开始时间
     */
    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        startTimes.put(String.valueOf(r.hashCode()), CurrentDateUtil.currentTimeMillis());
    }
}
