package com.ddmc.ims.config.eventbus;

import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.metrics.Metrics;
import com.csoss.monitor.api.metrics.Timer;
import com.ddmc.ims.common.enums.common.EventEntityStatusEnum;
import com.ddmc.ims.dal.mapper.ims.EventMapper;
import com.ddmc.ims.dal.model.ims.EventId;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Order(Integer.MAX_VALUE - 100)
@Aspect
@Slf4j
public class EventSubscribeAspect {

    /**
     * 刷新聚合库存打点
     */
    public static Timer timer = Metrics
        // 使用默认桶，一般情况无需指定。
        .newTimer("event_cost")//指定metricname
        .build();

    @Resource
    private EventMapper eventMapper;

    @AfterThrowing(pointcut = "@annotation(com.google.common.eventbus.Subscribe)", throwing = "ex")
    public void afterException(JoinPoint joinPoint, Throwable ex) {
        if (joinPoint.getArgs().length > 0) {
            Object obj = joinPoint.getArgs()[0];
            if (obj instanceof EventId) {
                EventId eventId = (EventId) obj;
                eventMapper.update(eventId.getId(), EventEntityStatusEnum.ERROR.getCode(),
                    EventEntityStatusEnum.PROCESSING.getCode());
                log.info("EventSubscribeAspect update status,eventId:{},keyId:{},keyType:{}",
                    eventId.getId(), eventId.getKeyId(), eventId.getKeyType(), ex);
            }
        }
    }

    @Around("@annotation(com.google.common.eventbus.Subscribe)")
    public void around(ProceedingJoinPoint pjp) throws Throwable {
        String keyType = "unknown";
        if (pjp.getArgs().length > 0) {
            Object obj = pjp.getArgs()[0];
            if (obj instanceof EventId) {
                EventId eventId = (EventId) obj;
                keyType = eventId.getKeyType();
            }
        }

        timer.start();
        try {
            pjp.proceed();
        } finally {
            timer.end(Attributes.of(AttributeKey.stringKey("keyType"), keyType));
        }
    }
}
