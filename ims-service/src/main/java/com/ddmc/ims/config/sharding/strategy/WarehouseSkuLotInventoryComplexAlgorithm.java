package com.ddmc.ims.config.sharding.strategy;

import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.ShardingDbUtil;
import com.ddmc.ims.config.sharding.ShardingProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingValue;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: warehouse_sku_inventory分表规则
 * @date 2021/7/26
 */
@Slf4j
@Component
public class WarehouseSkuLotInventoryComplexAlgorithm implements ComplexKeysShardingAlgorithm<String> {
    @Resource
    private ShardingProperties shardingProperties;

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<String> shardingValues) {
        // 0. 打印数据源集合 及 分片键属性集合
        if (log.isDebugEnabled()) {
            log.debug("availableTargetNames:" + JsonUtil.toJson(availableTargetNames) + ",shardingValues:" + JsonUtil.toJson(shardingValues));
        }
        return getTableNames(shardingValues);
    }

    private Set<String> getTableNames(ComplexKeysShardingValue<String> shardingValues) {
        Map<String, Collection<String>> svs = shardingValues.getColumnNameAndShardingValuesMap();
        Collection<String> codes = svs.get(ShardingProperties.WAREHOUSE_SKU_LOT_INVENTORY_FIRST_SHARDING_KEY);
        Collection<?> warehouseIds = svs.get(ShardingProperties.WAREHOUSE_SKU_LOT_INVENTORY_SECOND_SHARDING_KEY);
        Collection<String> warehouseIdsStr = warehouseIds.stream().map(String::valueOf).collect(Collectors.toList());
        return codes.stream().distinct().map(c -> getTableByLogicInventoryLocationCodeAndWarehouseIds(c, warehouseIdsStr))
            .flatMap(List::stream).collect(Collectors.toSet());
    }

    /**
     * 根据逻辑库位编码 + 仓库id计算物理表名
     * 总共分为8个物理表，先根据逻辑库位编码确定物理表范围，在使用仓库id确认具体的物理表；例如
     * 常规 -> 0~3
     * 其它 -> 4~7
     * 入参是：
     *  逻辑库位编码->常规
     *  仓库id -> 2
     * 逻辑库位编码是常规，物理表落在0~3范围内，仓库为2， 2 % 4 = 2，故实际的物理表为warehouse_sku_lot_inventory_2
     * @param logicInventoryLocationCode 逻辑库位编码
     * @param warehouseIds 仓库id
     * @return 物理表名
     */
    private List<String> getTableByLogicInventoryLocationCodeAndWarehouseIds(String logicInventoryLocationCode, Collection<String> warehouseIds) {
        Integer logicInventoryLocationCodeIndex = shardingProperties.getLogicInventoryLocationCodeRouteMap().get(logicInventoryLocationCode);
        return warehouseIds.stream().map(Integer::valueOf)
            .map(Math::abs)
            .map(w -> w % ShardingProperties.TABLE_NUM_4 + logicInventoryLocationCodeIndex * ShardingProperties.TABLE_NUM_4)
            .distinct().map(shadingIndex -> ShardingDbUtil.getActualTableName(ShardingProperties.WAREHOUSE_SKU_LOT_INVENTORY, shadingIndex.toString()))
            .collect(Collectors.toList());
    }
}
