package com.ddmc.ims.config.feign;

import com.ddmc.duc.configuration.SignInterceptor;
import feign.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.FeignLoggerFactory;
import org.springframework.context.annotation.Bean;

public class SsoNewFeignConfig {
    @Value("${spring.application.name:}")
    private String serviceName;

    @Value("${sso.app.id}")
    private String ssoAppId;

    @Value("${sso.app.secret}")
    private String ssoAppSecret;

    /**
     * 日志输出格式.
     */
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    /**
     * 请求响应日志.`
     */
    @Bean
    public FeignLoggerFactory feignLoggerFactory() {
        return new InfoFeignLoggerFactory();
    }

    @Bean
    public SignInterceptor signInterceptor() {
        return new SignInterceptor(ssoAppId, ssoAppSecret, serviceName);
    }
}
