package com.ddmc.ims.config.feign;

import feign.Logger;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;
import org.springframework.cloud.openfeign.FeignLoggerFactory;
import org.springframework.context.annotation.Bean;

public class CommonFeignConfig extends FeignClientsConfiguration {


    /**
     * 日志输出格式.
     */
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    /**
     * 请求响应日志.`
     */
    @Override
    public FeignLoggerFactory feignLoggerFactory() {
        return new InfoFeignLoggerFactory();
    }

}
