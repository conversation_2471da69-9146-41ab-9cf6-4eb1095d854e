package com.ddmc.ims.config.interceptor;

import com.ddmc.gateway.bg.client.model.AuthInfo;
import com.ddmc.gateway.bg.client.utils.AuthUtil;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.ThreadLocalContextHelper;
import com.ddmc.ims.common.util.user.CurrentUser;
import com.ddmc.ims.common.util.user.UserContextUtils;
import com.ddmc.ims.config.SpringProfileConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.lang.Nullable;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Data
@Order(1)
public class UserContextInterceptor implements HandlerInterceptor {

    @Autowired
    private SpringProfileConfig profileConfig;

    private static final String REQUEST_ID = "requestId";

    private void checkAuth() {
        AuthInfo authInfo = AuthUtil.getAuthInfo();
        if (authInfo == null) {
            throw new ImsBusinessException("获取用户信息错误");
        }
        CurrentUser currentUser = new CurrentUser();
        currentUser.setUserId(authInfo.getToken());
        currentUser.setUserName(authInfo.getUsername());
        currentUser.setEmail(authInfo.getEmail());
        currentUser.setSsoToken(authInfo.getToken());
        currentUser.setTimeout(authInfo.getAuthTime());
        UserContextUtils.setUserContext(currentUser);
    }


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (com.ddmc.ims.config.interceptor.RequestWhiteList.matches(request.getRequestURI())) {
            log.debug("[UserContextInterceptor] 白名单路径:{},跳过设置用户上下文", request.getRequestURI());
        }else if (profileConfig.isDev()) {
            setupDev();
            log.debug("[UserContextInterceptor] dev开发环境设置默认用户上下文");
        } else if (profileConfig.isTest()) {
            setupTest();
            log.debug("[UserContextInterceptor] Test测试环境设置用户上下文");
        }else {
            checkAuth();

            log.debug("[UserContextInterceptor] 请求{} 设置用户上下文{}", request.getRequestURI(),
                JsonUtil.toJson(UserContextUtils.get()));
        }
        return Boolean.TRUE;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
        @Nullable Exception ex) {
        ThreadLocalContextHelper.clearAll();
    }

    private void setupDev() {
        CurrentUser currentUser = new CurrentUser();
        currentUser.setUserName("dev开发环境模拟用户");
        currentUser.setUserId("dev");
        UserContextUtils.setUserContext(currentUser);
    }

    private void setupTest() {
        AuthInfo authInfo = AuthUtil.getAuthInfo();
        CurrentUser currentUser = new CurrentUser();
        if (authInfo == null) {
            currentUser.setUserName("测试环境模拟用户");
            currentUser.setUserId("test");
        }else{
            currentUser.setUserId(authInfo.getToken());
            currentUser.setUserName(authInfo.getUsername());
            currentUser.setEmail(authInfo.getEmail());
            currentUser.setSsoToken(authInfo.getToken());
            currentUser.setTimeout(authInfo.getAuthTime());
        }
        UserContextUtils.setUserContext(currentUser);
    }
}
