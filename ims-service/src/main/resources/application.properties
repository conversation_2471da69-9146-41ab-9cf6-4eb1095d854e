# åæ°æ¬å°éç½®è¡¨æå¨æ°æ®æº
scm.common.config.client.dataSourceBeanName = imsDataSource


# rocket mq çäº§ç«¯éç½® ä¼ä¸å¾®ä¿¡ç¾¤æºå¨äººåè­¦æ¶æ¯åéï¼commonæå¡ï¼
spring.cloud.stream.bindings.scmAlertMsgOutput.binder = rocketmq
spring.cloud.stream.bindings.alertMsgOutput.destination=scmAlertMsg
spring.cloud.stream.bindings.alertMsgOutput.content-type = application/json
spring.cloud.stream.bindings.scmAlertMsgOutput.destination = scmAlertMsg
spring.cloud.stream.bindings.scmAlertMsgOutput.content-type = application/json
spring.cloud.stream.rocketmq.bindings.scmAlertMsgOutput.producer.sync = true
spring.cloud.stream.rocketmq.bindings.scmAlertMsgOutput.producer.group = scmImsService_scmAlertMsg_output
################## Rocket - End ##################

# å®ä¹çäº§è
spring.cloud.stream.bindings.imsLotInfoOutput.binder=rocketmq
spring.cloud.stream.bindings.imsLotInfoOutput.destination=imsLotInfo
spring.cloud.stream.bindings.imsLotInfoOutput.content-type=application/json
spring.cloud.stream.rocketmq.bindings.imsLotInfoOutput.producer.group=scmImsService_imsLotInfo_output
spring.cloud.stream.rocketmq.bindings.imsLotInfoOutput.producer.sync=true

# confirmæ¶æ¯çäº§è
spring.cloud.stream.bindings.imsConfirmOutput.binder=rocketmq
spring.cloud.stream.bindings.imsConfirmOutput.destination=imsAsyncConfirm
spring.cloud.stream.bindings.imsConfirmOutput.content-type=application/json
spring.cloud.stream.rocketmq.bindings.imsConfirmOutput.producer.group=scmImsService_imsAsyncConfirm_output
spring.cloud.stream.rocketmq.bindings.imsConfirmOutput.producer.sync=true
spring.cloud.stream.bindings.imsConfirmOutput.producer.partition-key-expression = payload.warehouseId

# å®ä¹consumer confirmæ¶æ¯æ¶è´¹è mq
spring.cloud.stream.bindings.imsConfirmInput.binder=rocketmq
spring.cloud.stream.bindings.imsConfirmInput.destination=imsAsyncConfirm
spring.cloud.stream.bindings.imsConfirmInput.content-type=application/json
spring.cloud.stream.bindings.imsConfirmInput.group=scmImsService_imsAsyncConfirm_input
spring.cloud.stream.bindings.imsConfirmInput.consumer.concurrency=4
spring.cloud.stream.rocketmq.bindings.imsConfirmInput.consumer.enabled = true
spring.cloud.stream.rocketmq.bindings.imsConfirmInput.consumer.broadcasting = false
spring.cloud.stream.rocketmq.bindings.imsConfirmInput.consumer.orderly=true