app:
    id: scm-ims-service

apollo:
    bootstrap:
        namespaces: application,goc.preplan
        enabled: true
        eagerLoad:
            enabled: true

spring:
    main:
        allow-bean-definition-overriding: true
    profiles:
        active: default
    application:
        name: scm-ims-service
    cloud:
        sentinel:
            eager: true  #Sentinel自动化配置是否生效
            enabled: true  #取消Sentinel控制台懒加载
            datasource:
                flowconfig:
                    apollo:
                        namespace-name: application
                        flow-rules-key: sentinel.flowrules
                        rule-type: flow
logging:
    config: classpath:logback-ddmc.xml