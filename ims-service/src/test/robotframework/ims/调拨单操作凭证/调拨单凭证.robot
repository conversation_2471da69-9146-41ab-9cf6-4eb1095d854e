*** Settings ***
Resource          调拨单凭证.resource

*** Variables ***
${SQL Query change_transfer_info_credential}    select 1 from change_transfer_info_credential where order_no = 'orderNoStr'
${SQL query transfer_intransit_inventory}    select plan_qty,wait_alloc_qty from transfer_intransit_inventory where order_no = 'orderNoStr' and sku_id = 'skuIdStr'

*** Test Cases ***
发布调拨单信息
    ${expectArriveTime}    getAddDate    2
    ${orderNo}    Uuid 4
    ${orderNo}    Set Variable    ${orderNo[1:30]}
    ${item1}    Create Dictionary    qty=1    skuId=100    demandDate=${expectArriveTime}
    ${item2}    Create Dictionary    qty=2    skuId=200    demandDate=${expectArriveTime}
    ${item}    Create List    ${item1}    ${item2}
    &{request}    Create Transfer Init Request    ${orderNo}    ${item}
    Post Transfer Init API    &{request}
    ${queryTransferCredential}=    Replace String    ${SQL Query change_transfer_info_credential}    orderNoStr    ${orderNo}
    @{queryTransferCredential Result}    Sql Query    ${queryTransferCredential}
    Should Not Be Empty    @{queryTransferCredential Result}
    ${queryTransferIntransit}=    Replace String    ${SQL query transfer_intransit_inventory}    orderNoStr    ${orderNo}
    ${queryTransferIntransit}=    Replace String    ${queryTransferIntransit}    skuIdStr    100
    @{queryTransferIntransitResult}    Sql Query    ${queryTransferIntransit}
    Should Not Be Empty    @{queryTransferIntransitResult}
    ${planQtyResult}    Get From Dictionary    @{queryTransferIntransitResult}    plan_qty
    Should Be Equal As Strings    ${planQtyResult}    1.000
    ${waitAllocResult}    Get From Dictionary    @{queryTransferIntransitResult}    wait_alloc_qty
    Should Be Equal As Strings    ${waitAllocResult}    1.000

*** Keywords ***
Create Transfer Init Request
    [Arguments]    ${orderNo}    ${item}
    ${location}    Create Dictionary    warehouseId=17    cargoOwnerId=1    logicInventoryLocationCode=0001
    ${orderSource}    Set Variable    TO
    ${businessDate}    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    ${operatorId}    Set Variable    TEST
    ${operatorName}    Set Variable    TEST
    ${planOutTime}    getAddDate    1
    ${expectArriveTime}    getAddDate    2
    ${deliveryMode}    Set Variable    0
    ${changeType}    Set Variable    0
    ${toLocation}    Create Dictionary    warehouseId=2    cargoOwnerId=1    logicInventoryLocationCode=0001
    &{request}    Create Dictionary    location=${location}    orderSource=${orderSource}    orderNo=${orderNo}    businessDate=${businessDate}    operatorId=${operatorId}    operatorName=${operatorName}    toLocation=${toLocation}    planOutTime=${planOutTime}    expectArriveTime=${expectArriveTime}    deliveryMode=${deliveryMode}    changeType=${changeType}    items=${item}
    Return From Keyword    &{request}

Post Transfer Init API
    [Arguments]    &{request}
    Create Session    imsHost    ${IMS_HOST}
    &{headers}    Create Ims Header
    ${resp}    POST On Session    imsHost    /api/transferOrderInventory/publishTransferInfo    json=&{request}    headers=&{headers}
    ${data}    Set Variable    ${resp.json()}
    ${data_key}    Get Dictionary Keys    ${data}
    ${success}    Get From Dictionary    ${data}    success
    Should Be Equal As Strings    ${success}    True
