*** Settings ***
Library           RequestsLibrary
Library           DatabaseLibrary
Library           Collections
Library           String
Library           FakerLibrary
Library           DateTime
Library           JSONLibrary

*** Variables ***
${IMS_HOST}       http://*************:8080/
${IMS_SQL_INFO}    database='scm_ims',user='rw_scm',password='pFPNGuvzq08E',host='cdb1scm.te.test.srv.mc.dd',port=3306

*** Keywords ***
getAddDate
    [Arguments]    ${add}
    ${date}    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    ${result}    Add Time To Date    ${date}    ${add} days
    Return From Keyword    ${result}

Create Ims Header
    &{headers}    Create Dictionary    Content-Type=application/json
    Return From Keyword    &{headers}

Sql Query
    [Arguments]    ${sql}
    Connect To Database Using Custom Params    pymysql    ${IMS_SQL_INFO}
    @{sqlresult}    Query    ${sql}    false    ture
    Return From Keyword    @{sqlresult}
    Disconnect From Database
