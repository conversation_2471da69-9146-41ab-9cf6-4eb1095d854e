package com.ddmc.ims.event.handle;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.DeliveryModeEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.enums.ims.WarehouseTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.Warehouse;
import com.ddmc.ims.event.entity.inventory.TransferOutChangeNotifyEvent;
import com.ddmc.ims.event.handler.TransferOutChangeNotifyEventHandle;
import com.ddmc.ims.manager.imsAdmin.BookingInventoryManager;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class TransferOutChangeNotifyEventHandleTest {

    @Mock
    private DbEventStorage dbEventStorage;

    @Mock
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Mock
    private BookingInventoryManager bookingInventoryManager;

    @Mock
    private LocalParamService localParamService;

    @Mock
    private WarehouseService warehouseService;

    @InjectMocks
    private TransferOutChangeNotifyEventHandle transferOutChangeNotifyEventHandle;


    private final Long DEFAULT_SKU_ID = 25556L;

    private final Date DEFAULT_EXPECT_OUT_TIME = ThreadLocalDateUtils.parseYmd("2024-03-03");

    private final Date DEFAULT_EXPECT_IN_TIME = ThreadLocalDateUtils.parseYmd("2024-03-03");

    /**
     * 入库仓不是前置仓时不触发缺货预定计算
     */
    @Test
    public void testSyncTransferOutChangeNotifyEvent_general_store() {
//        Mockito.when(transferIntransitInventoryMapper.selectTransferIntransitInventory(Mockito.any()))
//            .thenReturn(Lists.newArrayList(buildIntransit(DeliveryModeEnum.ONE_DELIVERY.getCode())));


        transferOutChangeNotifyEventHandle.syncTransferOutChangeNotifyEvent(buildEvent(Lists.newArrayList(DEFAULT_SKU_ID)));

        Mockito.verify(bookingInventoryManager, Mockito.times(0))
            .notifyTransferOutEvent(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList(), Mockito.any());
        Mockito.verify(dbEventStorage).completeAndAsyncTriggerSubEvent(Mockito.any());
    }



    /**
     * 一配到门店正常触发缺货预定事件
     */
    @Test
    public void testSyncTransferOutChangeNotifyEvent_without_skuIds() {
        Mockito.when(transferIntransitInventoryMapper.selectTransferIntransitInventory(Mockito.any()))
            .thenReturn(Lists.newArrayList(buildIntransit(DeliveryModeEnum.ONE_DELIVERY.getCode())));

        Mockito.when(warehouseService.getWarehouse(Mockito.anyLong())).thenReturn(buildWarehouse(WarehouseTypeEnum.STORE_TYPE_SALE.getCode()));

        Mockito.when(localParamService.getStringValue(Mockito.anyString(), Mockito.anyString())).thenReturn(buildTriggerBookingInventoryChangeType());
        transferOutChangeNotifyEventHandle.syncTransferOutChangeNotifyEvent(buildEvent(Lists.newArrayList()));

        Mockito.verify(bookingInventoryManager, Mockito.times(1))
            .notifyTransferOutEvent(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyList(), Mockito.any());
        Mockito.verify(dbEventStorage).completeAndAsyncTriggerSubEvent(Mockito.any());
    }


    private Warehouse buildWarehouse(Integer type) {
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(1);
        warehouse.setType(type);
        return warehouse;
    }

    private String buildTriggerBookingInventoryChangeType() {
        TransferOutChangeNotifyEventHandle.BookingChangeTriggerTypeConfig changeTriggerTypeConfig =
            new TransferOutChangeNotifyEventHandle.BookingChangeTriggerTypeConfig();

        changeTriggerTypeConfig.setOrderType(OrderTypeEnum.TRANSFER_OUTBOUND.getCode());
        changeTriggerTypeConfig.setOrderOperateType(OrderOperateTypeEnum.APPLY_OUT_OF_STOCK.getCode());
        changeTriggerTypeConfig.setBookingChangeTriggerType("PUBLISH_TRANSFER_ORDER");
        return JsonUtil.toJson(Lists.newArrayList(changeTriggerTypeConfig));
    }


    private TransferOutChangeNotifyEvent buildEvent(List<Long> skuIds) {
        TransferOutChangeNotifyEvent event = new TransferOutChangeNotifyEvent();
        event.setOrderType(OrderTypeEnum.TRANSFER_OUTBOUND.getCode());
        event.setOrderOperateType(OrderOperateTypeEnum.APPLY_OUT_OF_STOCK.getCode());
        event.setOrderNo("oder00001");
        event.setOrderSource("TRANSFER");
        event.setCredentialId(123L);
        event.setSkuIds(skuIds);
        return event;
    }

    private TransferIntransitInventory buildIntransit(Integer deliveryMode) {
        TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setFromLogicInventoryLocationCode(CommonConstants.GOOD_PRODUCT);
        Long DEFAULT_CARGO_OWNER_ID = 4L;
        transferIntransitInventory.setFromCargoOwnerId(DEFAULT_CARGO_OWNER_ID);
        transferIntransitInventory.setFromWarehouseId(1L);
        transferIntransitInventory.setSkuId(DEFAULT_SKU_ID);
        transferIntransitInventory.setExpectOutTime(DEFAULT_EXPECT_OUT_TIME);
        transferIntransitInventory.setExpectInTime(DEFAULT_EXPECT_IN_TIME);
        transferIntransitInventory.setFromLogicInventoryLocationCode(CommonConstants.GOOD_PRODUCT);
        Long TO_WAREHOUSE_ID = 12L;
        transferIntransitInventory.setToWarehouseId(TO_WAREHOUSE_ID);
        transferIntransitInventory.setToCargoOwnerId(DEFAULT_CARGO_OWNER_ID);
        transferIntransitInventory.setPlanQty(new BigDecimal(100));
        transferIntransitInventory.setWaitAllocQty(BigDecimal.ZERO);
        transferIntransitInventory.setDeliveryMode(deliveryMode);
        transferIntransitInventory.setAllocQty(transferIntransitInventory.getPlanQty().subtract(transferIntransitInventory.getWaitAllocQty()).max(BigDecimal.ZERO));
        transferIntransitInventory.setIntransitQty(BigDecimal.ZERO);
        String DEFAULT_USAGE_CODE = "EC";
        transferIntransitInventory.setUsageCode(DEFAULT_USAGE_CODE);
        transferIntransitInventory.setToUsageCode(DEFAULT_USAGE_CODE);
        return transferIntransitInventory;
    }
}
