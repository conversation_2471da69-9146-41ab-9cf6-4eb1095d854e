package com.ddmc.ims.manager;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.sku.api.client.SkuClient;
import com.ddmc.sku.api.client.response.SkuBaseResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuQueryManagerTest {

    @Mock
    private SkuClient mockSkuClient;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private SkuQueryManager skuQueryManagerUnderTest;


    @Test
    public void testBatchGetSkuBySkuIds() {

        final SkuBaseResponse skuBaseResponse1 = new SkuBaseResponse();
        skuBaseResponse1.setBrandId(0L);
        skuBaseResponse1.setSkuId(0L);
        skuBaseResponse1.setSaleName("saleName");
        skuBaseResponse1.setManageCategoryId(0L);
        skuBaseResponse1.setOriginId("originId");
        skuBaseResponse1.setRegionCodePath("regionCodePath");
        skuBaseResponse1.setNetWeight(0);
        skuBaseResponse1.setNetWeightUnit("netWeightUnit");
        skuBaseResponse1.setSalePeriod(0);
        skuBaseResponse1.setQualityPeriod(0L);
        skuBaseResponse1.setIsNoQualityPeriod(0);
        skuBaseResponse1.setStorageValueId(0);
        skuBaseResponse1.setTransverseCabinetStorage(0);
        skuBaseResponse1.setAirCooledRefrigeratorStorage(0);
        skuBaseResponse1.setVolume("volume");
        skuBaseResponse1.setResponsibleGroupId("responsibleGroupId");
        final Map<Long, SkuBaseResponse> expectedResult = Map.ofEntries(Map.entry(0L, skuBaseResponse1));

        final ResponseBaseVo<List<SkuBaseResponse>> listResponseBaseVo = ResponseBaseVo.ok(List.of(skuBaseResponse1));
        when(mockSkuClient.batchGetOptionalSkuPropertiesBySkuIds(Mockito.any()))
            .thenReturn(listResponseBaseVo);

        // Run the test
        final Map<Long, SkuBaseResponse> result = skuQueryManagerUnderTest.batchGetSkuBySkuIds(List.of(0L));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchGetSkuBySkuIds_SkuClientReturnsNoItem() {

        when(mockSkuClient.batchGetOptionalSkuPropertiesBySkuIds(Mockito.any()))
            .thenReturn(ResponseBaseVo.ok());

        // Run the test
        final Map<Long, SkuBaseResponse> result = skuQueryManagerUnderTest.batchGetSkuBySkuIds(List.of(0L));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyMap());
        verify(mockAlertService).alertWarning(Mockito.any(), Mockito.any());
    }


}
