package com.ddmc.ims.manager.lot;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.ddmc.core.view.compat.CodeMsg;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.manager.pes.ExpireInfoRemoteManager;
import com.ddmc.ims.rpc.lot.ImsLotClient;
import com.ddmc.lot.client.response.LotDTO;
import com.ddmc.pes.client.response.ExpireInfoQueryResponse;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class LotManagerTest {

    @Mock
    private ExpireInfoRemoteManager mockExpireInfoRemoteManager;
    @Mock
    private ImsLotClient mockImsLotClient;

    @InjectMocks
    private LotManager lotManagerUnderTest;

    @Test
    public void testScanLot() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure ImsLotClient.scanLot(...).
        final LotDTO lotDTO = new LotDTO();
        final ResponseBaseVo<List<LotDTO>> listResponseBaseVo = ResponseBaseVo.ok(List.of(lotDTO));
        when(mockImsLotClient.scanLot(any())).thenReturn(listResponseBaseVo);

        // Configure ExpireInfoRemoteManager.batchGetExpireInfo(...).
        final ExpireInfoQueryResponse expireInfoQueryResponse = new ExpireInfoQueryResponse();
        expireInfoQueryResponse.setSkuId(0L);
        expireInfoQueryResponse.setWarehouseId(0L);
        expireInfoQueryResponse.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setQualityDays(0);
        expireInfoQueryResponse.setSaleDays(0);
        expireInfoQueryResponse.setUnsaleDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setBestEatDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<ExpireInfoQueryResponse> expireInfoQueryResponses = List.of(expireInfoQueryResponse);

        // Run the test
        final ImmutablePair<List<InventoryLotInfo>, Long> result = lotManagerUnderTest.scanLot(0, 0L, false);

        // Verify the results
        assertThat(result).isEqualTo(new ImmutablePair<>(List.of(new InventoryLotInfo()), null));
    }

    @Test
    public void testScanLot_ImsLotClientReturnsNoItem() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockImsLotClient.scanLot(any())).thenReturn(ResponseBaseVo.ok());

        // Configure ExpireInfoRemoteManager.batchGetExpireInfo(...).
        final ExpireInfoQueryResponse expireInfoQueryResponse = new ExpireInfoQueryResponse();
        expireInfoQueryResponse.setSkuId(0L);
        expireInfoQueryResponse.setWarehouseId(0L);
        expireInfoQueryResponse.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setQualityDays(0);
        expireInfoQueryResponse.setSaleDays(0);
        expireInfoQueryResponse.setUnsaleDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setBestEatDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final ImmutablePair<List<InventoryLotInfo>, Long> result = lotManagerUnderTest.scanLot(0, 0L, false);

        // Verify the results
        assertThat(result).isEqualTo(new ImmutablePair<>(List.of(), Long.MAX_VALUE));
    }

    @Test
    public void testScanLot_ImsLotClientReturnsNoItems() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure ImsLotClient.scanLot(...).
        final ResponseBaseVo<List<LotDTO>> listResponseBaseVo = ResponseBaseVo.ok(Collections.emptyList());
        when(mockImsLotClient.scanLot(any())).thenReturn(listResponseBaseVo);

        // Configure ExpireInfoRemoteManager.batchGetExpireInfo(...).
        final ExpireInfoQueryResponse expireInfoQueryResponse = new ExpireInfoQueryResponse();
        expireInfoQueryResponse.setSkuId(0L);
        expireInfoQueryResponse.setWarehouseId(0L);
        expireInfoQueryResponse.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setQualityDays(0);
        expireInfoQueryResponse.setSaleDays(0);
        expireInfoQueryResponse.setUnsaleDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setBestEatDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final ImmutablePair<List<InventoryLotInfo>, Long> result = lotManagerUnderTest.scanLot(0, 0L, false);

        // Verify the results
        assertThat(result).isEqualTo(new ImmutablePair<>(List.of(), Long.MAX_VALUE));
    }

    @Test
    public void testScanLot_ImsLotClientReturnsFailure() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure ImsLotClient.scanLot(...).
        final ResponseBaseVo<List<LotDTO>> listResponseBaseVo = ResponseBaseVo.ok(new CodeMsg(0, "msg"));
        when(mockImsLotClient.scanLot(any())).thenReturn(listResponseBaseVo);

        // Configure ExpireInfoRemoteManager.batchGetExpireInfo(...).
        final ExpireInfoQueryResponse expireInfoQueryResponse = new ExpireInfoQueryResponse();
        expireInfoQueryResponse.setSkuId(0L);
        expireInfoQueryResponse.setWarehouseId(0L);
        expireInfoQueryResponse.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setQualityDays(0);
        expireInfoQueryResponse.setSaleDays(0);
        expireInfoQueryResponse.setUnsaleDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setBestEatDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final ImmutablePair<List<InventoryLotInfo>, Long> result = lotManagerUnderTest.scanLot(0, 0L, false);

        // Verify the results
        assertThat(result).isEqualTo(new ImmutablePair<>(List.of(), Long.MAX_VALUE));
    }

    @Test
    public void testScanLot_ExpireInfoRemoteManagerReturnsNoItems() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final ImmutablePair<List<InventoryLotInfo>, Long> expectedResult = new ImmutablePair<>(List.of(new InventoryLotInfo()), null);

        // Configure ImsLotClient.scanLot(...).
        final LotDTO lotDTO = new LotDTO();
        final ResponseBaseVo<List<LotDTO>> listResponseBaseVo = ResponseBaseVo.ok(List.of(lotDTO));
        when(mockImsLotClient.scanLot(any())).thenReturn(listResponseBaseVo);


        // Run the test
        final ImmutablePair<List<InventoryLotInfo>, Long> result = lotManagerUnderTest.scanLot(0, 0L, false);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchGet() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> expectedResult = List.of(new InventoryLotInfo());

        // Configure ImsLotClient.multiGet(...).
        final LotDTO lotDTO = new LotDTO();
        final ResponseBaseVo<Map<String, LotDTO>> mapResponseBaseVo = ResponseBaseVo.ok(
            Map.ofEntries(Map.entry("value", lotDTO)));
        when(mockImsLotClient.multiGet(List.of("value"))).thenReturn(mapResponseBaseVo);

        // Configure ExpireInfoRemoteManager.batchGetExpireInfo(...).
        final ExpireInfoQueryResponse expireInfoQueryResponse = new ExpireInfoQueryResponse();
        expireInfoQueryResponse.setSkuId(0L);
        expireInfoQueryResponse.setWarehouseId(0L);
        expireInfoQueryResponse.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setQualityDays(0);
        expireInfoQueryResponse.setSaleDays(0);
        expireInfoQueryResponse.setUnsaleDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setBestEatDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final List<InventoryLotInfo> result = lotManagerUnderTest.batchGet(List.of("value"), false);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }



    @Test
    public void testBatchGet_ExpireInfoRemoteManagerReturnsNoItems() {
        // Setup
        // Configure ImsLotClient.multiGet(...).
        final LotDTO lotDTO = new LotDTO();
        final ResponseBaseVo<Map<String, LotDTO>> mapResponseBaseVo = ResponseBaseVo.ok(
            Map.ofEntries(Map.entry("value", lotDTO)));
        when(mockImsLotClient.multiGet(List.of("value"))).thenReturn(mapResponseBaseVo);


        // Run the test
        final List<InventoryLotInfo> result = lotManagerUnderTest.batchGet(List.of("value"), false);

        // Verify the results
        assertThat(result).isEqualTo(List.of(new InventoryLotInfo()));
    }

    @Test
    public void testGet() {
        // Setup
        final InventoryLotInfo expectedResult = new InventoryLotInfo();
        expectedResult.setId(0L);
        expectedResult.setSkuId(0L);
        expectedResult.setWarehouseId(0L);
        expectedResult.setLotId("lotId");
        expectedResult.setVendorId(0L);
        expectedResult.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setPurchaseNo("purchaseNo");
        expectedResult.setRegionCodePath("regionCodePath");
        expectedResult.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure ImsLotClient.get(...).
        final LotDTO lotDTO = new LotDTO();
        final ResponseBaseVo<LotDTO> lotDTOResponseBaseVo = ResponseBaseVo.ok(lotDTO);
        when(mockImsLotClient.get("lotId")).thenReturn(lotDTOResponseBaseVo);

        // Configure ExpireInfoRemoteManager.batchGetExpireInfo(...).
        final ExpireInfoQueryResponse expireInfoQueryResponse = new ExpireInfoQueryResponse();
        expireInfoQueryResponse.setSkuId(0L);
        expireInfoQueryResponse.setWarehouseId(0L);
        expireInfoQueryResponse.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setQualityDays(0);
        expireInfoQueryResponse.setSaleDays(0);
        expireInfoQueryResponse.setUnsaleDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setBestEatDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final InventoryLotInfo result = lotManagerUnderTest.get("lotId", false);

        // Verify the results
        assertThat(result).isEqualTo(new InventoryLotInfo());
    }




    @Test
    public void testGet_ExpireInfoRemoteManagerReturnsNoItems() {
        // Setup
        final InventoryLotInfo expectedResult = new InventoryLotInfo();
        expectedResult.setId(0L);
        expectedResult.setSkuId(0L);
        expectedResult.setWarehouseId(0L);
        expectedResult.setLotId("lotId");
        expectedResult.setVendorId(0L);
        expectedResult.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setPurchaseNo("purchaseNo");
        expectedResult.setRegionCodePath("regionCodePath");
        expectedResult.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure ImsLotClient.get(...).
        final LotDTO lotDTO = new LotDTO();
        final ResponseBaseVo<LotDTO> lotDTOResponseBaseVo = ResponseBaseVo.ok(lotDTO);
        when(mockImsLotClient.get("lotId")).thenReturn(lotDTOResponseBaseVo);


        // Run the test
        final InventoryLotInfo result = lotManagerUnderTest.get("lotId", false);

        // Verify the results
        assertThat(result).isEqualTo(new InventoryLotInfo());
    }




}
