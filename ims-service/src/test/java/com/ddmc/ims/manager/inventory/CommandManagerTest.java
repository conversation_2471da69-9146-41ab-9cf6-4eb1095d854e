package com.ddmc.ims.manager.inventory;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.command.WarehouseInventoryCommand;
import com.ddmc.ims.command.CommandHandleFactory;
import com.ddmc.ims.command.InventoryCommandHandle;
import com.ddmc.ims.command.WarehouseInventoryModifyAvailableCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.dto.InOutNumDto;
import com.ddmc.ims.service.credential.InventoryCredentialService;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CommandManagerTest {

    @Mock
    private InventoryCredentialService mockInventoryCredentialService;
    @Mock
    private CommandHandleFactory mockCommandHandleFactory;

    @InjectMocks
    private CommandManager commandManagerUnderTest;

    @Test
    public void testGetCommandInventoryNumList() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader);
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION);
        commandInventoryNumDto.setSkuId(1L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(BigDecimal.ZERO);
        commandInventoryNumDto.setFrozenQty(BigDecimal.ZERO);
        commandInventoryNumDto.setTransferIntransitQty(BigDecimal.ZERO);
        commandInventoryNumDto.setUsageCode("usageCode");
        commandInventoryNumDto.setInQty(BigDecimal.ZERO);
        commandInventoryNumDto.setOutQty(BigDecimal.ZERO);
        final List<CommandInventoryNumDto> expectedResult = List.of(commandInventoryNumDto);

        doReturn(List.of(getSkuInventoryCommand())).when(mockInventoryCredentialService)
            .getSkuInventoryCommand(credentialHeader);
        InventoryCommandHandle handle = new WarehouseInventoryModifyAvailableCommandHandle() ;

        when(mockCommandHandleFactory.getCommandHandle(WarehouseInventoryModifyAvailableCommandHandle.class)).thenReturn(
            handle);

        // Run the test
        final List<CommandInventoryNumDto> result = commandManagerUnderTest
            .getCommandInventoryNumList(credentialHeaderAndDetails);


        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetCommandInventoryNumList_InventoryCredentialServiceReturnsNoItems() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader);

        // Run the test
        final List<CommandInventoryNumDto> result = commandManagerUnderTest
            .getCommandInventoryNumList(credentialHeaderAndDetails);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetFmsInOutNumDto() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(1L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(1L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader);
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(1L);
        inOutNumDto.setWarehouseId(1L);
        inOutNumDto.setInQty(BigDecimal.ZERO);
        inOutNumDto.setOutQty(BigDecimal.ZERO);
        final List<InOutNumDto> expectedResult = List.of(inOutNumDto);
        doReturn(List.of(getSkuInventoryCommand())).when(mockInventoryCredentialService)
            .getSkuInventoryCommand(credentialHeader);
         InventoryCommandHandle handle = new WarehouseInventoryModifyAvailableCommandHandle() ;

        when(mockCommandHandleFactory.getCommandHandle(WarehouseInventoryModifyAvailableCommandHandle.class)).thenReturn(
            handle);

        // Run the test
        final List<InOutNumDto> result = commandManagerUnderTest.getFmsInOutNumDto(credentialHeaderAndDetails);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    private WarehouseInventoryCommand getSkuInventoryCommand() {
        return WarehouseInventoryCommand.builder()
            .fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .skuId(1L).lotId("lotId").usageCode("usageCode").qty(BigDecimal.ZERO).commandType(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE).build();
    }

    @Test
    public void testGetFmsInOutNumDto_InventoryCredentialServiceReturnsNoItems() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader);

        // Run the test
        final List<InOutNumDto> result = commandManagerUnderTest.getFmsInOutNumDto(credentialHeaderAndDetails);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
