package com.ddmc.ims.manager.inventory;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithSku;
import com.ddmc.ims.common.bo.SkuIdAndLotIdPair;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.dal.mapper.ims.InventoryLotInfoMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuLotInventoryMapper;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.ddmc.ims.dto.MultiSkuWarehouseInventoryWrapper;
import com.ddmc.ims.dto.SingleSkuWarehouseInventoryWrapper;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Set;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WarehouseInventoryWrapperManagerTest {

    @Mock
    private WarehouseSkuInventoryMapper mockWarehouseSkuInventoryMapper;
    @Mock
    private WarehouseSkuLotInventoryMapper mockWarehouseSkuLotInventoryMapper;
    @Mock
    private InventoryLotInfoMapper mockInventoryLotInfoMapper;

    @InjectMocks
    private WarehouseInventoryWrapperManager warehouseInventoryWrapperManagerUnderTest;

    @Test
    public void testLoadSkuAndLotInventory1() {
        // Setup
        final LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(0L, 0L,
            "logicInventoryLocationCode");
        final List<SkuIdAndLotIdPair> skuIdAndLotIdPairs = List.of(new SkuIdAndLotIdPair(0L, "lotId"));

        // Configure WarehouseSkuInventoryMapper.getByLogicInventoryLocationAndSkuIds(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setId(0L);
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setVersion(0);
        warehouseSkuInventory.setUsageCode("usageCode");
        final List<WarehouseSkuInventory> inventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper
            .getByLogicInventoryLocationAndSkuIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(inventories);

        // Configure WarehouseSkuLotInventoryMapper.getByLogicInventoryLocationAndLotIds(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setId(0L);
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setVersion(0);
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper
            .getByLogicInventoryLocationAndLotIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(new SkuIdAndLotIdPair(0L, "lotId")))).thenReturn(warehouseSkuLotInventories);

        // Run the test
        final MultiSkuWarehouseInventoryWrapper result = warehouseInventoryWrapperManagerUnderTest
            .loadSkuAndLotInventory(logicInventoryLocation, skuIdAndLotIdPairs,
                YesNoEnum.YES);
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testLoadSkuAndLotInventory1_WarehouseSkuInventoryMapperReturnsNoItems() {
        // Setup
        final LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(0L, 0L,
            "logicInventoryLocationCode");
        final List<SkuIdAndLotIdPair> skuIdAndLotIdPairs = List.of(new SkuIdAndLotIdPair(0L, "lotId"));
        when(mockWarehouseSkuInventoryMapper
            .getByLogicInventoryLocationAndSkuIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(Collections.emptyList());

        // Configure WarehouseSkuLotInventoryMapper.getByLogicInventoryLocationAndLotIds(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setId(0L);
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setVersion(0);
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper
            .getByLogicInventoryLocationAndLotIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(new SkuIdAndLotIdPair(0L, "lotId")))).thenReturn(warehouseSkuLotInventories);

        // Run the test
        final MultiSkuWarehouseInventoryWrapper result = warehouseInventoryWrapperManagerUnderTest
            .loadSkuAndLotInventory(logicInventoryLocation, skuIdAndLotIdPairs,
                YesNoEnum.YES);
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testLoadSkuAndLotInventory1_WarehouseSkuLotInventoryMapperReturnsNoItems() {
        // Setup
        final LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(0L, 0L,
            "logicInventoryLocationCode");
        final List<SkuIdAndLotIdPair> skuIdAndLotIdPairs = List.of(new SkuIdAndLotIdPair(0L, "lotId"));

        // Configure WarehouseSkuInventoryMapper.getByLogicInventoryLocationAndSkuIds(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setId(0L);
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setVersion(0);
        warehouseSkuInventory.setUsageCode("usageCode");
        final List<WarehouseSkuInventory> inventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper
            .getByLogicInventoryLocationAndSkuIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(inventories);

        when(mockWarehouseSkuLotInventoryMapper
            .getByLogicInventoryLocationAndLotIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(new SkuIdAndLotIdPair(0L, "lotId")))).thenReturn(Collections.emptyList());

        // Run the test
        final MultiSkuWarehouseInventoryWrapper result = warehouseInventoryWrapperManagerUnderTest
            .loadSkuAndLotInventory(logicInventoryLocation, skuIdAndLotIdPairs,
                YesNoEnum.YES);
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testLoadSkuInventory1() {
        // Setup
        final LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(0L, 0L,
            "logicInventoryLocationCode");

        // Configure WarehouseSkuInventoryMapper.getByLogicInventoryLocationAndSkuIds(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setId(0L);
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setVersion(0);
        warehouseSkuInventory.setUsageCode("usageCode");
        final List<WarehouseSkuInventory> inventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper
            .getByLogicInventoryLocationAndSkuIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(inventories);

        // Run the test
        final MultiSkuWarehouseInventoryWrapper result = warehouseInventoryWrapperManagerUnderTest
            .loadSkuInventory(logicInventoryLocation,
                Set.of(0L));
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testLoadSkuInventory1_WarehouseSkuInventoryMapperReturnsNoItems() {
        // Setup
        final LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(0L, 0L,
            "logicInventoryLocationCode");
        when(mockWarehouseSkuInventoryMapper
            .getByLogicInventoryLocationAndSkuIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final MultiSkuWarehouseInventoryWrapper result = warehouseInventoryWrapperManagerUnderTest
            .loadSkuInventory(logicInventoryLocation,
                Set.of(0L));
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testLoadSkuInventory2() {
        // Setup
        final LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(0L, 0L,
            "logicInventoryLocationCode");

        // Configure WarehouseSkuInventoryMapper.getByLogicInventoryLocationAndSkuIds(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setId(0L);
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setVersion(0);
        warehouseSkuInventory.setUsageCode("usageCode");
        final List<WarehouseSkuInventory> inventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper
            .getByLogicInventoryLocationAndSkuIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(inventories);

        // Configure WarehouseSkuLotInventoryMapper.selectBySkuIdInAndLogicInventoryLocation(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setId(0L);
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setVersion(0);


        // Configure InventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(...).
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final MultiSkuWarehouseInventoryWrapper result = warehouseInventoryWrapperManagerUnderTest
            .loadSkuInventory(logicInventoryLocation,
                Set.of(0L), false, false);
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testLoadSkuInventory2_WarehouseSkuInventoryMapperReturnsNoItems() {
        // Setup
        final LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(0L, 0L,
            "logicInventoryLocationCode");
        when(mockWarehouseSkuInventoryMapper
            .getByLogicInventoryLocationAndSkuIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(Collections.emptyList());

        // Configure WarehouseSkuLotInventoryMapper.selectBySkuIdInAndLogicInventoryLocation(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setId(0L);
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setVersion(0);


        // Configure InventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(...).
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final MultiSkuWarehouseInventoryWrapper result = warehouseInventoryWrapperManagerUnderTest
            .loadSkuInventory(logicInventoryLocation,
                Set.of(0L), false, false);
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testLoadSkuInventory2_WarehouseSkuLotInventoryMapperReturnsNoItems() {
        // Setup
        final LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(0L, 0L,
            "logicInventoryLocationCode");

        // Configure WarehouseSkuInventoryMapper.getByLogicInventoryLocationAndSkuIds(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setId(0L);
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setVersion(0);
        warehouseSkuInventory.setUsageCode("usageCode");
        final List<WarehouseSkuInventory> inventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper
            .getByLogicInventoryLocationAndSkuIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(inventories);


        // Run the test
        final MultiSkuWarehouseInventoryWrapper result = warehouseInventoryWrapperManagerUnderTest
            .loadSkuInventory(logicInventoryLocation,
                Set.of(0L), false, false);
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testLoadSkuInventory2_InventoryLotInfoMapperReturnsNoItems() {
        // Setup
        final LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(0L, 0L,
            "logicInventoryLocationCode");

        // Configure WarehouseSkuInventoryMapper.getByLogicInventoryLocationAndSkuIds(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setId(0L);
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setVersion(0);
        warehouseSkuInventory.setUsageCode("usageCode");
        final List<WarehouseSkuInventory> inventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper
            .getByLogicInventoryLocationAndSkuIds(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(inventories);

        // Configure WarehouseSkuLotInventoryMapper.selectBySkuIdInAndLogicInventoryLocation(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setId(0L);
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setVersion(0);


        // Run the test
        final MultiSkuWarehouseInventoryWrapper result = warehouseInventoryWrapperManagerUnderTest
            .loadSkuInventory(logicInventoryLocation,
                Set.of(0L), false, false);
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testSaveInventory() {
        // Setup
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setId(0L);
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setVersion(0);
        warehouseSkuInventory.setUsageCode("usageCode");
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setId(0L);
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setVersion(0);
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final MultiSkuWarehouseInventoryWrapper warehouseInventoryWrappers = new MultiSkuWarehouseInventoryWrapper(
            List.of(new SingleSkuWarehouseInventoryWrapper(
                new LogicInventoryLocationWithSku(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"), 0L),
                List.of(warehouseSkuInventory), List.of(warehouseSkuLotInventory), List.of(lotInfo))));

        // Run the test
        warehouseInventoryWrapperManagerUnderTest.saveInventory(warehouseInventoryWrappers);


        Assert.assertTrue(true);
    }

    @Test
    public void testDeleteProcessingInventory() {
        // Setup
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setId(0L);
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setVersion(0);
        warehouseSkuInventory.setUsageCode("usageCode");
        final List<WarehouseSkuInventory> needDelete = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.deleteByWarehouseSkuInventoryId(List.of(0L))).thenReturn(0);

        // Configure WarehouseSkuLotInventoryMapper.selectBySkuIdInAndLogicInventoryLocation(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setId(0L);
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuLotInventory.setVersion(0);
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.selectBySkuIdInAndLogicInventoryLocation(
            List.of(0L), new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode")))
            .thenReturn(warehouseSkuLotInventories);

        when(mockWarehouseSkuLotInventoryMapper
            .batchDelete(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L))).thenReturn(0);

        // Run the test
        warehouseInventoryWrapperManagerUnderTest.deleteProcessingInventory(needDelete);

        // Verify the results
        verify(mockWarehouseSkuInventoryMapper).deleteByWarehouseSkuInventoryId(List.of(0L));
        verify(mockWarehouseSkuLotInventoryMapper)
            .batchDelete(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"),
                List.of(0L));
    }

    @Test
    public void testDeleteProcessingInventory_WarehouseSkuLotInventoryMapperSelectBySkuIdInAndLogicInventoryLocationReturnsNoItems() {
        // Setup
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setId(0L);
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouseSkuInventory.setVersion(0);
        warehouseSkuInventory.setUsageCode("usageCode");
        final List<WarehouseSkuInventory> needDelete = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.deleteByWarehouseSkuInventoryId(List.of(0L))).thenReturn(0);
        when(mockWarehouseSkuLotInventoryMapper.selectBySkuIdInAndLogicInventoryLocation(
            List.of(0L), new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode")))
            .thenReturn(Collections.emptyList());

        // Run the test
        warehouseInventoryWrapperManagerUnderTest.deleteProcessingInventory(needDelete);

        // Verify the results
        verify(mockWarehouseSkuInventoryMapper).deleteByWarehouseSkuInventoryId(List.of(0L));

    }
}
