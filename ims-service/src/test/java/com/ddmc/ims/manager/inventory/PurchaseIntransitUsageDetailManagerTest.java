package com.ddmc.ims.manager.inventory;

import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.service.common.AlertService;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PurchaseIntransitUsageDetailManagerTest {

    @Mock
    private PurchaseIntransitInventoryMapper mockPurchaseIntransitInventoryMapper;
    @Mock
    private AlertService mockAlertService;
    @Mock
    private LocalParamConfig mockLocalParamConfig;

    @InjectMocks
    private PurchaseIntransitUsageDetailManager purchaseIntransitUsageDetailManagerUnderTest;

    @Test
    public void testGetUseageDetail_PurchaseIntransitInventoryMapperReturnsNoItems() {
        // Setup
        final CredentialHeader credentialHeaderAndDetail = new CredentialHeader();
        credentialHeaderAndDetail.setOrderSource("orderSource");
        credentialHeaderAndDetail.setOrderNo("orderNo");
        credentialHeaderAndDetail.setOrderOperateType(0);
        credentialHeaderAndDetail.setOrderOperateType(OrderOperateTypeEnum.CLEAR_PLAN_IN.getCode());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        credentialHeaderAndDetail.setCredentialDetailList(List.of(credentialDetail));

        List<CredentialUseageDetail> result = purchaseIntransitUsageDetailManagerUnderTest
            .getUseageDetail(credentialHeaderAndDetail);
        Assert.assertEquals(1, result.size());
    }
}
