package com.ddmc.ims.manager;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.rpc.imsconfig.ImsConfigInventoryUsageClient;
import com.ddmc.ims.service.common.AlertService;
import com.google.common.collect.Maps;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ImsConfigRemoteManagerTest {

    @Mock
    private AlertService mockAlertService;
    @Mock
    private ImsConfigInventoryUsageClient mockInventoryUsageClient;
    @Mock
    private ScheduledExecutorService mockScheduledExecutor;

    @InjectMocks
    private ImsConfigRemoteManager imsConfigRemoteManagerUnderTest;
    @Mock
    private LocalParamConfig localParamConfig;

    @Test
    public void testInit() {

        // Run the test
        imsConfigRemoteManagerUnderTest.scheduleRefreshCache();

        // Verify the results
        verify(mockScheduledExecutor)
            .scheduleAtFixedRate(Mockito.argThat(t->true), eq(0L), eq(4L), eq(TimeUnit.MINUTES));
    }



    @Test
    public void testGetTransferSceneResponseMap() {
        // Setup
        Map<String, Integer> expectedResult = Maps.newHashMapWithExpectedSize(2);
        expectedResult.put("EC",0);
        expectedResult.put("TB",1);
        when(mockInventoryUsageClient.transferScene()).thenReturn(ResponseBaseVo.ok(Lists.newArrayList("EC","TB")));
        when(localParamConfig.getEnableTransferScene()).thenReturn(true);
        CacheConfig.TRANSFER_SCENE.cleanUp();
        // Run the test
        final Map<String, Integer> result = imsConfigRemoteManagerUnderTest.getTransferSceneResponseMap();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTransferSceneResponseMap_ImsConfigInventoryUsageClientReturnsNoItem() {
        Map<String, Integer> expectedResult = Maps.newHashMapWithExpectedSize(2);
        expectedResult.put("TB",0);
        expectedResult.put("EC",1);
        when(localParamConfig.getEnableTransferScene()).thenReturn(false);
        // Run the test
        final Map<String, Integer> result = imsConfigRemoteManagerUnderTest.getTransferSceneResponseMap();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }




}
