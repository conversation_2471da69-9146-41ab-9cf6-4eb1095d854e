package com.ddmc.ims.manager.inventory;

import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.SnapshotCredentialDiff;
import com.ddmc.ims.service.common.AlertService;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SnapshotCredentialDiffManagerTest {

    @Mock
    private CredentialHeaderMapper mockCredentialHeaderMapper;
    @Mock
    private SnapshotCredentialDiffMapper mockSnapshotCredentialDiffMapper;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private SnapshotCredentialDiffManager snapshotCredentialDiffManagerUnderTest;

    @Test
    public void testDoSnapshotInventoryDiff() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockCredentialHeaderMapper.addLock("exeOrderSource", "exeOrderNo")).thenReturn(0L);

        // Configure CredentialHeaderMapper.listByExeOrderSourceAndNoAndOrderOperateType(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        credentialHeader1.setOrderNo("orderNo");
        credentialHeader1.setExeOrderSource("exeOrderSource");
        credentialHeader1.setExeOrderNo("exeOrderNo");
        credentialHeader1.setOrderType("orderType");
        credentialHeader1.setOrderOperateType(0);
        credentialHeader1.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader1.setSeqNo("seqNo");
        credentialHeader1.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader1.setDeliveryMode(0);
        credentialHeader1.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SnapshotCredentialDiffMapper.listByExeOrderSourceAndNo(...).
        final SnapshotCredentialDiff snapshotCredentialDiff = new SnapshotCredentialDiff();
        snapshotCredentialDiff.setId(0L);
        snapshotCredentialDiff.setWarehouseId(0L);
        snapshotCredentialDiff.setCredentialId(0L);
        snapshotCredentialDiff.setOrderSource("orderSource");
        snapshotCredentialDiff.setOrderNo("orderNo");
        snapshotCredentialDiff.setExeOrderSource("exeOrderSource");
        snapshotCredentialDiff.setExeOrderNo("exeOrderNo");
        snapshotCredentialDiff.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotCredentialDiff.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotCredentialDiff.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        snapshotCredentialDiffManagerUnderTest.doSnapshotInventoryDiff(credentialHeader);

        Assert.assertTrue(true);
    }

    @Test
    public void testDoSnapshotInventoryDiff_SnapshotCredentialDiffMapperListByExeOrderSourceAndNoReturnsNoItems() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockCredentialHeaderMapper.addLock("exeOrderSource", "exeOrderNo")).thenReturn(0L);

        // Configure CredentialHeaderMapper.listByExeOrderSourceAndNoAndOrderOperateType(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        credentialHeader1.setOrderNo("orderNo");
        credentialHeader1.setExeOrderSource("exeOrderSource");
        credentialHeader1.setExeOrderNo("exeOrderNo");
        credentialHeader1.setOrderType("orderType");
        credentialHeader1.setOrderOperateType(0);
        credentialHeader1.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader1.setSeqNo("seqNo");
        credentialHeader1.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader1.setDeliveryMode(0);
        credentialHeader1.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        snapshotCredentialDiffManagerUnderTest.doSnapshotInventoryDiff(credentialHeader);

        Assert.assertTrue(true);
    }

    @Test
    public void testSaveCompletedCredentialDiff() {
        // Setup
        final CredentialHeader header = new CredentialHeader();
        header.setId(0L);
        header.setIdempotentId("idempotentId");
        header.setWarehouseId(0L);
        header.setStatus(CredentialStatus.INIT);
        header.setOrderSource("orderSource");
        header.setOrderNo("orderNo");
        header.setExeOrderSource("exeOrderSource");
        header.setExeOrderNo("exeOrderNo");
        header.setOrderType("orderType");
        header.setOrderOperateType(0);
        header.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        header.setSeqNo("seqNo");
        header.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        header.setDeliveryMode(0);
        header.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        snapshotCredentialDiffManagerUnderTest.saveCompletedCredentialDiff(header);

        Assert.assertTrue(true);
    }


}
