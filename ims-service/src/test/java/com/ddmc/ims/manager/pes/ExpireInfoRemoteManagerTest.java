package com.ddmc.ims.manager.pes;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.ddmc.core.view.compat.CodeMsg;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.rpc.pes.PesExpireInfoClient;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.pes.client.request.ExpireInfoQueryRequest;
import com.ddmc.pes.client.response.ExpireInfoQueryResponse;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ExpireInfoRemoteManagerTest {

    @Mock
    private PesExpireInfoClient mockPesExpireInfoClient;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private ExpireInfoRemoteManager expireInfoRemoteManagerUnderTest;
    @Mock
    private LocalParamService localParamService;

    @Test
    public void testBatchGetExpireInfo() {
        // Setup
        final ExpireInfoQueryRequest request = new ExpireInfoQueryRequest();
        request.setSkuId(0L);
        request.setWarehouseId(0L);
        request.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<ExpireInfoQueryRequest> queryList = List.of(request);
        final ExpireInfoQueryResponse expireInfoQueryResponse = new ExpireInfoQueryResponse();
        expireInfoQueryResponse.setSkuId(0L);
        expireInfoQueryResponse.setWarehouseId(0L);
        expireInfoQueryResponse.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setQualityDays(0);
        expireInfoQueryResponse.setSaleDays(0);
        expireInfoQueryResponse.setUnsaleDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse.setBestEatDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<ExpireInfoQueryResponse> expectedResult = List.of(expireInfoQueryResponse);

        // Configure PesExpireInfoClient.batchGetExpireInfo(...).
        final ExpireInfoQueryResponse expireInfoQueryResponse1 = new ExpireInfoQueryResponse();
        expireInfoQueryResponse1.setSkuId(0L);
        expireInfoQueryResponse1.setWarehouseId(0L);
        expireInfoQueryResponse1.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse1.setQualityDays(0);
        expireInfoQueryResponse1.setSaleDays(0);
        expireInfoQueryResponse1.setUnsaleDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expireInfoQueryResponse1.setBestEatDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final ResponseBaseVo<List<ExpireInfoQueryResponse>> listResponseBaseVo = ResponseBaseVo
            .ok(List.of(expireInfoQueryResponse1));
        when(mockPesExpireInfoClient.batchGetExpireInfo(Mockito.any()))
            .thenReturn(listResponseBaseVo);

        // Run the test
        final List<ExpireInfoQueryResponse> result = expireInfoRemoteManagerUnderTest.batchGetExpireInfo(queryList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }



    @Test
    public void testBatchGetExpireInfo_PesExpireInfoClientReturnsNoItems() {
        // Setup
        final ExpireInfoQueryRequest request = new ExpireInfoQueryRequest();
        request.setSkuId(0L);
        request.setWarehouseId(0L);
        request.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<ExpireInfoQueryRequest> queryList = List.of(request);

        // Configure PesExpireInfoClient.batchGetExpireInfo(...).
        final ResponseBaseVo<List<ExpireInfoQueryResponse>> listResponseBaseVo = ResponseBaseVo
            .ok(Collections.emptyList());
        when(mockPesExpireInfoClient.batchGetExpireInfo(Mockito.any()))
            .thenReturn(listResponseBaseVo);

        // Run the test
        final List<ExpireInfoQueryResponse> result = expireInfoRemoteManagerUnderTest.batchGetExpireInfo(queryList);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testBatchGetExpireInfo_PesExpireInfoClientReturnsFailure() {
        // Setup
        final ExpireInfoQueryRequest request = new ExpireInfoQueryRequest();
        request.setSkuId(0L);
        request.setWarehouseId(0L);
        request.setProductionDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<ExpireInfoQueryRequest> queryList = List.of(request);

        // Configure PesExpireInfoClient.batchGetExpireInfo(...).
        final ResponseBaseVo<List<ExpireInfoQueryResponse>> listResponseBaseVo = ResponseBaseVo
            .fail(new CodeMsg(0, "msg"));
        when(mockPesExpireInfoClient.batchGetExpireInfo(Mockito.any()))
            .thenReturn(listResponseBaseVo);

        // Run the test
        final List<ExpireInfoQueryResponse> result = expireInfoRemoteManagerUnderTest.batchGetExpireInfo(queryList);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }


}
