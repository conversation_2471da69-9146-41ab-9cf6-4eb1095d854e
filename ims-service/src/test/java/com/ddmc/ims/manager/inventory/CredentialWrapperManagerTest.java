package com.ddmc.ims.manager.inventory;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.CredentialDetailMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderExtMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialUseageDetailMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.service.common.AlertService;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CredentialWrapperManagerTest {

    @Mock
    private CredentialHeaderMapper mockCredentialHeaderMapper;
    @Mock
    private CredentialDetailMapper mockCredentialDetailMapper;
    @Mock
    private CredentialUseageDetailMapper mockCredentialUsageDetailMapper;
    @Mock
    private AlertService mockAlertService;
    @Mock
    private LocalParamConfig mockLocalParamConfig;

    @InjectMocks
    private CredentialWrapperManager credentialWrapperManagerUnderTest;
    @Mock
    private CredentialHeaderExtMapper credentialHeaderExtMapper;

    @Test
    public void testCancel() {
        // Setup
        // Configure CredentialHeaderMapper.selectByIdempotentId(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        when(mockCredentialHeaderMapper.selectByIdempotentId("idempotentId")).thenReturn(credentialHeader);

        when(mockCredentialHeaderMapper.deleteById(0L)).thenReturn(1);
        when(mockCredentialDetailMapper.deleteByCredentialHeaderId(0L)).thenReturn(1);

        // Run the test
        credentialWrapperManagerUnderTest.cancel("idempotentId");

        // Verify the results
        verify(mockCredentialDetailMapper).deleteByCredentialHeaderId(0L);
    }

    @Test
    public void testGetCredentialHeaderAndDetail() {
        // Setup
        final CredentialHeader expectedResult = new CredentialHeader();
        expectedResult.setId(0L);
        expectedResult.setOrderNo("orderNo");
        expectedResult.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        expectedResult.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        expectedResult.setCredentialUseageDetailList(List.of(credentialUseageDetail));

        // Configure CredentialHeaderMapper.selectByIdempotentId(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail1 = new CredentialDetail();
        credentialDetail1.setCredentialHeaderId(0L);
        credentialDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail1.setFromCargoOwnerId(0L);
        credentialDetail1.setFromWarehouseId(0L);
        credentialDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail1.setToCargoOwnerId(0L);
        credentialDetail1.setToWarehouseId(0L);
        credentialDetail1.setSkuId(0L);
        credentialDetail1.setLotId("lotId");
        credentialDetail1.setQty(new BigDecimal("0.00"));
        credentialDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setUsageCode("usageCode");
        credentialDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setOrderTag("orderTag");
        credentialDetail1.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail1));
        final CredentialUseageDetail credentialUseageDetail1 = new CredentialUseageDetail();
        credentialUseageDetail1.setCredentialHeaderId(0L);
        credentialUseageDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail1.setFromCargoOwnerId(0L);
        credentialUseageDetail1.setFromWarehouseId(0L);
        credentialUseageDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail1.setToCargoOwnerId(0L);
        credentialUseageDetail1.setToWarehouseId(0L);
        credentialUseageDetail1.setSkuId(0L);
        credentialUseageDetail1.setLotId("lotId");
        credentialUseageDetail1.setQty(new BigDecimal("0.00"));
        credentialUseageDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setUsageCode("usageCode");
        credentialUseageDetail1.setOrderTag("orderTag");
        credentialUseageDetail1.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail1));
        when(mockCredentialHeaderMapper.selectByIdempotentId("idempotentId")).thenReturn(credentialHeader);

        // Configure CredentialDetailMapper.selectByCredentialHeaderId(...).
        final CredentialDetail credentialDetail2 = new CredentialDetail();
        credentialDetail2.setCredentialHeaderId(0L);
        credentialDetail2.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail2.setFromCargoOwnerId(0L);
        credentialDetail2.setFromWarehouseId(0L);
        credentialDetail2.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail2.setToCargoOwnerId(0L);
        credentialDetail2.setToWarehouseId(0L);
        credentialDetail2.setSkuId(0L);
        credentialDetail2.setLotId("lotId");
        credentialDetail2.setQty(new BigDecimal("0.00"));
        credentialDetail2.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail2.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail2.setUsageCode("usageCode");
        credentialDetail2.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail2.setOrderTag("orderTag");
        credentialDetail2.setToUsageCode("toUsageCode");
        final List<CredentialDetail> credentialDetails = List.of(credentialDetail2);
        when(mockCredentialDetailMapper.selectByCredentialHeaderId(0L)).thenReturn(credentialDetails);

        // Configure LocalParamConfig.getDefaultLocationUsageCodeMap(...).
        final Map<String, String> stringStringMap = Map.ofEntries(Map.entry("value", "value"));
        when(mockLocalParamConfig.getDefaultLocationUsageCodeMap()).thenReturn(stringStringMap);

        // Run the test
        final CredentialHeader result = credentialWrapperManagerUnderTest.getCredentialHeaderAndDetail("idempotentId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetCredentialHeaderAndDetail_CredentialDetailMapperReturnsNoItems() {
        // Setup
        final CredentialHeader expectedResult = new CredentialHeader();
        expectedResult.setId(0L);
        expectedResult.setOrderNo("orderNo");
        expectedResult.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        expectedResult.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        expectedResult.setCredentialUseageDetailList(List.of(credentialUseageDetail));

        // Configure CredentialHeaderMapper.selectByIdempotentId(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail1 = new CredentialDetail();
        credentialDetail1.setCredentialHeaderId(0L);
        credentialDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail1.setFromCargoOwnerId(0L);
        credentialDetail1.setFromWarehouseId(0L);
        credentialDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail1.setToCargoOwnerId(0L);
        credentialDetail1.setToWarehouseId(0L);
        credentialDetail1.setSkuId(0L);
        credentialDetail1.setLotId("lotId");
        credentialDetail1.setQty(new BigDecimal("0.00"));
        credentialDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setUsageCode("usageCode");
        credentialDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setOrderTag("orderTag");
        credentialDetail1.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail1));
        when(mockCredentialHeaderMapper.selectByIdempotentId("idempotentId")).thenReturn(credentialHeader);

        when(mockCredentialDetailMapper.selectByCredentialHeaderId(0L)).thenReturn(List.of(credentialDetail1));

        // Configure LocalParamConfig.getDefaultLocationUsageCodeMap(...).
        final Map<String, String> stringStringMap = Map.ofEntries(Map.entry("value", "value"));
        when(mockLocalParamConfig.getDefaultLocationUsageCodeMap()).thenReturn(stringStringMap);

        // Run the test
        final CredentialHeader result = credentialWrapperManagerUnderTest.getCredentialHeaderAndDetail("idempotentId");

        // Verify the results
        assertThat(result.getId()).isEqualTo(expectedResult.getId());
    }

    @Test
    public void testGetCredentialHeaderAndDetailList1() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        final List<CredentialHeader> expectedResult = List.of(credentialHeader);

        // Configure CredentialHeaderMapper.selectByIds(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setOrderNo("orderNo");
        credentialHeader1.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail1 = new CredentialDetail();
        credentialDetail1.setCredentialHeaderId(0L);
        credentialDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail1.setFromCargoOwnerId(0L);
        credentialDetail1.setFromWarehouseId(0L);
        credentialDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail1.setToCargoOwnerId(0L);
        credentialDetail1.setToWarehouseId(0L);
        credentialDetail1.setSkuId(0L);
        credentialDetail1.setLotId("lotId");
        credentialDetail1.setQty(new BigDecimal("0.00"));
        credentialDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setUsageCode("usageCode");
        credentialDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setOrderTag("orderTag");
        credentialDetail1.setToUsageCode("toUsageCode");
        credentialHeader1.setCredentialDetailList(List.of(credentialDetail1));
        final CredentialUseageDetail credentialUseageDetail1 = new CredentialUseageDetail();
        credentialUseageDetail1.setCredentialHeaderId(0L);
        credentialUseageDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail1.setFromCargoOwnerId(0L);
        credentialUseageDetail1.setFromWarehouseId(0L);
        credentialUseageDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail1.setToCargoOwnerId(0L);
        credentialUseageDetail1.setToWarehouseId(0L);
        credentialUseageDetail1.setSkuId(0L);
        credentialUseageDetail1.setLotId("lotId");
        credentialUseageDetail1.setQty(new BigDecimal("0.00"));
        credentialUseageDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setUsageCode("usageCode");
        credentialUseageDetail1.setOrderTag("orderTag");
        credentialUseageDetail1.setToUsageCode("toUsageCode");
        credentialHeader1.setCredentialUseageDetailList(List.of(credentialUseageDetail1));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader1);
        when(mockCredentialHeaderMapper.selectByIds(List.of(0L))).thenReturn(credentialHeaders);

        // Configure CredentialDetailMapper.selectByCredentialHeaderIds(...).
        final CredentialDetail credentialDetail2 = new CredentialDetail();
        credentialDetail2.setCredentialHeaderId(0L);
        credentialDetail2.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail2.setFromCargoOwnerId(0L);
        credentialDetail2.setFromWarehouseId(0L);
        credentialDetail2.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail2.setToCargoOwnerId(0L);
        credentialDetail2.setToWarehouseId(0L);
        credentialDetail2.setSkuId(0L);
        credentialDetail2.setLotId("lotId");
        credentialDetail2.setQty(new BigDecimal("0.00"));
        credentialDetail2.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail2.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail2.setUsageCode("usageCode");
        credentialDetail2.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail2.setOrderTag("orderTag");
        credentialDetail2.setToUsageCode("toUsageCode");
        final List<CredentialDetail> credentialDetails = List.of(credentialDetail2);
        when(mockCredentialDetailMapper.selectByCredentialHeaderIds(List.of(0L))).thenReturn(credentialDetails);

        // Configure CredentialUseageDetailMapper.selectByCredentialHeaderIds(...).
        final CredentialUseageDetail credentialUseageDetail2 = new CredentialUseageDetail();
        credentialUseageDetail2.setCredentialHeaderId(0L);
        credentialUseageDetail2.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail2.setFromCargoOwnerId(0L);
        credentialUseageDetail2.setFromWarehouseId(0L);
        credentialUseageDetail2.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail2.setToCargoOwnerId(0L);
        credentialUseageDetail2.setToWarehouseId(0L);
        credentialUseageDetail2.setSkuId(0L);
        credentialUseageDetail2.setLotId("lotId");
        credentialUseageDetail2.setQty(new BigDecimal("0.00"));
        credentialUseageDetail2.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail2.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail2.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail2.setUsageCode("usageCode");
        credentialUseageDetail2.setOrderTag("orderTag");
        credentialUseageDetail2.setToUsageCode("toUsageCode");
        final List<CredentialUseageDetail> credentialUseageDetails = List.of(credentialUseageDetail2);
        when(mockCredentialUsageDetailMapper.selectByCredentialHeaderIds(List.of(0L)))
            .thenReturn(credentialUseageDetails);

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .getCredentialHeaderAndDetailList(List.of(0L));

        // Verify the results
        assertThat(result).hasSameSizeAs(expectedResult);
    }

    @Test
    public void testGetCredentialHeaderAndDetailList1_CredentialHeaderMapperReturnsNoItems() {
        // Setup
        when(mockCredentialHeaderMapper.selectByIds(List.of(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .getCredentialHeaderAndDetailList(List.of(0L));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }


    @Test
    public void testListCredentialHeaderAndDetail() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        final List<CredentialHeader> expectedResult = List.of(credentialHeader);

        // Configure CredentialHeaderMapper.selectByIds(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setOrderNo("orderNo");
        credentialHeader1.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail1 = new CredentialDetail();
        credentialDetail1.setCredentialHeaderId(0L);
        credentialDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail1.setFromCargoOwnerId(0L);
        credentialDetail1.setFromWarehouseId(0L);
        credentialDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail1.setToCargoOwnerId(0L);
        credentialDetail1.setToWarehouseId(0L);
        credentialDetail1.setSkuId(0L);
        credentialDetail1.setLotId("lotId");
        credentialDetail1.setQty(new BigDecimal("0.00"));
        credentialDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setUsageCode("usageCode");
        credentialDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setOrderTag("orderTag");
        credentialDetail1.setToUsageCode("toUsageCode");
        credentialHeader1.setCredentialDetailList(List.of(credentialDetail1));
        final CredentialUseageDetail credentialUseageDetail1 = new CredentialUseageDetail();
        credentialUseageDetail1.setCredentialHeaderId(0L);
        credentialUseageDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail1.setFromCargoOwnerId(0L);
        credentialUseageDetail1.setFromWarehouseId(0L);
        credentialUseageDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail1.setToCargoOwnerId(0L);
        credentialUseageDetail1.setToWarehouseId(0L);
        credentialUseageDetail1.setSkuId(0L);
        credentialUseageDetail1.setLotId("lotId");
        credentialUseageDetail1.setQty(new BigDecimal("0.00"));
        credentialUseageDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setUsageCode("usageCode");
        credentialUseageDetail1.setOrderTag("orderTag");
        credentialUseageDetail1.setToUsageCode("toUsageCode");
        credentialHeader1.setCredentialUseageDetailList(List.of(credentialUseageDetail1));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader1);
        when(mockCredentialHeaderMapper.selectByIds(List.of(0L))).thenReturn(credentialHeaders);

        // Configure CredentialDetailMapper.selectByCredentialHeaderIds(...).
        final CredentialDetail credentialDetail2 = new CredentialDetail();
        credentialDetail2.setCredentialHeaderId(0L);
        credentialDetail2.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail2.setFromCargoOwnerId(0L);
        credentialDetail2.setFromWarehouseId(0L);
        credentialDetail2.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail2.setToCargoOwnerId(0L);
        credentialDetail2.setToWarehouseId(0L);
        credentialDetail2.setSkuId(0L);
        credentialDetail2.setLotId("lotId");
        credentialDetail2.setQty(new BigDecimal("0.00"));
        credentialDetail2.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail2.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail2.setUsageCode("usageCode");
        credentialDetail2.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail2.setOrderTag("orderTag");
        credentialDetail2.setToUsageCode("toUsageCode");
        final List<CredentialDetail> credentialDetails = List.of(credentialDetail2);
        when(mockCredentialDetailMapper.selectByCredentialHeaderIds(List.of(0L))).thenReturn(credentialDetails);

        // Configure CredentialUseageDetailMapper.selectByCredentialHeaderIds(...).
        final CredentialUseageDetail credentialUseageDetail2 = new CredentialUseageDetail();
        credentialUseageDetail2.setCredentialHeaderId(0L);
        credentialUseageDetail2.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail2.setFromCargoOwnerId(0L);
        credentialUseageDetail2.setFromWarehouseId(0L);
        credentialUseageDetail2.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail2.setToCargoOwnerId(0L);
        credentialUseageDetail2.setToWarehouseId(0L);
        credentialUseageDetail2.setSkuId(0L);
        credentialUseageDetail2.setLotId("lotId");
        credentialUseageDetail2.setQty(new BigDecimal("0.00"));
        credentialUseageDetail2.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail2.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail2.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail2.setUsageCode("usageCode");
        credentialUseageDetail2.setOrderTag("orderTag");
        credentialUseageDetail2.setToUsageCode("toUsageCode");
        final List<CredentialUseageDetail> credentialUseageDetails = List.of(credentialUseageDetail2);
        when(mockCredentialUsageDetailMapper.selectByCredentialHeaderIds(List.of(0L)))
            .thenReturn(credentialUseageDetails);

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .listCredentialHeaderAndDetail(List.of(0L));

        // Verify the results
        assertThat(result).hasSameSizeAs(expectedResult);
    }

    @Test
    public void testListCredentialHeaderAndDetail_CredentialHeaderMapperReturnsNoItems() {
        // Setup
        when(mockCredentialHeaderMapper.selectByIds(List.of(0L))).thenReturn(Collections.emptyList());
        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .listCredentialHeaderAndDetail(List.of(0L));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetMinCredentialHeader() {
        // Setup
        when(mockCredentialHeaderMapper.selectMinIdByWarehouseCompleteHeaderByBusinessTime(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0L);

        // Run the test
        final Long result = credentialWrapperManagerUnderTest
            .getMinCredentialHeader(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    public void testGetMaxCredentialHeader() {
        // Setup
        when(mockCredentialHeaderMapper.selectMaxIdByWarehouseCompleteHeaderByBusinessTime(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0L);

        // Run the test
        final Long result = credentialWrapperManagerUnderTest
            .getMaxCredentialHeader(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    public void testGetCredentialHeaders() {

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }



    @Test
    public void testGetCredentialHeaderAndDetailList2() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        final List<CredentialHeader> expectedResult = List.of(credentialHeader);

        // Configure CredentialHeaderMapper.selectByWarehouseIdAndIdAndBusinessBetween(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setOrderNo("orderNo");
        credentialHeader1.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail1 = new CredentialDetail();
        credentialDetail1.setCredentialHeaderId(0L);
        credentialDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail1.setFromCargoOwnerId(0L);
        credentialDetail1.setFromWarehouseId(0L);
        credentialDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail1.setToCargoOwnerId(0L);
        credentialDetail1.setToWarehouseId(0L);
        credentialDetail1.setSkuId(0L);
        credentialDetail1.setLotId("lotId");
        credentialDetail1.setQty(new BigDecimal("0.00"));
        credentialDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setUsageCode("usageCode");
        credentialDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setOrderTag("orderTag");
        credentialDetail1.setToUsageCode("toUsageCode");
        credentialHeader1.setCredentialDetailList(List.of(credentialDetail1));
        final CredentialUseageDetail credentialUseageDetail1 = new CredentialUseageDetail();
        credentialUseageDetail1.setCredentialHeaderId(0L);
        credentialUseageDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail1.setFromCargoOwnerId(0L);
        credentialUseageDetail1.setFromWarehouseId(0L);
        credentialUseageDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail1.setToCargoOwnerId(0L);
        credentialUseageDetail1.setToWarehouseId(0L);
        credentialUseageDetail1.setSkuId(0L);
        credentialUseageDetail1.setLotId("lotId");
        credentialUseageDetail1.setQty(new BigDecimal("0.00"));
        credentialUseageDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setUsageCode("usageCode");
        credentialUseageDetail1.setOrderTag("orderTag");
        credentialUseageDetail1.setToUsageCode("toUsageCode");
        credentialHeader1.setCredentialUseageDetailList(List.of(credentialUseageDetail1));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader1);
        when(mockCredentialHeaderMapper.selectByWarehouseIdAndIdAndBusinessBetween(0L, 0L, 0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CredentialDetailMapper.selectByCredentialHeaderIds(...).
        final CredentialDetail credentialDetail2 = new CredentialDetail();
        credentialDetail2.setCredentialHeaderId(0L);
        credentialDetail2.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail2.setFromCargoOwnerId(0L);
        credentialDetail2.setFromWarehouseId(0L);
        credentialDetail2.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail2.setToCargoOwnerId(0L);
        credentialDetail2.setToWarehouseId(0L);
        credentialDetail2.setSkuId(0L);
        credentialDetail2.setLotId("lotId");
        credentialDetail2.setQty(new BigDecimal("0.00"));
        credentialDetail2.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail2.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail2.setUsageCode("usageCode");
        credentialDetail2.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail2.setOrderTag("orderTag");
        credentialDetail2.setToUsageCode("toUsageCode");
        final List<CredentialDetail> credentialDetails = List.of(credentialDetail2);
        when(mockCredentialDetailMapper.selectByCredentialHeaderIds(List.of(0L))).thenReturn(credentialDetails);

        // Configure CredentialUseageDetailMapper.selectByCredentialHeaderIds(...).
        final CredentialUseageDetail credentialUseageDetail2 = new CredentialUseageDetail();
        credentialUseageDetail2.setCredentialHeaderId(0L);
        credentialUseageDetail2.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail2.setFromCargoOwnerId(0L);
        credentialUseageDetail2.setFromWarehouseId(0L);
        credentialUseageDetail2.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail2.setToCargoOwnerId(0L);
        credentialUseageDetail2.setToWarehouseId(0L);
        credentialUseageDetail2.setSkuId(0L);
        credentialUseageDetail2.setLotId("lotId");
        credentialUseageDetail2.setQty(new BigDecimal("0.00"));
        credentialUseageDetail2.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail2.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail2.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail2.setUsageCode("usageCode");
        credentialUseageDetail2.setOrderTag("orderTag");
        credentialUseageDetail2.setToUsageCode("toUsageCode");
        final List<CredentialUseageDetail> credentialUseageDetails = List.of(credentialUseageDetail2);
        when(mockCredentialUsageDetailMapper.selectByCredentialHeaderIds(List.of(0L)))
            .thenReturn(credentialUseageDetails);

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .getCredentialHeaderAndDetailList(0L, 0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetCredentialHeaderAndDetailList2_CredentialHeaderMapperReturnsNoItems() {

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .getCredentialHeaderAndDetailList(0L, 0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetCredentialHeaderAndDetailList2_CredentialDetailMapperReturnsNoItems() {
        // Setup
        // Configure CredentialHeaderMapper.selectByWarehouseIdAndIdAndBusinessBetween(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialHeaderMapper.selectByWarehouseIdAndIdAndBusinessBetween(0L, 0L, 0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        when(mockCredentialDetailMapper.selectByCredentialHeaderIds(List.of(0L))).thenReturn(Collections.emptyList());

        // Configure CredentialUseageDetailMapper.selectByCredentialHeaderIds(...).
        final CredentialUseageDetail credentialUseageDetail1 = new CredentialUseageDetail();
        credentialUseageDetail1.setCredentialHeaderId(0L);
        credentialUseageDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail1.setFromCargoOwnerId(0L);
        credentialUseageDetail1.setFromWarehouseId(0L);
        credentialUseageDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail1.setToCargoOwnerId(0L);
        credentialUseageDetail1.setToWarehouseId(0L);
        credentialUseageDetail1.setSkuId(0L);
        credentialUseageDetail1.setLotId("lotId");
        credentialUseageDetail1.setQty(new BigDecimal("0.00"));
        credentialUseageDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail1.setUsageCode("usageCode");
        credentialUseageDetail1.setOrderTag("orderTag");
        credentialUseageDetail1.setToUsageCode("toUsageCode");
        final List<CredentialUseageDetail> credentialUseageDetails = List.of(credentialUseageDetail1);
        when(mockCredentialUsageDetailMapper.selectByCredentialHeaderIds(List.of(0L)))
            .thenReturn(credentialUseageDetails);

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .getCredentialHeaderAndDetailList(0L, 0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(credentialHeaders);
    }

    @Test
    public void testGetCredentialHeaderAndDetailList2_CredentialUseageDetailMapperReturnsNoItems() {
        // Setup
        // Configure CredentialHeaderMapper.selectByWarehouseIdAndIdAndBusinessBetween(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialHeaderMapper.selectByWarehouseIdAndIdAndBusinessBetween(0L, 0L, 0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CredentialDetailMapper.selectByCredentialHeaderIds(...).
        final CredentialDetail credentialDetail1 = new CredentialDetail();
        credentialDetail1.setCredentialHeaderId(0L);
        credentialDetail1.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail1.setFromCargoOwnerId(0L);
        credentialDetail1.setFromWarehouseId(0L);
        credentialDetail1.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail1.setToCargoOwnerId(0L);
        credentialDetail1.setToWarehouseId(0L);
        credentialDetail1.setSkuId(0L);
        credentialDetail1.setLotId("lotId");
        credentialDetail1.setQty(new BigDecimal("0.00"));
        credentialDetail1.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail1.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setUsageCode("usageCode");
        credentialDetail1.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail1.setOrderTag("orderTag");
        credentialDetail1.setToUsageCode("toUsageCode");
        final List<CredentialDetail> credentialDetails = List.of(credentialDetail1);
        when(mockCredentialDetailMapper.selectByCredentialHeaderIds(List.of(0L))).thenReturn(credentialDetails);

        when(mockCredentialUsageDetailMapper.selectByCredentialHeaderIds(List.of(0L)))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .getCredentialHeaderAndDetailList(0L, 0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(credentialHeaders);
    }

    @Test
    public void testSaveCredential() {
        // Setup
        final CredentialHeader header = new CredentialHeader();
        header.setId(0L);
        header.setOrderNo("orderNo");
        header.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        header.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        header.setCredentialUseageDetailList(List.of(credentialUseageDetail));


        // Run the test
        credentialWrapperManagerUnderTest.saveCredential(header);

        verify(mockCredentialHeaderMapper).insert(Mockito.any());
    }

    @Test
    public void testSaveCredentialHeaderAndDetail() {
        // Setup
        final CredentialHeader header = new CredentialHeader();
        header.setId(0L);
        header.setOrderNo("orderNo");
        header.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        header.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        header.setCredentialUseageDetailList(List.of(credentialUseageDetail));

        // Run the test
        credentialWrapperManagerUnderTest.saveCredentialHeaderAndDetail(header);

        // Verify the results
        verify(mockCredentialHeaderMapper).insert(Mockito.any());
    }

    @Test
    public void testListByWarehouseIdAndOrderOperateTypeAndEndDateTime() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        final List<CredentialHeader> expectedResult = List.of(credentialHeader);

        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialHeaderMapper.listByWarehouseIdAndOrderOperateTypeAndEndDateTime(0L, OrderTypeEnum.TRANSFER_ORDER.getCode(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .listByWarehouseIdAndOrderOperateTypeAndEndDateTime(0L,
                OrderTypeEnum.TRANSFER_ORDER, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListByWarehouseIdAndOrderOperateTypeAndEndDateTime_CredentialHeaderMapperReturnsNoItems() {

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .listByWarehouseIdAndOrderOperateTypeAndEndDateTime(0L,
                OrderTypeEnum.TRANSFER_ORDER, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListByWarehouseIdAndOrderOperateTypeAndEndDateTime_CredentialDetailMapperReturnsNoItems() {
        // Setup
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));

        final List<CredentialHeader> expectedResult = List.of(credentialHeader);


        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockLocalParamConfig.getBusinessDateAheadEndDate()).thenReturn(0);
        when(mockCredentialHeaderMapper.listByWarehouseIdAndOrderOperateTypeAndEndDateTime(0L, OrderTypeEnum.TRANSFER_ORDER.getCode(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        when(mockCredentialDetailMapper.selectByCredentialHeaderIds(List.of(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final List<CredentialHeader> result = credentialWrapperManagerUnderTest
            .listByWarehouseIdAndOrderOperateTypeAndEndDateTime(0L,
                OrderTypeEnum.TRANSFER_ORDER, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
