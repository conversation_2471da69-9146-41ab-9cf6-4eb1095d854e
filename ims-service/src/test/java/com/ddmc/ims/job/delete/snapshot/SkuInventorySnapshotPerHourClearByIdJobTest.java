package com.ddmc.ims.job.delete.snapshot;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotPerHourClearByIdJobTest {

    @Mock
    private SkuInventorySnapshotPerHourMapper mockSkuInventorySnapshotPerHourMapper;

    @InjectMocks
    private SkuInventorySnapshotPerHourClearByIdJob skuInventorySnapshotPerHourClearByIdJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockSkuInventorySnapshotPerHourMapper.deleteByMinId(0L)).thenReturn(0L);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourClearByIdJobUnderTest.execute("param");

        // Verify the results
    }
}
