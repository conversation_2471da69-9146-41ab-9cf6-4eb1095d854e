package com.ddmc.ims.job;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

public class XxlJobConfigTest {

    private XxlJobConfig xxlJobConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        xxlJobConfigUnderTest = new XxlJobConfig();
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "adminAddresses", "adminAddresses");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "appName", "appname");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "ip", "ip");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "port", 0);
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "accessToken", "accessToken");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "logPath", "logPath");
        ReflectionTestUtils.setField(xxlJobConfigUnderTest, "logRetentionDays", 0);
    }

    @Test
    public void testXxlJobExecutor() {
        // Setup
        // Run the test
        final XxlJobSpringExecutor result = xxlJobConfigUnderTest.xxlJobExecutor();

        // Verify the results
    }
}
