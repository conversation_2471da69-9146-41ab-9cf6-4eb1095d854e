package com.ddmc.ims.job.notify;

import static org.mockito.Mockito.verify;

import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.GregorianCalendar;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FmsSnapshotNotifyJobTest {

    @Mock
    private FmsSnapshotNotifyMapper mockFmsSnapshotNotifyMapper;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private FmsSnapshotNotifyJob fmsSnapshotNotifyJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final ReturnT<String> result = fmsSnapshotNotifyJobUnderTest.execute("param");

        // Verify the results
        verify(mockAlertService).alert("财务快照异常", "财务快照通知未生成", AlertLevel.WARNING);
    }
}
