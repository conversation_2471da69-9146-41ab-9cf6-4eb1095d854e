package com.ddmc.ims.job.event;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.ims.EventMapper;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class EventClearJobHandlerTest {

    @Mock
    private EventMapper mockEventMapper;

    @InjectMocks
    private EventClearJobHandler eventClearJobHandlerUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(eventClearJobHandlerUnderTest, "isStopEventClearJob", false);
        ReflectionTestUtils.setField(eventClearJobHandlerUnderTest, "eventClearLimit", 0);
        ReflectionTestUtils.setField(eventClearJobHandlerUnderTest, "eventDeleteCount", 0);
        ReflectionTestUtils.setField(eventClearJobHandlerUnderTest, "delay", false);
        ReflectionTestUtils.setField(eventClearJobHandlerUnderTest, "stop", false);
        ReflectionTestUtils.setField(eventClearJobHandlerUnderTest, "eventDeleteDelayTime", 0);
    }

    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockEventMapper.getMinId()).thenReturn(0L);

        // Configure EventMapper.getGreaterEventEntity(...).
        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(0L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("keyType");
        eventEntity.setStatus(0);
        eventEntity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<EventEntity> eventEntities = List.of(eventEntity);

        // Run the test
        final ReturnT<String> result = eventClearJobHandlerUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_EventMapperGetGreaterEventEntityReturnsNoItems() {
        // Setup
        when(mockEventMapper.getMinId()).thenReturn(0L);

        // Run the test
        final ReturnT<String> result = eventClearJobHandlerUnderTest.execute("param");

        // Verify the results
    }
}
