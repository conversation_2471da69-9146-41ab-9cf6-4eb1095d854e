package com.ddmc.ims.job.snapshot;

import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.service.snapshot.SkuInventorySnapshotPerHourService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotPerHourRetryJobTest {

    @Mock
    private SnapshotTaskMapper mockSnapshotTaskMapper;
    @Mock
    private SkuInventorySnapshotPerHourService mockSnapshotPerHourService;
    @Mock
    private SkuInventorySnapshotPerHourMapper mockSkuInventorySnapshotPerHourMapper;

    @InjectMocks
    private SkuInventorySnapshotPerHourRetryJob skuInventorySnapshotPerHourRetryJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure SnapshotTaskMapper.selectBySnapshotTimeAndSnapshotTypeAndStatus(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        final List<SnapshotTask> snapshotTasks = List.of(snapshotTask);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourRetryJobUnderTest.execute("2023-01-01 00:00:00");


        // Confirm SkuInventorySnapshotPerHourService.handlePerHourSnapshotTask(...).
        final SnapshotTask snapshotTask1 = new SnapshotTask();
        snapshotTask1.setId(0L);
        snapshotTask1.setWarehouseId(0L);
        snapshotTask1.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask1.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask1.setStatus(SnapshotStatusEnum.INIT);

    }

    @Test
    public void testExecute_SnapshotTaskMapperReturnsNoItems() throws Exception {
        // Setup

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourRetryJobUnderTest.execute("2023-01-01 00:00:00");

        // Verify the results
    }
}
