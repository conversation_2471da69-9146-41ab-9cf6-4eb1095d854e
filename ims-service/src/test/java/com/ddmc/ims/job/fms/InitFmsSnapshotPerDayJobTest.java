package com.ddmc.ims.job.fms;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.rpc.alert.AlertLevel;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.FmsSkuInventorySnapshotPerDayService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class InitFmsSnapshotPerDayJobTest {

    @Mock
    private SnapshotTaskMapper mockSnapshotTaskMapper;
    @Mock
    private FmsSkuInventorySnapshotPerDayService mockFmsSkuInventorySnapshotPerDayService;
    @Mock
    private AlertService mockAlertService;
    @Mock
    private WarehouseMapper mockWarehouseMapper;
    @Mock
    private FmsSnapshotNotifyMapper mockFmsSnapshotNotifyMapper;
    @Mock
    private LocalParamConfig mockLocalParamConfig;

    @InjectMocks
    private InitFmsSnapshotPerDayJob initFmsSnapshotPerDayJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(List.of(0L));
        when(mockLocalParamConfig.excludeWarehouseForSnapshot(0L)).thenReturn(false);

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("value");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFmsSnapshotNotifyMapper.selectByDayEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(fmsSnapshotNotify);

        // Run the test
        final ReturnT<String> result = initFmsSnapshotPerDayJobUnderTest.execute("param");

        // Verify the results
        verify(mockFmsSkuInventorySnapshotPerDayService).initEndFmsSkuInventorySnapshot(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockAlertService).alert("生成财务快照完成", "message", AlertLevel.WARNING);

        // Confirm FmsSnapshotNotifyMapper.insert(...).
        final FmsSnapshotNotify entity = new FmsSnapshotNotify();
        entity.setId(0L);
        entity.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setStatus("value");
        entity.setSource("source");
        entity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockFmsSnapshotNotifyMapper).insert(entity);
    }

    @Test
    public void testExecute_WarehouseMapperReturnsNoItems() {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(Collections.emptyList());

        // Run the test
        final ReturnT<String> result = initFmsSnapshotPerDayJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_LocalParamConfigReturnsTrue() {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(List.of(0L));
        when(mockLocalParamConfig.excludeWarehouseForSnapshot(0L)).thenReturn(true);

        // Run the test
        final ReturnT<String> result = initFmsSnapshotPerDayJobUnderTest.execute("param");

        // Verify the results
    }
}
