package com.ddmc.ims.job.delete;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.manager.inventory.TransferInTransitManager;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DeleteTransferIntransitInventoryJobTest {

    @Mock
    private TransferIntransitInventoryMapper mockTransferIntransitInventoryMapper;
    @Mock
    private TransferInTransitManager mockTransferInTransitManager;

    @InjectMocks
    private DeleteTransferIntransitInventoryJob deleteTransferIntransitInventoryJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure TransferIntransitInventoryMapper.getByMinId(...).
        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("orderNo");
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<TransferIntransitInventory> transferIntransitInventories = List.of(transferIntransitInventory);

        // Run the test
        final ReturnT<String> result = deleteTransferIntransitInventoryJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_TransferIntransitInventoryMapperReturnsNoItems() {
        // Setup

        // Run the test
        final ReturnT<String> result = deleteTransferIntransitInventoryJobUnderTest.execute("param");

        // Verify the results
    }
}
