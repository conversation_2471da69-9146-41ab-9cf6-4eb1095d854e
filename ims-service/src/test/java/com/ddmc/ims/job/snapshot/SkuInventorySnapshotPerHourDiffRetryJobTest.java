package com.ddmc.ims.job.snapshot;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.condition.SnapshotFixCheckItem;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourDiffMapper;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHourDiff;
import com.xxl.job.core.biz.model.ReturnT;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotPerHourDiffRetryJobTest {

    @Mock
    private SkuInventorySnapshotPerHourDiffMapper mockSkuInventorySnapshotPerHourDiffMapper;
    @Mock
    private SkuInventorySnapshotPerHourFixCheckJob mockSkuInventorySnapshotPerHourFixCheckJob;
    @Mock
    private SkuInventorySnapshotPerHourDiffRetryJob mockSelfProxy;

    @InjectMocks
    private SkuInventorySnapshotPerHourDiffRetryJob skuInventorySnapshotPerHourDiffRetryJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup

        // Configure SkuInventorySnapshotPerHourDiffMapper.selectByWarehouseId(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> skuInventorySnapshotPerHourDiffs = List.of(
            skuInventorySnapshotPerHourDiff);


        // Configure SkuInventorySnapshotPerHourFixCheckJob.getSnapshotFixCheckItems(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> snapshotFixCheckItems = List.of(snapshotFixCheckItem);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourDiffRetryJobUnderTest.execute("1");

        // Verify the results


        // Confirm SkuInventorySnapshotPerHourDiffRetryJob.saveDiff(...).
        final SkuInventorySnapshotPerHourDiff diff = new SkuInventorySnapshotPerHourDiff();
        diff.setSkuId(0L);
        diff.setWarehouseId(0L);
        diff.setLogicLocationCode("logicInventoryLocationCode");
        diff.setCargoOwnerId(0L);
        diff.setLotId("lotId");
        diff.setSnapshotId(0L);
        diff.setSnapshotQty(new BigDecimal("0.00"));
        diff.setCredentialQty(new BigDecimal("0.00"));
        diff.setInventoryQty(new BigDecimal("0.00"));
        diff.setTriggerCount(0);
        diff.setCredentialStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        diff.setCredentialEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final SnapshotFixCheckItem snapshotFixCheckItem1 = new SnapshotFixCheckItem();
        snapshotFixCheckItem1.setSkuId(0L);
        snapshotFixCheckItem1.setWarehouseId(0L);
        snapshotFixCheckItem1.setLotId("lotId");
        snapshotFixCheckItem1.setCargoOwnerId(0L);
        snapshotFixCheckItem1.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem1.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem1.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem1.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem1.setPerHourId(0L);
        final List<SnapshotFixCheckItem> currentDiffs = List.of(snapshotFixCheckItem1);
    }

    @Test
    public void testExecute_SkuInventorySnapshotPerHourDiffMapperSelectDistinctWarehouseIdReturnsNoItems()
        throws Exception {
        // Setup

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourDiffRetryJobUnderTest.execute("1");

        // Verify the results
    }

    @Test
    public void testExecute_SkuInventorySnapshotPerHourFixCheckJobGetSnapshotFixCheckItemsReturnsNoItems()
        throws Exception {
        // Setup

        // Configure SkuInventorySnapshotPerHourDiffMapper.selectByWarehouseId(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> skuInventorySnapshotPerHourDiffs = List.of(
            skuInventorySnapshotPerHourDiff);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourDiffRetryJobUnderTest.execute("1");

        // Verify the results
        // Confirm SkuInventorySnapshotPerHourDiffRetryJob.saveDiff(...).
        final SkuInventorySnapshotPerHourDiff diff = new SkuInventorySnapshotPerHourDiff();
        diff.setSkuId(0L);
        diff.setWarehouseId(0L);
        diff.setLogicLocationCode("logicInventoryLocationCode");
        diff.setCargoOwnerId(0L);
        diff.setLotId("lotId");
        diff.setSnapshotId(0L);
        diff.setSnapshotQty(new BigDecimal("0.00"));
        diff.setCredentialQty(new BigDecimal("0.00"));
        diff.setInventoryQty(new BigDecimal("0.00"));
        diff.setTriggerCount(0);
        diff.setCredentialStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        diff.setCredentialEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> currentDiffs = List.of(snapshotFixCheckItem);
    }

    @Test
    public void testSaveDiff() {
        // Setup
        final SkuInventorySnapshotPerHourDiff diff = new SkuInventorySnapshotPerHourDiff();
        diff.setSkuId(0L);
        diff.setWarehouseId(0L);
        diff.setLogicLocationCode("logicInventoryLocationCode");
        diff.setCargoOwnerId(0L);
        diff.setLotId("lotId");
        diff.setSnapshotId(0L);
        diff.setSnapshotQty(new BigDecimal("0.00"));
        diff.setCredentialQty(new BigDecimal("0.00"));
        diff.setInventoryQty(new BigDecimal("0.00"));
        diff.setTriggerCount(0);
        diff.setCredentialStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        diff.setCredentialEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> currentDiffs = List.of(snapshotFixCheckItem);

        // Run the test
        skuInventorySnapshotPerHourDiffRetryJobUnderTest.saveDiff(diff, currentDiffs);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list = List.of(skuInventorySnapshotPerHourDiff);
    }
}
