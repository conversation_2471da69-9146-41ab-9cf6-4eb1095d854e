package com.ddmc.ims.job.event;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.ims.EventMapper;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class TransferOutEventClearJobHandlerTest {

    @Mock
    private EventMapper mockEventMapper;

    @InjectMocks
    private TransferOutEventClearJobHandler transferOutEventClearJobHandlerUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(transferOutEventClearJobHandlerUnderTest, "isStopEventClearJob", false);
        ReflectionTestUtils.setField(transferOutEventClearJobHandlerUnderTest, "eventClearLimit", 0);
        ReflectionTestUtils.setField(transferOutEventClearJobHandlerUnderTest, "eventDeleteCount", 0);
        ReflectionTestUtils.setField(transferOutEventClearJobHandlerUnderTest, "delay", false);
        ReflectionTestUtils.setField(transferOutEventClearJobHandlerUnderTest, "stop", false);
        ReflectionTestUtils.setField(transferOutEventClearJobHandlerUnderTest, "eventDeleteDelayTime", 0);
    }

    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure EventMapper.getProcessing(...).
        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(0L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("keyType");
        eventEntity.setStatus(0);
        eventEntity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<EventEntity> eventEntities = List.of(eventEntity);
        when(mockEventMapper.getProcessing()).thenReturn(eventEntities);

        // Run the test
        final ReturnT<String> result = transferOutEventClearJobHandlerUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_EventMapperGetProcessingReturnsNoItems() {
        // Setup
        when(mockEventMapper.getProcessing()).thenReturn(Collections.emptyList());

        // Run the test
        final ReturnT<String> result = transferOutEventClearJobHandlerUnderTest.execute("param");

        // Verify the results
    }
}
