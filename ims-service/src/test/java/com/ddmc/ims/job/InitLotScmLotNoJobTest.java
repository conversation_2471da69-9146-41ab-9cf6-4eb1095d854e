
package com.ddmc.ims.job;


import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.contains;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.config.RateLimiterConfig;
import com.ddmc.ims.dal.mapper.ims.InventoryLotInfoMapper;
import com.ddmc.ims.manager.lot.LotManager;
import com.ddmc.ims.service.common.AlertService;
import com.google.common.util.concurrent.MoreExecutors;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class InitLotScmLotNoJobTest {

    @Mock
    private AlertService mockAlertService;
    @Mock
    private LotManager mockLotManager;
    @Mock
    private RateLimiterConfig mockRateLimiterConfig;
    @Mock
    private InventoryLotInfoMapper mockInventoryLotInfoMapper;

    @InjectMocks
    private InitLotScmLotNoJob initLotScmLotNoJobUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(initLotScmLotNoJobUnderTest, "executorService",
            MoreExecutors.newDirectExecutorService());
        ReflectionTestUtils.setField(initLotScmLotNoJobUnderTest, "stop", false);
        ReflectionTestUtils.setField(initLotScmLotNoJobUnderTest, "map", Map.ofEntries(Map.entry(0, 0L)));
        ReflectionTestUtils.setField(initLotScmLotNoJobUnderTest, "shardingTableIndexList", List.of(0));
    }

    @Test
    public void testExecute_ExceptionHandling() throws Exception {
        // Setup
        when(mockInventoryLotInfoMapper.getInventoryLotInfoByTableIndex(0L, 0L, 1000))
            .thenThrow(new RuntimeException("Test Exception"));

        // Run the test
        final ReturnT<String> result = initLotScmLotNoJobUnderTest.execute("param");

        // Verify the results
        verify(mockAlertService).alert(eq("初始化批次信息异常"), contains("Test Exception"), any());
        assertEquals(ReturnT.SUCCESS, result);
    }

    @Test
    public void testExecute_EmptyInventoryLotInfo() {
        // Setup
        when(mockInventoryLotInfoMapper.getInventoryLotInfoByTableIndex(0L, 0L, 1000))
            .thenReturn(Collections.emptyList());

        // Run the test
        final ReturnT<String> result = initLotScmLotNoJobUnderTest.execute("param");

        // Verify the results
        assertEquals(ReturnT.SUCCESS, result);
    }

    @Test
    public void testExecute_StopCondition() throws Exception {
        // Setup
        ReflectionTestUtils.setField(initLotScmLotNoJobUnderTest, "stop", true);

        // Run the test
        final ReturnT<String> result = initLotScmLotNoJobUnderTest.execute("param");

        // Verify the results
        assertEquals(ReturnT.SUCCESS, result);
    }
}