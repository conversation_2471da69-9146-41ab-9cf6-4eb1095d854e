package com.ddmc.ims.job.fms;

import static org.mockito.Mockito.verify;

import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.SnapshotTaskService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.GregorianCalendar;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class InitFmsTaskJobTest {

    @Mock
    private SnapshotTaskService mockSnapshotTaskService;

    @InjectMocks
    private InitFmsTaskJob initFmsTaskJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        // Run the test
        final ReturnT<String> result = initFmsTaskJobUnderTest.execute("2023-01-01");

        // Verify the results
    }
}
