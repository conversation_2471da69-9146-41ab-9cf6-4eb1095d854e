package com.ddmc.ims.job.snapshot;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.snapshot.SnapshotWarehouseSkuItem;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.condition.SnapshotFixCheckItem;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuLotInventoryMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourDiffMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHourDiff;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotPerHourFixCheckJobTest {

    @Mock
    private WarehouseSkuLotInventoryMapper mockWarehouseSkuLotInventoryMapper;
    @Mock
    private CredentialWrapperManager mockCredentialWrapperManager;
    @Mock
    private SkuInventorySnapshotPerHourMapper mockSkuInventorySnapshotPerHourMapper;
    @Mock
    private WarehouseSkuInventoryMapper mockWarehouseSkuInventoryMapper;
    @Mock
    private CommandManager mockCommandManager;
    @Mock
    private WarehouseService mockWarehouseService;
    @Mock
    private SkuInventorySnapshotPerHourDiffMapper mockSkuInventorySnapshotPerHourDiffMapper;
    @Mock
    private LocalParamConfig mockLocalParamConfig;

    @InjectMocks
    private SkuInventorySnapshotPerHourFixCheckJob skuInventorySnapshotPerHourFixCheckJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testUpdateOrInsert() {
        // Setup
        // Run the test
        final boolean result = skuInventorySnapshotPerHourFixCheckJobUnderTest.updateOrInsert();

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockWarehouseService.getAllWarehouseIds()).thenReturn(List.of(0L));
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testExecute_WarehouseServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseService.getAllWarehouseIds()).thenReturn(Collections.emptyList());
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testExecute_WarehouseSkuInventoryMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.execute("");

        // Verify the results
    }

    @Test
    public void testExecute_SkuInventorySnapshotPerHourMapperSelectBySnapshotDateTimeAndWarehouseIdAndSkuIdInReturnsNoItems()
        throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(Collections.emptyList());
        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testExecute_LocalParamConfigReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(Collections.emptyList());

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testExecute_WarehouseSkuLotInventoryMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(Collections.emptyList());

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testExecute_CredentialWrapperManagerReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testExecute_CommandManagerReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(Collections.emptyList());

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testHandleSingleInventoryItem() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();
        final List<SnapshotWarehouseSkuItem> expectedResult = List.of(SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build());

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotWarehouseSkuItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.handleSingleInventoryItem(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), false, item);

        // Verify the results

        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testHandleSingleInventoryItem_SkuInventorySnapshotPerHourMapperSelectBySnapshotDateTimeAndWarehouseIdAndSkuIdInReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();
        final List<SnapshotWarehouseSkuItem> expectedResult = List.of(SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build());
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(Collections.emptyList());
        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotWarehouseSkuItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.handleSingleInventoryItem(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), false, item);

        // Verify the results

        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testHandleSingleInventoryItem_LocalParamConfigReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(Collections.emptyList());

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotWarehouseSkuItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.handleSingleInventoryItem(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), false, item);

        // Verify the results

        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testHandleSingleInventoryItem_WarehouseSkuLotInventoryMapperReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(Collections.emptyList());

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotWarehouseSkuItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.handleSingleInventoryItem(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), false, item);

        // Verify the results

        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testHandleSingleInventoryItem_CredentialWrapperManagerReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotWarehouseSkuItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.handleSingleInventoryItem(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), false, item);

        // Verify the results

        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testHandleSingleInventoryItem_CommandManagerReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();
        final List<SnapshotWarehouseSkuItem> expectedResult = List.of(SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build());

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<SnapshotWarehouseSkuItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.handleSingleInventoryItem(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), false, item);

        // Verify the results

        // Confirm SkuInventorySnapshotPerHourMapper.updateByFixItem(...).
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> fixCheckItems = List.of(snapshotFixCheckItem);

        // Confirm SkuInventorySnapshotPerHourMapper.insertList(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> list = List.of(skuInventorySnapshotPerHour1);

        // Confirm SkuInventorySnapshotPerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotPerHourDiff skuInventorySnapshotPerHourDiff = new SkuInventorySnapshotPerHourDiff();
        skuInventorySnapshotPerHourDiff.setSkuId(0L);
        skuInventorySnapshotPerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotPerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotPerHourDiff.setLotId("lotId");
        skuInventorySnapshotPerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotPerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHourDiff.setTriggerCount(0);
        skuInventorySnapshotPerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHourDiff> list1 = List.of(skuInventorySnapshotPerHourDiff);
    }

    @Test
    public void testGetSnapshotFixCheckItems1() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotFixCheckItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
    }

    @Test
    public void testGetSnapshotFixCheckItems1_SkuInventorySnapshotPerHourMapperReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(Collections.emptyList());
        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotFixCheckItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
    }

    @Test
    public void testGetSnapshotFixCheckItems1_LocalParamConfigReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(Collections.emptyList());

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotFixCheckItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
    }

    @Test
    public void testGetSnapshotFixCheckItems1_WarehouseSkuLotInventoryMapperReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(Collections.emptyList());

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotFixCheckItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
    }

    @Test
    public void testGetSnapshotFixCheckItems1_CredentialWrapperManagerReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotFixCheckItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
    }

    @Test
    public void testGetSnapshotFixCheckItems1_CommandManagerReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();
        final SnapshotFixCheckItem snapshotFixCheckItem = new SnapshotFixCheckItem();
        snapshotFixCheckItem.setSkuId(0L);
        snapshotFixCheckItem.setWarehouseId(0L);
        snapshotFixCheckItem.setLotId("lotId");
        snapshotFixCheckItem.setCargoOwnerId(0L);
        snapshotFixCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixCheckItem.setPerHourId(0L);
        final List<SnapshotFixCheckItem> expectedResult = List.of(snapshotFixCheckItem);

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotPerHours);

        when(mockLocalParamConfig.allLocationSortCodes()).thenReturn(List.of("value"));

        // Configure WarehouseSkuLotInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuLotInventory.setSkuId(0L);
        warehouseSkuLotInventory.setWarehouseId(0L);
        warehouseSkuLotInventory.setCargoOwnerId(0L);
        warehouseSkuLotInventory.setLotId("lotId");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        final List<WarehouseSkuLotInventory> warehouseSkuLotInventories = List.of(warehouseSkuLotInventory);
        when(mockWarehouseSkuLotInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(warehouseSkuLotInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<SnapshotFixCheckItem> result = skuInventorySnapshotPerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
    }
}
