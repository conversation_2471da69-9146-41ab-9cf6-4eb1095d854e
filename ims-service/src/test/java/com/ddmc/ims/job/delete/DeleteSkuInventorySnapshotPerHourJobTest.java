package com.ddmc.ims.job.delete;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DeleteSkuInventorySnapshotPerHourJobTest {

    @Mock
    private SkuInventorySnapshotPerHourMapper mockSkuInventorySnapshotPerHourMapper;

    @InjectMocks
    private DeleteSkuInventorySnapshotPerHourJob deleteSkuInventorySnapshotPerHourJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() {
        // Setup
        // Configure SkuInventorySnapshotPerHourMapper.getByMinId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);


        // Run the test
        final ReturnT<String> result = deleteSkuInventorySnapshotPerHourJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_SkuInventorySnapshotPerHourMapperGetByMinIdReturnsNoItems() {
        // Setup

        // Run the test
        final ReturnT<String> result = deleteSkuInventorySnapshotPerHourJobUnderTest.execute("param");

        // Verify the results
    }
}
