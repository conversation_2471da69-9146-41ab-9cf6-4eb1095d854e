package com.ddmc.ims.job.delete;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHour;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DeleteSkuInventorySnapshotUsagePerHourJobTest {

    @Mock
    private SkuInventorySnapshotUsagePerHourMapper mockSkuInventorySnapshotUsagePerHourMapper;

    @InjectMocks
    private DeleteSkuInventorySnapshotUsagePerHourJob deleteSkuInventorySnapshotUsagePerHourJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() {
        // Setup
        // Configure SkuInventorySnapshotUsagePerHourMapper.getByMinId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);


        // Run the test
        final ReturnT<String> result = deleteSkuInventorySnapshotUsagePerHourJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_SkuInventorySnapshotUsagePerHourMapperGetByMinIdReturnsNoItems() {
        // Setup

        // Run the test
        final ReturnT<String> result = deleteSkuInventorySnapshotUsagePerHourJobUnderTest.execute("param");

        // Verify the results
    }
}
