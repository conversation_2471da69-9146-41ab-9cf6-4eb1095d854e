
package com.ddmc.ims.job;


import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.config.RateLimiterConfig;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.manager.lot.LotManager;
import com.ddmc.ims.message.lot.LotProducer;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.lot.InventoryLotInfoService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.RateLimiter;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class InitLotJobTest {

    @Mock
    private AlertService mockAlertService;
    @Mock
    private LotManager mockLotManager;
    @Mock
    private InventoryLotInfoService mockInventoryLotInfoService;
    @Mock
    private RateLimiterConfig mockRateLimiterConfig;
    @Mock
    private LotProducer mockLotProducer;

    @InjectMocks
    private InitLotJob initLotJobUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(initLotJobUnderTest, "executorService", MoreExecutors.newDirectExecutorService());
        ReflectionTestUtils.setField(initLotJobUnderTest, "map", Map.ofEntries(Map.entry(0, 0L)));
        ReflectionTestUtils.setField(initLotJobUnderTest, "shardingTableIndexList", List.of(0));
    }

    @Test
    public void testExecute_SuccessfulExecution() throws Exception {
        // Setup
        final InventoryLotInfo inventoryLotInfo = new InventoryLotInfo();
        inventoryLotInfo.setId(0L);
        inventoryLotInfo.setSkuId(0L);
        inventoryLotInfo.setWarehouseId(0L);
        inventoryLotInfo.setLotId("lotId");
        inventoryLotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final ImmutablePair<List<InventoryLotInfo>, Long> listLongImmutablePair = new ImmutablePair<>(
            List.of(inventoryLotInfo), 0L);
        when(mockLotManager.scanLot(0, 0L, true)).thenReturn(listLongImmutablePair);


        // Run the test
        final ReturnT<String> result = initLotJobUnderTest.execute("param");

        // Verify the results
        final InventoryLotInfo inventoryLotInfo1 = new InventoryLotInfo();
        inventoryLotInfo1.setId(0L);
        inventoryLotInfo1.setSkuId(0L);
        inventoryLotInfo1.setWarehouseId(0L);
        inventoryLotInfo1.setLotId("lotId");
        inventoryLotInfo1.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> inventoryLotInfoList = List.of(inventoryLotInfo1);
        verify(mockInventoryLotInfoService).replaceInventoryLotInfoList(inventoryLotInfoList);
    }

    @Test
    public void testExecute_ExceptionHandling() throws Exception {
        // Setup
        when(mockLotManager.scanLot(0, 0L, true)).thenThrow(new RuntimeException("Test exception"));

        // Run the test
        final ReturnT<String> result = initLotJobUnderTest.execute("param");

        // Verify the results
    }

}