package com.ddmc.ims.job.snapshot;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.snapshot.SnapshotWarehouseSkuItem;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.config.sharding.ShardingProperties;
import com.ddmc.ims.dal.condition.SnapshotFixUsageCheckItem;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourDiffMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHour;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHourDiff;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotUsagePerHourFixCheckJobTest {

    @Mock
    private CredentialWrapperManager mockCredentialWrapperManager;
    @Mock
    private SkuInventorySnapshotUsagePerHourMapper mockSkuInventorySnapshotUsagePerHourMapper;
    @Mock
    private WarehouseSkuInventoryMapper mockWarehouseSkuInventoryMapper;
    @Mock
    private CommandManager mockCommandManager;
    @Mock
    private WarehouseService mockWarehouseService;
    @Mock
    private SkuInventorySnapshotUsagePerHourDiffMapper mockSkuInventorySnapshotUsagePerHourDiffMapper;
    @Mock
    private ShardingProperties mockShardingProperties;
    @Mock
    private LocalParamService localParamService;
    @InjectMocks
    private SkuInventorySnapshotUsagePerHourFixCheckJob skuInventorySnapshotUsagePerHourFixCheckJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockWarehouseService.getAllWarehouseIds()).thenReturn(List.of(0L));
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Configure WarehouseSkuInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode("usage");
        final List<WarehouseSkuInventory> skuInventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(skuInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Configure SkuInventorySnapshotUsagePerHourMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour1 = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour1.setId(0L);
        skuInventorySnapshotUsagePerHour1.setSkuId(0L);
        skuInventorySnapshotUsagePerHour1.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour1.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> list = List.of(skuInventorySnapshotUsagePerHour1);
        when(mockSkuInventorySnapshotUsagePerHourMapper.insertList(list)).thenReturn(0);

        // Configure SkuInventorySnapshotUsagePerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHourDiff skuInventorySnapshotUsagePerHourDiff = new SkuInventorySnapshotUsagePerHourDiff();
        skuInventorySnapshotUsagePerHourDiff.setSkuId(0L);
        skuInventorySnapshotUsagePerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHourDiff.setUsageCode("usage");
        skuInventorySnapshotUsagePerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotUsagePerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setTriggerCount(0);
        skuInventorySnapshotUsagePerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHourDiff> list1 = List.of(skuInventorySnapshotUsagePerHourDiff);
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.insertList(list1)).thenReturn(0);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotUsagePerHourMapper.updateByFixItem(...).
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usage");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> fixCheckItems = List.of(snapshotFixUsageCheckItem);
    }

    @Test
    public void testExecute_WarehouseServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseService.getAllWarehouseIds()).thenReturn(Collections.emptyList());
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Configure WarehouseSkuInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode("usage");
        final List<WarehouseSkuInventory> skuInventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(skuInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Configure SkuInventorySnapshotUsagePerHourMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour1 = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour1.setId(0L);
        skuInventorySnapshotUsagePerHour1.setSkuId(0L);
        skuInventorySnapshotUsagePerHour1.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour1.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> list = List.of(skuInventorySnapshotUsagePerHour1);
        when(mockSkuInventorySnapshotUsagePerHourMapper.insertList(list)).thenReturn(0);

        // Configure SkuInventorySnapshotUsagePerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHourDiff skuInventorySnapshotUsagePerHourDiff = new SkuInventorySnapshotUsagePerHourDiff();
        skuInventorySnapshotUsagePerHourDiff.setSkuId(0L);
        skuInventorySnapshotUsagePerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHourDiff.setUsageCode("usage");
        skuInventorySnapshotUsagePerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotUsagePerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setTriggerCount(0);
        skuInventorySnapshotUsagePerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHourDiff> list1 = List.of(skuInventorySnapshotUsagePerHourDiff);
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.insertList(list1)).thenReturn(0);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotUsagePerHourMapper.updateByFixItem(...).
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usage");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> fixCheckItems = List.of(snapshotFixUsageCheckItem);
    }

    @Test
    public void testExecute_WarehouseSkuInventoryMapperSelectSkuIdByWarehouseIdReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.execute("");

        // Verify the results
    }

    @Test
    public void testExecute_SkuInventorySnapshotUsagePerHourMapperSelectBySnapshotDateTimeAndWarehouseIdAndSkuIdInReturnsNoItems()
        throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(Collections.emptyList());
        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Configure WarehouseSkuInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode("usage");
        final List<WarehouseSkuInventory> skuInventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(skuInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Configure SkuInventorySnapshotUsagePerHourMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> list = List.of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.insertList(list)).thenReturn(0);

        // Configure SkuInventorySnapshotUsagePerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHourDiff skuInventorySnapshotUsagePerHourDiff = new SkuInventorySnapshotUsagePerHourDiff();
        skuInventorySnapshotUsagePerHourDiff.setSkuId(0L);
        skuInventorySnapshotUsagePerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHourDiff.setUsageCode("usage");
        skuInventorySnapshotUsagePerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotUsagePerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setTriggerCount(0);
        skuInventorySnapshotUsagePerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHourDiff> list1 = List.of(skuInventorySnapshotUsagePerHourDiff);
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.insertList(list1)).thenReturn(0);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotUsagePerHourMapper.updateByFixItem(...).
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usage");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> fixCheckItems = List.of(snapshotFixUsageCheckItem);
    }

    @Test
    public void testExecute_WarehouseSkuInventoryMapperGetByWarehouseIdListReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(Collections.emptyList());

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Configure SkuInventorySnapshotUsagePerHourMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour1 = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour1.setId(0L);
        skuInventorySnapshotUsagePerHour1.setSkuId(0L);
        skuInventorySnapshotUsagePerHour1.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour1.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> list = List.of(skuInventorySnapshotUsagePerHour1);
        when(mockSkuInventorySnapshotUsagePerHourMapper.insertList(list)).thenReturn(0);

        // Configure SkuInventorySnapshotUsagePerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHourDiff skuInventorySnapshotUsagePerHourDiff = new SkuInventorySnapshotUsagePerHourDiff();
        skuInventorySnapshotUsagePerHourDiff.setSkuId(0L);
        skuInventorySnapshotUsagePerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHourDiff.setUsageCode("usage");
        skuInventorySnapshotUsagePerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotUsagePerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setTriggerCount(0);
        skuInventorySnapshotUsagePerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHourDiff> list1 = List.of(skuInventorySnapshotUsagePerHourDiff);
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.insertList(list1)).thenReturn(0);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotUsagePerHourMapper.updateByFixItem(...).
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usage");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> fixCheckItems = List.of(snapshotFixUsageCheckItem);
    }

    @Test
    public void testExecute_CredentialWrapperManagerReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Configure WarehouseSkuInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode("usage");
        final List<WarehouseSkuInventory> skuInventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(skuInventories);

        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Configure SkuInventorySnapshotUsagePerHourMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour1 = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour1.setId(0L);
        skuInventorySnapshotUsagePerHour1.setSkuId(0L);
        skuInventorySnapshotUsagePerHour1.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour1.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> list = List.of(skuInventorySnapshotUsagePerHour1);
        when(mockSkuInventorySnapshotUsagePerHourMapper.insertList(list)).thenReturn(0);

        // Configure SkuInventorySnapshotUsagePerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHourDiff skuInventorySnapshotUsagePerHourDiff = new SkuInventorySnapshotUsagePerHourDiff();
        skuInventorySnapshotUsagePerHourDiff.setSkuId(0L);
        skuInventorySnapshotUsagePerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHourDiff.setUsageCode("usage");
        skuInventorySnapshotUsagePerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotUsagePerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setTriggerCount(0);
        skuInventorySnapshotUsagePerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHourDiff> list1 = List.of(skuInventorySnapshotUsagePerHourDiff);
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.insertList(list1)).thenReturn(0);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotUsagePerHourMapper.updateByFixItem(...).
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usage");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> fixCheckItems = List.of(snapshotFixUsageCheckItem);
    }

    @Test
    public void testExecute_CommandManagerReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseSkuInventoryMapper.selectSkuIdByWarehouseId(0L)).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Configure WarehouseSkuInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode("usage");
        final List<WarehouseSkuInventory> skuInventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(skuInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(Collections.emptyList());

        // Configure SkuInventorySnapshotUsagePerHourMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour1 = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour1.setId(0L);
        skuInventorySnapshotUsagePerHour1.setSkuId(0L);
        skuInventorySnapshotUsagePerHour1.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour1.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour1.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour1.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> list = List.of(skuInventorySnapshotUsagePerHour1);
        when(mockSkuInventorySnapshotUsagePerHourMapper.insertList(list)).thenReturn(0);

        // Configure SkuInventorySnapshotUsagePerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHourDiff skuInventorySnapshotUsagePerHourDiff = new SkuInventorySnapshotUsagePerHourDiff();
        skuInventorySnapshotUsagePerHourDiff.setSkuId(0L);
        skuInventorySnapshotUsagePerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHourDiff.setUsageCode("usage");
        skuInventorySnapshotUsagePerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotUsagePerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setTriggerCount(0);
        skuInventorySnapshotUsagePerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHourDiff> list1 = List.of(skuInventorySnapshotUsagePerHourDiff);
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.insertList(list1)).thenReturn(0);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.execute("");

        // Verify the results
        // Confirm SkuInventorySnapshotUsagePerHourMapper.updateByFixItem(...).
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usage");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> fixCheckItems = List.of(snapshotFixUsageCheckItem);
    }

    @Test
    public void testGetSnapshotFixCheckItems1() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Configure WarehouseSkuInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode("usage");
        final List<WarehouseSkuInventory> skuInventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(skuInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotFixUsageCheckItem> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetSnapshotFixCheckItems1_SkuInventorySnapshotUsagePerHourMapperReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(Collections.emptyList());
        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Configure WarehouseSkuInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode("usage");
        final List<WarehouseSkuInventory> skuInventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(skuInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotFixUsageCheckItem> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetSnapshotFixCheckItems1_WarehouseSkuInventoryMapperReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(Collections.emptyList());

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotFixUsageCheckItem> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetSnapshotFixCheckItems1_CredentialWrapperManagerReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Configure WarehouseSkuInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode("usage");
        final List<WarehouseSkuInventory> skuInventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(skuInventories);

        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        final LogicInventoryLocation location = new LogicInventoryLocation();
        location.setWarehouseId(0L);
        location.setCargoOwnerId(0L);
        location.setLogicInventoryLocationCode("logicInventoryLocationCode");
        commandInventoryNumDto.setLocation(location);
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        final List<CommandInventoryNumDto> commandInventoryNumDtos = List.of(commandInventoryNumDto);
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(commandInventoryNumDtos);

        // Run the test
        final List<SnapshotFixUsageCheckItem> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
    }

    @Test
    public void testGetSnapshotFixCheckItems1_CommandManagerReturnsNoItems() {
        // Setup
        final SnapshotWarehouseSkuItem item = SnapshotWarehouseSkuItem.builder()
            .warehouseId(0L)
            .skuIds(List.of(0L))
            .build();
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usage");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setFixQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> expectedResult = List.of(snapshotFixUsageCheckItem);

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setSnapshotDateTime(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List.of(
            skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseIdAndSkuIdIn(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, List.of(0L)))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Configure WarehouseSkuInventoryMapper.getByWarehouseIdList(...).
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode("logicInventoryLocationCode");
        warehouseSkuInventory.setSkuId(0L);
        warehouseSkuInventory.setWarehouseId(0L);
        warehouseSkuInventory.setCargoOwnerId(0L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode("usage");
        final List<WarehouseSkuInventory> skuInventories = List.of(warehouseSkuInventory);
        when(mockWarehouseSkuInventoryMapper.getByWarehouseIdList(0L, List.of("value"), List.of(0L)))
            .thenReturn(skuInventories);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager.getCredentialHeaders(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CredentialHeader credentialHeader1 = new CredentialHeader();
        credentialHeader1.setId(0L);
        credentialHeader1.setIdempotentId("idempotentId");
        credentialHeader1.setWarehouseId(0L);
        credentialHeader1.setStatus(CredentialStatus.INIT);
        credentialHeader1.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaderAndDetails = List.of(credentialHeader1);
        when(mockCommandManager.getCommandInventoryNumList(credentialHeaderAndDetails))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<SnapshotFixUsageCheckItem> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), item);

        // Verify the results
    }

    @Test
    public void testAllLocationSortCodes() {
        // Setup
        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap())
            .thenReturn(Map.ofEntries(Map.entry("value", 0)));

        // Run the test
        final List<String> result = skuInventorySnapshotUsagePerHourFixCheckJobUnderTest.allLocationSortCodes();

        // Verify the results
    }
}
