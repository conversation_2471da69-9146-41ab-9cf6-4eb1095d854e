package com.ddmc.ims.job.snapshot;

import static org.mockito.Mockito.when;

import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dto.WarehouseIdAndSnapshotDateDto;
import com.ddmc.ims.service.snapshot.SkuTransferInventorySnapshotPerHourService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuTransferInventorySnapshotPerHourJobTest {

    @Mock
    private WarehouseService mockWarehouseService;
    @Mock
    private LocalParamConfig mockLocalParamConfig;
    @Mock
    private SkuTransferInventorySnapshotPerHourService mockSkuTransferInventorySnapshotPerHourService;

    @InjectMocks
    private SkuTransferInventorySnapshotPerHourJob skuTransferInventorySnapshotPerHourJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockWarehouseService.getAllWarehouseIds()).thenReturn(List.of(0L));
        when(mockLocalParamConfig.excludeWarehouseForSnapshot(0L)).thenReturn(false);

        WarehouseIdAndSnapshotDateDto dto =  new WarehouseIdAndSnapshotDateDto();
        dto.setWarehouseIds(Lists.newArrayList());
        dto.setSnapshotDate(new Date());


        // Run the test
        final ReturnT<String> result = skuTransferInventorySnapshotPerHourJobUnderTest.execute(JsonUtil.toJson(dto));
    }

    @Test
    public void testExecute_WarehouseServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseService.getAllWarehouseIds()).thenReturn(Collections.emptyList());
        when(mockLocalParamConfig.excludeWarehouseForSnapshot(0L)).thenReturn(false);

        WarehouseIdAndSnapshotDateDto dto =  new WarehouseIdAndSnapshotDateDto();
        dto.setWarehouseIds(Lists.newArrayList());
        dto.setSnapshotDate(new Date());


        // Run the test
        final ReturnT<String> result = skuTransferInventorySnapshotPerHourJobUnderTest.execute(JsonUtil.toJson(dto));
    }

    @Test
    public void testExecute_LocalParamConfigReturnsTrue() throws Exception {
        // Setup
        when(mockWarehouseService.getAllWarehouseIds()).thenReturn(List.of(0L));
        when(mockLocalParamConfig.excludeWarehouseForSnapshot(0L)).thenReturn(true);
        WarehouseIdAndSnapshotDateDto dto =  new WarehouseIdAndSnapshotDateDto();
        dto.setWarehouseIds(Lists.newArrayList());
        dto.setSnapshotDate(new Date());


        // Run the test
        final ReturnT<String> result = skuTransferInventorySnapshotPerHourJobUnderTest.execute(JsonUtil.toJson(dto));

        // Verify the results
    }
}
