package com.ddmc.ims.job.event;

import static org.mockito.Mockito.when;

import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.event.EventService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Collections;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EventManualExecutorJobTest {

    @Mock
    private EventService mockEventService;
    @Mock
    private DbEventStorage mockDbEventStorage;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private EventManualExecutorJob eventManualExecutorJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure EventService.listByCondition(...).
        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(0L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("keyType");
        eventEntity.setKeyId("keyId");
        eventEntity.setType("type");
        final List<EventEntity> eventEntities = List.of(eventEntity);
        final ListEventCondition condition = new ListEventCondition();
        condition.setEventId(0L);
        condition.setEventIdList(List.of(0L));
        condition.setParentEventId(0L);
        condition.setStatus(0);
        condition.setStatusList(List.of(0));

        // Run the test
        final ReturnT<String> result = eventManualExecutorJobUnderTest.execute("[1]");

        // Verify the results

        // Confirm DbEventStorage.executeEvent(...).
        final EventEntity eventEntity1 = new EventEntity();
        eventEntity1.setId(0L);
        eventEntity1.setParentId(0L);
        eventEntity1.setKeyType("keyType");
        eventEntity1.setKeyId("keyId");
        eventEntity1.setType("type");
        final List<EventEntity> eventEntityList = List.of(eventEntity1);
    }

    @Test
    public void testExecute_EventServiceReturnsNoItems() {
        // Setup
        // Configure EventService.listByCondition(...).
        final ListEventCondition condition = new ListEventCondition();
        condition.setEventId(0L);
        condition.setEventIdList(List.of(0L));
        condition.setParentEventId(0L);
        condition.setStatus(0);
        condition.setStatusList(List.of(0));

        // Run the test
        final ReturnT<String> result = eventManualExecutorJobUnderTest.execute("[1]");

        // Verify the results
    }
}
