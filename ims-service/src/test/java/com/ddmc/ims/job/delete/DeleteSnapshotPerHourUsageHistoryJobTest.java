package com.ddmc.ims.job.delete;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.GregorianCalendar;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DeleteSnapshotPerHourUsageHistoryJobTest {

    @Mock
    private SkuInventorySnapshotUsagePerHourMapper mockSkuInventorySnapshotUsagePerHourMapper;

    @InjectMocks
    private DeleteSnapshotPerHourUsageHistoryJob deleteSnapshotPerHourUsageHistoryJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockSkuInventorySnapshotUsagePerHourMapper.deleteByMaxId(0L)).thenReturn(0L);

        // Run the test
        final ReturnT<String> result = deleteSnapshotPerHourUsageHistoryJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testGetMaxId() {
        // Setup

        // Run the test
        final Long result = deleteSnapshotPerHourUsageHistoryJobUnderTest.getMaxId();

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }

    @Test
    public void testGetInterrupt() {
        // Setup
        // Run the test
        final boolean result = deleteSnapshotPerHourUsageHistoryJobUnderTest.getInterrupt();

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetSleep() {
        // Setup
        // Run the test
        final Long result = deleteSnapshotPerHourUsageHistoryJobUnderTest.getSleep();

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }

    @Test
    public void testDeleteByMaxId() {
        // Setup
        when(mockSkuInventorySnapshotUsagePerHourMapper.deleteByMaxId(0L)).thenReturn(0L);

        // Run the test
        final Long result = deleteSnapshotPerHourUsageHistoryJobUnderTest.deleteByMaxId(0L);

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }
}
