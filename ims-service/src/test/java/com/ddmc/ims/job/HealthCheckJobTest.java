package com.ddmc.ims.job;

import static org.mockito.Mockito.when;

import com.ddmc.ims.config.SpringProfileConfig;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

@RunWith(MockitoJUnitRunner.class)
public class HealthCheckJobTest {

    @Mock
    private RedisTemplate<String, Long> mockRedisTemplate;
    @Mock
    private SpringProfileConfig mockProfileConfig;

    @InjectMocks
    private HealthCheckJob healthCheckJobUnderTest;

    @Test
    public void testPostConstruct() {
        // Setup
        when(mockProfileConfig.isPe()).thenReturn(false);

        // Run the test
        healthCheckJobUnderTest.postConstruct();

        // Verify the results
    }

    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final ReturnT<String> result = healthCheckJobUnderTest.execute("param");

        // Verify the results
    }
}
