package com.ddmc.ims.job.snapshot;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotUsagePerHourClearByIdJobTest {

    @Mock
    private SkuInventorySnapshotUsagePerHourMapper mockSkuInventorySnapshotUsagePerHourMapper;

    @InjectMocks
    private SkuInventorySnapshotUsagePerHourClearByIdJob skuInventorySnapshotUsagePerHourClearByIdJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockSkuInventorySnapshotUsagePerHourMapper.deleteByMinId(0L)).thenReturn(0L);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourClearByIdJobUnderTest.execute("param");

        // Verify the results
    }
}
