package com.ddmc.ims.job.delete;

import static org.mockito.Mockito.when;

import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSkuInventorySnapshotPerDayMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dto.WarehouseIdAndSnapshotDateDto;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DeleteFmsSnapshotTaskJobTest {

    @Mock
    private SnapshotTaskMapper mockSnapshotTaskMapper;
    @Mock
    private FmsSkuInventorySnapshotPerDayMapper mockFmsSkuInventorySnapshotPerDayMapper;
    @Mock
    private FmsSnapshotNotifyMapper mockFmsSnapshotNotifyMapper;
    @Mock
    private WarehouseMapper mockWarehouseMapper;

    @InjectMocks
    private DeleteFmsSnapshotTaskJob deleteFmsSnapshotTaskJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(List.of(0L));
        WarehouseIdAndSnapshotDateDto dto = new WarehouseIdAndSnapshotDateDto();
        dto.setWarehouseIds(Lists.newArrayList());
        dto.setSnapshotDate(new Date());

        // Run the test
        final ReturnT<String> result = deleteFmsSnapshotTaskJobUnderTest.execute(JsonUtil.toJson(dto));

    }

    @Test
    public void testExecute_WarehouseMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(Collections.emptyList());
        WarehouseIdAndSnapshotDateDto dto = new WarehouseIdAndSnapshotDateDto();
        dto.setWarehouseIds(Lists.newArrayList());
        dto.setSnapshotDate(new Date());

        // Run the test
        final ReturnT<String> result = deleteFmsSnapshotTaskJobUnderTest.execute(JsonUtil.toJson(dto));

        // Verify the results
    }
}
