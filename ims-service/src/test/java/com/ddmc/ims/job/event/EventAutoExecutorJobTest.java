package com.ddmc.ims.job.event;

import static org.mockito.Mockito.when;

import com.ddmc.ims.config.eventbus.DbEventStorage;
import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.event.EventService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class EventAutoExecutorJobTest {

    @Mock
    private EventService mockEventService;
    @Mock
    private DbEventStorage mockDbEventStorage;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private EventAutoExecutorJob eventAutoExecutorJobUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(eventAutoExecutorJobUnderTest, "autoMaxCount", 0);
        ReflectionTestUtils.setField(eventAutoExecutorJobUnderTest, "eventAutoExecutorEndTime", 0);
        ReflectionTestUtils.setField(eventAutoExecutorJobUnderTest, "eventAutoExecutorJobLimit", 0);
    }

    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure EventService.listByCondition(...).
        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(0L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("keyType");
        eventEntity.setKeyId("keyId");
        eventEntity.setType("type");
        final List<EventEntity> eventEntities = List.of(eventEntity);
        final ListEventCondition condition = new ListEventCondition();
        condition.setEventIdList(List.of(0L));
        condition.setStatusList(List.of(0));
        condition.setCreateTimeEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        condition.setTriggerCountEnd(0);
        condition.setLimit(0);

        // Run the test
        final ReturnT<String> result = eventAutoExecutorJobUnderTest.execute("param");


    }

    @Test
    public void testExecute_EventServiceReturnsNoItems() {
        // Setup
        // Configure EventService.listByCondition(...).
        final ListEventCondition condition = new ListEventCondition();
        condition.setEventIdList(List.of(0L));
        condition.setStatusList(List.of(0));
        condition.setCreateTimeEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        condition.setTriggerCountEnd(0);
        condition.setLimit(0);

        // Run the test
        final ReturnT<String> result = eventAutoExecutorJobUnderTest.execute("param");

        // Verify the results
    }
}
