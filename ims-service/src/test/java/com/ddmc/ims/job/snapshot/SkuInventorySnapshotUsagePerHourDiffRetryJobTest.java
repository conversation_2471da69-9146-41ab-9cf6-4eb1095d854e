package com.ddmc.ims.job.snapshot;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.snapshot.SnapshotWarehouseSkuItem;
import com.ddmc.ims.dal.condition.SnapshotFixUsageCheckItem;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourDiffMapper;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHourDiff;
import com.xxl.job.core.biz.model.ReturnT;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotUsagePerHourDiffRetryJobTest {

    @Mock
    private SkuInventorySnapshotUsagePerHourDiffMapper mockSkuInventorySnapshotUsagePerHourDiffMapper;
    @Mock
    private SkuInventorySnapshotUsagePerHourFixCheckJob mockSkuInventorySnapshotUsagePerHourFixCheckJob;
    @Mock
    private SkuInventorySnapshotUsagePerHourDiffRetryJob mockSelfProxy;

    @InjectMocks
    private SkuInventorySnapshotUsagePerHourDiffRetryJob skuInventorySnapshotUsagePerHourDiffRetryJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.selectDistinctWarehouseId()).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotUsagePerHourDiffMapper.selectByWarehouseId(...).
        final SkuInventorySnapshotUsagePerHourDiff skuInventorySnapshotUsagePerHourDiff = new SkuInventorySnapshotUsagePerHourDiff();
        skuInventorySnapshotUsagePerHourDiff.setSkuId(0L);
        skuInventorySnapshotUsagePerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHourDiff.setUsageCode("usageCode");
        skuInventorySnapshotUsagePerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotUsagePerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setTriggerCount(0);
        skuInventorySnapshotUsagePerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHourDiff> skuInventorySnapshotUsagePerHourDiffs = List.of(
            skuInventorySnapshotUsagePerHourDiff);
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.selectByWarehouseId(0L))
            .thenReturn(skuInventorySnapshotUsagePerHourDiffs);

        // Configure SkuInventorySnapshotUsagePerHourFixCheckJob.getSnapshotFixCheckItems(...).
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usageCode");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> snapshotFixUsageCheckItems = List.of(snapshotFixUsageCheckItem);
        when(mockSkuInventorySnapshotUsagePerHourFixCheckJob.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), SnapshotWarehouseSkuItem.builder()
                .warehouseId(0L)
                .skuIds(List.of(0L))
                .build())).thenReturn(snapshotFixUsageCheckItems);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourDiffRetryJobUnderTest.execute("param");

        // Verify the results
        // Confirm SkuInventorySnapshotUsagePerHourDiffRetryJob.saveDiff(...).
        final SkuInventorySnapshotUsagePerHourDiff diff = new SkuInventorySnapshotUsagePerHourDiff();
        diff.setSkuId(0L);
        diff.setWarehouseId(0L);
        diff.setLogicLocationCode("logicInventoryLocationCode");
        diff.setCargoOwnerId(0L);
        diff.setUsageCode("usageCode");
        diff.setSnapshotId(0L);
        diff.setSnapshotQty(new BigDecimal("0.00"));
        diff.setCredentialQty(new BigDecimal("0.00"));
        diff.setInventoryQty(new BigDecimal("0.00"));
        diff.setTriggerCount(0);
        diff.setCredentialStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        diff.setCredentialEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem1 = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem1.setSkuId(0L);
        snapshotFixUsageCheckItem1.setWarehouseId(0L);
        snapshotFixUsageCheckItem1.setUsageCode("usageCode");
        snapshotFixUsageCheckItem1.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem1.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem1.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem1.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem1.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem1.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> currentDiffs = List.of(snapshotFixUsageCheckItem1);
        verify(mockSelfProxy).saveDiff(diff, currentDiffs);
    }

    @Test
    public void testExecute_SkuInventorySnapshotUsagePerHourDiffMapperSelectDistinctWarehouseIdReturnsNoItems()
        throws Exception {
        // Setup
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.selectDistinctWarehouseId())
            .thenReturn(Collections.emptyList());

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourDiffRetryJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_SkuInventorySnapshotUsagePerHourDiffMapperSelectByWarehouseIdReturnsNoItems()
        throws Exception {
        // Setup
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.selectDistinctWarehouseId()).thenReturn(List.of(0L));
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.selectByWarehouseId(0L))
            .thenReturn(Collections.emptyList());

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourDiffRetryJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_SkuInventorySnapshotUsagePerHourFixCheckJobReturnsNoItems() throws Exception {
        // Setup
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.selectDistinctWarehouseId()).thenReturn(List.of(0L));

        // Configure SkuInventorySnapshotUsagePerHourDiffMapper.selectByWarehouseId(...).
        final SkuInventorySnapshotUsagePerHourDiff skuInventorySnapshotUsagePerHourDiff = new SkuInventorySnapshotUsagePerHourDiff();
        skuInventorySnapshotUsagePerHourDiff.setSkuId(0L);
        skuInventorySnapshotUsagePerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHourDiff.setUsageCode("usageCode");
        skuInventorySnapshotUsagePerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotUsagePerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setTriggerCount(0);
        skuInventorySnapshotUsagePerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHourDiff> skuInventorySnapshotUsagePerHourDiffs = List.of(
            skuInventorySnapshotUsagePerHourDiff);
        when(mockSkuInventorySnapshotUsagePerHourDiffMapper.selectByWarehouseId(0L))
            .thenReturn(skuInventorySnapshotUsagePerHourDiffs);

        when(mockSkuInventorySnapshotUsagePerHourFixCheckJob.getSnapshotFixCheckItems(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), SnapshotWarehouseSkuItem.builder()
                .warehouseId(0L)
                .skuIds(List.of(0L))
                .build())).thenReturn(Collections.emptyList());

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotUsagePerHourDiffRetryJobUnderTest.execute("param");

        // Verify the results
        // Confirm SkuInventorySnapshotUsagePerHourDiffRetryJob.saveDiff(...).
        final SkuInventorySnapshotUsagePerHourDiff diff = new SkuInventorySnapshotUsagePerHourDiff();
        diff.setSkuId(0L);
        diff.setWarehouseId(0L);
        diff.setLogicLocationCode("logicInventoryLocationCode");
        diff.setCargoOwnerId(0L);
        diff.setUsageCode("usageCode");
        diff.setSnapshotId(0L);
        diff.setSnapshotQty(new BigDecimal("0.00"));
        diff.setCredentialQty(new BigDecimal("0.00"));
        diff.setInventoryQty(new BigDecimal("0.00"));
        diff.setTriggerCount(0);
        diff.setCredentialStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        diff.setCredentialEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usageCode");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> currentDiffs = List.of(snapshotFixUsageCheckItem);
    }

    @Test
    public void testSaveDiff() {
        // Setup
        final SkuInventorySnapshotUsagePerHourDiff diff = new SkuInventorySnapshotUsagePerHourDiff();
        diff.setSkuId(0L);
        diff.setWarehouseId(0L);
        diff.setLogicLocationCode("logicInventoryLocationCode");
        diff.setCargoOwnerId(0L);
        diff.setUsageCode("usageCode");
        diff.setSnapshotId(0L);
        diff.setSnapshotQty(new BigDecimal("0.00"));
        diff.setCredentialQty(new BigDecimal("0.00"));
        diff.setInventoryQty(new BigDecimal("0.00"));
        diff.setTriggerCount(0);
        diff.setCredentialStartDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        diff.setCredentialEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final SnapshotFixUsageCheckItem snapshotFixUsageCheckItem = new SnapshotFixUsageCheckItem();
        snapshotFixUsageCheckItem.setSkuId(0L);
        snapshotFixUsageCheckItem.setWarehouseId(0L);
        snapshotFixUsageCheckItem.setUsageCode("usageCode");
        snapshotFixUsageCheckItem.setCargoOwnerId(0L);
        snapshotFixUsageCheckItem.setLogicInventoryLocationCode("logicInventoryLocationCode");
        snapshotFixUsageCheckItem.setInventoryQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setSnapShotQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setCredentialQty(new BigDecimal("0.00"));
        snapshotFixUsageCheckItem.setPerHourId(0L);
        final List<SnapshotFixUsageCheckItem> currentDiffs = List.of(snapshotFixUsageCheckItem);

        // Run the test
        skuInventorySnapshotUsagePerHourDiffRetryJobUnderTest.saveDiff(diff, currentDiffs);

        // Verify the results
        verify(mockSkuInventorySnapshotUsagePerHourDiffMapper).deleteByWarehouseId(0L);

        // Confirm SkuInventorySnapshotUsagePerHourDiffMapper.insertList(...).
        final SkuInventorySnapshotUsagePerHourDiff skuInventorySnapshotUsagePerHourDiff = new SkuInventorySnapshotUsagePerHourDiff();
        skuInventorySnapshotUsagePerHourDiff.setSkuId(0L);
        skuInventorySnapshotUsagePerHourDiff.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHourDiff.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHourDiff.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHourDiff.setUsageCode("usageCode");
        skuInventorySnapshotUsagePerHourDiff.setSnapshotId(0L);
        skuInventorySnapshotUsagePerHourDiff.setSnapshotQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setCredentialQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setInventoryQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHourDiff.setTriggerCount(0);
        skuInventorySnapshotUsagePerHourDiff.setCredentialStartDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHourDiff.setCredentialEndDate(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHourDiff> list = List.of(skuInventorySnapshotUsagePerHourDiff);
    }
}
