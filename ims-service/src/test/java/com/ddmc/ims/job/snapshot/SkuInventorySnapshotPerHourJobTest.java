package com.ddmc.ims.job.snapshot;

import static org.mockito.Mockito.when;

import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.SkuInventorySnapshotPerHourService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotPerHourJobTest {

    @Mock
    private WarehouseService mockWarehouseService;
    @Mock
    private LocalParamConfig mockLocalParamConfig;
    @Mock
    private SkuInventorySnapshotPerHourService mockSkuInventorySnapshotPerHourService;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private SkuInventorySnapshotPerHourJob skuInventorySnapshotPerHourJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure LocalParamConfig.getSnapShotPerHourDate(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockLocalParamConfig.getSnapShotPerHourDate()).thenReturn(date);

        when(mockLocalParamConfig.isOnlyClearSnapshotInfo()).thenReturn(false);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourJobUnderTest.execute("1");


    }

    @Test
    public void testExecute_LocalParamConfigGetSnapShotPerHourDateReturnsNull() throws Exception {
        // Setup
        when(mockLocalParamConfig.getSnapShotPerHourDate()).thenReturn(null);
        when(mockLocalParamConfig.isOnlyClearSnapshotInfo()).thenReturn(false);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourJobUnderTest.execute("1");


    }

    @Test
    public void testExecute_LocalParamConfigIsOnlyClearSnapshotInfoReturnsTrue() throws Exception {
        // Setup
        // Configure LocalParamConfig.getSnapShotPerHourDate(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockLocalParamConfig.getSnapShotPerHourDate()).thenReturn(date);

        when(mockLocalParamConfig.isOnlyClearSnapshotInfo()).thenReturn(true);

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourJobUnderTest.execute("1");

        // Verify the results
    }

    @Test
    public void testExecute_WarehouseServiceReturnsNoItems() throws Exception {
        // Setup

        // Configure LocalParamConfig.getSnapShotPerHourDate(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockLocalParamConfig.getSnapShotPerHourDate()).thenReturn(date);


        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourJobUnderTest.execute("1");

        // Verify the results
    }

    @Test
    public void testExecute_LocalParamConfigExcludeWarehouseForSnapshotReturnsTrue() throws Exception {
        // Setup

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourJobUnderTest.execute("1");

        // Verify the results
    }
}
