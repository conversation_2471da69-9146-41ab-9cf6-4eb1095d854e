package com.ddmc.ims.job.delete.snapshot;

import static org.mockito.Mockito.when;

import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.SkuInventorySnapshotPerHourService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Collections;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotPerHourClearJobTest {

    @Mock
    private WarehouseService mockWarehouseService;
    @Mock
    private SkuInventorySnapshotPerHourService mockSkuInventorySnapshotPerHourService;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private SkuInventorySnapshotPerHourClearJob skuInventorySnapshotPerHourClearJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourClearJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_WarehouseServiceReturnsNoItems() throws Exception {
        // Setup

        // Run the test
        final ReturnT<String> result = skuInventorySnapshotPerHourClearJobUnderTest.execute("param");

        // Verify the results
    }
}
