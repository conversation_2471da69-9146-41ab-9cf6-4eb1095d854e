package com.ddmc.ims.job.delete;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.GregorianCalendar;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DeleteSnapshotPerHourHistoryJobTest {

    @Mock
    private SkuInventorySnapshotPerHourMapper mockSkuInventorySnapshotPerHourMapper;

    @InjectMocks
    private DeleteSnapshotPerHourHistoryJob deleteSnapshotPerHourHistoryJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockSkuInventorySnapshotPerHourMapper.deleteByMaxId(0L)).thenReturn(0L);

        // Run the test
        final ReturnT<String> result = deleteSnapshotPerHourHistoryJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testGetMaxId() {
        // Setup

        // Run the test
        final Long result = deleteSnapshotPerHourHistoryJobUnderTest.getMaxId();

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }

    @Test
    public void testGetInterrupt() {
        // Setup
        // Run the test
        final boolean result = deleteSnapshotPerHourHistoryJobUnderTest.getInterrupt();

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetSleep() {
        // Setup
        // Run the test
        final Long result = deleteSnapshotPerHourHistoryJobUnderTest.getSleep();

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }

    @Test
    public void testDeleteByMaxId() {
        // Setup
        when(mockSkuInventorySnapshotPerHourMapper.deleteByMaxId(0L)).thenReturn(0L);

        // Run the test
        final Long result = deleteSnapshotPerHourHistoryJobUnderTest.deleteByMaxId(0L);

        // Verify the results
        assertThat(result).isEqualTo(0L);
    }
}
