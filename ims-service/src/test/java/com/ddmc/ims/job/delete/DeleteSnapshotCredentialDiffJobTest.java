package com.ddmc.ims.job.delete;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.ims.CredentialDetailMapper;
import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.GregorianCalendar;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DeleteSnapshotCredentialDiffJobTest {

    @Mock
    private SnapshotCredentialDiffMapper mockSnapshotCredentialDiffMapper;
    @Mock
    private CredentialDetailMapper mockCredentialDetailMapper;

    @InjectMocks
    private DeleteSnapshotCredentialDiffJob deleteSnapshotCredentialDiffJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockSnapshotCredentialDiffMapper.deleteByMaxIdLimit500(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Run the test
        final ReturnT<String> result = deleteSnapshotCredentialDiffJobUnderTest.execute("param");

        // Verify the results
    }
}
