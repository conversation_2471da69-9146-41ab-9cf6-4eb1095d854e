package com.ddmc.ims.job.delete;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.monitor.SkuTransferInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.model.monitor.SkuTransferInventorySnapshotPerHour;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DeleteSkuTransferInventorySnapshotPerHourJobTest {

    @Mock
    private SkuTransferInventorySnapshotPerHourMapper mockSkuTransferInventorySnapshotPerHourMapper;

    @InjectMocks
    private DeleteSkuTransferInventorySnapshotPerHourJob deleteSkuTransferInventorySnapshotPerHourJobUnderTest;
    @Mock
    private AlertService alertService;

    @Mock
    protected LocalParamService localParamService;
    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure SkuTransferInventorySnapshotPerHourMapper.getByMinId(...).
        final SkuTransferInventorySnapshotPerHour perHour = new SkuTransferInventorySnapshotPerHour();
        perHour.setId(0L);
        perHour.setSkuId(0L);
        perHour.setFromWarehouseId(0L);
        perHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        perHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuTransferInventorySnapshotPerHour> skuTransferInventorySnapshotPerHours = List.of(perHour);


        // Run the test
        final ReturnT<String> result = deleteSkuTransferInventorySnapshotPerHourJobUnderTest.execute("param");

        // Verify the results
    }

    @Test
    public void testExecute_SkuTransferInventorySnapshotPerHourMapperGetByMinIdReturnsNoItems() {
        // Setup

        // Run the test
        final ReturnT<String> result = deleteSkuTransferInventorySnapshotPerHourJobUnderTest.execute("param");

        // Verify the results
    }
}
