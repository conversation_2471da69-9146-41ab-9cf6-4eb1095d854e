package com.ddmc.ims.job;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.manager.ocs.InventoryCredentialManager;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.credential.InventoryCredentialService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CredentialStatusCheckJobTest {

    @Mock
    private AlertService mockAlertService;
    @Mock
    private WarehouseMapper mockWarehouseMapper;
    @Mock
    private CredentialHeaderMapper mockCredentialHeaderMapper;
    @Mock
    private InventoryCredentialManager mockInventoryCredentialManager;
    @Mock
    private InventoryCredentialService mockInventoryCredentialService;

    @InjectMocks
    private CredentialStatusCheckJob credentialStatusCheckJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure CredentialHeaderMapper.getInitCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialHeaderMapper.getInitCredentialHeaders(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0)).thenReturn(credentialHeaders);

        when(mockInventoryCredentialManager.checkInventoryCredentialExists("idempotentId")).thenReturn(false);

        // Run the test
        final ReturnT<String> result = credentialStatusCheckJobUnderTest.execute("param");

        // Verify the results
        verify(mockInventoryCredentialService).cancel("idempotentId");
        verify(mockInventoryCredentialService).confirm("idempotentId");
    }

    @Test
    public void testExecute_CredentialHeaderMapperReturnsNoItems() {
        // Setup
        when(mockCredentialHeaderMapper.getInitCredentialHeaders(
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0)).thenReturn(Collections.emptyList());
        when(mockInventoryCredentialManager.checkInventoryCredentialExists("idempotentId")).thenReturn(false);

        // Run the test
        final ReturnT<String> result = credentialStatusCheckJobUnderTest.execute("param");

        // Verify the results
        verify(mockInventoryCredentialService).cancel("idempotentId");
        verify(mockInventoryCredentialService).confirm("idempotentId");
    }
}
