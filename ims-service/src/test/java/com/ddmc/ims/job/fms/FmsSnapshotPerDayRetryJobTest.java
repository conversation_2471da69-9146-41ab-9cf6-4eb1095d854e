package com.ddmc.ims.job.fms;

import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSkuInventorySnapshotPerDayMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.FmsSnapshotNotifyService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FmsSnapshotPerDayRetryJobTest {

    @Mock
    private SnapshotTaskMapper mockSnapshotTaskMapper;
    @Mock
    private FmsSnapshotNotifyMapper mockFmsSnapshotNotifyMapper;
    @Mock
    private AlertService mockAlertService;
    @Mock
    private WarehouseMapper mockWarehouseMapper;
    @Mock
    private FmsSnapshotNotifyService mockFmsSnapshotNotifyService;
    @Mock
    private FmsSkuInventorySnapshotPerDayMapper mockFmsSkuInventorySnapshotPerDayMapper;
    @Mock
    private FmsSnapshotPerDayJob mockFmsSnapshotPerDayJob;

    @InjectMocks
    private FmsSnapshotPerDayRetryJob fmsSnapshotPerDayRetryJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("value");
        fmsSnapshotNotify.setSource("desc");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final ReturnT<String> result = fmsSnapshotPerDayRetryJobUnderTest.execute("param");

        // Verify the results
        // Confirm FmsSnapshotNotifyMapper.insert(...).
        final FmsSnapshotNotify entity = new FmsSnapshotNotify();
        entity.setId(0L);
        entity.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        entity.setStatus("value");
        entity.setSource("desc");
        entity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Confirm SnapshotTaskMapper.batchInsert(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        final List<SnapshotTask> taskList = List.of(snapshotTask);

        // Confirm FmsSnapshotPerDayJob.processSnapshotTasks(...).
        final SnapshotTask snapshotTask1 = new SnapshotTask();
        snapshotTask1.setId(0L);
        snapshotTask1.setWarehouseId(0L);
        snapshotTask1.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask1.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask1.setStatus(SnapshotStatusEnum.INIT);
        final List<SnapshotTask> snapshotTasks = List.of(snapshotTask1);
    }
}
