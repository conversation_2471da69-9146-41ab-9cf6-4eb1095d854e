package com.ddmc.ims.job.fms;

import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.WarehouseIdAndSnapshotDateDto;
import com.ddmc.ims.event.producer.FmsCompareProducer;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.snapshot.FmsSkuInventorySnapshotPerDayService;
import com.ddmc.ims.service.snapshot.FmsSnapshotNotifyService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FmsSnapshotPerDayJobTest {

    @Mock
    private SnapshotTaskMapper mockSnapshotTaskMapper;
    @Mock
    private FmsSkuInventorySnapshotPerDayService mockFmsSkuInventorySnapshotPerDayService;
    @Mock
    private AlertService mockAlertService;
    @Mock
    private WarehouseMapper mockWarehouseMapper;
    @Mock
    private FmsCompareProducer mockFmsCompareProducer;
    @Mock
    private FmsSnapshotNotifyMapper mockFmsSnapshotNotifyMapper;
    @Mock
    private FmsSnapshotNotifyService mockFmsSnapshotNotifyService;
    @Mock
    private LocalParamConfig mockLocalParamConfig;

    @InjectMocks
    private FmsSnapshotPerDayJob fmsSnapshotPerDayJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(List.of(0L));
        when(mockLocalParamConfig.excludeWarehouseForSnapshot(0L)).thenReturn(false);

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SnapshotTaskMapper.selectBySnapshotTypeAndSnapshotTimeAndStatus(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        final List<SnapshotTask> snapshotTasks = List.of(snapshotTask);

        // Configure SnapshotTaskMapper.selectOneBySnapshotTypeAndSnapshotTimeAndStatus(...).
        final SnapshotTask snapshotTask1 = new SnapshotTask();
        snapshotTask1.setId(0L);
        snapshotTask1.setWarehouseId(0L);
        snapshotTask1.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask1.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask1.setStatus(SnapshotStatusEnum.INIT);

        WarehouseIdAndSnapshotDateDto dto = new WarehouseIdAndSnapshotDateDto();
        dto.setWarehouseIds(Lists.newArrayList());
        dto.setSnapshotDate(new Date());

        // Run the test
        final ReturnT<String> result = fmsSnapshotPerDayJobUnderTest.execute("");

        // Verify the results
    }

    @Test
    public void testExecute_WarehouseMapperReturnsNoItems() {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(Collections.emptyList());

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        WarehouseIdAndSnapshotDateDto dto = new WarehouseIdAndSnapshotDateDto();
        dto.setWarehouseIds(Lists.newArrayList());
        dto.setSnapshotDate(new Date());

        // Run the test
        final ReturnT<String> result = fmsSnapshotPerDayJobUnderTest.execute(JsonUtil.toJson(dto));

        // Verify the results
    }

    @Test
    public void testExecute_LocalParamConfigReturnsTrue() {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(List.of(0L));
        when(mockLocalParamConfig.excludeWarehouseForSnapshot(0L)).thenReturn(true);

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        WarehouseIdAndSnapshotDateDto dto = new WarehouseIdAndSnapshotDateDto();
        dto.setWarehouseIds(Lists.newArrayList());
        dto.setSnapshotDate(new Date());

        // Run the test
        final ReturnT<String> result = fmsSnapshotPerDayJobUnderTest.execute(JsonUtil.toJson(dto));

        // Verify the results
    }

    @Test
    public void testExecute_SnapshotTaskMapperSelectBySnapshotTypeAndSnapshotTimeAndStatusReturnsNoItems() {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(List.of(0L));
        when(mockLocalParamConfig.excludeWarehouseForSnapshot(0L)).thenReturn(false);

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());


        // Configure SnapshotTaskMapper.selectOneBySnapshotTypeAndSnapshotTimeAndStatus(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);

        // Run the test
        WarehouseIdAndSnapshotDateDto dto = new WarehouseIdAndSnapshotDateDto();
        dto.setWarehouseIds(Lists.newArrayList());
        dto.setSnapshotDate(new Date());

        // Run the test
        final ReturnT<String> result = fmsSnapshotPerDayJobUnderTest.execute(JsonUtil.toJson(dto));

        // Verify the results
    }

    @Test
    public void testProcessSnapshotTasks() {
        // Setup
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        final List<SnapshotTask> snapshotTasks = List.of(snapshotTask);

        // Configure SnapshotTaskMapper.selectOneBySnapshotTypeAndSnapshotTimeAndStatus(...).
        final SnapshotTask snapshotTask1 = new SnapshotTask();
        snapshotTask1.setId(0L);
        snapshotTask1.setWarehouseId(0L);
        snapshotTask1.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask1.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask1.setStatus(SnapshotStatusEnum.INIT);

        // Run the test
        final List<Long> result = fmsSnapshotPerDayJobUnderTest.processSnapshotTasks(snapshotTasks);

        // Verify the results

    }
}
