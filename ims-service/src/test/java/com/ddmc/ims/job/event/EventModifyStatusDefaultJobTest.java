package com.ddmc.ims.job.event;

import static org.mockito.Mockito.when;

import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.event.EventService;
import com.xxl.job.core.biz.model.ReturnT;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EventModifyStatusDefaultJobTest {

    @Mock
    private EventService mockEventService;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private EventModifyStatusDefaultJob eventModifyStatusDefaultJobUnderTest;

    @Test
    public void testExecute() throws Exception {
        // Setup

        // Run the test
        final ReturnT<String> result = eventModifyStatusDefaultJobUnderTest.execute("[1]");

        // Verify the results
    }
}
