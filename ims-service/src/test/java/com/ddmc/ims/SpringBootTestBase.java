package com.ddmc.ims;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@EnableAsync
//@DirtiesContext
@EnableTransactionManagement(order = Integer.MAX_VALUE - 1)
public abstract class SpringBootTestBase {


    protected LogicInventoryLocation getLogicInventoryLocation() {
        return new LogicInventoryLocation(1L, 100000L, "COMMON");
    }

    static {
        //单元测试 关闭注册中心
        System.setProperty("spring.cloud.zookeeper.discovery.register", "false");
        //单元测试关闭 xxjob
        System.setProperty("xxjob.enable", "false");
        //单元测试开启 swagger文档更新
        System.setProperty("swagger.enable", "true");
        // 指明是本地环境，能进行接口用户验证
        System.setProperty("local", "true");
        System.setProperty("apollo.cluster", "TE");
        System.setProperty("env_name", "TE");
        System.setProperty("cluster", "TE");
    }

}
