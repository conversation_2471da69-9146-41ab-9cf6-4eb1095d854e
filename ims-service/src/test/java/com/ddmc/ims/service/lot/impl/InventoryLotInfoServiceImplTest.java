package com.ddmc.ims.service.lot.impl;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.conditions.WarehouseIdLotIdPair;
import com.ddmc.ims.dal.mapper.ims.InventoryLotInfoMapper;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.manager.lot.LotManager;
import com.ddmc.ims.message.lot.LotProducer;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.ims.service.lot.InventoryLotInfoService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class InventoryLotInfoServiceImplTest {

    @Mock
    private InventoryLotInfoMapper mockInventoryLotInfoMapper;
    @Mock
    private WarehouseService mockWarehouseService;
    @Mock
    private LotManager mockLotManager;
    @Mock
    private InventoryLotInfoService mockSelfProxy;
    @Mock
    private LotProducer mockLotProducer;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private InventoryLotInfoServiceImpl inventoryLotInfoServiceImplUnderTest;






    @Test
    public void testReplaceInventoryLotInfoList() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> inventoryLotInfoList = List.of(lotInfo);

        // Configure InventoryLotInfoMapper.batchGetByWarehouseIdAndLotId(...).
        final InventoryLotInfo lotInfo1 = new InventoryLotInfo();
        lotInfo1.setId(0L);
        lotInfo1.setSkuId(0L);
        lotInfo1.setWarehouseId(0L);
        lotInfo1.setLotId("lotId");
        lotInfo1.setVendorId(0L);
        lotInfo1.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setPurchaseNo("purchaseNo");
        lotInfo1.setRegionCodePath("regionCodePath");
        lotInfo1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> lotInfos = List.of(lotInfo1);
        when(mockInventoryLotInfoMapper.batchGetByWarehouseIdAndLotId(List.of(new WarehouseIdLotIdPair(0L, "lotId"))))
            .thenReturn(lotInfos);


        // Run the test
        inventoryLotInfoServiceImplUnderTest.replaceInventoryLotInfoList(inventoryLotInfoList);

        Assert.assertTrue(true);
    }

    @Test
    public void testReplaceInventoryLotInfoList_InventoryLotInfoMapperBatchGetByWarehouseIdAndLotIdReturnsNoItems() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> inventoryLotInfoList = List.of(lotInfo);
        when(mockInventoryLotInfoMapper.batchGetByWarehouseIdAndLotId(List.of(new WarehouseIdLotIdPair(0L, "lotId"))))
            .thenReturn(Collections.emptyList());

        // Run the test
        inventoryLotInfoServiceImplUnderTest.replaceInventoryLotInfoList(inventoryLotInfoList);

        Assert.assertTrue(true);
    }


    @Test
    public void testAddMissInventoryLotInfo1() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> expectedResult = List.of(lotInfo);

        // Configure InventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(...).
        final InventoryLotInfo lotInfo1 = new InventoryLotInfo();
        lotInfo1.setId(0L);
        lotInfo1.setSkuId(0L);
        lotInfo1.setWarehouseId(0L);
        lotInfo1.setLotId("lotId");
        lotInfo1.setVendorId(0L);
        lotInfo1.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setPurchaseNo("purchaseNo");
        lotInfo1.setRegionCodePath("regionCodePath");
        lotInfo1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> lotInfos = List.of(lotInfo1);
        when(mockInventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(0L, List.of("value"))).thenReturn(lotInfos);

        // Configure LotManager.batchGet(...).
        final InventoryLotInfo lotInfo2 = new InventoryLotInfo();
        lotInfo2.setId(0L);
        lotInfo2.setSkuId(0L);
        lotInfo2.setWarehouseId(0L);
        lotInfo2.setLotId("lotId");
        lotInfo2.setVendorId(0L);
        lotInfo2.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo2.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo2.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo2.setPurchaseNo("purchaseNo");
        lotInfo2.setRegionCodePath("regionCodePath");
        lotInfo2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> lotInfos1 = List.of(lotInfo2);

        // Run the test
        final List<InventoryLotInfo> result = inventoryLotInfoServiceImplUnderTest
            .addMissInventoryLotInfo(0L, List.of("value"));

        Assert.assertTrue(true);
    }

    @Test
    public void testAddMissInventoryLotInfo1_InventoryLotInfoMapperReturnsNoItems() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> expectedResult = List.of(lotInfo);
        when(mockInventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(0L, List.of("value")))
            .thenReturn(Collections.emptyList());

        // Configure LotManager.batchGet(...).
        final InventoryLotInfo lotInfo1 = new InventoryLotInfo();
        lotInfo1.setId(0L);
        lotInfo1.setSkuId(0L);
        lotInfo1.setWarehouseId(0L);
        lotInfo1.setLotId("lotId");
        lotInfo1.setVendorId(0L);
        lotInfo1.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setPurchaseNo("purchaseNo");
        lotInfo1.setRegionCodePath("regionCodePath");
        lotInfo1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> lotInfos = List.of(lotInfo1);


        // Run the test
        final List<InventoryLotInfo> result = inventoryLotInfoServiceImplUnderTest
            .addMissInventoryLotInfo(0L, List.of("value"));

        Assert.assertTrue(true);
    }

    @Test
    public void testAddMissInventoryLotInfo1_LotManagerReturnsNoItems() {
        // Setup
        // Configure InventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(...).
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> lotInfos = List.of(lotInfo);
        when(mockInventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(0L, List.of("value"))).thenReturn(lotInfos);


        // Run the test
        final List<InventoryLotInfo> result = inventoryLotInfoServiceImplUnderTest
            .addMissInventoryLotInfo(0L, List.of("value"));

        // Verify the results
        Assert.assertTrue(true);
    }

    @Test
    public void testQueryInventoryLotInfo() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final Map<String, InventoryLotInfo> expectedResult = Map.ofEntries(Map.entry("value", lotInfo));

        // Configure InventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(...).
        final InventoryLotInfo lotInfo1 = new InventoryLotInfo();
        lotInfo1.setId(0L);
        lotInfo1.setSkuId(0L);
        lotInfo1.setWarehouseId(0L);
        lotInfo1.setLotId("lotId");
        lotInfo1.setVendorId(0L);
        lotInfo1.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setPurchaseNo("purchaseNo");
        lotInfo1.setRegionCodePath("regionCodePath");
        lotInfo1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> lotInfos = List.of(lotInfo1);
        when(mockInventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(0L, List.of("value"))).thenReturn(lotInfos);

        // Configure LotManager.batchGet(...).
        final InventoryLotInfo lotInfo2 = new InventoryLotInfo();
        lotInfo2.setId(0L);
        lotInfo2.setSkuId(0L);
        lotInfo2.setWarehouseId(0L);
        lotInfo2.setLotId("lotId");
        lotInfo2.setVendorId(0L);
        lotInfo2.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo2.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo2.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo2.setPurchaseNo("purchaseNo");
        lotInfo2.setRegionCodePath("regionCodePath");
        lotInfo2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        final Map<String, InventoryLotInfo> result = inventoryLotInfoServiceImplUnderTest
            .queryInventoryLotInfo(Set.of("value"));

        // Verify the results
        Assert.assertTrue(true);
    }

    @Test
    public void testQueryInventoryLotInfo_InventoryLotInfoMapperReturnsNoItems() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final Map<String, InventoryLotInfo> expectedResult = Map.ofEntries(Map.entry("value", lotInfo));
        when(mockInventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(0L, List.of("value")))
            .thenReturn(Collections.emptyList());

        // Configure LotManager.batchGet(...).
        final InventoryLotInfo lotInfo1 = new InventoryLotInfo();
        lotInfo1.setId(0L);
        lotInfo1.setSkuId(0L);
        lotInfo1.setWarehouseId(0L);
        lotInfo1.setLotId("lotId");
        lotInfo1.setVendorId(0L);
        lotInfo1.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setPurchaseNo("purchaseNo");
        lotInfo1.setRegionCodePath("regionCodePath");
        lotInfo1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> lotInfos = List.of(lotInfo1);


        // Run the test
        final Map<String, InventoryLotInfo> result = inventoryLotInfoServiceImplUnderTest
            .queryInventoryLotInfo(Set.of("value"));

        Assert.assertTrue(true);
    }

    @Test
    public void testQueryInventoryLotInfo_LotManagerReturnsNoItems() {
        // Setup
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final Map<String, InventoryLotInfo> expectedResult = Map.ofEntries(Map.entry("value", lotInfo));

        // Configure InventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(...).
        final InventoryLotInfo lotInfo1 = new InventoryLotInfo();
        lotInfo1.setId(0L);
        lotInfo1.setSkuId(0L);
        lotInfo1.setWarehouseId(0L);
        lotInfo1.setLotId("lotId");
        lotInfo1.setVendorId(0L);
        lotInfo1.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setPurchaseNo("purchaseNo");
        lotInfo1.setRegionCodePath("regionCodePath");
        lotInfo1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<InventoryLotInfo> lotInfos = List.of(lotInfo1);
        when(mockInventoryLotInfoMapper.batchGetByWarehouseIdAndLotIds(0L, List.of("value"))).thenReturn(lotInfos);


        // Run the test
        final Map<String, InventoryLotInfo> result = inventoryLotInfoServiceImplUnderTest
            .queryInventoryLotInfo(Set.of("value"));

        Assert.assertTrue(true);
    }
}
