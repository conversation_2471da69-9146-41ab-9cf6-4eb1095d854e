package com.ddmc.ims.service.credential.impl;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.command.CommandHandleFactory;
import com.ddmc.ims.common.bo.BizOrderOperatorCommandConfig;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.CredentialDetailMapper;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.event.producer.AsyncConfirmEventProducer;
import com.ddmc.ims.event.producer.SnapshotCredentialDiffProducer;
import com.ddmc.ims.manager.inventory.CredentialUsageDetailManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.manager.inventory.SnapshotCredentialDiffManager;
import com.ddmc.ims.service.credential.InventoryCredentialService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class InventoryCredentialServiceImplTest {

    @Mock
    private CredentialWrapperManager mockCredentialWrapperManager;
    @Mock
    private CommandHandleFactory mockCommandHandleFactory;
    @Mock
    private CredentialHeaderMapper mockCredentialHeaderMapper;
    @Mock
    private CredentialDetailMapper mockCredentialDetailMapper;
    @Mock
    private LocalParamConfig mockLocalParamConfig;
    @Mock
    private SnapshotCredentialDiffProducer mockSnapshotCredentialDiffProducer;
    @Mock
    private SnapshotCredentialDiffManager mockSnapshotCredentialDiffManager;
    @Mock
    private WarehouseSkuInventoryMapper mockWarehouseSkuInventoryMapper;
    @Mock
    private LocalParamService mockLocalParamService;
    @Mock
    private CredentialUsageDetailManager mockCredentialUsageDetailManager;
    @Mock
    private AsyncConfirmEventProducer mockAsyncConfirmEventProducer;

    @InjectMocks
    private InventoryCredentialServiceImpl inventoryCredentialServiceImplUnderTest;

    @Mock
    private InventoryCredentialService selfProxy;

    @Test
    public void testConfirm() throws Exception {
        // Setup
        // Configure CredentialWrapperManager.getCredentialHeaderAndDetail(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setFromLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        when(mockCredentialWrapperManager.getCredentialHeaderAndDetail("idempotentId")).thenReturn(credentialHeader);

        when(mockLocalParamService.getStringValue("ims.asyncConfirmOrderType", "")).thenReturn("{}");

        // Configure LocalParamService.getDateTimeValue(...).


        // Configure LocalParamConfig.getBizOrderOperatorCommandConfigByTransferCompatible(...).
        final BizOrderOperatorCommandConfig bizOrderOperatorCommandConfig = new BizOrderOperatorCommandConfig();
        bizOrderOperatorCommandConfig.setBizOrder("bizOrder");
        bizOrderOperatorCommandConfig.setOrderOperateType(0);
        bizOrderOperatorCommandConfig.setCommands(List.of("value"));


        // Configure LocalParamConfig.getBizOrderOperatorCommandConfig(...).
        final BizOrderOperatorCommandConfig bizOrderOperatorCommandConfig1 = new BizOrderOperatorCommandConfig();
        bizOrderOperatorCommandConfig1.setBizOrder("bizOrder");
        bizOrderOperatorCommandConfig1.setOrderOperateType(0);
        bizOrderOperatorCommandConfig1.setCommands(List.of("value"));


        // Run the test
        inventoryCredentialServiceImplUnderTest.confirm("idempotentId");


        Assert.assertTrue(true);
    }

    @Test
    public void testConfirm_LocalParamServiceGetDateTimeValueThrowsParseException() throws Exception {
        // Setup
        // Configure CredentialWrapperManager.getCredentialHeaderAndDetail(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setFromLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        when(mockCredentialWrapperManager.getCredentialHeaderAndDetail("idempotentId")).thenReturn(credentialHeader);

        when(mockLocalParamService.getStringValue("ims.asyncConfirmOrderType", "")).thenReturn("{}");
        inventoryCredentialServiceImplUnderTest.confirm("idempotentId");

        Assert.assertTrue(true);
    }

    @Test
    public void testDirectHandleCredential() throws Exception {
        // Setup
        final CredentialHeader credentialHeaderAndDetail = new CredentialHeader();
        credentialHeaderAndDetail.setIdempotentId("idempotentId");
        credentialHeaderAndDetail.setWarehouseId(0L);
        credentialHeaderAndDetail.setStatus(CredentialStatus.INIT);
        credentialHeaderAndDetail.setOrderSource("orderSource");
        credentialHeaderAndDetail.setOrderNo("orderNo");
        credentialHeaderAndDetail.setExeOrderNo("exeOrderNo");
        credentialHeaderAndDetail.setOrderType("orderType");
        credentialHeaderAndDetail.setOrderOperateType(0);
        credentialHeaderAndDetail.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setDeliveryMode(0);
        credentialHeaderAndDetail.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialHeaderAndDetail.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setFromLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeaderAndDetail.setCredentialUseageDetailList(List.of(credentialUseageDetail));

        when(mockCredentialHeaderMapper.confirmCredentialByIdempotentId("idempotentId")).thenReturn(1);

        // Configure LocalParamConfig.getBizOrderOperatorCommandConfigByTransferCompatible(...).
        final BizOrderOperatorCommandConfig bizOrderOperatorCommandConfig = new BizOrderOperatorCommandConfig();
        bizOrderOperatorCommandConfig.setBizOrder("bizOrder");
        bizOrderOperatorCommandConfig.setOrderOperateType(0);
        bizOrderOperatorCommandConfig.setCommands(List.of("value"));

        // Configure LocalParamConfig.getBizOrderOperatorCommandConfig(...).
        final BizOrderOperatorCommandConfig bizOrderOperatorCommandConfig1 = new BizOrderOperatorCommandConfig();
        bizOrderOperatorCommandConfig1.setBizOrder("bizOrder");
        bizOrderOperatorCommandConfig1.setOrderOperateType(0);
        bizOrderOperatorCommandConfig1.setCommands(List.of("value"));

        // Run the test
        inventoryCredentialServiceImplUnderTest.directHandleCredential(credentialHeaderAndDetail);

        Assert.assertTrue(true);
    }

    @Test
    public void testDirectHandleCredential_LocalParamServiceThrowsParseException() throws Exception {
        // Setup
        final CredentialHeader credentialHeaderAndDetail = new CredentialHeader();
        credentialHeaderAndDetail.setIdempotentId("idempotentId");
        credentialHeaderAndDetail.setWarehouseId(0L);
        credentialHeaderAndDetail.setStatus(CredentialStatus.INIT);
        credentialHeaderAndDetail.setOrderSource("orderSource");
        credentialHeaderAndDetail.setOrderNo("orderNo");
        credentialHeaderAndDetail.setExeOrderNo("exeOrderNo");
        credentialHeaderAndDetail.setOrderType("orderType");
        credentialHeaderAndDetail.setOrderOperateType(0);
        credentialHeaderAndDetail.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setDeliveryMode(0);
        credentialHeaderAndDetail.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialHeaderAndDetail.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setFromLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeaderAndDetail.setCredentialUseageDetailList(List.of(credentialUseageDetail));


        // Run the test
        assertThatThrownBy(
            () -> inventoryCredentialServiceImplUnderTest.directHandleCredential(credentialHeaderAndDetail))
            .isInstanceOf(ImsBusinessException.class);
    }

    @Test
    public void testGetSkuInventoryCommand() {
        // Setup
        final CredentialHeader credentialHeaderAndDetail = new CredentialHeader();
        credentialHeaderAndDetail.setIdempotentId("idempotentId");
        credentialHeaderAndDetail.setWarehouseId(0L);
        credentialHeaderAndDetail.setStatus(CredentialStatus.INIT);
        credentialHeaderAndDetail.setOrderSource("orderSource");
        credentialHeaderAndDetail.setOrderNo("orderNo");
        credentialHeaderAndDetail.setExeOrderNo("exeOrderNo");
        credentialHeaderAndDetail.setOrderType("orderType");
        credentialHeaderAndDetail.setOrderOperateType(0);
        credentialHeaderAndDetail.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setDeliveryMode(0);
        credentialHeaderAndDetail.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialHeaderAndDetail.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setFromLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeaderAndDetail.setCredentialUseageDetailList(List.of(credentialUseageDetail));

        // Configure LocalParamConfig.getBizOrderOperatorCommandConfigByTransferCompatible(...).
        final BizOrderOperatorCommandConfig bizOrderOperatorCommandConfig = new BizOrderOperatorCommandConfig();
        bizOrderOperatorCommandConfig.setBizOrder("bizOrder");
        bizOrderOperatorCommandConfig.setOrderOperateType(0);
        bizOrderOperatorCommandConfig.setCommands(List.of("value"));

        // Configure LocalParamConfig.getBizOrderOperatorCommandConfig(...).
        final BizOrderOperatorCommandConfig bizOrderOperatorCommandConfig1 = new BizOrderOperatorCommandConfig();
        bizOrderOperatorCommandConfig1.setBizOrder("bizOrder");
        bizOrderOperatorCommandConfig1.setOrderOperateType(0);
        bizOrderOperatorCommandConfig1.setCommands(List.of("value"));

        // Run the test
        final List<? extends SkuInventoryCommand> result = inventoryCredentialServiceImplUnderTest
            .getSkuInventoryCommand(credentialHeaderAndDetail);
        Assert.assertTrue(true);
        // Verify the results
    }

    @Test
    public void testAddLock() {
        // Setup
        final CredentialHeader credentialHeaderAndDetail = new CredentialHeader();
        credentialHeaderAndDetail.setIdempotentId("idempotentId");
        credentialHeaderAndDetail.setWarehouseId(0L);
        credentialHeaderAndDetail.setStatus(CredentialStatus.INIT);
        credentialHeaderAndDetail.setOrderSource("orderSource");
        credentialHeaderAndDetail.setOrderNo("orderNo");
        credentialHeaderAndDetail.setExeOrderNo("exeOrderNo");
        credentialHeaderAndDetail.setOrderType("orderType");
        credentialHeaderAndDetail.setOrderOperateType(0);
        credentialHeaderAndDetail.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setDeliveryMode(0);
        credentialHeaderAndDetail.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeaderAndDetail.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialHeaderAndDetail.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setFromLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeaderAndDetail.setCredentialUseageDetailList(List.of(credentialUseageDetail));


        // Run the test
        inventoryCredentialServiceImplUnderTest.addLock(credentialHeaderAndDetail);

        Assert.assertTrue(true);
    }

    @Test
    public void testCancel() {
        // Setup
        // Configure CredentialHeaderMapper.selectByIdempotentId(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setEndDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setToLogicLocationCode("toLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setFromLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("logicInventoryLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("usageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        credentialHeader.setCredentialUseageDetailList(List.of(credentialUseageDetail));
        when(mockCredentialHeaderMapper.selectByIdempotentId("idempotentId")).thenReturn(credentialHeader);

        // Run the test
        inventoryCredentialServiceImplUnderTest.cancel("idempotentId");

        // Verify the results
        verify(mockCredentialWrapperManager).cancel("idempotentId");
    }


}
