package com.ddmc.ims.service.warehouse.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.ims.CargoOwnerMapper;
import com.ddmc.ims.dal.mapper.ims.CoverStationDetailMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.model.ims.Warehouse;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WarehouseServiceImplTest {

    @Mock
    private WarehouseMapper mockWarehouseMapper;
    @Mock
    private CoverStationDetailMapper mockCoverStationDetailMapper;


    @InjectMocks
    private WarehouseServiceImpl warehouseServiceImplUnderTest;
    @Mock
    private CargoOwnerMapper cargoOwnerMapper;



    @Test
    public void testGetAllWarehouses() {
        // Setup
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        final List<Warehouse> expectedResult = List.of(warehouse);

        // Configure WarehouseMapper.getAllWarehouses(...).
        final Warehouse warehouse1 = new Warehouse();
        warehouse1.setId(0L);
        warehouse1.setName("name");
        warehouse1.setCorporationCode("corporationCode");
        warehouse1.setZoneId(0);
        warehouse1.setCityId("cityId");
        warehouse1.setStatus(0);
        warehouse1.setType(0);
        warehouse1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse1.setCreateUser("createUser");
        warehouse1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse1.setUpdateUser("updateUser");
        final List<Warehouse> warehouses = List.of(warehouse1);
        when(mockWarehouseMapper.getAllWarehouses()).thenReturn(warehouses);

        // Run the test
        final List<Warehouse> result = warehouseServiceImplUnderTest.getAllWarehouses();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAllWarehouses_WarehouseMapperReturnsNoItems() {
        // Setup
        when(mockWarehouseMapper.getAllWarehouses()).thenReturn(Collections.emptyList());

        // Run the test
        final List<Warehouse> result = warehouseServiceImplUnderTest.getAllWarehouses();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetAllWarehouseIds() {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(List.of(0L));

        // Run the test
        final List<Long> result = warehouseServiceImplUnderTest.getAllWarehouseIds();

        // Verify the results
        assertThat(result).isEqualTo(List.of(0L));
    }

    @Test
    public void testGetAllWarehouseIds_WarehouseMapperReturnsNoItems() {
        // Setup
        when(mockWarehouseMapper.selectAllId()).thenReturn(Collections.emptyList());

        // Run the test
        final List<Long> result = warehouseServiceImplUnderTest.getAllWarehouseIds();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }


    @Test
    public void testGetWarehouse() {
        // Setup
        final Warehouse expectedResult = new Warehouse();
        expectedResult.setId(0L);
        expectedResult.setName("name");
        expectedResult.setCorporationCode("corporationCode");
        expectedResult.setZoneId(0);
        expectedResult.setCityId("cityId");
        expectedResult.setStatus(0);
        expectedResult.setType(0);
        expectedResult.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setCreateUser("createUser");
        expectedResult.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        expectedResult.setUpdateUser("updateUser");

        // Configure WarehouseMapper.selectById(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");

        // Run the test
        final Warehouse result = warehouseServiceImplUnderTest.getWarehouse(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }


    @Test
    public void testLoadFromCache() {
        // Setup
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        final Map<Long, Warehouse> expectedResult = Map.ofEntries(Map.entry(0L, warehouse));

        // Configure WarehouseMapper.selectById(...).
        final Warehouse warehouse1 = new Warehouse();
        warehouse1.setId(0L);
        warehouse1.setName("name");
        warehouse1.setCorporationCode("corporationCode");
        warehouse1.setZoneId(0);
        warehouse1.setCityId("cityId");
        warehouse1.setStatus(0);
        warehouse1.setType(0);
        warehouse1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse1.setCreateUser("createUser");
        warehouse1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse1.setUpdateUser("updateUser");
        when(mockWarehouseMapper.selectById(0L)).thenReturn(warehouse1);

        // Run the test
        final Map<Long, Warehouse> result = warehouseServiceImplUnderTest.loadFromCache(List.of(0L));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
