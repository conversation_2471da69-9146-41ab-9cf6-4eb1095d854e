package com.ddmc.ims.service.snapshot.impl;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.monitor.SkuInventoryInOutSnapshot;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotUsagePerHour;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.dto.InOutNumDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.snapshot.SkuInventorySnapshotPerHourService;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotPerHourServiceImplTest {

    @Mock
    private CredentialWrapperManager mockCredentialWrapperManager;
    @Mock
    private CommandManager mockCommandManager;
    @Mock
    private SkuInventorySnapshotPerHourMapper mockSkuInventorySnapshotPerHourMapper;
    @Mock
    private SnapshotTaskMapper mockSnapshotTaskMapper;
    @Mock
    private SkuInventorySnapshotPerHourService mockSelfProxy;
    @Mock
    private SkuInventoryInOutSnapshotMapper mockSkuInventoryInOutSnapshotMapper;
    @Mock
    private LocalParamConfig mockLocalParamConfig;
    @Mock
    private LocalParamService mockLocalParamService;
    @Mock
    private SkuInventorySnapshotUsagePerHourMapper mockSkuInventorySnapshotUsagePerHourMapper;

    @InjectMocks
    private SkuInventorySnapshotPerHourServiceImpl skuInventorySnapshotPerHourServiceImplUnderTest;

    @Test
    public void testHandleWarehouseSnapshotPerHour() {
        // Setup
        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);
        snapshotTask.setCreateTime(null);
        snapshotTask.setUpdateTime(null);
        when(mockLocalParamService.getBooleanValue("ims.SkuInventorySnapshotPerHourReplay", false)).thenReturn(false);
        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest
            .handleWarehouseSnapshotPerHour(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                List.of(0L));

        verify(mockSnapshotTaskMapper).batchInsert(List.of(snapshotTask));
    }

    @Test
    public void testHandleWarehouseSnapshotPerHour_SnapshotTaskMapperSelectByWarehouseIdInAndSnapshotTypeAndSnapshotTimeReturnsNoItems() {
        // Setup
        when(mockLocalParamService.getBooleanValue("ims.SkuInventorySnapshotPerHourReplay", false)).thenReturn(false);
        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest
            .handleWarehouseSnapshotPerHour(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                List.of(0L));

        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);
        snapshotTask.setCreateTime(null);
        snapshotTask.setUpdateTime(null);

        verify(mockSnapshotTaskMapper).batchInsert(List.of(snapshotTask));
    }

    @Test
    public void testHandleWarehouseSnapshotPerHour_CredentialWrapperManagerReturnsNoItems() {
        // Setup
        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamService.getBooleanValue("ims.SkuInventorySnapshotPerHourReplay", false)).thenReturn(false);
        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest
            .handleWarehouseSnapshotPerHour(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                List.of(0L));

        verify(mockSnapshotTaskMapper).batchInsert(List.of(snapshotTask));
    }

    @Test
    public void testHandleWarehouseSnapshotPerHour_CommandManagerGetCommandInventoryNumListReturnsNoItems() {
        // Setup
        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamService.getBooleanValue("ims.SkuInventorySnapshotPerHourReplay", false)).thenReturn(false);
        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest
            .handleWarehouseSnapshotPerHour(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                List.of(0L));

        verify(mockSnapshotTaskMapper).batchInsert(List.of(snapshotTask));
    }

    @Test
    public void testHandleWarehouseSnapshotPerHour_SkuInventorySnapshotPerHourMapperSelectBySnapshotDateTimeAndWarehouseIdReturnsNoItems() {
        // Setup
        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamService.getBooleanValue("ims.SkuInventorySnapshotPerHourReplay", false)).thenReturn(false);
        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(Collections.emptyList());

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest
            .handleWarehouseSnapshotPerHour(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                List.of(0L));

        verify(mockSnapshotTaskMapper).batchInsert(List.of(snapshotTask));
    }

    @Test
    public void testHandleWarehouseSnapshotPerHour_CommandManagerGetFmsInOutNumDtoReturnsNoItems() {
        // Setup
        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamService.getBooleanValue("ims.SkuInventorySnapshotPerHourReplay", false)).thenReturn(false);
        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);


        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest
            .handleWarehouseSnapshotPerHour(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                List.of(0L));

        verify(mockSnapshotTaskMapper).batchInsert(List.of(snapshotTask));
    }

    @Test
    public void testHandleWarehouseSnapshotPerHour_SkuInventoryInOutSnapshotMapperSelectBySnapshotDateTimeBetweenReturnsNoItems() {
        // Setup
        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamService.getBooleanValue("ims.SkuInventorySnapshotPerHourReplay", false)).thenReturn(false);
        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest
            .handleWarehouseSnapshotPerHour(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                List.of(0L));

        verify(mockSnapshotTaskMapper).batchInsert(List.of(snapshotTask));
    }

    @Test
    public void testHandleWarehouseSnapshotPerHour_SkuInventorySnapshotUsagePerHourMapperReturnsNoItems() {
        // Setup
        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamService.getBooleanValue("ims.SkuInventorySnapshotPerHourReplay", false)).thenReturn(false);
        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(Collections.emptyList());

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest
            .handleWarehouseSnapshotPerHour(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                List.of(0L));

        Assert.assertTrue(true);

    }

    @Test
    public void testHandlePerHourSnapshotTask() {
        // Setup
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest.handlePerHourSnapshotTask(snapshotTask);
        Assert.assertTrue(true);
    }

    @Test
    public void testHandlePerHourSnapshotTask_CredentialWrapperManagerReturnsNoItems() {
        // Setup
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(Collections.emptyList());

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockLocalParamService.getBooleanValue("ims.SnapshotNotNeedProcessSwitch", false)).thenReturn(false);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest.handlePerHourSnapshotTask(snapshotTask);
        Assert.assertTrue(true);
    }

    @Test
    public void testHandlePerHourSnapshotTask_CommandManagerGetCommandInventoryNumListReturnsNoItems() {
        // Setup
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockLocalParamService.getBooleanValue("ims.SnapshotNotNeedProcessSwitch", false)).thenReturn(false);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest.handlePerHourSnapshotTask(snapshotTask);
        Assert.assertTrue(true);
    }

    @Test
    public void testHandlePerHourSnapshotTask_SkuInventorySnapshotPerHourMapperSelectBySnapshotDateTimeAndWarehouseIdReturnsNoItems() {
        // Setup
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(Collections.emptyList());

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockLocalParamService.getBooleanValue("ims.SnapshotNotNeedProcessSwitch", false)).thenReturn(false);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest.handlePerHourSnapshotTask(snapshotTask);
        Assert.assertTrue(true);
    }

    @Test
    public void testHandlePerHourSnapshotTask_CommandManagerGetFmsInOutNumDtoReturnsNoItems() {
        // Setup
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);


        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockLocalParamService.getBooleanValue("ims.SnapshotNotNeedProcessSwitch", false)).thenReturn(false);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest.handlePerHourSnapshotTask(snapshotTask);
        Assert.assertTrue(true);
    }

    @Test
    public void testHandlePerHourSnapshotTask_SkuInventoryInOutSnapshotMapperSelectBySnapshotDateTimeBetweenReturnsNoItems() {
        // Setup
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotUsagePerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotUsagePerHour skuInventorySnapshotUsagePerHour = new SkuInventorySnapshotUsagePerHour();
        skuInventorySnapshotUsagePerHour.setId(0L);
        skuInventorySnapshotUsagePerHour.setSkuId(0L);
        skuInventorySnapshotUsagePerHour.setWarehouseId(0L);
        skuInventorySnapshotUsagePerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotUsagePerHour.setCargoOwnerId(0L);
        skuInventorySnapshotUsagePerHour.setUsageCode("usage");
        skuInventorySnapshotUsagePerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotUsagePerHour
            .setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotUsagePerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotUsagePerHour> skuInventorySnapshotUsagePerHours = List
            .of(skuInventorySnapshotUsagePerHour);
        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotUsagePerHours);

        when(mockLocalParamService.getBooleanValue("ims.SnapshotNotNeedProcessSwitch", false)).thenReturn(false);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest.handlePerHourSnapshotTask(snapshotTask);
        Assert.assertTrue(true);
    }

    @Test
    public void testHandlePerHourSnapshotTask_SkuInventorySnapshotUsagePerHourMapperReturnsNoItems() {
        // Setup
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);

        when(mockLocalParamConfig.isInterruptSnapshotPerHour()).thenReturn(false);
        when(mockLocalParamConfig.getSnapshotPerHourSleepInterval()).thenReturn(0L);

        // Configure SkuInventorySnapshotPerHourMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventorySnapshotPerHourMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date);

        // Configure SkuInventoryInOutSnapshotMapper.selectOneMaxSnapshotDateTimeByWarehouseId(...).
        final Date date1 = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockSkuInventoryInOutSnapshotMapper
            .selectOneMaxSnapshotDateTimeByWarehouseId(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(date1);

        // Configure CredentialWrapperManager.getCredentialHeaders(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setSeqNo("seqNo");
        credentialHeader.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);
        when(mockCredentialWrapperManager
            .getCredentialHeaders(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(credentialHeaders);

        // Configure CommandManager.getCommandInventoryNumList(...).
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usage");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);
        when(mockSkuInventorySnapshotPerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(skuInventorySnapshotPerHours);

        // Configure CommandManager.getFmsInOutNumDto(...).
        final InOutNumDto inOutNumDto = new InOutNumDto();
        inOutNumDto.setSkuId(0L);
        inOutNumDto.setWarehouseId(0L);
        inOutNumDto.setInQty(new BigDecimal("0.00"));
        inOutNumDto.setOutQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeBetween(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockSkuInventorySnapshotUsagePerHourMapper
            .selectBySnapshotDateTimeAndWarehouseId(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L))
            .thenReturn(Collections.emptyList());
        when(mockLocalParamService.getBooleanValue("ims.SnapshotNotNeedProcessSwitch", false)).thenReturn(false);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest.handlePerHourSnapshotTask(snapshotTask);
        Assert.assertTrue(true);
    }



    @Test
    public void testClearSnapshotPerHour() {
        // Setup
        when(mockSkuInventorySnapshotPerHourMapper.deleteByWarehouseIdInAndSnapshotDateTime(List.of(0L),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);
        when(mockSkuInventoryInOutSnapshotMapper.deleteByWarehouseIdInAndSnapshotDateTime(List.of(0L),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);
        when(mockSkuInventorySnapshotUsagePerHourMapper.deleteByWarehouseIdInAndSnapshotDateTime(
            List.of(0L), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(0);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest.clearSnapshotPerHour(
            List.of(0L), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockSnapshotTaskMapper).deleteByWarehouseIdInAndSnapshotTimeAndSnapshotType(
            List.of(0L), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), SnapshotTypeEnum.INVENTORY_HOUR);
        verify(mockSkuInventorySnapshotPerHourMapper).deleteByWarehouseIdInAndSnapshotDateTime(
            List.of(0L), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockSkuInventoryInOutSnapshotMapper).deleteByWarehouseIdInAndSnapshotDateTime(
            List.of(0L), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockSkuInventorySnapshotUsagePerHourMapper).deleteByWarehouseIdInAndSnapshotDateTime(List.of(0L),
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }
}
