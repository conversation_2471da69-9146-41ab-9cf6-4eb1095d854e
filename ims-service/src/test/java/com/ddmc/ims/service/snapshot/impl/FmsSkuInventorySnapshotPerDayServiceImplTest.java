package com.ddmc.ims.service.snapshot.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.dal.mapper.monitor.FmsSkuInventorySnapshotPerDayMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuTransferInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.Warehouse;
import com.ddmc.ims.dal.model.monitor.FmsSkuInventorySnapshotPerDay;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.dal.model.monitor.SkuInventoryInOutSnapshot;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.CommandChangeQtyDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.manager.ocs.InventoryCredentialManager;
import com.ddmc.ims.service.snapshot.FmsSkuInventorySnapshotPerDayService;
import com.ddmc.ims.service.snapshot.SnapshotCredentialDiffService;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.ddmc.ocs.inventorycredential.response.ManufactureDemandDetailResponse;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FmsSkuInventorySnapshotPerDayServiceImplTest {

    @Mock
    private FmsSkuInventorySnapshotPerDayMapper mockFmsSkuInventorySnapshotPerDayMapper;
    @Mock
    private SnapshotTaskMapper mockSnapshotTaskMapper;
    @Mock
    private CredentialWrapperManager mockCredentialWrapperManager;
    @Mock
    private FmsSkuInventorySnapshotPerDayService mockSelfProxy;
    @Mock
    private CommandManager mockCommandManager;
    @Mock
    private SkuInventorySnapshotPerHourMapper mockSkuInventorySnapshotPerHourMapper;
    @Mock
    private SnapshotCredentialDiffService mockSnapshotCredentialDiffService;
    @Mock
    private SkuInventoryInOutSnapshotMapper mockSkuInventoryInOutSnapshotMapper;
    @Mock
    private FmsSnapshotNotifyMapper mockFmsSnapshotNotifyMapper;
    @Mock
    private InventoryCredentialManager mockInventoryCredentialManager;
    @Mock
    private WarehouseService mockWarehouseService;

    @InjectMocks
    private FmsSkuInventorySnapshotPerDayServiceImpl fmsSkuInventorySnapshotPerDayServiceImplUnderTest;
    @Mock
    private SkuTransferInventorySnapshotPerHourMapper skuTransferInventorySnapshotPerHourMapper;

    @Test
    public void testDealEndFmsSkuInventorySnapshot() {

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());


        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        snapshotTask.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SnapshotTask> snapshotTasks = List.of(snapshotTask);

        // Configure FmsSkuInventorySnapshotPerDayMapper.selectByWarehouseIdAndSnapshotDate(...).
        final FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay = new FmsSkuInventorySnapshotPerDay();
        fmsSkuInventorySnapshotPerDay.setId(0L);
        fmsSkuInventorySnapshotPerDay.setWarehouseId(0L);
        fmsSkuInventorySnapshotPerDay.setSkuId(0L);
        fmsSkuInventorySnapshotPerDay.setSnapshotDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setInQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setOutQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setImsQty(new BigDecimal("0.00"));


        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);


        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots = List.of(skuInventoryInOutSnapshot);

        // Configure WarehouseService.getWarehouse(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        when(mockWarehouseService.getWarehouse(0L)).thenReturn(warehouse);

        // Configure CredentialWrapperManager.listByWarehouseIdAndOrderOperateTypeAndEndDateTime(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);

        // Configure InventoryCredentialManager.queryManufactureDemandDetail(...).
        final ManufactureDemandDetailResponse manufactureDemandDetailResponse = new ManufactureDemandDetailResponse();
        manufactureDemandDetailResponse.setMaterialId(0L);
        manufactureDemandDetailResponse.setUseQty(new BigDecimal("0.00"));
        final List<ManufactureDemandDetailResponse> manufactureDemandDetailResponses = List
            .of(manufactureDemandDetailResponse);


        // Configure SnapshotCredentialDiffService.getFmsLessInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap);

        // Configure SnapshotCredentialDiffService.getFmsMoreInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto1 = new CommandChangeQtyDto();
        commandChangeQtyDto1.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap1 = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto1));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsMoreInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap1);

        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .dealEndFmsSkuInventorySnapshot(0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testDealEndFmsSkuInventorySnapshot_SnapshotTaskMapperSelectByWarehouseIdInAndSnapshotTypeAndSnapshotTimeReturnsNoItems() {

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());


        // Configure FmsSkuInventorySnapshotPerDayMapper.selectByWarehouseIdAndSnapshotDate(...).
        final FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay = new FmsSkuInventorySnapshotPerDay();
        fmsSkuInventorySnapshotPerDay.setId(0L);
        fmsSkuInventorySnapshotPerDay.setWarehouseId(0L);
        fmsSkuInventorySnapshotPerDay.setSkuId(0L);
        fmsSkuInventorySnapshotPerDay.setSnapshotDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setInQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setOutQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setImsQty(new BigDecimal("0.00"));


        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);


        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots = List.of(skuInventoryInOutSnapshot);


        // Configure WarehouseService.getWarehouse(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        when(mockWarehouseService.getWarehouse(0L)).thenReturn(warehouse);

        // Configure CredentialWrapperManager.listByWarehouseIdAndOrderOperateTypeAndEndDateTime(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);

        // Configure InventoryCredentialManager.queryManufactureDemandDetail(...).
        final ManufactureDemandDetailResponse manufactureDemandDetailResponse = new ManufactureDemandDetailResponse();
        manufactureDemandDetailResponse.setMaterialId(0L);
        manufactureDemandDetailResponse.setUseQty(new BigDecimal("0.00"));
        final List<ManufactureDemandDetailResponse> manufactureDemandDetailResponses = List
            .of(manufactureDemandDetailResponse);


        // Configure SnapshotCredentialDiffService.getFmsLessInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap);

        // Configure SnapshotCredentialDiffService.getFmsMoreInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto1 = new CommandChangeQtyDto();
        commandChangeQtyDto1.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap1 = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto1));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsMoreInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap1);

        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .dealEndFmsSkuInventorySnapshot(0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testDealEndFmsSkuInventorySnapshot_FmsSkuInventorySnapshotPerDayMapperReturnsNoItems() {

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        snapshotTask.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SnapshotTask> snapshotTasks = List.of(snapshotTask);


        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots = List.of(skuInventoryInOutSnapshot);


        // Configure WarehouseService.getWarehouse(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        when(mockWarehouseService.getWarehouse(0L)).thenReturn(warehouse);

        // Configure CredentialWrapperManager.listByWarehouseIdAndOrderOperateTypeAndEndDateTime(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);

        // Configure InventoryCredentialManager.queryManufactureDemandDetail(...).
        final ManufactureDemandDetailResponse manufactureDemandDetailResponse = new ManufactureDemandDetailResponse();
        manufactureDemandDetailResponse.setMaterialId(0L);
        manufactureDemandDetailResponse.setUseQty(new BigDecimal("0.00"));
        final List<ManufactureDemandDetailResponse> manufactureDemandDetailResponses = List
            .of(manufactureDemandDetailResponse);


        // Configure SnapshotCredentialDiffService.getFmsLessInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap);

        // Configure SnapshotCredentialDiffService.getFmsMoreInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto1 = new CommandChangeQtyDto();
        commandChangeQtyDto1.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap1 = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto1));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsMoreInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap1);

        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .dealEndFmsSkuInventorySnapshot(0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testDealEndFmsSkuInventorySnapshot_SkuInventorySnapshotPerHourMapperReturnsNoItems() {

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        snapshotTask.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SnapshotTask> snapshotTasks = List.of(snapshotTask);


        // Configure FmsSkuInventorySnapshotPerDayMapper.selectByWarehouseIdAndSnapshotDate(...).
        final FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay = new FmsSkuInventorySnapshotPerDay();
        fmsSkuInventorySnapshotPerDay.setId(0L);
        fmsSkuInventorySnapshotPerDay.setWarehouseId(0L);
        fmsSkuInventorySnapshotPerDay.setSkuId(0L);
        fmsSkuInventorySnapshotPerDay.setSnapshotDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setInQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setOutQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setImsQty(new BigDecimal("0.00"));

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots = List.of(skuInventoryInOutSnapshot);

        // Configure WarehouseService.getWarehouse(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        when(mockWarehouseService.getWarehouse(0L)).thenReturn(warehouse);

        // Configure CredentialWrapperManager.listByWarehouseIdAndOrderOperateTypeAndEndDateTime(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);

        // Configure InventoryCredentialManager.queryManufactureDemandDetail(...).
        final ManufactureDemandDetailResponse manufactureDemandDetailResponse = new ManufactureDemandDetailResponse();
        manufactureDemandDetailResponse.setMaterialId(0L);
        manufactureDemandDetailResponse.setUseQty(new BigDecimal("0.00"));
        final List<ManufactureDemandDetailResponse> manufactureDemandDetailResponses = List
            .of(manufactureDemandDetailResponse);


        // Configure SnapshotCredentialDiffService.getFmsLessInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap);

        // Configure SnapshotCredentialDiffService.getFmsMoreInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto1 = new CommandChangeQtyDto();
        commandChangeQtyDto1.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap1 = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto1));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsMoreInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap1);

        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .dealEndFmsSkuInventorySnapshot(0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testDealEndFmsSkuInventorySnapshot_SkuInventoryInOutSnapshotMapperReturnsNoItems() {

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());


        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        snapshotTask.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());


        // Configure FmsSkuInventorySnapshotPerDayMapper.selectByWarehouseIdAndSnapshotDate(...).
        final FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay = new FmsSkuInventorySnapshotPerDay();
        fmsSkuInventorySnapshotPerDay.setId(0L);
        fmsSkuInventorySnapshotPerDay.setWarehouseId(0L);
        fmsSkuInventorySnapshotPerDay.setSkuId(0L);
        fmsSkuInventorySnapshotPerDay.setSnapshotDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setInQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setOutQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setImsQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);


        // Configure WarehouseService.getWarehouse(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        when(mockWarehouseService.getWarehouse(0L)).thenReturn(warehouse);

        // Configure CredentialWrapperManager.listByWarehouseIdAndOrderOperateTypeAndEndDateTime(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));
        final List<CredentialHeader> credentialHeaders = List.of(credentialHeader);

        // Configure InventoryCredentialManager.queryManufactureDemandDetail(...).
        final ManufactureDemandDetailResponse manufactureDemandDetailResponse = new ManufactureDemandDetailResponse();
        manufactureDemandDetailResponse.setMaterialId(0L);
        manufactureDemandDetailResponse.setUseQty(new BigDecimal("0.00"));
        final List<ManufactureDemandDetailResponse> manufactureDemandDetailResponses = List
            .of(manufactureDemandDetailResponse);

        // Configure SnapshotCredentialDiffService.getFmsLessInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap);

        // Configure SnapshotCredentialDiffService.getFmsMoreInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto1 = new CommandChangeQtyDto();
        commandChangeQtyDto1.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap1 = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto1));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsMoreInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap1);

        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .dealEndFmsSkuInventorySnapshot(0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testDealEndFmsSkuInventorySnapshot_CredentialWrapperManagerReturnsNoItems() {

        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());


        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        snapshotTask.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SnapshotTask> snapshotTasks = List.of(snapshotTask);


        // Configure FmsSkuInventorySnapshotPerDayMapper.selectByWarehouseIdAndSnapshotDate(...).
        final FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay = new FmsSkuInventorySnapshotPerDay();
        fmsSkuInventorySnapshotPerDay.setId(0L);
        fmsSkuInventorySnapshotPerDay.setWarehouseId(0L);
        fmsSkuInventorySnapshotPerDay.setSkuId(0L);
        fmsSkuInventorySnapshotPerDay.setSnapshotDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setInQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setOutQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setImsQty(new BigDecimal("0.00"));

        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);

        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots = List.of(skuInventoryInOutSnapshot);


        // Configure WarehouseService.getWarehouse(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        when(mockWarehouseService.getWarehouse(0L)).thenReturn(warehouse);

        // Configure InventoryCredentialManager.queryManufactureDemandDetail(...).
        final ManufactureDemandDetailResponse manufactureDemandDetailResponse = new ManufactureDemandDetailResponse();
        manufactureDemandDetailResponse.setMaterialId(0L);
        manufactureDemandDetailResponse.setUseQty(new BigDecimal("0.00"));
        final List<ManufactureDemandDetailResponse> manufactureDemandDetailResponses = List
            .of(manufactureDemandDetailResponse);

        // Configure SnapshotCredentialDiffService.getFmsLessInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap);

        // Configure SnapshotCredentialDiffService.getFmsMoreInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto1 = new CommandChangeQtyDto();
        commandChangeQtyDto1.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap1 = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto1));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsMoreInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap1);

        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .dealEndFmsSkuInventorySnapshot(0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testDealEndFmsSkuInventorySnapshot_InventoryCredentialManagerReturnsNoItems() {


        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());


        // Configure SnapshotTaskMapper.selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(...).
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setId(0L);
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.INIT);
        snapshotTask.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());


        // Configure FmsSkuInventorySnapshotPerDayMapper.selectByWarehouseIdAndSnapshotDate(...).
        final FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay = new FmsSkuInventorySnapshotPerDay();
        fmsSkuInventorySnapshotPerDay.setId(0L);
        fmsSkuInventorySnapshotPerDay.setWarehouseId(0L);
        fmsSkuInventorySnapshotPerDay.setSkuId(0L);
        fmsSkuInventorySnapshotPerDay.setSnapshotDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setInQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setOutQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setImsQty(new BigDecimal("0.00"));


        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);


        // Configure SkuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setId(0L);
        skuInventoryInOutSnapshot.setSkuId(0L);
        skuInventoryInOutSnapshot.setWarehouseId(0L);
        skuInventoryInOutSnapshot.setLogicLocationCode("logicLocationCode");
        skuInventoryInOutSnapshot.setCargoOwnerId(0L);
        skuInventoryInOutSnapshot.setLotId("lotId");
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("0.00"));
        skuInventoryInOutSnapshot.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventoryInOutSnapshot.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventoryInOutSnapshot> skuInventoryInOutSnapshots = List.of(skuInventoryInOutSnapshot);


        // Configure WarehouseService.getWarehouse(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        when(mockWarehouseService.getWarehouse(0L)).thenReturn(warehouse);

        // Configure CredentialWrapperManager.listByWarehouseIdAndOrderOperateTypeAndEndDateTime(...).
        final CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("idempotentId");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("orderSource");
        credentialHeader.setOrderNo("orderNo");
        credentialHeader.setExeOrderSource("exeOrderSource");
        credentialHeader.setExeOrderNo("exeOrderNo");
        credentialHeader.setOrderType("orderType");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setFromLogicLocationCode("fromLogicLocationCode");
        credentialDetail.setSkuId(0L);
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialHeader.setCredentialDetailList(List.of(credentialDetail));

        // Configure SnapshotCredentialDiffService.getFmsLessInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap);

        // Configure SnapshotCredentialDiffService.getFmsMoreInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto1 = new CommandChangeQtyDto();
        commandChangeQtyDto1.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap1 = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto1));
        when(mockSnapshotCredentialDiffService
            .getTodayFmsMoreInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap1);

        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .dealEndFmsSkuInventorySnapshot(0L, 0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testUpdateTaskAndBatchSaveFmsSkuInventorySnapshotPerDay() {
        // Setup
        final FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay = new FmsSkuInventorySnapshotPerDay();
        fmsSkuInventorySnapshotPerDay.setId(0L);
        fmsSkuInventorySnapshotPerDay.setWarehouseId(0L);
        fmsSkuInventorySnapshotPerDay.setSkuId(0L);
        fmsSkuInventorySnapshotPerDay.setSnapshotDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setInQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setOutQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setImsQty(new BigDecimal("0.00"));
        final List<FmsSkuInventorySnapshotPerDay> items = List.of(fmsSkuInventorySnapshotPerDay);


        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .updateTaskAndBatchSaveFmsSkuInventorySnapshotPerDay(0L, items);

        Assert.assertTrue(true);
    }

    @Test
    public void testInitEndFmsSkuInventorySnapshot() {
        // Setup
        // Configure SkuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(...).
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> skuInventorySnapshotPerHours = List.of(skuInventorySnapshotPerHour);


        // Configure SnapshotCredentialDiffService.getInitFmsLessInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto));

        // Configure SnapshotCredentialDiffService.getInitFmsMoreInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto1 = new CommandChangeQtyDto();
        commandChangeQtyDto1.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap1 = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto1));
        when(mockSnapshotCredentialDiffService
            .getInitFmsMoreInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap1);

        // Configure WarehouseService.getWarehouse(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        when(mockWarehouseService.getWarehouse(0L)).thenReturn(warehouse);

        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .initEndFmsSkuInventorySnapshot(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testInitEndFmsSkuInventorySnapshot_SkuInventorySnapshotPerHourMapperReturnsNoItems() {

        // Configure SnapshotCredentialDiffService.getInitFmsLessInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto));

        // Configure SnapshotCredentialDiffService.getInitFmsMoreInventoryQtyMap(...).
        final CommandChangeQtyDto commandChangeQtyDto1 = new CommandChangeQtyDto();
        commandChangeQtyDto1.setAllQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setInQty(new BigDecimal("0.00"));
        commandChangeQtyDto1.setOutQty(new BigDecimal("0.00"));
        final Map<Long, CommandChangeQtyDto> longCommandChangeQtyDtoMap1 = Map.ofEntries(
            Map.entry(0L, commandChangeQtyDto1));
        when(mockSnapshotCredentialDiffService
            .getInitFmsMoreInventoryQtyMap(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(longCommandChangeQtyDtoMap1);


        // Configure WarehouseService.getWarehouse(...).
        final Warehouse warehouse = new Warehouse();
        warehouse.setId(0L);
        warehouse.setName("name");
        warehouse.setCorporationCode("corporationCode");
        warehouse.setZoneId(0);
        warehouse.setCityId("cityId");
        warehouse.setStatus(0);
        warehouse.setType(0);
        warehouse.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setCreateUser("createUser");
        warehouse.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        warehouse.setUpdateUser("updateUser");
        when(mockWarehouseService.getWarehouse(0L)).thenReturn(warehouse);

        // Run the test
        fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .initEndFmsSkuInventorySnapshot(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());


        Assert.assertTrue(true);
    }

    @Test
    public void testQueryByWarehouseAndSnapshotDate() {
        // Setup
        final FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay = new FmsSkuInventorySnapshotPerDay();
        fmsSkuInventorySnapshotPerDay.setId(0L);
        fmsSkuInventorySnapshotPerDay.setWarehouseId(0L);
        fmsSkuInventorySnapshotPerDay.setSkuId(0L);
        fmsSkuInventorySnapshotPerDay.setSnapshotDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay.setQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setInQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setOutQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay.setImsQty(new BigDecimal("0.00"));
        final List<FmsSkuInventorySnapshotPerDay> expectedResult = List.of(fmsSkuInventorySnapshotPerDay);

        // Configure FmsSkuInventorySnapshotPerDayMapper.selectByWarehouseIdAndSnapshotDateAndMinSkuId(...).
        final FmsSkuInventorySnapshotPerDay fmsSkuInventorySnapshotPerDay1 = new FmsSkuInventorySnapshotPerDay();
        fmsSkuInventorySnapshotPerDay1.setId(0L);
        fmsSkuInventorySnapshotPerDay1.setWarehouseId(0L);
        fmsSkuInventorySnapshotPerDay1.setSkuId(0L);
        fmsSkuInventorySnapshotPerDay1.setSnapshotDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSkuInventorySnapshotPerDay1.setQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay1.setInQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay1.setOutQty(new BigDecimal("0.00"));
        fmsSkuInventorySnapshotPerDay1.setImsQty(new BigDecimal("0.00"));
        final List<FmsSkuInventorySnapshotPerDay> fmsSkuInventorySnapshotPerDays = List
            .of(fmsSkuInventorySnapshotPerDay1);
        when(mockFmsSkuInventorySnapshotPerDayMapper.selectByWarehouseIdAndSnapshotDateAndMinSkuId(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0))
            .thenReturn(fmsSkuInventorySnapshotPerDays);

        // Run the test
        final List<FmsSkuInventorySnapshotPerDay> result = fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .queryByWarehouseAndSnapshotDate(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByWarehouseAndSnapshotDate_FmsSkuInventorySnapshotPerDayMapperReturnsNoItems() {
        // Setup
        when(mockFmsSkuInventorySnapshotPerDayMapper.selectByWarehouseIdAndSnapshotDateAndMinSkuId(0L,
            new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0)).thenReturn(Collections.emptyList());

        // Run the test
        final List<FmsSkuInventorySnapshotPerDay> result = fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .queryByWarehouseAndSnapshotDate(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0);

        Assert.assertTrue(true);
    }

    @Test
    public void testCountByWarehouseAndSnapshotDate() {
        // Setup
        when(mockFmsSkuInventorySnapshotPerDayMapper
            .countByWarehouseAndSnapshotDate(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(0);

        // Run the test
        final Integer result = fmsSkuInventorySnapshotPerDayServiceImplUnderTest
            .countByWarehouseAndSnapshotDate(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isZero();
    }
}
