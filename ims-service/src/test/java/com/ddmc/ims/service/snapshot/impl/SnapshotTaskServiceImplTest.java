package com.ddmc.ims.service.snapshot.impl;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.ims.WarehouseMapper;
import com.ddmc.ims.dal.mapper.monitor.FmsSnapshotNotifyMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.monitor.FmsSnapshotNotify;
import com.ddmc.ims.event.producer.FmsCompareProducer;
import com.ddmc.ims.service.common.AlertService;
import java.util.Calendar;
import java.util.GregorianCalendar;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SnapshotTaskServiceImplTest {

    @Mock
    private WarehouseMapper mockWarehouseMapper;
    @Mock
    private SnapshotTaskMapper mockSnapshotTaskMapper;
    @Mock
    private FmsSnapshotNotifyMapper mockFmsSnapshotNotifyMapper;
    @Mock
    private FmsCompareProducer mockFmsCompareProducer;
    @Mock
    private AlertService mockAlertService;

    @InjectMocks
    private SnapshotTaskServiceImpl snapshotTaskServiceImplUnderTest;

    @Test
    public void testDayEndTimeNotice() {
        // Setup
        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFmsSnapshotNotifyMapper.selectByDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(fmsSnapshotNotify);


        // Run the test
        snapshotTaskServiceImplUnderTest.dayEndTimeNotice(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testDayEndTimeNotice_WarehouseMapperReturnsNoItems() {
        // Setup
        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFmsSnapshotNotifyMapper.selectByDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(fmsSnapshotNotify);


        // Run the test
        snapshotTaskServiceImplUnderTest.dayEndTimeNotice(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testDayEndTimeNoticeJob() {
        // Setup
        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFmsSnapshotNotifyMapper.selectByDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(fmsSnapshotNotify);


        // Run the test
        snapshotTaskServiceImplUnderTest
            .dayEndTimeNoticeJob(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testDayEndTimeNoticeJob_WarehouseMapperReturnsNoItems() {
        // Setup
        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFmsSnapshotNotifyMapper.selectByDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(fmsSnapshotNotify);


        // Run the test
        snapshotTaskServiceImplUnderTest
            .dayEndTimeNoticeJob(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(true);
    }

    @Test
    public void testCompareResult() {
        // Setup
        // Configure FmsSnapshotNotifyMapper.selectByDayEndDate(...).
        final FmsSnapshotNotify fmsSnapshotNotify = new FmsSnapshotNotify();
        fmsSnapshotNotify.setId(0L);
        fmsSnapshotNotify.setDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setStatus("status");
        fmsSnapshotNotify.setSource("source");
        fmsSnapshotNotify.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        fmsSnapshotNotify.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockFmsSnapshotNotifyMapper.selectByDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(fmsSnapshotNotify);

        when(mockFmsSnapshotNotifyMapper
            .updateStatusByDayEndDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "status"))
            .thenReturn(0);

        // Run the test
        snapshotTaskServiceImplUnderTest
            .compareResult(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), "status");

        Assert.assertTrue(true);
    }
}
