package com.ddmc.ims.test.service.snapshot;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.dto.CommandChangeQtyDto;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.snapshot.impl.SnapshotCredentialDiffServiceImpl;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */

@RunWith(MockitoJUnitRunner.class)
public class SnapshotCredentialDiffServiceTest {

    @InjectMocks
    private SnapshotCredentialDiffServiceImpl snapshotCredentialDiffService;
    @Mock
    private SnapshotCredentialDiffMapper snapshotCredentialDiffMapper;
    @Mock
    private CredentialWrapperManager credentialWrapperManager;


    @Test
    public void getFmsLessInventoryQtyMap_emptyCredential() {
        Long warehouseId = 1L;
        Date businessTime = new Date();
        when(snapshotCredentialDiffMapper.selectByWarehouseIdAndBusinessTime(warehouseId, businessTime))
            .thenReturn(Collections.emptyList());
        Map<Long, CommandChangeQtyDto> map = snapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(warehouseId, businessTime);
        Assert.assertEquals(0, map.size());
    }


    @Test
    public void getFmsLessInventoryQtyMap() {
        Long warehouseId = 1L;
        Date businessTime = new Date();
        when(snapshotCredentialDiffMapper.selectByWarehouseIdAndBusinessTime(warehouseId, businessTime))
            .thenReturn(Collections.emptyList());
        Map<Long, CommandChangeQtyDto> map = snapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(warehouseId, businessTime);
        Assert.assertEquals(0, map.size());
    }

    @Test
    public void getCommandInventoryNumListByHeadIds_empty() {

        snapshotCredentialDiffService
            .getCommandInventoryNumListByHeadIds(Collections.emptyList());
        Mockito.verify(credentialWrapperManager, Mockito.times(0)).getCredentialHeaderAndDetailList(Mockito.any());
    }

}
