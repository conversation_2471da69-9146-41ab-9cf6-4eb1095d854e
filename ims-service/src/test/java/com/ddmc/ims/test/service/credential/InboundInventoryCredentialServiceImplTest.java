package com.ddmc.ims.test.service.credential;

import static org.mockito.Mockito.when;

import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import com.ddmc.ims.service.credential.impl.InboundInventoryCredentialServiceImpl;
import java.util.Collections;
import java.util.Date;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class InboundInventoryCredentialServiceImplTest extends CommonInventoryCredentialServiceImplTest {


    @InjectMocks
    private InboundInventoryCredentialServiceImpl inboundInventoryCredentialService;

    @Test
    public void testTryInbound_exist() {
        InboundCredentialRequest request = buildInboundCredentialRequest();
        CredentialHeader credentialHeader = new CredentialHeader();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(credentialHeader);
        try {
            inboundInventoryCredentialService.tryInbound(request);
        } catch (Exception e) {
            Assert.assertNotNull(e);
            Assert.assertTrue(e instanceof ImsBusinessException);
            Assert.assertEquals(e.getMessage(), CommonErrorCode.EXIST_CREDENTIAL.getMsg());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testTryInbound() {
        InboundCredentialRequest request = buildInboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        inboundInventoryCredentialService.tryInbound(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkInboundCredential(t, request)));
    }

    private boolean checkInboundCredential(CredentialHeader t, InboundCredentialRequest request) {
        checkCredential(t, request);
        Assert.assertTrue(checkCredential(t, request));
        Assert.assertEquals(t.getCredentialDetailList().size(), request.getOperateDetails().size());
        return checkDetailEqual(t.getCredentialDetailList().get(0), request.getOperateDetails().get(0));
    }

    @Test
    public void testTryPublishInbound() {
        InboundCredentialRequest request = buildInboundCredentialRequest();
        request.setExpectArriveTime(new Date());
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        inboundInventoryCredentialService.tryPublishInbound(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkInboundCredential(t, request)));
    }

    @Test
    public void testTryPublishInbound_noTime() {
        InboundCredentialRequest request = buildInboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        try {
            inboundInventoryCredentialService.tryPublishInbound(request);
        } catch (Exception e) {
            Assert.assertNotNull(e);
            Assert.assertTrue(e instanceof ImsBusinessException);
            Assert.assertEquals(e.getMessage(), CommonErrorCode.EXPECT_ARRIVE_TIME_IS_NULL.getMsg());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testTryFinishInbound() {
        InboundCredentialRequest request = buildInboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        inboundInventoryCredentialService.tryFinishInbound(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkInboundCredential(t, request)));
    }

    @Test
    public void testTryModifyExpectArriveTime() {
        InboundCredentialRequest request = buildInboundCredentialRequest();
        request.setExpectArriveTime(new Date());
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        inboundInventoryCredentialService.tryModifyExpectArriveTime(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkInboundCredential(t, request)));
    }

    @Test
    public void testTryModifyExpectArriveTime_noTime() {
        InboundCredentialRequest request = buildInboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        try {
            inboundInventoryCredentialService.tryModifyExpectArriveTime(request);
        } catch (Exception e) {
            Assert.assertNotNull(e);
            Assert.assertTrue(e instanceof ImsBusinessException);
            Assert.assertEquals(e.getMessage(), CommonErrorCode.EXPECT_ARRIVE_TIME_IS_NULL.getMsg());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testTryCancelInbound() {
        InboundCredentialRequest request = buildInboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        inboundInventoryCredentialService.tryCancelInbound(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkInboundCredential(t, request)));
    }

    @Test
    public void testTryCleanPlanIn() {
        InboundCredentialRequest request = buildInboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        inboundInventoryCredentialService.tryCleanPlanIn(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkInboundCredential(t, request)));
    }

    private InboundCredentialRequest buildInboundCredentialRequest() {
        InboundCredentialRequest request = new InboundCredentialRequest();
        buildCredentialRequest(request);
        CredentialDetailRequest adjustCredentialDetailRequest = new CredentialDetailRequest();
        buildCredentialDetailRequest(adjustCredentialDetailRequest);
        request.setOperateDetails(Collections.singletonList(adjustCredentialDetailRequest));
        return request;
    }
}