package com.ddmc.ims.test.service.snapshot;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotUsagePerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.snapshot.SkuInventorySnapshotPerHourService;
import com.ddmc.ims.service.snapshot.impl.SkuInventorySnapshotPerHourServiceImpl;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuInventorySnapshotPerHourServiceImplTest {

    @Mock
    private CredentialWrapperManager mockCredentialWrapperManager;
    @Mock
    private CommandManager mockCommandManager;
    @Mock
    private SkuInventorySnapshotPerHourMapper mockSkuInventorySnapshotPerHourMapper;
    @Mock
    private SnapshotTaskMapper mockSnapshotTaskMapper;
    @Mock
    private SkuInventorySnapshotPerHourService mockSelfProxy;
    @Mock
    private LocalParamService localParamService;

    @Mock
    private LocalParamConfig localParamConfig;
    @Mock
    private SkuInventoryInOutSnapshotMapper skuInventoryInOutSnapshotMapper;
    @Mock
    private SkuInventorySnapshotUsagePerHourMapper skuInventorySnapshotUsagePerHourMapper;

    @Spy
    @InjectMocks
    private SkuInventorySnapshotPerHourServiceImpl skuInventorySnapshotPerHourServiceImplUnderTest;


    /**
     * 验证功能:验证处理仓库小时逻辑快照
     * 场景：当前小时快照任务已经存在
     * 预期结果：抛出异常，异常内容为已存在相同的快照任务
     */
    @Test
    public void testHandleWarehouseSnapshotPerHour_ExistSnapshotPerHour() {
        List<Long> warehouseIds = Arrays.asList(1L, 2L, 3L);
        Date nowHourDate = DateUtil.getNowHourDate();
        when(mockSnapshotTaskMapper
            .selectByWarehouseIdInAndSnapshotTypeAndSnapshotTime(warehouseIds, SnapshotTypeEnum.INVENTORY_HOUR,
                nowHourDate)).thenReturn(Collections.singletonList(new SnapshotTask()));

        try {
            skuInventorySnapshotPerHourServiceImplUnderTest.handleWarehouseSnapshotPerHour(nowHourDate, warehouseIds);
        } catch (Exception e) {
            Assert.assertEquals("已存在相同的快照任务", e.getMessage());
            return;
        }
        Assert.fail();

    }


    /**
     * 验证功能:验证处理仓库小时逻辑快照
     * 场景：小时分片为1，仓库为100
     * 预期结果：
     * 1.插入一条仓库为100，类型为逻辑库存-小时，状态为初始化的快照任务
     * 2.正确执行一次handlePerHourSnapshotTask方法，入参为上面的快照任务
     */
    @Test
    public void testHandleWarehouseSnapshotPerHour() {
        Date nowHourDate = DateUtil.getNowHourDate();
        final SnapshotTask snapshotTask = getSnapshotTask(100L, nowHourDate);
        final List<SnapshotTask> expectedResult = Collections.singletonList(snapshotTask);
        doNothing().when(skuInventorySnapshotPerHourServiceImplUnderTest).handlePerHourSnapshotTask(snapshotTask);

        // Run the test
        skuInventorySnapshotPerHourServiceImplUnderTest
            .handleWarehouseSnapshotPerHour(nowHourDate, Collections.singletonList(100L));

        // Verify the results
        verify(mockSnapshotTaskMapper).batchInsert(expectedResult);
        verify(skuInventorySnapshotPerHourServiceImplUnderTest, times(1))
            .handlePerHourSnapshotTask(snapshotTask);
    }


    /**
     * 验证功能:验证处理仓库小时逻辑快照
     * 场景：逻辑库存小时快照中断中断
     * 预期结果：抛出异常，异常内容为逻辑库存小时快照中断
     */
    @Test
    public void testHandlePerHourSnapshotTask_isInterruptSnapshotPerHour() {

        Date nowHourDate = DateUtil.getNowHourDate();
        final SnapshotTask snapshotTask = getSnapshotTask(100L, nowHourDate);

        when(localParamConfig.isInterruptSnapshotPerHour()).thenReturn(true);

        try {
            skuInventorySnapshotPerHourServiceImplUnderTest.handlePerHourSnapshotTask(snapshotTask);
        } catch (Exception e) {
            Assert.assertEquals("逻辑库存小时快照中断", e.getMessage());
            return;
        }
        Assert.fail();
    }









    private SnapshotTask getSnapshotTask(Long warehouseId, Date nowHourDate) {
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(100L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(nowHourDate);
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);
        return snapshotTask;
    }

}
