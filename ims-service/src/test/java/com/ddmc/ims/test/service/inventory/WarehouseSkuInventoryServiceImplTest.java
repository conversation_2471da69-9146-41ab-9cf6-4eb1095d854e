package com.ddmc.ims.test.service.inventory;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.common.bo.SkuIdAndLotIdPair;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.dal.model.ims.LogicInventoryLocationTemplateDetail;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.ddmc.ims.dto.MultiSkuWarehouseInventoryWrapper;
import com.ddmc.ims.dto.SingleSkuWarehouseInventoryWrapper;
import com.ddmc.ims.manager.inventory.WarehouseInventoryWrapperManager;
import com.ddmc.ims.service.inventory.impl.WarehouseSkuInventoryServiceImpl;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WarehouseSkuInventoryServiceImplTest {

    @Mock
    private WarehouseInventoryWrapperManager mockWarehouseInventoryWrapperManager;

    @InjectMocks
    private WarehouseSkuInventoryServiceImpl warehouseSkuInventoryServiceImplUnderTest;


    @Test
    public void testInventoryChange_RealInventory() {

        final MultiSkuWarehouseInventoryWrapper multiSkuWarehouseInventoryWrapper = baseInventoryChange(
            YesNoEnum.YES);

        List<WarehouseSkuLotInventory> changedWarehouseSkuLotInventories = multiSkuWarehouseInventoryWrapper
            .getChangedWarehouseSkuLotInventories();

        List<WarehouseSkuLotInventory> addWarehouseSkuLotInventories = multiSkuWarehouseInventoryWrapper
            .getAddWarehouseSkuLotInventories();

        List<WarehouseSkuInventory> changedWarehouseSkuInventories = multiSkuWarehouseInventoryWrapper
            .getChangedWarehouseSkuInventories();

        List<WarehouseSkuInventory> addWarehouseSkuInventories = multiSkuWarehouseInventoryWrapper
            .getAddWarehouseSkuInventories();

        Assert.assertEquals(1, changedWarehouseSkuLotInventories.size());
        Assert.assertEquals(5, addWarehouseSkuLotInventories.size());
        Assert.assertEquals(1, changedWarehouseSkuInventories.size());
        Assert.assertEquals(1, addWarehouseSkuInventories.size());
        checkChangedWarehouseSkuLotInventory(changedWarehouseSkuLotInventories.get(0));
        checkAddWarehouseSkuLotInventories(addWarehouseSkuLotInventories);
        checkChangedWarehouseSkuInventories(changedWarehouseSkuInventories.get(0));
        checkAddedWarehouseSkuInventories(addWarehouseSkuInventories.get(0));
    }


    private void checkChangedWarehouseSkuLotInventory(WarehouseSkuLotInventory warehouseSkuLotInventory) {
        Assert.assertTrue(warehouseSkuLotInventory.getFreeQty().equals(new BigDecimal("1.00"))
            && warehouseSkuLotInventory.getFrozenQty().equals(new BigDecimal("1.00"))
            && warehouseSkuLotInventory.getLotId().equals("1")
            && warehouseSkuLotInventory.getSkuId().equals(1L));
    }

    private void checkAddWarehouseSkuLotInventories(List<WarehouseSkuLotInventory> addWarehouseSkuLotInventories) {
        Map<ImmutablePair<Long, String>, WarehouseSkuLotInventory> map = addWarehouseSkuLotInventories.stream()
            .collect(Collectors.toMap(t -> new ImmutablePair<>(t.getSkuId(), t.getLotId()),
                Function.identity()));
        WarehouseSkuLotInventory sku1Lot2 = map.get(new ImmutablePair<>(1L, "2"));
        Assert.assertTrue(sku1Lot2.getFreeQty().equals(new BigDecimal("10"))
            && sku1Lot2.getFrozenQty().equals(new BigDecimal("10"))
            && sku1Lot2.getLotId().equals("2")
            && sku1Lot2.getSkuId().equals(1L));

        WarehouseSkuLotInventory sku1Lot3 = map.get(new ImmutablePair<>(1L, "3"));
        Assert.assertTrue(sku1Lot3.getFreeQty().equals(new BigDecimal(-5))
            && sku1Lot3.getFrozenQty().equals(new BigDecimal(-5))
            && sku1Lot3.getLotId().equals("3")
            && sku1Lot3.getSkuId().equals(1L));
    }

    private void checkChangedWarehouseSkuInventories(WarehouseSkuInventory warehouseSkuInventory) {
        Assert.assertTrue(warehouseSkuInventory.getFreeQty().equals(new BigDecimal("6.00"))
            && warehouseSkuInventory.getFrozenQty().equals(new BigDecimal("6.00"))
            && warehouseSkuInventory.getSkuId().equals(1L));

    }

    private void checkAddedWarehouseSkuInventories(WarehouseSkuInventory warehouseSkuInventory) {
        Assert.assertTrue(warehouseSkuInventory.getFreeQty().equals(new BigDecimal("6"))
            && warehouseSkuInventory.getFrozenQty().equals(new BigDecimal("6"))
            && warehouseSkuInventory.getSkuId().equals(2L));

    }



    private MultiSkuWarehouseInventoryWrapper baseInventoryChange(YesNoEnum realInventory) {
        // Setup
        final InventoryChange inventoryChange = getInventoryChange();
        final MultiSkuWarehouseInventoryWrapper multiSkuWarehouseInventoryWrapper = getMultiSkuWarehouseInventoryWrapper(
            realInventory);


        List<SkuIdAndLotIdPair> pair = inventoryChange.getInventoryChangeItemList().stream()
            .map(t -> new SkuIdAndLotIdPair(t.getSkuId(), t.getLotId())).collect(Collectors.toList());

        when(mockWarehouseInventoryWrapperManager
            .loadSkuAndLotInventory(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION, pair, realInventory))
            .thenReturn(multiSkuWarehouseInventoryWrapper);

        // Run the test
        warehouseSkuInventoryServiceImplUnderTest.changInventory(inventoryChange);

        // Verify the results
        verify(mockWarehouseInventoryWrapperManager).saveInventory(any(MultiSkuWarehouseInventoryWrapper.class));
        return multiSkuWarehouseInventoryWrapper;
    }

    private MultiSkuWarehouseInventoryWrapper getMultiSkuWarehouseInventoryWrapper(YesNoEnum realInventory) {
        final LogicInventoryLocationTemplateDetail logicInventoryLocationTemplateDetail = new LogicInventoryLocationTemplateDetail();
        logicInventoryLocationTemplateDetail.setRefRealInventory(realInventory);
        final WarehouseSkuInventory warehouseSkuInventory = getWarehouseSkuInventory();
        final WarehouseSkuLotInventory warehouseSkuLotInventory = getWarehouseSkuLotInventory();

        return new MultiSkuWarehouseInventoryWrapper(
            Collections.singletonList(new SingleSkuWarehouseInventoryWrapper(TestConstants.getLogicInventoryLocationWithSku(warehouseSkuInventory),Collections.singletonList(warehouseSkuInventory),
                Collections.singletonList(warehouseSkuLotInventory), Collections.emptyList())));
    }


    private InventoryChange getInventoryChange() {
        final InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION);
        List<InventoryChangeItem> itemList = getInventoryChangeItems();
        inventoryChange.setInventoryChangeItemList(itemList);
        return inventoryChange;
    }

    private WarehouseSkuLotInventory getWarehouseSkuLotInventory() {
        final WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setLogicInventoryLocationCode(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getLogicInventoryLocationCode());
        warehouseSkuLotInventory.setSkuId(1L);
        warehouseSkuLotInventory.setWarehouseId(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getWarehouseId());
        warehouseSkuLotInventory.setCargoOwnerId(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getCargoOwnerId());
        warehouseSkuLotInventory.setLotId("1");
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuLotInventory.setVersion(0);
        return warehouseSkuLotInventory;
    }

    private WarehouseSkuInventory getWarehouseSkuInventory() {
        final WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getLogicInventoryLocationCode());
        warehouseSkuInventory.setSkuId(1L);
        warehouseSkuInventory.setWarehouseId(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getWarehouseId());
        warehouseSkuInventory.setCargoOwnerId(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getCargoOwnerId());
        warehouseSkuInventory.setFreeQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setFrozenQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setUsageCode(TestConstants.USAGE);
        return warehouseSkuInventory;
    }

    private List<InventoryChangeItem> getInventoryChangeItems() {
        List<InventoryChangeItem> itemList = new ArrayList<>();
        itemList.add(InventoryChangeItem.builder().skuId(1L).lotId("1").qty(new BigDecimal(1))
            .usage(TestConstants.USAGE)
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE).inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
            .build());
        itemList.add(InventoryChangeItem.builder().skuId(1L).lotId("2").qty(new BigDecimal(10))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE).inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
            .usage(TestConstants.USAGE)
            .build());
        itemList.add(InventoryChangeItem.builder().skuId(1L).lotId("3").qty(new BigDecimal(5))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE).usage(TestConstants.USAGE)
            .inventoryOperateTypeEnum(InventoryOperateTypeEnum.DECREASE).build());
        itemList.add(InventoryChangeItem.builder().skuId(1L).lotId("1").qty(new BigDecimal(1))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FROZEN).inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
            .usage(TestConstants.USAGE)
            .build());
        itemList.add(InventoryChangeItem.builder().skuId(1L).lotId("2").qty(new BigDecimal(10))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FROZEN).inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
            .usage(TestConstants.USAGE)
            .build());
        itemList.add(InventoryChangeItem.builder().skuId(1L).lotId("3").qty(new BigDecimal(5))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FROZEN).usage(TestConstants.USAGE)
            .inventoryOperateTypeEnum(InventoryOperateTypeEnum.DECREASE).build());

        itemList.add(InventoryChangeItem.builder().skuId(2L).lotId("1").qty(new BigDecimal(1))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE).inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
            .usage(TestConstants.USAGE)
            .build());
        itemList.add(InventoryChangeItem.builder().skuId(2L).lotId("2").qty(new BigDecimal(10))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE).inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
            .usage(TestConstants.USAGE)
            .build());
        itemList.add(InventoryChangeItem.builder().skuId(2L).lotId("3").qty(new BigDecimal(5))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE).usage(TestConstants.USAGE)
            .inventoryOperateTypeEnum(InventoryOperateTypeEnum.DECREASE).build());
        itemList.add(InventoryChangeItem.builder().skuId(2L).lotId("1").qty(new BigDecimal(1))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FROZEN).inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
            .usage(TestConstants.USAGE)
            .build());
        itemList.add(InventoryChangeItem.builder().skuId(2L).lotId("2").qty(new BigDecimal(10))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FROZEN).inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
            .usage(TestConstants.USAGE)
            .build());
        itemList.add(InventoryChangeItem.builder().skuId(2L).lotId("3").qty(new BigDecimal(5))
            .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FROZEN)
            .usage(TestConstants.USAGE)
            .inventoryOperateTypeEnum(InventoryOperateTypeEnum.DECREASE).build());
        return itemList;
    }
}
