package com.ddmc.ims.test.converter;

import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.converter.usage.OutStoreAdjustWarehouseUsageDetailConverter;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.test.common.TestConstants;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class OutStoreAdjustWarehouseUsageDetailConverterTest {

    @InjectMocks
    private OutStoreAdjustWarehouseUsageDetailConverter outStoreAdjustWarehouseUsageDetailConverter;
    @Mock
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;

    /**
     * 验证调整逻辑 当库存存在，优先级库存不充足
     */
    @Test
    public void test6() {
        CredentialHeader credentialHeader = getCredentialHeader();
        CredentialDetail credentialDetail = getCredentialDetail(1L, "EU,TB", new BigDecimal(10),
            InventoryStatusEnum.NOT_AVAILABLE);
        credentialHeader.setCredentialDetailList(Lists.newArrayList(credentialDetail));
        WarehouseSkuInventory warehouseSkuInventory = getWarehouseSkuInventory("EU",
            BigDecimal.ZERO, new BigDecimal(6));
        WarehouseSkuInventory warehouseSkuInventory1 = getWarehouseSkuInventory("TB",
            BigDecimal.ZERO, new BigDecimal(3));
        List<WarehouseSkuInventory> warehouseSkuInventories = Lists.newArrayList(warehouseSkuInventory,warehouseSkuInventory1);
        when(warehouseSkuInventoryMapper
            .selectWarehouseIdAndSkuIds(2L, Collections.singleton(TestConstants.COMMON_LOGIC),
                Lists.newArrayList(1L)))
            .thenReturn(warehouseSkuInventories);
        List<CredentialUseageDetail> credentialUseageDetailList = outStoreAdjustWarehouseUsageDetailConverter
            .convert(ConfirmContext.builder().credentialHeader(credentialHeader).build());
        Assert.assertEquals(2, credentialUseageDetailList.size());
        Assert.assertEquals("EU", credentialUseageDetailList.get(0).getUsageCode());
        Assert.assertEquals(new BigDecimal(6), credentialUseageDetailList.get(0).getQty());
        Assert.assertEquals("TB", credentialUseageDetailList.get(1).getUsageCode());
        Assert.assertEquals(new BigDecimal(4), credentialUseageDetailList.get(1).getQty());
    }

    public WarehouseSkuInventory getWarehouseSkuInventory(String usageCode, BigDecimal freeQty, BigDecimal frozenQty) {
        WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setWarehouseId(2L);
        warehouseSkuInventory.setFreeQty(freeQty);
        warehouseSkuInventory.setFrozenQty(frozenQty);
        warehouseSkuInventory.setUsageCode(usageCode);
        warehouseSkuInventory.setSkuId(1L);
        warehouseSkuInventory.setCargoOwnerId(1L);
        warehouseSkuInventory.setLogicInventoryLocationCode(TestConstants.COMMON_LOGIC);
        return warehouseSkuInventory;

    }


    public CredentialHeader getCredentialHeader() {
        CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setOrderOperateType(OrderOperateTypeEnum.ADJUSTMENT.getCode());
        credentialHeader.setOrderType(OrderTypeEnum.AGRICULTURAL_MATERIAL_OUTBOUND.getCode());
        credentialHeader.setId(1L);
        credentialHeader.setWarehouseId(2L);
        return credentialHeader;
    }

    public CredentialDetail getCredentialDetail(Long skuId, String usageCode, BigDecimal qty,
        InventoryStatusEnum inventoryStatusEnum) {
        CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setUsageCode(usageCode);
        credentialDetail.setCredentialHeaderId(1L);
        credentialDetail.setFromWarehouseId(2L);
        credentialDetail.setQty(qty);
        credentialDetail.setSkuId(skuId);
        credentialDetail.setFromCargoOwnerId(1L);
        credentialDetail.setFromLogicLocationCode(TestConstants.COMMON_LOGIC);
        credentialDetail.setInventoryStatus(inventoryStatusEnum);
        return credentialDetail;
    }


}
