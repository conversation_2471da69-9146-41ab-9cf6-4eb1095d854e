package com.ddmc.ims.test.converter;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;

import com.ddmc.ims.bo.snapshot.SnapshotItem;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.converter.SnapshotConverter;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Test;

public class SnapshotConverterTest {

    /**
     * 验证功能：验证转换为SkuInventorySnapshotPerHour
     * 场景：入参lastSnapshotList为空。commandInventoryNumList不为空
     * 预期结果：正确转换commandInventoryNumList成SkuInventorySnapshotPerHour
     */
    @Test
    public void testConvertToSkuInventorySnapshotPerHour_lastSnapshotListIsEmpty() {

        //设定入参lastSnapshotList为空
        final List<SkuInventorySnapshotPerHour> lastSnapshotList = Collections.emptyList();


        final CommandInventoryNumDto c1 = new CommandInventoryNumDto();
        c1.setLocation(new LogicInventoryLocation(100L, 10L, "1001"));
        c1.setSkuId(1L);
        c1.setLotId("123");
        c1.setFreeQty(new BigDecimal("10.00"));
        c1.setFrozenQty(new BigDecimal("20.00"));
        c1.setTransferIntransitQty(new BigDecimal("30.00"));


        final CommandInventoryNumDto c2 = new CommandInventoryNumDto();
        c2.setLocation(new LogicInventoryLocation(100L, 10L, "1001"));
        c2.setSkuId(1L);
        c2.setLotId("456");
        c2.setFreeQty(new BigDecimal("15.00"));
        c2.setFrozenQty(new BigDecimal("25.00"));
        c2.setTransferIntransitQty(new BigDecimal("35.00"));



        final List<CommandInventoryNumDto> commandInventoryNumList = Collections.singletonList(c1);
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setSkuId(1L);
        skuInventorySnapshotPerHour1.setWarehouseId(100L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("1001");
        skuInventorySnapshotPerHour1.setCargoOwnerId(10L);
        skuInventorySnapshotPerHour1.setLotId("123");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("10.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("20.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("30.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> expectedResult = Collections
            .singletonList(skuInventorySnapshotPerHour1);

        // Run the test
        final List<SkuInventorySnapshotPerHour> result = SnapshotConverter
            .convertToSkuInventorySnapshotPerHour(lastSnapshotList, commandInventoryNumList,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }



    @Test
    public void testConvertToSkuInventorySnapshotPerHour2() {
        // Create a list of last snapshot data
        SkuInventorySnapshotPerHour lastSnapshot = new SkuInventorySnapshotPerHour();
        lastSnapshot.setSkuId(1L);
        lastSnapshot.setWarehouseId(1L);
        lastSnapshot.setLogicLocationCode("A1");
        lastSnapshot.setCargoOwnerId(1L);
        lastSnapshot.setLotId("Lot1");
        lastSnapshot.setFreeQty(new BigDecimal("10"));
        lastSnapshot.setFrozenQty(new BigDecimal("5"));
        lastSnapshot.setTransferIntransitQty(new BigDecimal("2"));
        lastSnapshot.setSnapshotDateTime(new Date());

        List<SkuInventorySnapshotPerHour> lastSnapshotList = Collections.singletonList(lastSnapshot);

        // Create a list of command inventory data
        CommandInventoryNumDto commandInventory = new CommandInventoryNumDto();
        commandInventory.setLocation(new LogicInventoryLocation(1L, 1L, "A1"));
        commandInventory.setSkuId(1L);
        commandInventory.setLotId("Lot1");
        commandInventory.setFreeQty(new BigDecimal("5"));
        commandInventory.setFrozenQty(new BigDecimal("2"));
        commandInventory.setTransferIntransitQty(new BigDecimal("1"));

        List<CommandInventoryNumDto> commandInventoryNumList = Collections.singletonList(commandInventory);

        // Call the method being tested
        Date snapshotDateTime = new Date();
        List<SkuInventorySnapshotPerHour> result = SnapshotConverter.convertToSkuInventorySnapshotPerHour(
            lastSnapshotList, commandInventoryNumList, snapshotDateTime);

        // Verify the result
        assertEquals(1, result.size());

        SkuInventorySnapshotPerHour expected = new SkuInventorySnapshotPerHour();
        expected.setId(null);
        expected.setSkuId(1L);
        expected.setWarehouseId(1L);
        expected.setLogicLocationCode("A1");
        expected.setCargoOwnerId(1L);
        expected.setLotId("Lot1");
        expected.setFreeQty(new BigDecimal("15"));
        expected.setFrozenQty(new BigDecimal("7"));
        expected.setTransferIntransitQty(new BigDecimal("3"));
        expected.setSnapshotDateTime(snapshotDateTime);
        expected.setCreateTime(null);

        assertEquals(expected, result.get(0));
    }


    @Test
    public void testConvertToSkuInventorySnapshotPerHour() {
        // Setup
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour.setId(0L);
        skuInventorySnapshotPerHour.setSkuId(0L);
        skuInventorySnapshotPerHour.setWarehouseId(0L);
        skuInventorySnapshotPerHour.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour.setLotId("lotId");
        skuInventorySnapshotPerHour.setFreeQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> lastSnapshotList = Arrays.asList(skuInventorySnapshotPerHour);
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("1.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> commandInventoryNumList = Arrays.asList(commandInventoryNumDto);
        final SkuInventorySnapshotPerHour skuInventorySnapshotPerHour1 = new SkuInventorySnapshotPerHour();
        skuInventorySnapshotPerHour1.setId(0L);
        skuInventorySnapshotPerHour1.setSkuId(0L);
        skuInventorySnapshotPerHour1.setWarehouseId(0L);
        skuInventorySnapshotPerHour1.setLogicLocationCode("logicInventoryLocationCode");
        skuInventorySnapshotPerHour1.setCargoOwnerId(0L);
        skuInventorySnapshotPerHour1.setLotId("lotId");
        skuInventorySnapshotPerHour1.setFreeQty(new BigDecimal("1.00"));
        skuInventorySnapshotPerHour1.setFrozenQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setTransferIntransitQty(new BigDecimal("0.00"));
        skuInventorySnapshotPerHour1.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        skuInventorySnapshotPerHour1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<SkuInventorySnapshotPerHour> expectedResult = Arrays.asList(skuInventorySnapshotPerHour1);

        // Run the test
        final List<SkuInventorySnapshotPerHour> result = SnapshotConverter
            .convertToSkuInventorySnapshotPerHour(lastSnapshotList, commandInventoryNumList,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetSnapshotItem1() {
        // Setup
        final SkuInventorySnapshotPerHour t = new SkuInventorySnapshotPerHour();
        t.setId(0L);
        t.setSkuId(0L);
        t.setWarehouseId(0L);
        t.setLogicLocationCode("logicInventoryLocationCode");
        t.setCargoOwnerId(0L);
        t.setLotId("lotId");
        t.setFreeQty(new BigDecimal("0.00"));
        t.setFrozenQty(new BigDecimal("0.00"));
        t.setTransferIntransitQty(new BigDecimal("0.00"));
        t.setSnapshotDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        t.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final SnapshotItem expectedResult = new SnapshotItem(0L,  "lotId",0L, "logicInventoryLocationCode", 0L);

        // Run the test
        final SnapshotItem result = SnapshotConverter.getSnapshotItem(t);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetSnapshotItem2() {
        // Setup
        final CommandInventoryNumDto item = new CommandInventoryNumDto();
        item.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        item.setSkuId(0L);
        item.setLotId("lotId");
        item.setFreeQty(new BigDecimal("0.00"));
        item.setFrozenQty(new BigDecimal("0.00"));
        item.setTransferIntransitQty(new BigDecimal("0.00"));

        final SnapshotItem expectedResult = new SnapshotItem(0L, "lotId",0L, "logicInventoryLocationCode", 0L);

        // Run the test
        final SnapshotItem result = SnapshotConverter.getSnapshotItemByCommandDto(item);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    /**
     * 验证功能：测试转换SnapshotTask功能
     * 场景：无
     * 预期结果：转换后的值与期望值一致
     */
    @Test
    public void testConvertToSnapshotTask() {
        // Setup
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(0L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.FMS);
        snapshotTask.setSnapshotTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);
        final List<SnapshotTask> expectedResult = Collections.singletonList(snapshotTask);

        // Run the test
        final List<SnapshotTask> result = SnapshotConverter.convertToSnapshotTask(
            Collections.singletonList(0L), new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
            SnapshotTypeEnum.FMS);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

}
