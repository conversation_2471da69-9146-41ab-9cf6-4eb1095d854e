package com.ddmc.ims.test.manager;

import com.ddmc.ims.bo.command.SkuInventoryCommand;
import com.ddmc.ims.bo.command.WarehouseInventoryCommand;
import com.ddmc.ims.command.CommandHandleFactory;
import com.ddmc.ims.command.WarehouseInventoryModifyAvailableCommandHandle;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.service.credential.InventoryCredentialService;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class CommandManagerTest {

    @InjectMocks
    private CommandManager commandManager;
    @Mock
    private InventoryCredentialService inventoryCredentialService;
    @Mock
    private WarehouseInventoryModifyAvailableCommandHandle warehouseInventoryModifyAvailableCommandHandle;
    @Mock
    private CommandHandleFactory commandHandleFactory;

    @Test
    public void test() {
        CredentialHeader credentialHeader = getCredentialHeader();
        SkuInventoryCommand skuInventoryCommand = getCommand(1L, "1", BigDecimal.ONE, InventoryStatusEnum.AVAILABLE);
        List<SkuInventoryCommand> skuInventoryCommands = Lists.newArrayList(skuInventoryCommand);
        List<CredentialHeader> credentialHeaderAndDetails = Lists.newArrayList(credentialHeader);
        Mockito.doReturn(skuInventoryCommands)
            .when(inventoryCredentialService).getSkuInventoryCommand(credentialHeader);
        Mockito.doReturn(warehouseInventoryModifyAvailableCommandHandle).when(commandHandleFactory).getCommandHandle(WarehouseInventoryModifyAvailableCommandHandle.class);
        CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        List<CommandInventoryNumDto> list = Lists.newArrayList(commandInventoryNumDto);
        Mockito.doReturn(list).when(warehouseInventoryModifyAvailableCommandHandle).getCommandInventoryNum(Mockito.any());

        commandManager
            .getCommandInventoryNumList(credentialHeaderAndDetails);
        Mockito.verify(warehouseInventoryModifyAvailableCommandHandle,Mockito.times(1)).getCommandInventoryNum(Mockito.any());
    }


    private CredentialHeader getCredentialHeader() {
        CredentialHeader header = new CredentialHeader();
        header.setIsStockChange(YesNoEnum.NO);
        header.setId(2L);
        header.setOrderNo("orderNo");
        header.setOrderSource("orderSource");
        header.setBusinessTime(new Date());
        header.setEndDateTime(new Date());
        return header;
    }

    private WarehouseInventoryCommand getCommand(Long skuId, String lotId, BigDecimal qty, InventoryStatusEnum status) {
        return WarehouseInventoryCommand.builder()
            .toLocation(TestConstants.EMPTY_LOGIC_INVENTORY_LOCATION)
            .fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .commandType(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE)
            .fromInventoryStatus(status)
            .skuId(skuId)
            .lotId(lotId)
            .qty(qty)
            .build();
    }
}
