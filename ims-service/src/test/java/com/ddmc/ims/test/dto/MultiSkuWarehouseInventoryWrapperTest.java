package com.ddmc.ims.test.dto;

import com.ddmc.ims.common.bo.LogicInventoryLocationWithLot;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithSku;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.ddmc.ims.dto.MultiSkuWarehouseInventoryWrapper;
import com.ddmc.ims.dto.SingleSkuWarehouseInventoryWrapper;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;

@Slf4j
public class MultiSkuWarehouseInventoryWrapperTest {

    /**
     * 已存在的批次增加库存
     */
    @Test
    public void testChangeWarehouseInventoryForLot() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper();

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        LogicInventoryLocationWithLot logicInventoryLocationWithLot = new LogicInventoryLocationWithLot(
            singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku().getLogicInventoryLocation(),
            singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku().getSkuId(), "1","");
        warehouseInventoryWrapper
            .changeWarehouseInventoryWithLot(logicInventoryLocationWithLot, new BigDecimal("1"), BigDecimal.ZERO);

        warehouseInventoryWrapper
            .changeWarehouseInventoryWithLot(logicInventoryLocationWithLot, new BigDecimal("1"), BigDecimal.ZERO);
        //
        Assert.assertEquals("货品维度库存应该为4.12", 0, singleSkuWarehouseInventoryWrapper.getWarehouseSkuUseageInventory("")
            .getFreeQty().compareTo(new BigDecimal("4.12")));
        Assert.assertEquals("货品批次库存应该为4.12", 0, singleSkuWarehouseInventoryWrapper.getWarehouseSkuLotInventory("1")
            .getFreeQty().compareTo(new BigDecimal("4.12")));
        Assert.assertEquals("变更的商品数量是1", 1, warehouseInventoryWrapper.getChangedWarehouseSkuInventories().size());
        Assert.assertEquals("新增的批次数量是1", 1, warehouseInventoryWrapper.getChangedWarehouseSkuLotInventories().size());
        Assert.assertEquals("新增的商品数量是0", 0, warehouseInventoryWrapper.getAddWarehouseSkuInventories().size());
        Assert.assertEquals("新增的批次数量是0", 0, warehouseInventoryWrapper.getAddWarehouseSkuLotInventories().size());
    }

    private SingleSkuWarehouseInventoryWrapper buildSingleSkuWarehouseInventoryWrapper() {
        WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setWarehouseId(1L);
        warehouseSkuInventory.setSkuId(123L);
        warehouseSkuInventory.setLogicInventoryLocationCode("0001");
        warehouseSkuInventory.setCargoOwnerId(1L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("2.12"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("1.12"));
        warehouseSkuInventory.setFrozenQty(BigDecimal.ZERO);
        warehouseSkuInventory.setVersion(1);
        warehouseSkuInventory.setUsageCode("");

        WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setWarehouseId(1L);
        warehouseSkuLotInventory.setSkuId(123L);
        warehouseSkuLotInventory.setLotId("1");
        warehouseSkuLotInventory.setLogicInventoryLocationCode("0001");
        warehouseSkuLotInventory.setCargoOwnerId(1L);
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("2.12"));
        warehouseSkuLotInventory.setFrozenQty(BigDecimal.ZERO);
        warehouseSkuLotInventory.setVersion(1);

        InventoryLotInfo inventoryLotInfo = new InventoryLotInfo();
        inventoryLotInfo.setLotId("1");
        inventoryLotInfo.setUnsalableDate(ThreadLocalDateUtils.parseYmd("2022-12-02"));

        return new SingleSkuWarehouseInventoryWrapper(TestConstants.getLogicInventoryLocationWithSku(warehouseSkuInventory), Collections.singletonList(warehouseSkuInventory),
            Lists.newArrayList(warehouseSkuLotInventory), Lists.newArrayList(inventoryLotInfo));
    }

    private SingleSkuWarehouseInventoryWrapper buildSingleSkuWarehouseInventoryWrapperWithOutLot() {
        WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setWarehouseId(1L);
        warehouseSkuInventory.setSkuId(123L);
        warehouseSkuInventory.setLogicInventoryLocationCode("0001");
        warehouseSkuInventory.setCargoOwnerId(1L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("2.12"));
        warehouseSkuInventory.setAllocQty(BigDecimal.ZERO);
        warehouseSkuInventory.setFrozenQty(BigDecimal.ZERO);
        warehouseSkuInventory.setVersion(1);
        warehouseSkuInventory.setUsageCode("");
        return new SingleSkuWarehouseInventoryWrapper(TestConstants.getLogicInventoryLocationWithSku(warehouseSkuInventory), Collections.singletonList(warehouseSkuInventory),
            Lists.newArrayList(), Collections.emptyList());
    }

    /**
     * 已存在的品增加库存
     */
    @Test
    public void testChangeWarehouseInventoryForSku() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper();
        WarehouseSkuInventory warehouseSkuInventory = singleSkuWarehouseInventoryWrapper.getWarehouseSkuUseageInventory("");

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        LogicInventoryLocationWithSku logicInventoryLocationWithSku = singleSkuWarehouseInventoryWrapper
            .getLogicInventoryLocationWithSku();
        warehouseInventoryWrapper
            .changeWarehouseInventory(logicInventoryLocationWithSku,"", new BigDecimal("1"), new BigDecimal("1.1"),
                new BigDecimal("2.2"));
        warehouseInventoryWrapper
            .changeWarehouseInventory(logicInventoryLocationWithSku,"", new BigDecimal("1"), new BigDecimal("1.1"),
                new BigDecimal("2.2"));

        Assert.assertEquals("货品可用库存应该为4.12", 0, warehouseSkuInventory.getFreeQty().compareTo(new BigDecimal("4.12")));
        Assert.assertEquals("货品冻结库存应该为2.2", 0, warehouseSkuInventory.getFrozenQty().compareTo(new BigDecimal("2.2")));
        Assert.assertEquals("货品占用库存应该为4.4", 0, warehouseSkuInventory.getAllocQty().compareTo(new BigDecimal("5.52")));

        Assert.assertEquals("变更的商品数量是1", 1, warehouseInventoryWrapper.getChangedWarehouseSkuInventories().size());
        Assert.assertEquals("新增的批次数量是1", 0, warehouseInventoryWrapper.getChangedWarehouseSkuLotInventories().size());
        Assert.assertEquals("新增的商品数量是0", 0, warehouseInventoryWrapper.getAddWarehouseSkuInventories().size());
        Assert.assertEquals("新增的批次数量是0", 0, warehouseInventoryWrapper.getAddWarehouseSkuLotInventories().size());
    }

    /**
     * 新增批次库存
     */
    @Test
    public void testAddWarehouseSkuLotInventory() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper();
        WarehouseSkuInventory warehouseSkuInventory = singleSkuWarehouseInventoryWrapper.getWarehouseSkuUseageInventory("");

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        LogicInventoryLocationWithLot logicInventoryLocationWithLot = new LogicInventoryLocationWithLot(
            singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku().getLogicInventoryLocation(),
            234L, "2","");
        warehouseInventoryWrapper
            .changeWarehouseInventoryWithLot(logicInventoryLocationWithLot, new BigDecimal("1"), BigDecimal.ZERO);

        warehouseInventoryWrapper
            .changeWarehouseInventoryWithLot(logicInventoryLocationWithLot, new BigDecimal("1"), BigDecimal.ZERO);

        Assert.assertEquals("货品维度库存应该为2.12", 0, warehouseSkuInventory.getFreeQty().compareTo(new BigDecimal("2.12")));
        Assert.assertEquals("货品批次1库存应该为2.12", 0, singleSkuWarehouseInventoryWrapper.getWarehouseSkuLotInventory("1")
            .getFreeQty().compareTo(new BigDecimal("2.12")));

        Assert.assertEquals("新增1个品", 1, warehouseInventoryWrapper.getAddWarehouseSkuInventories().size());
        Assert.assertEquals("新增1个批次", 1, warehouseInventoryWrapper.getAddWarehouseSkuLotInventories().size());

        Assert.assertEquals("变更的商品数量是0", 0, warehouseInventoryWrapper.getChangedWarehouseSkuInventories().size());
        Assert.assertEquals("新增的批次数量是0", 0, warehouseInventoryWrapper.getChangedWarehouseSkuLotInventories().size());
        Assert.assertEquals("新增的商品数量是1", 1, warehouseInventoryWrapper.getAddWarehouseSkuInventories().size());
        Assert.assertEquals("新增的批次数量是1", 1, warehouseInventoryWrapper.getAddWarehouseSkuLotInventories().size());
    }

    /**
     * 新增货品库存
     */
    @Test
    public void testAddWarehouseSkuInventory() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapperWithOutLot();
        WarehouseSkuInventory warehouseSkuInventory = singleSkuWarehouseInventoryWrapper.getWarehouseSkuUseageInventory("");

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        LogicInventoryLocationWithSku logicInventoryLocationWithSku = new LogicInventoryLocationWithSku(
            singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku().getLogicInventoryLocation(),
            234L);
        warehouseInventoryWrapper
            .changeWarehouseInventory(logicInventoryLocationWithSku,"", new BigDecimal("1"), BigDecimal.ZERO,
                BigDecimal.ZERO);

        LogicInventoryLocationWithSku logicInventoryLocationWithSku123 = new LogicInventoryLocationWithSku(
            singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku().getLogicInventoryLocation(),
            123L);
        warehouseInventoryWrapper
            .changeWarehouseInventory(logicInventoryLocationWithSku123,"", new BigDecimal("2"), BigDecimal.ZERO,
                BigDecimal.ZERO);

        Assert
            .assertEquals("货品123维度库存应该为4.12", 0, warehouseSkuInventory.getFreeQty().compareTo(new BigDecimal("4.12")));

        Assert.assertEquals("变更的商品数量是1", 1, warehouseInventoryWrapper.getChangedWarehouseSkuInventories().size());
        Assert.assertEquals("新增的批次数量是1", 0, warehouseInventoryWrapper.getChangedWarehouseSkuLotInventories().size());
        Assert.assertEquals("新增的商品数量是1", 1, warehouseInventoryWrapper.getAddWarehouseSkuInventories().size());
        Assert.assertEquals("新增的批次数量是1", 0, warehouseInventoryWrapper.getAddWarehouseSkuLotInventories().size());
    }

    /**
     * 测试货品品维度的可用库存
     */
    @Test
    public void testGetFreeQtyAfterAlloc() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper();

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));
        BigDecimal freeQty = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku());
        Assert.assertEquals("可用库存应该为2.12-1.12 = 1", 0, new BigDecimal("1").compareTo(freeQty));
    }

    /**
     * 没有可用的效期库存
     */
    @Test
    public void testGetFreeQtyAfterAlloc2() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper();

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        Date demandDate = ThreadLocalDateUtils.parseYmd("2022-12-03");
        BigDecimal freeQty = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                demandDate, Lists.newArrayList());
        Assert.assertEquals("2022-12-03可用效期库存为0", 0, BigDecimal.ZERO.compareTo(freeQty));
    }


    /**
     * 批次可占用库存>货品可占用库存时，取小值
     * 库存分布：
     * 货品维度 2.12（可用），1.12（占用）
     * 批次维度
     * 2022-12-02 2.12
     * 批次可占用库存
     * 2022-12-02 1
     */
    @Test
    public void testGetFreeQtyAfterAlloc3() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper();

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        Date demandDate = ThreadLocalDateUtils.parseYmd("2022-12-02");
        BigDecimal freeQty = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                demandDate, Lists.newArrayList());
        Assert.assertEquals("2022-12-02可用效期库存为1", 0, new BigDecimal("1").compareTo(freeQty));
    }

    private SingleSkuWarehouseInventoryWrapper buildSingleSkuWarehouseInventoryWrapper2() {
        WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setWarehouseId(1L);
        warehouseSkuInventory.setSkuId(123L);
        warehouseSkuInventory.setLogicInventoryLocationCode("0001");
        warehouseSkuInventory.setCargoOwnerId(1L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("2.12"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("3.00"));
        warehouseSkuInventory.setFrozenQty(BigDecimal.ZERO);
        warehouseSkuInventory.setVersion(1);
        warehouseSkuInventory.setUsageCode("");

        WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setWarehouseId(1L);
        warehouseSkuLotInventory.setSkuId(123L);
        warehouseSkuLotInventory.setLotId("1");
        warehouseSkuLotInventory.setLogicInventoryLocationCode("0001");
        warehouseSkuLotInventory.setCargoOwnerId(1L);
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("2.12"));
        warehouseSkuLotInventory.setFrozenQty(BigDecimal.ZERO);
        warehouseSkuLotInventory.setVersion(1);

        InventoryLotInfo inventoryLotInfo = new InventoryLotInfo();
        inventoryLotInfo.setLotId("1");
        inventoryLotInfo.setUnsalableDate(ThreadLocalDateUtils.parseYmd("2022-12-02"));
        return new SingleSkuWarehouseInventoryWrapper(TestConstants.getLogicInventoryLocationWithSku(warehouseSkuInventory),Collections.singletonList(warehouseSkuInventory),
            Lists.newArrayList(warehouseSkuLotInventory), Lists.newArrayList(inventoryLotInfo));
    }

    /**
     * 货品维度占用库存大于可用库存，效期库存为0
     */
    @Test
    public void testGetFreeQtyAfterAlloc4() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper2();

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        Date demandDate = ThreadLocalDateUtils.parseYmd("2022-12-01");
        BigDecimal freeQty = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                demandDate, Lists.newArrayList());
        Assert.assertEquals("货品维度占用库存大于可用库存，效期库存为0", 1, BigDecimal.ZERO.compareTo(freeQty));
    }

    private SingleSkuWarehouseInventoryWrapper buildSingleSkuWarehouseInventoryWrapper3() {
        WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setWarehouseId(1L);
        warehouseSkuInventory.setSkuId(123L);
        warehouseSkuInventory.setLogicInventoryLocationCode("0001");
        warehouseSkuInventory.setCargoOwnerId(1L);
        warehouseSkuInventory.setFreeQty(new BigDecimal("2.12"));
        warehouseSkuInventory.setAllocQty(new BigDecimal("2.12"));
        warehouseSkuInventory.setFrozenQty(BigDecimal.ZERO);
        warehouseSkuInventory.setVersion(1);
        warehouseSkuInventory.setUsageCode("");

        WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setWarehouseId(1L);
        warehouseSkuLotInventory.setSkuId(123L);
        warehouseSkuLotInventory.setLotId("1");
        warehouseSkuLotInventory.setLogicInventoryLocationCode("0001");
        warehouseSkuLotInventory.setCargoOwnerId(1L);
        warehouseSkuLotInventory.setFreeQty(new BigDecimal("2.12"));
        warehouseSkuLotInventory.setFrozenQty(BigDecimal.ZERO);
        warehouseSkuLotInventory.setVersion(1);

        InventoryLotInfo inventoryLotInfo = new InventoryLotInfo();
        inventoryLotInfo.setLotId("1");
        inventoryLotInfo.setUnsalableDate(ThreadLocalDateUtils.parseYmd("2022-12-02"));
        return new SingleSkuWarehouseInventoryWrapper(TestConstants.getLogicInventoryLocationWithSku(warehouseSkuInventory),Collections.singletonList(warehouseSkuInventory),
            Lists.newArrayList(warehouseSkuLotInventory), Lists.newArrayList(inventoryLotInfo));
    }

    /**
     * 货品维度占用库存等于可用库存，效期库存为0
     */
    @Test
    public void testGetFreeQtyAfterAlloc5() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper3();

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        Date demandDate = ThreadLocalDateUtils.parseYmd("2022-12-01");
        BigDecimal freeQty = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                demandDate, Lists.newArrayList());
        Assert.assertEquals("货品维度占用库存大于可用库存，效期库存为0", 0, BigDecimal.ZERO.compareTo(freeQty));
    }

    private WarehouseSkuInventory newWarehouseSkuInventory(Long skuId, BigDecimal freeQty, BigDecimal allocQty) {
        WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setWarehouseId(1L);
        warehouseSkuInventory.setSkuId(skuId);
        warehouseSkuInventory.setLogicInventoryLocationCode("0001");
        warehouseSkuInventory.setCargoOwnerId(1L);
        warehouseSkuInventory.setFreeQty(freeQty);
        warehouseSkuInventory.setAllocQty(allocQty);
        warehouseSkuInventory.setUsageCode("");
        return warehouseSkuInventory;
    }

    private WarehouseSkuLotInventory newWarehouseSkuLotInventory(Long skuId, String lotId, BigDecimal freeQty) {
        WarehouseSkuLotInventory warehouseSkuLotInventory = new WarehouseSkuLotInventory();
        warehouseSkuLotInventory.setWarehouseId(1L);
        warehouseSkuLotInventory.setSkuId(skuId);
        warehouseSkuLotInventory.setLotId(lotId);
        warehouseSkuLotInventory.setLogicInventoryLocationCode("0001");
        warehouseSkuLotInventory.setCargoOwnerId(1L);
        warehouseSkuLotInventory.setFreeQty(freeQty);
        return warehouseSkuLotInventory;
    }

    private InventoryLotInfo newInventoryLotInfo(Long skuId, String lotId, String ymdDate) {
        InventoryLotInfo inventoryLotInfo = new InventoryLotInfo();
        inventoryLotInfo.setLotId(lotId);
        if (StringUtils.isNotEmpty(ymdDate)) {
            inventoryLotInfo.setUnsalableDate(ThreadLocalDateUtils.parseYmd(ymdDate));
        }
        inventoryLotInfo.setSkuId(skuId);
        return inventoryLotInfo;
    }


    private SingleSkuWarehouseInventoryWrapper buildSingleSkuWarehouseInventoryWrapper4() {

        WarehouseSkuInventory warehouseSkuInventory = newWarehouseSkuInventory(123L, new BigDecimal(10),
            new BigDecimal(5));

        WarehouseSkuLotInventory warehouseSkuLotInventory = newWarehouseSkuLotInventory(123L, "1", new BigDecimal(4));

        InventoryLotInfo inventoryLotInfo = newInventoryLotInfo(123L, "1", "2022-12-02");

        WarehouseSkuLotInventory warehouseSkuLotInventory2 = newWarehouseSkuLotInventory(123L, "2", new BigDecimal(3));

        InventoryLotInfo inventoryLotInfo2 = newInventoryLotInfo(123L, "2", "2022-12-03");

        WarehouseSkuLotInventory warehouseSkuLotInventory3 = newWarehouseSkuLotInventory(123L, "3", new BigDecimal(3));

        InventoryLotInfo inventoryLotInfo3 = newInventoryLotInfo(123L, "3", "2022-12-04");

        return new SingleSkuWarehouseInventoryWrapper(TestConstants.getLogicInventoryLocationWithSku(warehouseSkuInventory),Collections.singletonList(warehouseSkuInventory),
            Lists.newArrayList(warehouseSkuLotInventory, warehouseSkuLotInventory2, warehouseSkuLotInventory3),
            Lists.newArrayList(inventoryLotInfo, inventoryLotInfo2, inventoryLotInfo3));
    }

    private WarehouseSkuInventoryAllocDetail newWarehouseSkuInventoryAllocDetail(Long skuId, String ymdDemandDate,
        BigDecimal allocQty) {
        WarehouseSkuInventoryAllocDetail detail = new WarehouseSkuInventoryAllocDetail();
        detail.setSkuId(skuId);
        detail.setDemandTime(ThreadLocalDateUtils.parseYmd(ymdDemandDate));
        detail.setAllocQty(allocQty);
        return detail;
    }

    /**
     * 库存分布：
     * 货品维度 10（可用），5（占用）
     * 批次维度
     * 2022-12-02 4
     * 2022-12-03 3
     * 2022-12-04 3
     *
     * 已占用明细
     * 2022-12-03 2
     * 2022-12-04 3
     *
     * 剩余可用
     * 2022-12-02 4
     * 2022-12-03 1
     * 2022-12-04 0
     */
    @Test
    public void testGetFreeQtyAfterAlloc6() {
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper4();

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        WarehouseSkuInventoryAllocDetail detail = newWarehouseSkuInventoryAllocDetail(123L, "2022-12-03",
            new BigDecimal("2"));
        WarehouseSkuInventoryAllocDetail detail2 = newWarehouseSkuInventoryAllocDetail(123L, "2022-12-04",
            new BigDecimal("3"));
        BigDecimal freeQty = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                ThreadLocalDateUtils.parseYmd("2022-12-02"), Lists.newArrayList(detail, detail2));
        Assert.assertEquals("2022-12-02库存应该为5", 0, new BigDecimal(5).compareTo(freeQty));

        BigDecimal freeQty1 = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                ThreadLocalDateUtils.parseYmd("2022-12-03"), Lists.newArrayList(detail, detail2));
        Assert.assertEquals("2022-12-03库存应该为1", 0, new BigDecimal(1).compareTo(freeQty1));

        BigDecimal freeQty2 = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                ThreadLocalDateUtils.parseYmd("2022-12-04"), Lists.newArrayList(detail, detail2));
        Assert.assertEquals("2022-12-04库存应该为0", 0, BigDecimal.ZERO.compareTo(freeQty2));
    }


    private SingleSkuWarehouseInventoryWrapper buildSingleSkuWarehouseInventoryWrapper5() {

        WarehouseSkuInventory warehouseSkuInventory = newWarehouseSkuInventory(123L, new BigDecimal(10),
            new BigDecimal(5));

        WarehouseSkuLotInventory warehouseSkuLotInventory = newWarehouseSkuLotInventory(123L, "1", new BigDecimal(4));

        InventoryLotInfo inventoryLotInfo = newInventoryLotInfo(123L, "1", "2022-12-02");

        WarehouseSkuLotInventory warehouseSkuLotInventory2 = newWarehouseSkuLotInventory(123L, "2", new BigDecimal(3));

        InventoryLotInfo inventoryLotInfo2 = newInventoryLotInfo(123L, "2", "2022-12-02");

        WarehouseSkuLotInventory warehouseSkuLotInventory3 = newWarehouseSkuLotInventory(123L, "3", new BigDecimal(3));

        InventoryLotInfo inventoryLotInfo3 = newInventoryLotInfo(123L, "3", "");

        return new SingleSkuWarehouseInventoryWrapper(TestConstants.getLogicInventoryLocationWithSku(warehouseSkuInventory),Collections.singletonList(warehouseSkuInventory),
            Lists.newArrayList(warehouseSkuLotInventory, warehouseSkuLotInventory2, warehouseSkuLotInventory3),
            Lists.newArrayList(inventoryLotInfo, inventoryLotInfo2, inventoryLotInfo3));
    }

    /**
     * 测试临期日期为空的批次不参与效期库存计算
     * 库存分布：
     * 货品维度 10（可用），5（占用）
     *
     * 批次维度
     * 批次id 可售期      库存
     * 1   2022-12-02 7
     * 2   2022-12-03 0
     * 3   null       3
     * 已占用明细
     * 2022-12-02 2
     * 2022-12-03 3
     * 剩余可用
     * 2022-12-02 5
     * 2022-12-03 0
     */
    @Test
    public void testGetFreeQtyAfterAlloc7() {
        String warehouseSkuInventoryJson = "{\"logicInventoryLocationCode\":\"0001\",\"skuId\":123,\"warehouseId\":1,\"cargoOwnerId\":1,\"freeQty\":10,\"allocQty\":5}";

        String warehouseSkuLotInventoryJson = "[" +
            "{\"logicInventoryLocationCode\":\"0001\",\"skuId\":123,\"warehouseId\":1,\"cargoOwnerId\":1,\"lotId\":\"1\",\"freeQty\":4},"
            +
            "{\"logicInventoryLocationCode\":\"0001\",\"skuId\":123,\"warehouseId\":1,\"cargoOwnerId\":1,\"lotId\":\"2\",\"freeQty\":3},"
            +
            "{\"logicInventoryLocationCode\":\"0001\",\"skuId\":123,\"warehouseId\":1,\"cargoOwnerId\":1,\"lotId\":\"3\",\"freeQty\":3}"
            +
            "]";

        String inventoryLotInfoJson = "[" +
            "{\"skuId\":123,\"lotId\":\"1\",\"unsalableDate\":\"2022-12-02 00:00:00\"}," +
            "{\"skuId\":123,\"lotId\":\"2\",\"unsalableDate\":\"2022-12-02 00:00:00\"}," +
            "{\"skuId\":123,\"lotId\":\"3\",\"unsalableDate\":\"\"}" +
            "]";
        SingleSkuWarehouseInventoryWrapper singleSkuWarehouseInventoryWrapper = buildSingleSkuWarehouseInventoryWrapper(
            warehouseSkuInventoryJson,
            warehouseSkuLotInventoryJson, inventoryLotInfoJson);

        MultiSkuWarehouseInventoryWrapper warehouseInventoryWrapper = new MultiSkuWarehouseInventoryWrapper(
            Lists.newArrayList(singleSkuWarehouseInventoryWrapper));

        WarehouseSkuInventoryAllocDetail detail = newWarehouseSkuInventoryAllocDetail(123L, "2022-12-02",
            new BigDecimal("2"));
        BigDecimal freeQty = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                ThreadLocalDateUtils.parseYmd("2022-12-02"), Lists.newArrayList(detail));
        Assert.assertEquals("2022-12-02库存应该为5", 0, new BigDecimal(5).compareTo(freeQty));

        BigDecimal freeQty1 = warehouseInventoryWrapper
            .getFreeQtyAfterAlloc(singleSkuWarehouseInventoryWrapper.getLogicInventoryLocationWithSku(),
                ThreadLocalDateUtils.parseYmd("2022-12-03"), Lists.newArrayList(detail));
        Assert.assertEquals("2022-12-03库存应该为0", 0, BigDecimal.ZERO.compareTo(freeQty1));
    }

    private SingleSkuWarehouseInventoryWrapper buildSingleSkuWarehouseInventoryWrapper(String warehouseSkuInventoryJson,
        String warehouseSkuLotInventoryJson, String inventoryLotInfoJson) {
        WarehouseSkuInventory warehouseSkuInventory = JsonUtil
            .fromJson(warehouseSkuInventoryJson, WarehouseSkuInventory.class);
        List<WarehouseSkuLotInventory> warehouseSkuLotInventoryList = JsonUtil
            .parseList(warehouseSkuLotInventoryJson, WarehouseSkuLotInventory.class);
        List<InventoryLotInfo> inventoryLotInfos = Lists.newArrayList();
        if (StringUtils.isNotEmpty(inventoryLotInfoJson)) {
            inventoryLotInfos = JsonUtil.parseList(inventoryLotInfoJson, InventoryLotInfo.class);
        }

        return new SingleSkuWarehouseInventoryWrapper(TestConstants.getLogicInventoryLocationWithSku(warehouseSkuInventory),Collections.singletonList(warehouseSkuInventory),
            warehouseSkuLotInventoryList,
            inventoryLotInfos);
    }
}
