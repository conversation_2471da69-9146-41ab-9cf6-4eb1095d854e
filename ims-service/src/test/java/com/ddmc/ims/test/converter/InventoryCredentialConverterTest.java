package com.ddmc.ims.test.converter;

import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.converter.InventoryCredentialConverter;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import org.junit.Assert;
import org.junit.Test;

import java.util.Date;

public class InventoryCredentialConverterTest {

    @Test
    public void testConvertInboundCredentialHeader_witchTransferInbound(){
        InboundCredentialRequest inboundCredentialRequest = new InboundCredentialRequest();
        inboundCredentialRequest.setOrderType(OrderTypeEnum.TRANSFER_INBOUND.getCode());
        inboundCredentialRequest.setOrderNo("1-2-20250510-1");
        inboundCredentialRequest.setExpectOutTime(ThreadLocalDateUtils.parseYmd("2025-05-12"));
        inboundCredentialRequest.setExpectArriveTime(ThreadLocalDateUtils.parseYmd("2025-05-13"));
        inboundCredentialRequest.setDeliveryMode(0);
        inboundCredentialRequest.setOrderSource("WMS");
        inboundCredentialRequest.setOrderOperateType(1);
        inboundCredentialRequest.setBusinessTime(new Date());
        inboundCredentialRequest.setWarehouseId(1L);
        inboundCredentialRequest.setDayEndTime(new Date());
        inboundCredentialRequest.setStockSourceType("601");
        inboundCredentialRequest.setSeqNo("SEQ_NO");
        inboundCredentialRequest.setExeOrderSource("EXE_ORDER_SOURCE");
        inboundCredentialRequest.setExeOrderNo("EXE_ORDER_NO");
        inboundCredentialRequest.setOperateDetails(null);
        inboundCredentialRequest.setIdempotentId("IDEM_ID");
        CredentialHeader header = InventoryCredentialConverter.convertInboundCredentialHeader(inboundCredentialRequest);

        Assert.assertEquals(OrderTypeEnum.TRANSFER_INBOUND.getCode(), header.getOrderType());
        Assert.assertEquals(1, header.getDeliveryMode().intValue());
        Assert.assertEquals(ThreadLocalDateUtils.parseYmd("2025-05-10"), header.getExpectOutTime());
    }

    @Test
    public void testConvertInboundCredentialHeader_witchTransferInbound2(){
        InboundCredentialRequest inboundCredentialRequest = new InboundCredentialRequest();
        inboundCredentialRequest.setOrderType(OrderTypeEnum.TRANSFER_INBOUND.getCode());
        inboundCredentialRequest.setOrderNo("BD0001");
        inboundCredentialRequest.setExpectOutTime(ThreadLocalDateUtils.parseYmd("2025-05-12"));
        inboundCredentialRequest.setExpectArriveTime(ThreadLocalDateUtils.parseYmd("2025-05-13"));
        inboundCredentialRequest.setDeliveryMode(1);
        inboundCredentialRequest.setOrderSource("WMS");
        inboundCredentialRequest.setOrderOperateType(1);
        inboundCredentialRequest.setBusinessTime(new Date());
        inboundCredentialRequest.setWarehouseId(1L);
        inboundCredentialRequest.setDayEndTime(new Date());
        inboundCredentialRequest.setStockSourceType("601");
        inboundCredentialRequest.setSeqNo("SEQ_NO");
        inboundCredentialRequest.setExeOrderSource("EXE_ORDER_SOURCE");
        inboundCredentialRequest.setExeOrderNo("EXE_ORDER_NO");
        inboundCredentialRequest.setOperateDetails(null);
        inboundCredentialRequest.setIdempotentId("IDEM_ID");
        CredentialHeader header = InventoryCredentialConverter.convertInboundCredentialHeader(inboundCredentialRequest);

        Assert.assertEquals(OrderTypeEnum.TRANSFER_INBOUND.getCode(), header.getOrderType());
        Assert.assertEquals(0, header.getDeliveryMode().intValue());
        Assert.assertNull("调拨入库时若单据不是4要素，预计出库日期应为null", header.getExpectOutTime());
    }

    @Test
    public void testConvertInboundCredentialHeader_witchMaterialReturn(){
        InboundCredentialRequest inboundCredentialRequest = new InboundCredentialRequest();
        inboundCredentialRequest.setOrderType(OrderTypeEnum.MATERIAL_RETURN.getCode());
        inboundCredentialRequest.setOrderNo("1-2-20250510-0");
        inboundCredentialRequest.setExpectOutTime(ThreadLocalDateUtils.parseYmd("2025-05-11"));
        inboundCredentialRequest.setExpectArriveTime(ThreadLocalDateUtils.parseYmd("2025-05-11"));
        inboundCredentialRequest.setDeliveryMode(1);
        inboundCredentialRequest.setOrderSource("WMS");
        inboundCredentialRequest.setOrderOperateType(1);
        inboundCredentialRequest.setBusinessTime(new Date());
        inboundCredentialRequest.setWarehouseId(1L);
        inboundCredentialRequest.setDayEndTime(new Date());
        inboundCredentialRequest.setStockSourceType("229");
        inboundCredentialRequest.setSeqNo("SEQ_NO");
        inboundCredentialRequest.setExeOrderSource("EXE_ORDER_SOURCE");
        inboundCredentialRequest.setExeOrderNo("EXE_ORDER_NO");
        inboundCredentialRequest.setOperateDetails(null);
        inboundCredentialRequest.setIdempotentId("IDEM_ID");
        CredentialHeader header = InventoryCredentialConverter.convertInboundCredentialHeader(inboundCredentialRequest);

        Assert.assertEquals(OrderTypeEnum.MATERIAL_RETURN.getCode(), header.getOrderType());
        Assert.assertEquals(inboundCredentialRequest.getDeliveryMode(), header.getDeliveryMode());
        Assert.assertEquals(inboundCredentialRequest.getExpectOutTime(),
                header.getExpectOutTime());
    }
}
