package com.ddmc.ims.test.command;

import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.command.PurchaseIntransitInventoryCleanCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class PurchaseIntransitInventoryCleanCommandHandleTest {

    @InjectMocks
    private PurchaseIntransitInventoryCleanCommandHandle purchaseIntransitInventoryCleanCommandHandle;
    @Mock
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;

    /**
     * 根据单据号查询不到在途数据，不处理在途库存
     */
    @Test
    public void cleanPurchaseInTransit_intransit_not_exist() {
        PurchaseIntransitInventoryCommand command = getCommand("orderSource", "orderNo", 1L);
        when(purchaseIntransitInventoryMapper
            .selectByOrderSourceAndOrderNoAndSkuIdIn(command.getOrderSource(), command.getOrderNo(), Collections.singletonList(1L))).thenReturn(
            Collections.emptyList());
        purchaseIntransitInventoryCleanCommandHandle.handleInventoryCommand(Lists.newArrayList(command));
        Mockito.verify(purchaseIntransitInventoryMapper, Mockito.times(0)).deleteBatchIds(Mockito.any());
    }

    /**
     * 根据单据号查询不到在途数据，需要删除查询出来的在途数据
     */
    @Test
    public void cleanPurchaseInTransit() {
        PurchaseIntransitInventoryCommand command = getCommand("orderSource", "orderNo", 1L);
        PurchaseIntransitInventory purchaseIntransitInventory = getPurchaseIntransitInventory(1L);
        purchaseIntransitInventory.setIntransitQty(new BigDecimal(10));
        List<PurchaseIntransitInventory> purchaseIntransitInventories = Lists.newArrayList(purchaseIntransitInventory);
        when(purchaseIntransitInventoryMapper
            .selectByOrderSourceAndOrderNoAndSkuIdIn(command.getOrderSource(), command.getOrderNo(),Collections.singletonList(1L)))
            .thenReturn(purchaseIntransitInventories);
        purchaseIntransitInventoryCleanCommandHandle.handleInventoryCommand(Lists.newArrayList(command));
        Mockito.verify(purchaseIntransitInventoryMapper, Mockito.times(1)).deleteBatchIds(Lists.newArrayList(1L));
    }

    /**
     * skuId为null
     * 根据单据号查询在途数据，需要删除查询出来的在途数据
     */
    @Test
    public void cleanPurchaseInTransit2() {
        PurchaseIntransitInventoryCommand command = getCommand("orderSource", "orderNo", null);
        PurchaseIntransitInventory purchaseIntransitInventory = getPurchaseIntransitInventory(1L);
        purchaseIntransitInventory.setIntransitQty(new BigDecimal(10));
        List<PurchaseIntransitInventory> purchaseIntransitInventories = Lists.newArrayList(purchaseIntransitInventory);
        when(purchaseIntransitInventoryMapper
            .selectByOrderSourceAndOrderNoAndSkuIdIn(command.getOrderSource(), command.getOrderNo(),Collections.emptyList()))
            .thenReturn(purchaseIntransitInventories);
        purchaseIntransitInventoryCleanCommandHandle.handleInventoryCommand(Lists.newArrayList(command));
        Mockito.verify(purchaseIntransitInventoryMapper, Mockito.times(1)).deleteBatchIds(Lists.newArrayList(1L));
    }


    private PurchaseIntransitInventory getPurchaseIntransitInventory(Long id) {
        PurchaseIntransitInventory purchaseIntransitInventory = new PurchaseIntransitInventory();
        purchaseIntransitInventory.setId(id);
        return purchaseIntransitInventory;
    }


    private PurchaseIntransitInventoryCommand getCommand(String orderSource, String orderNo, Long skuId) {
        return PurchaseIntransitInventoryCommand.builder()
            .toLocation(TestConstants.EMPTY_LOGIC_INVENTORY_LOCATION)
            .fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .commandType(CommandTypeEnum.CLEAN_PURCHASE_IN_TRANSIT)
            .orderSource(orderSource)
            .skuId(skuId)
            .orderNo(orderNo)
            .lotId("lotId")
            .qty(BigDecimal.ONE)
            .build();
    }


}
