package com.ddmc.ims.test.job;

import static org.junit.Assert.assertEquals;

import com.ddmc.ims.job.snapshot.SkuInventorySnapshotPerHourJob;
import com.xxl.job.core.util.ShardingUtil;
import com.xxl.job.core.util.ShardingUtil.ShardingVO;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

@RunWith(PowerMockRunner.class)
@PrepareForTest(SkuInventorySnapshotPerHourJob.class)
public class SkuInventorySnapshotPerHourJobPowerMockTest {

    
    
    @Test
    public void testGetShardingWarehouseIds() throws Exception {
        SkuInventorySnapshotPerHourJob job = new SkuInventorySnapshotPerHourJob();

        // 准备测试数据
        int index = 1;
        int total = 3;
        ShardingUtil.setShardingVo(new ShardingVO(index, total));
        List<Long> allWarehouseIds = Arrays.asList(1L,2L,3L,4L);

        // 调用被测试方法
        List<Long> result = Whitebox.invokeMethod(job, "getShardingWarehouseIds", allWarehouseIds);

        // 验证返回结果
        List<Long> expected = allWarehouseIds.stream()
            .filter(t -> t % total == index)
            .collect(Collectors.toList());
        assertEquals(expected, result);
    }
}
