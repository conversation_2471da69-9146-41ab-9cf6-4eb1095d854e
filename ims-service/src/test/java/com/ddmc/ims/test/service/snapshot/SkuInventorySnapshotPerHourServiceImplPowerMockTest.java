package com.ddmc.ims.test.service.snapshot;

import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.util.DateUtil;
import com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper;
import com.ddmc.ims.dal.model.monitor.SkuInventoryInOutSnapshot;
import com.ddmc.ims.dal.model.monitor.SnapshotTask;
import com.ddmc.ims.dto.InOutNumDto;
import com.ddmc.ims.service.snapshot.impl.SkuInventorySnapshotPerHourServiceImpl;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SkuInventorySnapshotPerHourServiceImpl.class, DateUtil.class, SkuInventoryInOutSnapshotMapper.class})
public class SkuInventorySnapshotPerHourServiceImplPowerMockTest {

    private final SkuInventoryInOutSnapshotMapper skuInventoryInOutSnapshotMapper = PowerMockito.mock(SkuInventoryInOutSnapshotMapper.class);

    @Test
    public void testGetSkuInventoryInOutSnapshots() throws Exception {
        // 模拟依赖对象
        SnapshotTask snapshotTask = getSnapshotTask(100L, DateUtil.getNowHourDate());

        // 模拟静态方法调用
        PowerMockito.mockStatic(DateUtil.class);
        PowerMockito.when(DateUtil.getDateHour(Mockito.any())).thenReturn(0);

        SkuInventoryInOutSnapshot skuInventoryInOutSnapshot = new SkuInventoryInOutSnapshot();
        skuInventoryInOutSnapshot.setSkuId(10L);
        skuInventoryInOutSnapshot.setWarehouseId(100L);
        skuInventoryInOutSnapshot.setLogicLocationCode("code");
        skuInventoryInOutSnapshot.setCargoOwnerId(10L);
        skuInventoryInOutSnapshot.setLotId(StringUtils.EMPTY);
        skuInventoryInOutSnapshot.setInQty(new BigDecimal("10"));
        skuInventoryInOutSnapshot.setOutQty(new BigDecimal("20"));
        // 模拟skuInventoryInOutSnapshotMapper的行为
        PowerMockito.when(skuInventoryInOutSnapshotMapper
            .selectBySnapshotDateTimeBetween(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(
            Collections.singletonList(skuInventoryInOutSnapshot));

        // 创建一个SkuInventorySnapshotPerHourServiceImpl对象
        SkuInventorySnapshotPerHourServiceImpl skuInventorySnapshotPerHourService = new SkuInventorySnapshotPerHourServiceImpl();
        // 将模拟对象注入到被测试的对象中
        Whitebox.setInternalState(skuInventorySnapshotPerHourService, "skuInventoryInOutSnapshotMapper",
            skuInventoryInOutSnapshotMapper);

        final InOutNumDto commandInventoryNumDto = new InOutNumDto();
        commandInventoryNumDto.setWarehouseId(100L);
        commandInventoryNumDto.setSkuId(10L);
        commandInventoryNumDto.setInQty(new BigDecimal("5.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("6.00"));
        final List<InOutNumDto> commandInventoryNumDtos = Collections.singletonList(commandInventoryNumDto);

        // 使用Whitebox来调用私有方法并获取结果
        List<SkuInventoryInOutSnapshot> result = Whitebox.invokeMethod(
            skuInventorySnapshotPerHourService, "getSkuInventoryInOutSnapshots", snapshotTask, commandInventoryNumDtos);

        SkuInventoryInOutSnapshot exceptSnapshot = new SkuInventoryInOutSnapshot();
        exceptSnapshot.setSkuId(10L);
        exceptSnapshot.setWarehouseId(100L);
        exceptSnapshot.setLogicLocationCode("code");
        exceptSnapshot.setCargoOwnerId(10L);
        exceptSnapshot.setLotId(StringUtils.EMPTY);
        exceptSnapshot.setInQty(new BigDecimal("15.00"));
        exceptSnapshot.setOutQty(new BigDecimal("26.00"));
        exceptSnapshot.setSnapshotDateTime(snapshotTask.getSnapshotTime());
        List<SkuInventoryInOutSnapshot> exceptSkuInventoryInOutSnapshots = Collections.singletonList(exceptSnapshot);

        // 验证结果
        Assert.assertEquals(exceptSkuInventoryInOutSnapshots, result);
    }


    private SnapshotTask getSnapshotTask(Long warehouseId, Date nowHourDate) {
        final SnapshotTask snapshotTask = new SnapshotTask();
        snapshotTask.setWarehouseId(100L);
        snapshotTask.setSnapshotType(SnapshotTypeEnum.INVENTORY_HOUR);
        snapshotTask.setSnapshotTime(nowHourDate);
        snapshotTask.setStatus(SnapshotStatusEnum.PROCESSING);
        return snapshotTask;
    }
}
