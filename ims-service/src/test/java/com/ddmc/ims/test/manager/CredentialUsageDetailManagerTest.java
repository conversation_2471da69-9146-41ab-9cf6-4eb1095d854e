package com.ddmc.ims.test.manager;

import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.converter.usage.CommonUsageDetailConverter;
import com.ddmc.ims.converter.usage.UsageDetailConverter;
import com.ddmc.ims.dal.mapper.ims.CredentialUseageDetailMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.inventory.CredentialUsageDetailManager;
import java.util.Collections;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class CredentialUsageDetailManagerTest {

    @InjectMocks
    private CredentialUsageDetailManager credentialUsageDetailManage;
    @Mock
    private CredentialUseageDetailMapper credentialUseageDetailMapper;
    @Mock
    private LocalParamConfig localParamConfig;

    @Mock
    private List<UsageDetailConverter> usageDetailConverters;
    @Mock
    private CommonUsageDetailConverter commonUsageDetailConverter;

    @Test
    public void test() {
        CredentialHeader credentialHeader = getCredentialHeader();
        List<CredentialUseageDetail> credentialUseageDetailList = getCredentialUseageDetailList();
        when(commonUsageDetailConverter.convert(ConfirmContext.builder().credentialHeader(credentialHeader).build())).thenReturn(credentialUseageDetailList);
        credentialUsageDetailManage.saveCredentialUsageDetail(
            ConfirmContext.builder().credentialHeader(credentialHeader).build());
        Assert.assertEquals(credentialUseageDetailList, credentialHeader.getCredentialUseageDetailList());
    }

    private CredentialHeader getCredentialHeader() {
        CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setOrderOperateType(OrderOperateTypeEnum.ADJUSTMENT.getCode());
        credentialHeader.setOrderType(OrderTypeEnum.AGRICULTURAL_MATERIAL_OUTBOUND.getCode());
        credentialHeader.setId(1L);
        CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setUsageCode("TE,EU");
        credentialDetail.setCredentialHeaderId(1L);
        credentialHeader.setCredentialDetailList(Collections.singletonList(credentialDetail));
        return credentialHeader;
    }

    private List<CredentialUseageDetail> getCredentialUseageDetailList() {
        CredentialUseageDetail credentialDetail = new CredentialUseageDetail();
        credentialDetail.setUsageCode("TE");
        credentialDetail.setCredentialHeaderId(1L);
        credentialDetail.setCommandType(CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT.getCode());
        return Collections.singletonList(credentialDetail);
    }
}
