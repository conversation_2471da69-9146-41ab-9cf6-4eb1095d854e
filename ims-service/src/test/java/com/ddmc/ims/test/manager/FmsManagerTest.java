package com.ddmc.ims.test.manager;

import com.ddmc.ims.common.exception.ImsRemoteInvocationException;
import com.ddmc.ims.manager.FmsManager;
import com.ddmc.ims.rpc.fms.FmsBaseClient;
import com.ddmc.ims.rpc.fms.dto.FmsBaseResponseVo;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsDto;
import com.ddmc.ims.rpc.fms.dto.GetCorporationsDto.CorporationInfo;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class FmsManagerTest {

    @InjectMocks
    private FmsManager fmsManager;
    @Mock
    private FmsBaseClient fmsBaseClient;



    @Test(expected = ImsRemoteInvocationException.class)
    public void testGetAllCorporations_error() {
        FmsBaseResponseVo<GetCorporationsDto> fmsBaseResponseVo = new FmsBaseResponseVo<>();
        fmsBaseResponseVo.setSuccess(false);
        GetCorporationsDto getCorporationsDto = new GetCorporationsDto();
        List<CorporationInfo> corporationInfoList = new ArrayList<>();
        CorporationInfo corporationInfo1 = new CorporationInfo();
        corporationInfo1.setCorporationCode("test1");
        corporationInfo1.setCorporationName("test1");
        corporationInfoList.add(corporationInfo1);
        CorporationInfo corporationInfo2 = new CorporationInfo();
        corporationInfo2.setCorporationCode("test2");
        corporationInfo2.setCorporationName("test2");
        corporationInfoList.add(corporationInfo2);
        getCorporationsDto.setRecords(corporationInfoList);
        fmsBaseResponseVo.setData(getCorporationsDto);
        Mockito.doReturn(fmsBaseResponseVo).when(fmsBaseClient).getCorporations(Mockito.any());
        fmsManager.getAllCorporations();

    }

    @Test
    public void testMapCorporation() {
        FmsBaseResponseVo<GetCorporationsDto> fmsBaseResponseVo = new FmsBaseResponseVo<>();
        fmsBaseResponseVo.setSuccess(true);
        GetCorporationsDto getCorporationsDto = new GetCorporationsDto();
        List<CorporationInfo> corporationInfoList = new ArrayList<>();
        CorporationInfo corporationInfo1 = new CorporationInfo();
        corporationInfo1.setCorporationCode("test1");
        corporationInfo1.setCorporationName("test1");
        corporationInfoList.add(corporationInfo1);
        CorporationInfo corporationInfo2 = new CorporationInfo();
        corporationInfo2.setCorporationCode("test2");
        corporationInfo2.setCorporationName("test2");
        corporationInfoList.add(corporationInfo2);
        getCorporationsDto.setRecords(corporationInfoList);
        fmsBaseResponseVo.setData(getCorporationsDto);
        Mockito.doReturn(fmsBaseResponseVo).when(fmsBaseClient).getCorporations(Mockito.any());
        Map<String, String> map = fmsManager.mapCorporation();
        Assert.assertEquals(corporationInfo1.getCorporationName(), map.get(corporationInfo1.getCorporationCode()));
        Assert.assertEquals(corporationInfo2.getCorporationName(), map.get(corporationInfo2.getCorporationCode()));
    }
}