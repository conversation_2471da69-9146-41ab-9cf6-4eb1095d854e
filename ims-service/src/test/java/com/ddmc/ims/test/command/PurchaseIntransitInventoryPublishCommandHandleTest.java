package com.ddmc.ims.test.command;

import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.command.PurchaseIntransitInventoryPublishCommandHandle;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class PurchaseIntransitInventoryPublishCommandHandleTest {


    @InjectMocks
    private PurchaseIntransitInventoryPublishCommandHandle purchaseIntransitInventoryPublishCommandHandle;
    @Mock
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;



    @Test
    public void publishPurchaseInTransit(){
        String commandStr = "{\"fromLocation\":{\"warehouseId\":1,\"cargoOwnerId\":100000,\"logicInventoryLocationCode\":\"0006\"},\"toLocation\":{\"warehouseId\":1,\"cargoOwnerId\":100000,\"logicInventoryLocationCode\":\"0006\"},\"skuId\":2,\"commandType\":\"PUBLISH_PURCHASE_IN_TRANSIT\",\"orderSource\":\"orderSource1\",\"orderNo\":\"orderNo1\",\"expectArriveTime\":\"2023-03-20 15:41:43\",\"qty\":1,\"lotId\":\"123\"}";
        PurchaseIntransitInventoryCommand command = JsonUtil.fromJson(commandStr,PurchaseIntransitInventoryCommand.class);
        purchaseIntransitInventoryPublishCommandHandle.handleInventoryCommand(Lists.newArrayList(command));
        Mockito.verify(purchaseIntransitInventoryMapper, Mockito.times(1)).batchInsert(Mockito.any());
    }




}
