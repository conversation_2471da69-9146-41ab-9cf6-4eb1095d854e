package com.ddmc.ims.test.manager;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.SnapshotCredentialDiff;
import com.ddmc.ims.manager.inventory.SnapshotCredentialDiffManager;
import com.google.common.collect.Lists;
import java.util.Calendar;
import java.util.Date;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class SnapshotCredentialDiffManagerTest {

    @InjectMocks
    private SnapshotCredentialDiffManager snapshotCredentialDiffManager;
    @Mock
    private CredentialHeaderMapper credentialHeaderMapper;
    @Mock
    private SnapshotCredentialDiffMapper snapshotCredentialDiffMapper;



    /**
     * 库存异动
     * 完成凭证不存在，需要插入到差异表中
     */
    @Test
    public void isStockChange_yes_noFinishCredential() {
        CredentialHeader header = getCredentialHeader();
        when(credentialHeaderMapper.listByExeOrderSourceAndNoAndOrderOperateType(header.getExeOrderSource(),
            header.getExeOrderNo(), OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode()))
            .thenReturn(null);
        snapshotCredentialDiffManager.doSnapshotInventoryDiff(header);
        verify(snapshotCredentialDiffMapper, Mockito.times(1)).insert(Mockito.any());

    }

    /**
     * 库存异动
     * 完成凭证存在，业务时间和日结时间不一致，需要插入到差异表中
     */
    @Test
    public void isStockChange_yes_diffEndDay() {
        CredentialHeader header = getCredentialHeader();
        when(credentialHeaderMapper.listByExeOrderSourceAndNoAndOrderOperateType(header.getExeOrderSource(),
            header.getExeOrderNo(), OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode()))
            .thenReturn(null);
        snapshotCredentialDiffManager.doSnapshotInventoryDiff(header);

        verify(snapshotCredentialDiffMapper, Mockito.times(1)).insert(Mockito.any());

    }

    /**
     * 库存异动
     * 完成凭证存在，业务时间和日结时间一致，不需要插入到差异表中
     */
    @Test
    public void isStockChange_yes_sameEndDay() {
        CredentialHeader header = getCredentialHeader();
        CredentialHeader finishCredentialHeader = getFinishCredentialHeader(new Date());

        when(credentialHeaderMapper.listByExeOrderSourceAndNoAndOrderOperateType(header.getExeOrderSource(),
            header.getExeOrderNo(), OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode()))
            .thenReturn(finishCredentialHeader);
        snapshotCredentialDiffManager.doSnapshotInventoryDiff(header);
        verify(snapshotCredentialDiffMapper, Mockito.times(0)).insert(Mockito.any());
    }

    /**
     * 处理完成的凭证
     * 如果 差异记录中，业务时间和日结时间一致，差异数据删除
     * 如果 差异记录中，业务时间和日结时间不一致，更加差异数据中日结时间
     */
    @Test
    public void isStockChange_no() {
        CredentialHeader header = getFinishCredentialHeader(DateUtils.truncate(new Date(), Calendar.DATE));
        SnapshotCredentialDiff deleteDiff = getSnapshotCredentialDiff(1L, new Date());
        SnapshotCredentialDiff updateDiff = getSnapshotCredentialDiff(2L, DateUtils.addDays(new Date(), 1));
        when(snapshotCredentialDiffMapper.listByExeOrderSourceAndNo(header.getExeOrderSource(),
            header.getExeOrderNo()))
            .thenReturn(Lists.newArrayList(deleteDiff, updateDiff));
        snapshotCredentialDiffManager.doSnapshotInventoryDiff(header);
        verify(snapshotCredentialDiffMapper, Mockito.times(1)).deleteBatchIds(Lists.newArrayList(1L));
    }

    private CredentialHeader getFinishCredentialHeader(Date endDateTime) {
        CredentialHeader header = new CredentialHeader();
        header.setIsStockChange(YesNoEnum.NO);
        header.setId(2L);
        header.setOrderNo("orderNo");
        header.setOrderSource("orderSource");
        header.setExeOrderNo("exeOrderNo");
        header.setExeOrderSource("exeOrderSource");
        header.setBusinessTime(new Date());
        header.setOrderOperateType(OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode());
        header.setEndDateTime(endDateTime);
        return header;
    }

    private CredentialHeader getCredentialHeader() {
        CredentialHeader header = new CredentialHeader();
        header.setIsStockChange(YesNoEnum.YES);
        header.setId(1L);
        header.setBusinessTime(new Date());
        header.setOrderOperateType(OrderOperateTypeEnum.IN_STOCK.getCode());
        header.setOrderNo("orderNo");
        header.setOrderSource("orderSource");
        header.setExeOrderNo("exeOrderNo");
        header.setExeOrderSource("exeOrderSource");
        return header;
    }

    private SnapshotCredentialDiff getSnapshotCredentialDiff(Long id, Date date) {
        SnapshotCredentialDiff diff = new SnapshotCredentialDiff();
        diff.setId(id);
        diff.setBusinessTime(date);
        return diff;
    }
}
