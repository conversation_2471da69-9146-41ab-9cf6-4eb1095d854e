package com.ddmc.ims.test.command;

import com.ddmc.ims.bo.command.WarehouseInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.command.WarehouseInventoryTransferCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class WarehouseInventoryTransferCommandHandleTest {


    @InjectMocks
    private WarehouseInventoryTransferCommandHandle warehouseInventoryTransferCommandHandle;
    @Mock
    private WarehouseSkuInventoryService warehouseSkuInventoryService;
    @Mock
    private LocalParamConfig localParamConfig;


    /**
     * 逻辑库位转移命令对应在库库存数量变化
     * 一个转移命令会对应两次库存操作，来源逻辑库位数量减少，目标逻辑库位数量增加
     */
    @Test
    public void transferInventory() {
        WarehouseInventoryCommand command = getCommand(1L, "001", BigDecimal.ONE);
        warehouseInventoryTransferCommandHandle.handleInventoryCommand(Lists.newArrayList(command));
        ArgumentCaptor<InventoryChange> argument = ArgumentCaptor.forClass(InventoryChange.class);
        Mockito.verify(warehouseSkuInventoryService, Mockito.times(2)).changInventory(argument.capture());
        List<InventoryChange> inventoryTransfers = argument.getAllValues();
        Assert.assertEquals(2, inventoryTransfers.size());

        Assert.assertEquals(JsonUtil.toJson(buildFromTransferInventory(Lists.newArrayList(command))),
            JsonUtil.toJson(inventoryTransfers.get(0)));

        Assert.assertEquals(JsonUtil.toJson(buildToTransferInventory(Lists.newArrayList(command))),
            JsonUtil.toJson(inventoryTransfers.get(1)));
    }

    /**
     * 根据命令获取变动量，一个转移变动命令会生成两个变动量
     * 命令 sku 1  数量 1
     * 变动量：来源逻辑库位 数量为-1
     * 目标逻辑库位 数量 为1
     */
    @Test
    public void getCommandInventoryNum() {
        WarehouseInventoryCommand command = getCommand(1L, "23", BigDecimal.ONE);
        List<CommandInventoryNumDto> commandInventoryNumList = warehouseInventoryTransferCommandHandle
            .getCommandInventoryNum(Lists.newArrayList(command));
        Assert.assertEquals(2, commandInventoryNumList.size());
        CommandInventoryNumDto dto = commandInventoryNumList.get(0);
        CommandInventoryNumDto dto1 = commandInventoryNumList.get(1);
        Assert.assertTrue(dto.getFreeQty().equals(BigDecimal.ONE.negate())
            && dto.getLocation().equals(command.getFromLocation())
            && dto.getFrozenQty().equals(BigDecimal.ZERO)
            && dto.getTransferIntransitQty().equals(BigDecimal.ZERO));

        Assert.assertTrue(dto1.getFreeQty().equals(BigDecimal.ONE)
            && dto1.getLocation().equals(command.getToLocation())
            && dto1.getFrozenQty().equals(BigDecimal.ZERO)
            && dto1.getTransferIntransitQty().equals(BigDecimal.ZERO));
    }


    private WarehouseInventoryCommand getCommand(Long skuId, String lotId, BigDecimal qty) {
        return WarehouseInventoryCommand.builder()
            .toLocation(TestConstants.EMPTY_LOGIC_INVENTORY_LOCATION)
            .fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .commandType(CommandTypeEnum.TRANSFER_INVENTORY)
            .fromInventoryStatus(InventoryStatusEnum.AVAILABLE)
            .skuId(skuId)
            .lotId(lotId)
            .qty(qty)
            .build();
    }

    /**
     * 来源逻辑库位转变动映射
     * 库存操作为减少
     */
    InventoryChange buildFromTransferInventory(List<WarehouseInventoryCommand> inventoryCommands) {
        WarehouseInventoryCommand inventoryCommand = inventoryCommands.get(0);
        InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(inventoryCommand.getFromLocation());
        List<InventoryChangeItem> inventoryChangeItems = inventoryCommands.stream().map(i -> {
            InventoryChangeItem.InventoryChangeItemBuilder item = InventoryChangeItem.builder();
            item.skuId(i.getSkuId());
            item.lotId(i.getLotId());
            item.inventoryOperateTypeEnum(InventoryOperateTypeEnum.DECREASE);
            item.qty(i.getQty());
            item.inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE);
            return item.build();
        }).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItems);
        return inventoryChange;
    }

    /**
     * 目标逻辑库位转变动映射
     * 库存操作为减少
     */
    InventoryChange buildToTransferInventory(List<WarehouseInventoryCommand> inventoryCommands) {
        WarehouseInventoryCommand inventoryCommand = inventoryCommands.get(0);
        InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(inventoryCommand.getToLocation());
        List<InventoryChangeItem> inventoryChangeItems = inventoryCommands.stream().map(i -> {
            InventoryChangeItem.InventoryChangeItemBuilder item = InventoryChangeItem.builder();
            item.skuId(i.getSkuId());
            item.lotId(i.getLotId());
            item.inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD);
            item.qty(i.getQty());
            item.inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE);
            return item.build();
        }).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItems);
        return inventoryChange;
    }
}
