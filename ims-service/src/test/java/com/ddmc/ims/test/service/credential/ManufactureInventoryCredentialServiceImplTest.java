package com.ddmc.ims.test.service.credential;

import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.request.credential.oc.AdjustCredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.ManufactureCredentialRequest;
import com.ddmc.ims.service.credential.impl.ManufactureInventoryCredentialServiceImpl;
import java.util.Collections;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ManufactureInventoryCredentialServiceImplTest extends CommonInventoryCredentialServiceImplTest{


    @InjectMocks
    private ManufactureInventoryCredentialServiceImpl manufactureInventoryCredentialService;

    @Test
    public void testTryManufacture_exist() {
        ManufactureCredentialRequest request = buildManufactureCredentialRequest();
        CredentialHeader credentialHeader = new CredentialHeader();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(credentialHeader);
        try {
            manufactureInventoryCredentialService.tryManufacture(request);
        } catch (Exception e) {
            Assert.assertNotNull(e);
            Assert.assertTrue(e instanceof ImsBusinessException);
            Assert.assertEquals(e.getMessage(), CommonErrorCode.EXIST_CREDENTIAL.getMsg());
            return;
        }
        Assert.fail();
    }


    @Test
    public void testTryManufacture() {
        ManufactureCredentialRequest request = buildManufactureCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        manufactureInventoryCredentialService.tryManufacture(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkManufactureCredential(t, request)));
    }


    private boolean checkManufactureCredential(CredentialHeader t, ManufactureCredentialRequest request) {
        checkCredential(t, request);
        Assert.assertTrue(checkCredential(t, request));
        Assert.assertEquals(t.getCredentialDetailList().size(), request.getOperateDetails().size());
        return checkDetailEqual(t.getCredentialDetailList().get(0), request.getOperateDetails().get(0));
    }



    private ManufactureCredentialRequest buildManufactureCredentialRequest() {
        ManufactureCredentialRequest request = new ManufactureCredentialRequest();
        buildCredentialRequest(request);
        AdjustCredentialDetailRequest adjustCredentialDetailRequest = new AdjustCredentialDetailRequest();
        buildCredentialDetailRequest(adjustCredentialDetailRequest);
        adjustCredentialDetailRequest.setInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        request.setOperateDetails(Collections.singletonList(adjustCredentialDetailRequest));
        return request;
    }

}