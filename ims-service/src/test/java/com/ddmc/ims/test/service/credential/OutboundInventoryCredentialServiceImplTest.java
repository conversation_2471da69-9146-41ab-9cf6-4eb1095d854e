package com.ddmc.ims.test.service.credential;

import static org.mockito.Mockito.when;

import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;
import com.ddmc.ims.service.credential.impl.OutboundInventoryCredentialServiceImpl;
import java.util.Collections;
import java.util.Date;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class OutboundInventoryCredentialServiceImplTest extends CommonInventoryCredentialServiceImplTest {


    @InjectMocks
    private OutboundInventoryCredentialServiceImpl outboundInventoryCredentialService;

    @Test
    public void testTryOutbound_exist() {
        OutboundCredentialRequest request = buildOutboundCredentialRequest();
        CredentialHeader credentialHeader = new CredentialHeader();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(credentialHeader);
        try {
            outboundInventoryCredentialService.tryOutbound(request);
        } catch (Exception e) {
            Assert.assertNotNull(e);
            Assert.assertTrue(e instanceof ImsBusinessException);
            Assert.assertEquals(e.getMessage(), CommonErrorCode.EXIST_CREDENTIAL.getMsg());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testTryOutbound() {
        OutboundCredentialRequest request = buildOutboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        outboundInventoryCredentialService.tryOutbound(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkOutboundCredential(t, request)));
    }


    @Test
    public void testTryFinishOutbound() {
        OutboundCredentialRequest request = buildOutboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        outboundInventoryCredentialService.tryFinishOutbound(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkOutboundCredential(t, request)));
    }

    @Test
    public void testTryCancelOutbound() {
        OutboundCredentialRequest request = buildOutboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        outboundInventoryCredentialService.tryCancelOutbound(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkOutboundCredential(t, request)));
    }

    @Test
    public void testTryModifyOutboundPlanQty() {
        OutboundCredentialRequest request = buildOutboundCredentialRequest();
        try {
            outboundInventoryCredentialService.tryModifyOutboundPlanQty(request);
        } catch (Exception e) {
            Assert.assertNotNull(e);
            Assert.assertTrue(e instanceof UnsupportedOperationException);
            return;
        }
        Assert.fail();

    }

    @Test
    public void testTryPublishOutbound() {
        OutboundCredentialRequest request = buildOutboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        outboundInventoryCredentialService.tryPublishOutbound(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkOutboundCredential(t, request)));
    }

    @Test
    public void testTryModifyExpectOutTime() {
        OutboundCredentialRequest request = buildOutboundCredentialRequest();
        request.setExpectOutTime(new Date());
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        outboundInventoryCredentialService.tryModifyExpectOutTime(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkOutboundCredential(t, request)));
    }

    @Test
    public void testTryTransferReject() {
        OutboundCredentialRequest request = buildOutboundCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        outboundInventoryCredentialService.tryTransferReject(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkOutboundCredential(t, request)));
    }

    private boolean checkOutboundCredential(CredentialHeader t, OutboundCredentialRequest request) {
        checkCredential(t, request);
        Assert.assertTrue(checkCredential(t, request));
        Assert.assertEquals(t.getCredentialDetailList().size(), request.getOperateDetails().size());
        return checkDetailEqual(t.getCredentialDetailList().get(0), request.getOperateDetails().get(0));
    }


    private OutboundCredentialRequest buildOutboundCredentialRequest() {
        OutboundCredentialRequest request = new OutboundCredentialRequest();
        buildCredentialRequest(request);
        CredentialDetailRequest adjustCredentialDetailRequest = new CredentialDetailRequest();
        buildCredentialDetailRequest(adjustCredentialDetailRequest);
        request.setOperateDetails(Collections.singletonList(adjustCredentialDetailRequest));
        return request;
    }

}