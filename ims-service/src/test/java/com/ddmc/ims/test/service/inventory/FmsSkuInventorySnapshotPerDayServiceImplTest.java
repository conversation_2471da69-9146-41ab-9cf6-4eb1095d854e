package com.ddmc.ims.test.service.inventory;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.SnapshotStatusEnum;
import com.ddmc.ims.common.enums.ims.SnapshotTypeEnum;
import com.ddmc.ims.common.enums.ims.WarehouseTypeEnum;
import com.ddmc.ims.dal.mapper.monitor.FmsSkuInventorySnapshotPerDayMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventoryInOutSnapshotMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SkuTransferInventorySnapshotPerHourMapper;
import com.ddmc.ims.dal.mapper.monitor.SnapshotTaskMapper;
import com.ddmc.ims.dal.model.ims.Warehouse;
import com.ddmc.ims.dal.model.monitor.FmsSkuInventorySnapshotPerDay;
import com.ddmc.ims.dal.model.monitor.SkuInventorySnapshotPerHour;
import com.ddmc.ims.dto.CommandChangeQtyDto;
import com.ddmc.ims.service.snapshot.FmsSkuInventorySnapshotPerDayService;
import com.ddmc.ims.service.snapshot.SnapshotCredentialDiffService;
import com.ddmc.ims.service.snapshot.impl.FmsSkuInventorySnapshotPerDayServiceImpl;
import com.ddmc.ims.service.warehouse.WarehouseService;
import com.google.common.collect.Maps;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class FmsSkuInventorySnapshotPerDayServiceImplTest {


    @Mock
    private SkuInventorySnapshotPerHourMapper skuInventorySnapshotPerHourMapper;

    @Mock
    private SnapshotCredentialDiffService snapshotCredentialDiffService;

    @InjectMocks
    private FmsSkuInventorySnapshotPerDayServiceImpl fmsSkuInventorySnapshotPerDayServiceImpl;

    @Mock
    private FmsSkuInventorySnapshotPerDayService fmsSkuInventorySnapshotPerDayService;
    @Mock
    private FmsSkuInventorySnapshotPerDayMapper fmsSkuInventorySnapshotPerDayMapper;
    @Mock
    private SnapshotTaskMapper snapshotTaskMapper;
    @Mock
    private SkuInventoryInOutSnapshotMapper skuInventoryInOutSnapshotMapper;
    @Mock
    private WarehouseService warehouseService;
    @Mock
    private SkuTransferInventorySnapshotPerHourMapper skuTransferInventorySnapshotPerHourMapper;




    /**
     * 测试财务快照期末数据
     * FMS-END = FMS-START +（IMS-END - IMS-START）- DIFF(3.1)
     * * DIFF(3.1) = （业务日期 = 3.1，但归结日期 ！=3.1）- (业务日期！=3.1，归结日期=3.1)
     *
     * 财务期初数据 sku 1 数量 10
     * 业务库存期初数据 sku 1 数量 9
     * 业务库存期末数据 sku 1 数量为20
     * sku2 数量为8
     * 差异数据：业务库存比财务库存多的数据 sku 1 数量 2
     * 财务库存比业务库存多的数据 sku 2 数量 2
     * 测试财务期末快照数据
     * sku 1   10+20-9-2
     * sku 2   8+2
     */
    @Test
    public void finishFmsSnapshot() {
        Long warehouseId = 1L;
        Date snapshotDate = new Date();
        when(snapshotTaskMapper.countByWarehouseIdAndSnapshotTimeAndStatus(warehouseId, DateUtils.addDays(snapshotDate, 1),
            SnapshotTypeEnum.INVENTORY_HOUR.getCode(), 1)).thenReturn(1);

        //快照日库存快照
        SkuInventorySnapshotPerHour endInventory = buildSkuInventorySnapshotPerHour(1L, new BigDecimal(20),
            BigDecimal.ZERO);
        SkuInventorySnapshotPerHour endInventory2 = buildSkuInventorySnapshotPerHour(2L, new BigDecimal(7),
            BigDecimal.ONE);
        when(skuInventorySnapshotPerHourMapper.selectBySnapshotDateTimeAndWarehouseId(DateUtils.addDays(snapshotDate, 1), warehouseId))
            .thenReturn(Lists.newArrayList(endInventory, endInventory2));

        when(skuInventoryInOutSnapshotMapper.selectBySnapshotDateTimeAndWarehouseId(Mockito.any(), Mockito.any()))
            .thenReturn(
                Collections.emptyList());

        //财务少的
        Map<Long, CommandChangeQtyDto> fmsLess = Maps.newConcurrentMap();
        fmsLess.put(1L, getCommandChangeQty(new BigDecimal(2)));
        when(snapshotCredentialDiffService.getTodayFmsLessInventoryQtyMap(warehouseId, snapshotDate))
            .thenReturn(fmsLess);
        Warehouse warehouse = new Warehouse();
        warehouse.setType(WarehouseTypeEnum.STORE_TYPE_SALE.getCode());
        when(warehouseService.getWarehouse(warehouseId))
            .thenReturn(warehouse);
        //财务多的
        Map<Long, CommandChangeQtyDto> fmsMore = Maps.newConcurrentMap();
        fmsMore.put(2L, getCommandChangeQty(new BigDecimal(2)));
        when(snapshotCredentialDiffService.getTodayFmsMoreInventoryQtyMap(warehouseId, snapshotDate))
            .thenReturn(fmsMore);

        fmsSkuInventorySnapshotPerDayServiceImpl.dealEndFmsSkuInventorySnapshot(1L, warehouseId, snapshotDate);
        Mockito.verify(fmsSkuInventorySnapshotPerDayService)
            .updateTaskAndBatchSaveFmsSkuInventorySnapshotPerDay(Mockito.argThat(taskId -> taskId == 1),
                Mockito.argThat(this::checkFmsSkuInventorySnapshotPerDays)
            );
    }


    private boolean checkFmsSkuInventorySnapshotPerDays(List<FmsSkuInventorySnapshotPerDay> items) {
        Assert.assertEquals(2, items.size());
        Assert.assertTrue(items.get(0).getQty().compareTo(new BigDecimal(20)) == 0
            && items.get(1).getQty().compareTo(new BigDecimal(8)) == 0);
        return true;
    }


    @Test
    public void testUpdateTaskAndBatchSaveFmsSkuInventorySnapshotPerDay() {

        List<FmsSkuInventorySnapshotPerDay> fmsSnapshots = new ArrayList<>();
        FmsSkuInventorySnapshotPerDay fmsSnapshot = buildFmsPerDay(1L, BigDecimal.ONE);
        fmsSnapshot.setId(1L);
        fmsSnapshots.add(fmsSnapshot);
        fmsSkuInventorySnapshotPerDayServiceImpl
            .updateTaskAndBatchSaveFmsSkuInventorySnapshotPerDay(1L, fmsSnapshots);
        Mockito.verify(snapshotTaskMapper, Mockito.times(1))
            .updateStatusById(1L, SnapshotStatusEnum.COMPLETE, SnapshotStatusEnum.PROCESSING);
        Mockito.verify(fmsSkuInventorySnapshotPerDayMapper, Mockito.times(1))
            .batchInsert(fmsSnapshots);
    }


    private FmsSkuInventorySnapshotPerDay buildFmsPerDay(Long skuId, BigDecimal qty) {
        FmsSkuInventorySnapshotPerDay fms = new FmsSkuInventorySnapshotPerDay();
        fms.setSkuId(skuId);
        fms.setWarehouseId(1L);
        fms.setQty(qty);
        return fms;
    }

    private SkuInventorySnapshotPerHour buildSkuInventorySnapshotPerHour(Long skuId, BigDecimal qty,
        BigDecimal frozenQty) {
        SkuInventorySnapshotPerHour inventory = new SkuInventorySnapshotPerHour();
        inventory.setSkuId(skuId);
        inventory.setFreeQty(qty);
        inventory.setFrozenQty(frozenQty);
        inventory.setLogicLocationCode(CommonConstants.COMMON_LOGIC_INVENTORY_LOCATION_CODE);
        inventory.setWarehouseId(1L);
        return inventory;
    }






    private boolean checkInitFmsSkuInventorySnapshotPerDays(List<FmsSkuInventorySnapshotPerDay> items) {
        Assert.assertEquals(2, items.size());
        Assert.assertTrue(items.get(0).getQty().compareTo(new BigDecimal(18)) == 0
            && items.get(1).getQty().compareTo(new BigDecimal(10)) == 0);
        return true;
    }

    @Test
    public void queryByWarehouseAndSnapshotDate() {
        Long warehouseId = 1L;
        Date snapshotDate = new Date();
        fmsSkuInventorySnapshotPerDayServiceImpl.queryByWarehouseAndSnapshotDate(warehouseId, snapshotDate, 1L, 100);
        verify(fmsSkuInventorySnapshotPerDayMapper, Mockito.times(1))
            .selectByWarehouseIdAndSnapshotDateAndMinSkuId(warehouseId, snapshotDate, 1L, 100);

    }


    private CommandChangeQtyDto getCommandChangeQty(BigDecimal qty) {
        CommandChangeQtyDto commandChangeQtyDto = new CommandChangeQtyDto();
        commandChangeQtyDto.setAllQty(qty);
        return commandChangeQtyDto;
    }

}
