package com.ddmc.ims.test.command;

import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.command.PurchaseIntransitInventoryInStoreCommandHandle;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class PurchaseIntransitInventoryInStoreCommandHandleTest {

    @InjectMocks
    private PurchaseIntransitInventoryInStoreCommandHandle purchaseIntransitInventoryInStoreCommandHandle;
    @Mock
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;
    @Mock
    private WarehouseSkuInventoryService warehouseSkuInventoryService;

    /**
     * 验证: 入库 逻辑库位1  sku 1 数量1
     *           逻辑库位2sku2  数量2
     *
     * 在途数据 sku 1 数量10
     * 结果：在途数据sku 1 数量9
     *
     *      在库库存需要更新2次
     */
    @Test
    public void purchaseInTransitToAvailable() {
        String oderNo = "orderNo";
        String orderSource = "orderSource";
        PurchaseIntransitInventoryCommand command = getCommand(oderNo, orderSource, 1L,1L, "123", new BigDecimal(1));
        PurchaseIntransitInventoryCommand command1 = getCommand(oderNo, orderSource, 2L,2L, "123", new BigDecimal(1));

        PurchaseIntransitInventory purchaseIntransitInventory = getPurchaseIntransitInventory(1L, new BigDecimal(10));
        when(purchaseIntransitInventoryMapper.listByOrderSourceAndPurchaseNo(orderSource, oderNo))
            .thenReturn(Lists.newArrayList(purchaseIntransitInventory));

        purchaseIntransitInventoryInStoreCommandHandle.handleInventoryCommand(Lists.newArrayList(command, command1));
        Mockito.verify(purchaseIntransitInventoryMapper, Mockito.times(1))
            .batchUpdate(Lists.newArrayList(purchaseIntransitInventory));
        Assert.assertEquals(new BigDecimal(9), purchaseIntransitInventory.getIntransitQty());
        ArgumentCaptor<InventoryChange> argument = ArgumentCaptor.forClass(InventoryChange.class);
        Mockito.verify(warehouseSkuInventoryService, Mockito.times(2)).changInventory(argument.capture());
        List<InventoryChange> inventoryChangeList = argument.getAllValues();
        Assert.assertEquals(JsonUtil.toJson(inventoryChangeList.get(0)),
            JsonUtil.toJson(getInStoreInventoryChange(Lists.newArrayList(command))));
        Assert.assertEquals(JsonUtil.toJson(inventoryChangeList.get(1)),
            JsonUtil.toJson(getInStoreInventoryChange(Lists.newArrayList(command1))));

    }

    private PurchaseIntransitInventory getPurchaseIntransitInventory(Long skuId, BigDecimal intransitQty) {
        PurchaseIntransitInventory purchaseIntransitInventory = new PurchaseIntransitInventory();
        purchaseIntransitInventory.setSkuId(skuId);
        purchaseIntransitInventory.setId(1L);
        purchaseIntransitInventory.setIntransitQty(intransitQty);
        purchaseIntransitInventory.setBookedIntransitQty(intransitQty);
        return purchaseIntransitInventory;
    }


    private InventoryChange getInStoreInventoryChange(List<PurchaseIntransitInventoryCommand> commands) {
        InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(commands.get(0).getFromLocation());
        List<InventoryChangeItem> inventoryChangeItemList = commands.stream()
            .map(t -> InventoryChangeItem.builder()
                .skuId(t.getSkuId())
                .lotId(t.getLotId()).qty(t.getQty())
                .inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD)
                .inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE).build()).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItemList);
        return inventoryChange;
    }

    private PurchaseIntransitInventoryCommand getCommand(String orderNo, String orderSource, Long cargoOwnerId,
        Long skuId, String lotId,
        BigDecimal qty) {
        LogicInventoryLocation fromLocation = TestConstants.getCommonLocation();
        fromLocation.setCargoOwnerId(cargoOwnerId);
        return PurchaseIntransitInventoryCommand.builder()
            .toLocation(TestConstants.EMPTY_LOGIC_INVENTORY_LOCATION)
            .fromLocation(fromLocation)
            .commandType(CommandTypeEnum.PURCHASE_IN_TRANSIT_TO_AVAILABLE)
            .orderSource(orderSource)
            .orderNo(orderNo)
            .skuId(skuId)
            .lotId(lotId)
            .qty(qty)
            .build();
    }


}
