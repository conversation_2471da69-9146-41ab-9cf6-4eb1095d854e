package com.ddmc.ims.test.command;

import com.ddmc.ims.bo.command.WarehouseInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.command.WarehouseInventoryModifyAvailableCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.List;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class WarehouseInventoryModifyAvailableCommandHandleTest {

    @InjectMocks
    private WarehouseInventoryModifyAvailableCommandHandle warehouseInventoryModifyAvailableCommandHandle;
    @Mock
    private WarehouseSkuInventoryService warehouseSkuInventoryService;
    @Mock
    private LocalParamConfig localParamConfig;

    /**
     * 变动命令对应在库库存数量变化
     * 变动可用，增加来源逻辑库位可用数量
     *
     */
    @Test
    public void modifyInventoryAvailable() {
        String lotId = "23";
        Long skuId= 1L;
        WarehouseInventoryCommand command = getCommand(skuId, lotId, BigDecimal.ONE, InventoryStatusEnum.AVAILABLE);
        warehouseInventoryModifyAvailableCommandHandle.handleInventoryCommand(Lists.newArrayList(command));
        ArgumentCaptor<InventoryChange> argument = ArgumentCaptor.forClass(InventoryChange.class);
        Mockito.verify(warehouseSkuInventoryService, Mockito.times(1)).changInventory(argument.capture());
        InventoryChange inventoryTransfer = argument.getValue();
        Assert.assertEquals(command.getFromLocation(), inventoryTransfer.getLogicInventoryLocation());
        InventoryChangeItem inventoryChangeItem = inventoryTransfer.getInventoryChangeItemList().get(0);
        Assert.assertTrue(inventoryChangeItem.getLotId().equals(lotId)
        && inventoryChangeItem.getSkuId().equals(skuId)
        && inventoryChangeItem.getQty().compareTo(BigDecimal.ONE) == 0
        && inventoryChangeItem.getInventoryOperateTypeEnum().equals(InventoryOperateTypeEnum.ADD)
        && inventoryChangeItem.getInventoryWorkTypeEnum().equals(InventoryWorkTypeEnum.FREE));
    }

    /**
     * 根本变动可用命令获取变动量
     * 变动逻辑库位为来源逻辑库位，可用数量增加，冻结数量为0，在途数量为0
     */
    @Test
    public void getCommandInventoryNum() {
        WarehouseInventoryCommand command = getCommand(1L, "23", BigDecimal.ONE, InventoryStatusEnum.AVAILABLE);
        List<CommandInventoryNumDto> commandInventoryNumList = warehouseInventoryModifyAvailableCommandHandle
            .getCommandInventoryNum(Lists.newArrayList(command));
        Assert.assertEquals(1, commandInventoryNumList.size());
        CommandInventoryNumDto dto = commandInventoryNumList.get(0);
        Assert.assertTrue(dto.getFreeQty().equals(BigDecimal.ONE)
            && dto.getLocation().equals(command.getFromLocation())
            && dto.getFrozenQty().equals(BigDecimal.ZERO)
            && dto.getTransferIntransitQty().equals(BigDecimal.ZERO));


    }


    private WarehouseInventoryCommand getCommand(Long skuId, String lotId, BigDecimal qty, InventoryStatusEnum status) {
        return WarehouseInventoryCommand.builder()
            .toLocation(TestConstants.EMPTY_LOGIC_INVENTORY_LOCATION)
            .fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .commandType(CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE)
            .fromInventoryStatus(status)
            .skuId(skuId)
            .lotId(lotId)
            .qty(qty)
            .build();
    }

}
