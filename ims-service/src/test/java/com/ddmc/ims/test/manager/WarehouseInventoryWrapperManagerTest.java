package com.ddmc.ims.test.manager;

import com.ddmc.ims.common.bo.LogicInventoryLocationWithLot;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.SkuIdAndLotIdPair;
import com.ddmc.ims.common.enums.common.YesNoEnum;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuLotInventoryMapper;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.ddmc.ims.dto.MultiSkuWarehouseInventoryWrapper;
import com.ddmc.ims.manager.inventory.WarehouseInventoryWrapperManager;
import com.ddmc.ims.test.common.TestConstants;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class WarehouseInventoryWrapperManagerTest {

    @Mock
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;

    @Mock
    private WarehouseSkuLotInventoryMapper warehouseSkuLotInventoryMapper;

    @InjectMocks
    private WarehouseInventoryWrapperManager warehouseInventoryWrapperManager;

    /**
     * 验证在库库位变更逻辑 原始库存 sku 1 可用5不可用4，sku1 对应批次 001 可用3不可用2 批次002 可用2不可用2
     * 新增 sku 1 批次001 新增可用1 减少不可以1，批次003 新增可用1，减少不可用1
     * 新增 sku 2 批次001 新增可用1 减少不可以1
     *
     * 验证：新增sku维度数量为1，批次维度数量为2
     * 变更sku维度数量为1，批次维度数量为1
     * 新增品sku 2 ，可用1不可用-1；
     * 新增批次维度 ，sku2 批次001 可用为1，不可用为-1，sku1批次003，可用为1，不可用为-1
     * 修改sku维度 sku1 可用为7不可用为2 ，批次 sku1 001 可用为4 不可用为1
     *
     *
     */
    @Test
    public void loadSkuAndLotInventoryTest() {
        List<SkuIdAndLotIdPair> skuIdAndLotIdPairs = getSkuIdAndLotIdPairs();
        LogicInventoryLocation logicInventoryLocation = getLogicInventoryLocation();
        Mockito.when(warehouseSkuLotInventoryMapper.getByLogicInventoryLocationAndLotIds(Mockito.any(), Mockito.any()))
            .thenReturn(getWarehouseSkuLotInventoryList());
        Mockito.when(warehouseSkuInventoryMapper.getByLogicInventoryLocationAndSkuIds(Mockito.any(), Mockito.any()))
            .thenReturn(getWarehouseSkuInventoryList());
        MultiSkuWarehouseInventoryWrapper multiSkuWarehouseInventoryWrapper = warehouseInventoryWrapperManager
            .loadSkuAndLotInventory(logicInventoryLocation, skuIdAndLotIdPairs, YesNoEnum.YES);

        skuIdAndLotIdPairs.forEach(skuIdAndLotIdPair -> {
            LogicInventoryLocationWithLot logicInventoryLocationWithLot = new LogicInventoryLocationWithLot(
                logicInventoryLocation, skuIdAndLotIdPair.getSkuId(), skuIdAndLotIdPair.getLotId(),TestConstants.USAGE);
            multiSkuWarehouseInventoryWrapper
                .changeWarehouseInventoryWithLot(logicInventoryLocationWithLot, BigDecimal.ONE, BigDecimal.ONE.negate());
        });
        List<WarehouseSkuInventory> addWarehouseSkuInventories = multiSkuWarehouseInventoryWrapper
            .getAddWarehouseSkuInventories();
        List<WarehouseSkuLotInventory> addWarehouseSkuLotInventories = multiSkuWarehouseInventoryWrapper
            .getAddWarehouseSkuLotInventories();
        List<WarehouseSkuInventory> changedWarehouseSkuInventories = multiSkuWarehouseInventoryWrapper
            .getChangedWarehouseSkuInventories();
        List<WarehouseSkuLotInventory> changedWarehouseSkuLotInventories = multiSkuWarehouseInventoryWrapper
            .getChangedWarehouseSkuLotInventories();
        Assert.assertEquals(1, addWarehouseSkuInventories.size());
        Assert.assertEquals(2, addWarehouseSkuLotInventories.size());
        Assert.assertEquals(1, changedWarehouseSkuInventories.size());
        Assert.assertEquals(1, changedWarehouseSkuLotInventories.size());
        WarehouseSkuInventory addSku = addWarehouseSkuInventories.get(0);
        WarehouseSkuInventory changeSku = changedWarehouseSkuInventories.get(0);
        Map<Long, WarehouseSkuLotInventory> warehouseSkuLotInventoryMap = addWarehouseSkuLotInventories.stream()
            .collect(Collectors.toMap(WarehouseSkuLotInventory::getSkuId, Function.identity()));
        WarehouseSkuLotInventory addSkuLot1 = warehouseSkuLotInventoryMap.get(1L);
        WarehouseSkuLotInventory addSkuLot2 = warehouseSkuLotInventoryMap.get(2L);
        Assert.assertTrue(addSkuLot1.getSkuId().equals(1L)
            && addSkuLot1.getFreeQty().compareTo(new BigDecimal(1)) == 0
            && addSkuLot1.getFrozenQty().compareTo(new BigDecimal(-1)) == 0
            && addSkuLot1.getLotId().equals("003"));
        Assert.assertTrue(addSkuLot2.getSkuId().equals(2L)
            && addSkuLot2.getFreeQty().compareTo(new BigDecimal(1)) == 0
            && addSkuLot2.getFrozenQty().compareTo(new BigDecimal(-1)) == 0
            && addSkuLot2.getLotId().equals("001"));
        WarehouseSkuLotInventory changeSkuLot = changedWarehouseSkuLotInventories.get(0);
        Assert.assertTrue(addSku.getSkuId().equals(2L)
            && addSku.getFreeQty().compareTo(new BigDecimal(1)) == 0
            && addSku.getFrozenQty().compareTo(new BigDecimal(-1)) == 0);
        Assert.assertTrue(changeSku.getSkuId().equals(1L)
            && changeSku.getFreeQty().compareTo(new BigDecimal(7)) == 0
            && changeSku.getFrozenQty().compareTo(new BigDecimal(2)) == 0);
        Assert.assertTrue(changeSkuLot.getSkuId().equals(1L)
            && changeSkuLot.getFreeQty().compareTo(new BigDecimal(4)) == 0
            && changeSkuLot.getFrozenQty().compareTo(new BigDecimal(1)) == 0);
        warehouseInventoryWrapperManager.saveInventory(multiSkuWarehouseInventoryWrapper);
        Mockito.verify(warehouseSkuInventoryMapper).batchUpdate(logicInventoryLocation, changedWarehouseSkuInventories);
        Mockito.verify(warehouseSkuInventoryMapper).batchInsert(addWarehouseSkuInventories);
        Mockito.verify(warehouseSkuLotInventoryMapper).batchUpdate(logicInventoryLocation, changedWarehouseSkuLotInventories);
        Mockito.verify(warehouseSkuLotInventoryMapper).batchInsert(addWarehouseSkuLotInventories);

    }

    private List<SkuIdAndLotIdPair> getSkuIdAndLotIdPairs() {
        SkuIdAndLotIdPair skuIdAndLotIdPair1 = new SkuIdAndLotIdPair(1L, "001");
        SkuIdAndLotIdPair skuIdAndLotIdPair2 = new SkuIdAndLotIdPair(1L, "003");
        SkuIdAndLotIdPair skuIdAndLotIdPair3 = new SkuIdAndLotIdPair(2L, "001");
        return Lists.newArrayList(skuIdAndLotIdPair1, skuIdAndLotIdPair2, skuIdAndLotIdPair3);
    }


    private LogicInventoryLocation getLogicInventoryLocation() {
        return new LogicInventoryLocation(1L, 2L, "001");
    }

    /**
     * mock在库库存
     */
    private List<WarehouseSkuInventory> getWarehouseSkuInventoryList() {
        LogicInventoryLocation logicInventoryLocation = getLogicInventoryLocation();
        WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        warehouseSkuInventory.setLogicInventoryLocationCode(logicInventoryLocation.getLogicInventoryLocationCode());
        warehouseSkuInventory.setWarehouseId(logicInventoryLocation.getWarehouseId());
        warehouseSkuInventory.setCargoOwnerId(logicInventoryLocation.getCargoOwnerId());
        warehouseSkuInventory.setAllocQty(new BigDecimal("0.00"));
        warehouseSkuInventory.setSkuId(1L);
        warehouseSkuInventory.setId(100L);
        warehouseSkuInventory.setFreeQty(new BigDecimal(5));
        warehouseSkuInventory.setFrozenQty(new BigDecimal(4));
        warehouseSkuInventory.setUsageCode(TestConstants.USAGE);
        return Lists.newArrayList(warehouseSkuInventory);
    }

    /**
     * mock在库库存详情
     */
    private List<WarehouseSkuLotInventory> getWarehouseSkuLotInventoryList() {
        LogicInventoryLocation logicInventoryLocation = getLogicInventoryLocation();
        WarehouseSkuLotInventory WarehouseSkuLotInventory1 = new WarehouseSkuLotInventory();
        WarehouseSkuLotInventory1.setLogicInventoryLocationCode(logicInventoryLocation.getLogicInventoryLocationCode());
        WarehouseSkuLotInventory1.setWarehouseId(logicInventoryLocation.getWarehouseId());
        WarehouseSkuLotInventory1.setCargoOwnerId(logicInventoryLocation.getCargoOwnerId());
        WarehouseSkuLotInventory1.setId(1L);
        WarehouseSkuLotInventory1.setSkuId(1L);
        WarehouseSkuLotInventory1.setFreeQty(new BigDecimal(3));
        WarehouseSkuLotInventory1.setFrozenQty(new BigDecimal(2));
        WarehouseSkuLotInventory1.setLotId("001");
        WarehouseSkuLotInventory WarehouseSkuLotInventory2 = new WarehouseSkuLotInventory();
        WarehouseSkuLotInventory2.setLogicInventoryLocationCode(logicInventoryLocation.getLogicInventoryLocationCode());
        WarehouseSkuLotInventory2.setWarehouseId(logicInventoryLocation.getWarehouseId());
        WarehouseSkuLotInventory2.setCargoOwnerId(logicInventoryLocation.getCargoOwnerId());
        WarehouseSkuLotInventory2.setId(2L);
        WarehouseSkuLotInventory2.setSkuId(1L);
        WarehouseSkuLotInventory2.setFreeQty(new BigDecimal(2));
        WarehouseSkuLotInventory2.setFrozenQty(new BigDecimal(2));
        WarehouseSkuLotInventory2.setLotId("002");
        return Lists.newArrayList(WarehouseSkuLotInventory1, WarehouseSkuLotInventory2);
    }


}
