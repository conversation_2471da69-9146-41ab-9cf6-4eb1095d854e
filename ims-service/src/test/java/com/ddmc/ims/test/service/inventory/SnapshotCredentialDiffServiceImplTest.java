package com.ddmc.ims.test.service.inventory;

import static org.mockito.Mockito.when;

import com.ddmc.ims.dal.mapper.ims.SnapshotCredentialDiffMapper;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dto.CommandChangeQtyDto;
import com.ddmc.ims.dto.InOutNumDto;
import com.ddmc.ims.manager.inventory.CommandManager;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.service.snapshot.impl.SnapshotCredentialDiffServiceImpl;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class SnapshotCredentialDiffServiceImplTest {

    @InjectMocks
    private SnapshotCredentialDiffServiceImpl snapshotCredentialDiffService;
    @Mock
    private SnapshotCredentialDiffMapper snapshotCredentialDiffMapper;

    @Mock
    private CredentialWrapperManager credentialWrapperManager;

    @Mock
    private CommandManager commandManager;

    /**
     * 验证财务库存少于业务库存的变动数据量
     * 根据业务时间查询出差异表数据查询差异凭证
     * 根据凭证得到凭证对应的变动量
     * 过滤加工中库存，按照sku汇总变动量
     *
     * 正常逻辑库位 sku1  批次 001  数量 2
     * 正常逻辑库位 sku1 批次 002 数量3
     * 加工中 sku1   批次 003 数量2
     *
     * 正常逻辑库位 sku2 批次 001 数量 2
     *
     * 结果 sku1  5
     * sku2  2
     */
    @Test
    public void getFmsLessInventoryQtyMap() {
        Long warehouseId = 1L;
        Date snapshotDate = new Date();
        when(snapshotCredentialDiffMapper.selectByWarehouseIdAndBusinessTime(warehouseId, snapshotDate))
            .thenReturn(Lists.newArrayList(1L));
        CredentialHeader header = getCredentialHeader(1L);
        List<CredentialHeader> heads = Lists.newArrayList(header);
        //根据差异id获取凭证
        when(credentialWrapperManager.getCredentialHeaderAndDetailList(Lists.newArrayList(1L)))
            .thenReturn(heads);

        InOutNumDto dto1 = getInOutNumDto( new BigDecimal(5), new BigDecimal(2), 1L);
        InOutNumDto dto2 = getInOutNumDto( new BigDecimal(4), new BigDecimal(2), 1L);
        InOutNumDto dto3 = getInOutNumDto( new BigDecimal(2), new BigDecimal(1), 2L);
        InOutNumDto dto4 = getInOutNumDto( new BigDecimal(2), new BigDecimal(1), 2L);

        //根据凭证获取变动记录
        when(commandManager.getFmsInOutNumDto(heads)).thenReturn(Lists.newArrayList(dto1, dto2, dto3, dto4));
        Map<Long, CommandChangeQtyDto> fmsLessInventoryMap = snapshotCredentialDiffService
            .getTodayFmsLessInventoryQtyMap(warehouseId, snapshotDate);
        Assert.assertEquals(2, fmsLessInventoryMap.size());
        Assert.assertEquals(new BigDecimal(5), fmsLessInventoryMap.get(1L).getAllQty());
        Assert.assertEquals(new BigDecimal(2), fmsLessInventoryMap.get(2L).getAllQty());
    }


    private CredentialHeader getCredentialHeader(Long id) {
        CredentialHeader header = new CredentialHeader();
        header.setId(id);
        return header;
    }


    private InOutNumDto getInOutNumDto(
        BigDecimal inQty,
        BigDecimal outQty,
        Long skuId) {
        InOutNumDto dto = new InOutNumDto();
        dto.setSkuId(skuId);
        dto.setWarehouseId(1L);
        dto.setInQty(inQty);
        dto.setOutQty(outQty);
        return dto;
    }



}
