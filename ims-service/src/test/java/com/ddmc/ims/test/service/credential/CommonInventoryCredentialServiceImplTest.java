package com.ddmc.ims.test.service.credential;

import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.CredentialHeaderMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.manager.inventory.CredentialWrapperManager;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.InventoryOperateCredentialRequest;
import com.ddmc.ims.test.common.TestConstants;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Date;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;

/**
 * <AUTHOR>
 */
public class CommonInventoryCredentialServiceImplTest{

    @Mock
    protected CredentialHeaderMapper credentialHeaderMapper;
    @Mock
    protected CredentialWrapperManager credentialWrapperManager;
    @Mock
    protected LocalParamConfig localParamConfig;


    public boolean checkCredential(CredentialHeader t,InventoryOperateCredentialRequest request) {
        return request.getBusinessTime().equals(t.getBusinessTime())
            && request.getIdempotentId().equals(t.getIdempotentId())
            && request.getDayEndTime().equals(t.getEndDateTime())
            && request.getOrderNo().equals(t.getOrderNo())
            && request.getOrderOperateType().equals(t.getOrderOperateType())
            && request.getExeOrderNo().equals(t.getExeOrderNo())
            && request.getExeOrderSource().equals(t.getExeOrderSource())
            && request.getOrderSource().equals(t.getOrderSource())
            && request.getOrderType().equals(t.getOrderType())
            && request.getSeqNo().equals(t.getSeqNo())
            && request.getWarehouseId().equals(t.getWarehouseId());

    }

    public boolean checkDetailEqual(CredentialDetail detail, CredentialDetailRequest requestDetail) {
        return requestDetail.getFromInventoryStatus().equals(detail.getInventoryStatus().getCode())
            && requestDetail.getDemandDate().equals(detail.getDemand())
            && requestDetail.getToInventoryStatus().equals(detail.getToInventoryStatus().getCode())
            && requestDetail.getFromLocation().getLogicInventoryLocationCode().equals(detail.getFromLogicLocationCode())
            && requestDetail.getFromLocation().getWarehouseId().equals(detail.getFromWarehouseId())
            && requestDetail.getFromLocation().getCargoOwnerId().equals(detail.getFromCargoOwnerId())
            && requestDetail.getLotId().equals(detail.getLotId())
            && requestDetail.getOrderTag().equals(detail.getOrderTag())
            && requestDetail.getToLocation().getLogicInventoryLocationCode().equals(detail.getToLogicLocationCode())
            && requestDetail.getToLocation().getWarehouseId().equals(detail.getToWarehouseId())
            && requestDetail.getToLocation().getCargoOwnerId().equals(detail.getToCargoOwnerId())
            && requestDetail.getFromLocation().getWarehouseId().equals(detail.getWarehouseId())
            && requestDetail.getUsages().equals(detail.getUsageList())
            && requestDetail.getToUsages().equals(detail.getUsageList())
            ;

    }


    public InventoryOperateCredentialRequest buildCredentialRequest(InventoryOperateCredentialRequest request) {
        request.setIdempotentId("setIdempotentId");
        request.setOrderSource("setOrderSource");
        request.setOrderNo("setOrderNo");
        request.setOrderType("setOrderType");
        request.setOrderOperateType(OrderOperateTypeEnum.ADJUSTMENT.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("setExeOrderSource");
        request.setExeOrderNo("setExeOrderNo");
        request.setSeqNo("setSeqNo");
        request.setIsStockChange(1);
        request.setWarehouseId(23L);
        request.setDayEndTime(new Date());
        return request;

    }

    public CredentialDetailRequest buildCredentialDetailRequest(CredentialDetailRequest request) {

        request.setFromLocation(TestConstants.getCommonLocation());
        request.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        request.setToLocation(TestConstants.EMPTY_LOGIC_INVENTORY_LOCATION);
        request.setToInventoryStatus(InventoryStatusEnum.NOT_AVAILABLE.getCode());
        request.setSkuId(1L);
        request.setLotId("23");
        request.setQty(BigDecimal.ONE);
        request.setDemandDate(new Date());
        request.setOrderTag("orderTag");
        request.setUsages(Lists.newArrayList("tb", "ec"));
        request.setToUsages(Lists.newArrayList("tb", "ec"));
        return request;

    }

    @Test
    public void test(){
        int i = 1;
        Assert.assertEquals(1,i);
    }

}
