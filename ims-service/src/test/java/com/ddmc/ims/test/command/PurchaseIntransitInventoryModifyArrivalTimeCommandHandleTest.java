package com.ddmc.ims.test.command;

import com.ddmc.ims.bo.command.PurchaseIntransitInventoryCommand;
import com.ddmc.ims.command.PurchaseIntransitInventoryModifyArrivalTimeCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.Date;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class PurchaseIntransitInventoryModifyArrivalTimeCommandHandleTest {


    @InjectMocks
    private PurchaseIntransitInventoryModifyArrivalTimeCommandHandle purchaseIntransitInventoryModifyArrivalTimeCommandHandle;

    @Mock
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;


    @Test
    public void modifyPurchaseArrivalTime() {
        PurchaseIntransitInventoryCommand command = getCommand("orderSource", "orderNo", new Date());
        purchaseIntransitInventoryModifyArrivalTimeCommandHandle.handleInventoryCommand(Lists.newArrayList(command));
        Mockito.verify(purchaseIntransitInventoryMapper, Mockito.times(1))
            .updateExpectArriveTimeByOrder(command.getExpectArriveTime(), command.getOrderSource(),
                command.getOrderNo());
    }


    private PurchaseIntransitInventoryCommand getCommand(String orderSource, String orderNo, Date date) {
        return PurchaseIntransitInventoryCommand.builder()
            .toLocation(TestConstants.EMPTY_LOGIC_INVENTORY_LOCATION)
            .fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .commandType(CommandTypeEnum.MODIFY_PURCHASE_ARRIVAL_TIME)
            .orderSource(orderSource)
            .orderNo(orderNo)
            .expectArriveTime(date)
            .skuId(1L)
            .lotId("lotId")
            .qty(BigDecimal.ONE)
            .build();
    }


}
