package com.ddmc.ims.test.manager;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.common.exception.ImsRemoteInvocationException;
import com.ddmc.ims.config.cache.CacheConfig;
import com.ddmc.ims.manager.PmsCommonRemoteManager;
import com.ddmc.ims.rpc.common.CommonDictionaryClient;
import com.ddmc.ims.service.common.AlertService;
import com.ddmc.pms.common.client.response.dictionary.SystemDictionaryVO;
import java.util.List;
import java.util.Map;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class PmsCommonRemoteManagerTest {

    @InjectMocks
    private PmsCommonRemoteManager pmsCommonRemoteManager;
    @Mock
    private CommonDictionaryClient dictionaryClient;
    @Mock
    private AlertService alertService;

    @Test(expected = ImsRemoteInvocationException.class)
    public void testZoneIdAndNameMap_error() {
        ResponseBaseVo<List<SystemDictionaryVO>> responseBaseVo = new ResponseBaseVo<>();
        responseBaseVo.setSuccess(false);
        SystemDictionaryVO vo = new SystemDictionaryVO();
        vo.setValue("1");
        vo.setName("华东大区");
        responseBaseVo.setData(Lists.newArrayList(vo));
        Mockito.doNothing().when(alertService).alert(Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.doReturn(responseBaseVo).when(dictionaryClient).getSystemDictionaryList(Mockito.anyString());
        CacheConfig.COMMON_DICTIONARY_CACHE.invalidate("SCM-Zone");
        pmsCommonRemoteManager.zoneIdAndNameMap();
    }

    @Test
    public void testZoneIdAndNameMap() {
        ResponseBaseVo<List<SystemDictionaryVO>> responseBaseVo = new ResponseBaseVo<>();
        responseBaseVo.setSuccess(true);
        SystemDictionaryVO vo = new SystemDictionaryVO();
        vo.setValue("1");
        vo.setName("华东大区");
        responseBaseVo.setData(Lists.newArrayList(vo));
        Mockito.doReturn(responseBaseVo).when(dictionaryClient).getSystemDictionaryList(Mockito.anyString());
        Map<String, String> zoneIdMap = pmsCommonRemoteManager.zoneIdAndNameMap();
        Assert.assertEquals(vo.getName(), zoneIdMap.get(vo.getValue()));
        Map<String, String> zoneIdMap1 = pmsCommonRemoteManager.zoneIdAndNameMap();
        Mockito.verify(dictionaryClient,Mockito.times(1)).getSystemDictionaryList(Mockito.anyString());
        Assert.assertEquals(vo.getName(), zoneIdMap1.get(vo.getValue()));
    }


}