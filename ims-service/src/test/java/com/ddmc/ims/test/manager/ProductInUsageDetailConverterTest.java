package com.ddmc.ims.test.manager;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.bo.credential.WarehouseLotInventoryBo;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.converter.usage.ProductInUsageDetailConverter;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.inventory.WarehouseSkuLotInventoryManager;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductInUsageDetailConverterTest {

    @InjectMocks
    private ProductInUsageDetailConverter productInUsageDetailConverter;
    @Mock
    private WarehouseSkuLotInventoryManager warehouseSkuLotInventoryManager;


    /**
     * 凭证
     * 商品	    逻辑库位	        批次	    数量
     * 1	    good	        003	    10
     * 2	    processing	            -10
     *
     * 库存
     * 商品	    逻辑库位	        批次	    数量

     * 2	    processing	    001	    3
     * 2	    processing	    002     4
     * 2	    processing	    003     0
     *
     *
     * 用途维度凭证
     * 商品	    逻辑库位	        批次	    数量  命令
     * 1	    good	        003	    10   CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE
     * 2	    processing	    001     -3   CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE
     * 2	    processing	    002     -7   CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE
     */
    @Test
    public void testConvert() {
        CredentialDetail credentialDetail = getCredentialDetail(1L, CommonConstants.GOOD_PRODUCT, "003", 10);
        CredentialDetail credentialDetail1 = getCredentialDetail(2L,
            CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE, "003", -10);
        LogicInventoryLocation location = new LogicInventoryLocation(credentialDetail1.getFromWarehouseId(),
            credentialDetail1.getFromCargoOwnerId(), credentialDetail1.getFromLogicLocationCode());
        ConfirmContext confirmContext = getConfirmContext(Lists.newArrayList(credentialDetail, credentialDetail1));

        Mockito.when(warehouseSkuLotInventoryManager.getWarehouseLotInventoryBo(Lists.newArrayList(2L),
            location)).thenReturn(Lists.newArrayList(getLotInventory(2L, "001", 3),
            getLotInventory(2L, "002", 4),
            getLotInventory(2L, "003", 0)));
        List<CredentialUseageDetail> credentialUseageDetails = productInUsageDetailConverter.convert(confirmContext);
        Map<String, BigDecimal> bigDecimalMap = credentialUseageDetails.stream().collect(
            Collectors.toMap(t -> t.getSkuId() + "-" + t.getLotId(), CredentialUseageDetail::getQty, (k1, k2) -> k1));
        Assert.assertEquals(3, credentialUseageDetails.size());
        Assert.assertTrue(credentialUseageDetails.stream().allMatch(t-> CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode().equals( t.getCommandType())));
        Assert.assertEquals(new BigDecimal(10),bigDecimalMap.get("1-003"));
        Assert.assertEquals(new BigDecimal(-3),bigDecimalMap.get("2-001"));
        Assert.assertEquals(new BigDecimal(-7),bigDecimalMap.get("2-002"));
    }

    /**
     * 凭证
     * 商品	    逻辑库位	        批次	    数量
     * 1	    good	        003	    10
     * 2	    processing	            10
     *
     * 库存
     * 商品	    逻辑库位	        批次	    数量

     * 2	    processing	    001	    3
     * 2	    processing	    002     4
     * 2	    processing	    003     0
     *
     *
     * 用途维度凭证
     * 商品	    逻辑库位	        批次	    数量  命令
     * 1	    good	        003	    10   CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE
     * 2	    processing	    001     10   CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE
     */
    @Test
    public void testConvert1() {
        CredentialDetail credentialDetail = getCredentialDetail(1L, CommonConstants.GOOD_PRODUCT, "003", 10);
        CredentialDetail credentialDetail1 = getCredentialDetail(2L,
            CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE, "003", 10);
        LogicInventoryLocation location = new LogicInventoryLocation(credentialDetail1.getFromWarehouseId(),
            credentialDetail1.getFromCargoOwnerId(), credentialDetail1.getFromLogicLocationCode());
        ConfirmContext confirmContext = getConfirmContext(Lists.newArrayList(credentialDetail, credentialDetail1));

        Mockito.when(warehouseSkuLotInventoryManager.getWarehouseLotInventoryBo(Lists.newArrayList(2L),
            location)).thenReturn(Lists.newArrayList(getLotInventory(2L, "001", 3),
            getLotInventory(2L, "002", 4),
            getLotInventory(2L, "003", 0)));
        List<CredentialUseageDetail> credentialUseageDetails = productInUsageDetailConverter.convert(confirmContext);
        Map<String, BigDecimal> bigDecimalMap = credentialUseageDetails.stream().collect(
            Collectors.toMap(t -> t.getSkuId() + "-" + t.getLotId(), CredentialUseageDetail::getQty, (k1, k2) -> k1));
        Assert.assertEquals(2, credentialUseageDetails.size());
        Assert.assertTrue(credentialUseageDetails.stream().allMatch(t-> CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode().equals( t.getCommandType())));
        Assert.assertEquals(new BigDecimal(10),bigDecimalMap.get("1-003"));
        Assert.assertEquals(new BigDecimal(10),bigDecimalMap.get("2-001"));
    }


    /**
     * 凭证
     * 商品	    逻辑库位	        批次	    数量
     * 1	    good	        003	    10
     * 2	    processing	            -10
     *
     * 库存
     * 商品	    逻辑库位	        批次	    数量

     * 2	    processing	    001	    -3
     *
     *
     * 用途维度凭证
     * 商品	    逻辑库位	        批次	    数量  命令
     * 1	    good	        003	    10   CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE
     * 2	    processing	    001     -10   CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE
     */
    @Test
    public void testConvert2() {
        CredentialDetail credentialDetail = getCredentialDetail(1L, CommonConstants.GOOD_PRODUCT, "003", 10);
        CredentialDetail credentialDetail1 = getCredentialDetail(2L,
            CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE, "003", -10);
        LogicInventoryLocation location = new LogicInventoryLocation(credentialDetail1.getFromWarehouseId(),
            credentialDetail1.getFromCargoOwnerId(), credentialDetail1.getFromLogicLocationCode());
        ConfirmContext confirmContext = getConfirmContext(Lists.newArrayList(credentialDetail, credentialDetail1));

        Mockito.when(warehouseSkuLotInventoryManager.getWarehouseLotInventoryBo(Lists.newArrayList(2L),
            location)).thenReturn(Lists.newArrayList(getLotInventory(2L, "001", -3)));
        List<CredentialUseageDetail> credentialUseageDetails = productInUsageDetailConverter.convert(confirmContext);
        Map<String, BigDecimal> bigDecimalMap = credentialUseageDetails.stream().collect(
            Collectors.toMap(t -> t.getSkuId() + "-" + t.getLotId(), CredentialUseageDetail::getQty, (k1, k2) -> k1));
        Assert.assertEquals(2, credentialUseageDetails.size());
        Assert.assertTrue(credentialUseageDetails.stream().allMatch(t-> CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode().equals( t.getCommandType())));
        Assert.assertEquals(new BigDecimal(10),bigDecimalMap.get("1-003"));
        Assert.assertEquals(new BigDecimal(-10),bigDecimalMap.get("2-001"));
    }

    /**
     * 凭证
     * 商品	    逻辑库位	        批次	    数量
     * 1	    good	        003	    10
     * 2	    processing	            -10
     *
     * 库存
     * 商品	    逻辑库位	        批次	    数量

     * 2
     *
     *
     * 用途维度凭证
     * 商品	    逻辑库位	        批次	    数量  命令
     * 1	    good	        003	    10   CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE
     * 2	    processing	    ""     -10   CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE
     */
    @Test
    public void testConvert4() {
        CredentialDetail credentialDetail = getCredentialDetail(1L, CommonConstants.GOOD_PRODUCT, "003", 10);
        CredentialDetail credentialDetail1 = getCredentialDetail(2L,
            CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE, "003", -10);
        LogicInventoryLocation location = new LogicInventoryLocation(credentialDetail1.getFromWarehouseId(),
            credentialDetail1.getFromCargoOwnerId(), credentialDetail1.getFromLogicLocationCode());
        ConfirmContext confirmContext = getConfirmContext(Lists.newArrayList(credentialDetail, credentialDetail1));
        Mockito.when(warehouseSkuLotInventoryManager.getLotId(credentialDetail.getWarehouseId(),2L)).thenReturn("004");
        Mockito.when(warehouseSkuLotInventoryManager.getWarehouseLotInventoryBo(Lists.newArrayList(2L),
            location)).thenReturn(Lists.newArrayList());
        List<CredentialUseageDetail> credentialUseageDetails = productInUsageDetailConverter.convert(confirmContext);
        Map<String, BigDecimal> bigDecimalMap = credentialUseageDetails.stream().collect(
            Collectors.toMap(t -> t.getSkuId() + "-" + t.getLotId(), CredentialUseageDetail::getQty, (k1, k2) -> k1));
        Assert.assertEquals(2, credentialUseageDetails.size());
        Assert.assertTrue(credentialUseageDetails.stream().allMatch(t-> CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode().equals( t.getCommandType())));
        Assert.assertEquals(new BigDecimal(10),bigDecimalMap.get("1-003"));
        Assert.assertEquals(new BigDecimal(-10),bigDecimalMap.get("2-004"));
    }

    private CredentialDetail getCredentialDetail(Long skuId, String locationCode, String lotId, Integer qty) {
        CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setSkuId(skuId);
        credentialDetail.setLotId(lotId);
        credentialDetail.setFromLogicLocationCode(locationCode);
        credentialDetail.setQty(new BigDecimal(qty));
        return credentialDetail;

    }

    private ConfirmContext getConfirmContext(List<CredentialDetail> credentialDetailList) {
        CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setWarehouseId(10L);
        credentialHeader.setCredentialDetailList(credentialDetailList);
        return ConfirmContext.builder().credentialHeader(credentialHeader).build();

    }

    private WarehouseLotInventoryBo getLotInventory(Long skuId, String lotId, Integer qty) {
        WarehouseLotInventoryBo lotInventory = new WarehouseLotInventoryBo();
        lotInventory.setSkuId(skuId);
        lotInventory.setLotId(lotId);
        lotInventory.setFreeQty(new BigDecimal(qty));
        return lotInventory;

    }


}
