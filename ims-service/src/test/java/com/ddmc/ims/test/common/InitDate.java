package com.ddmc.ims.test.common;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithSku;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.IntransitInventoryAllocDetail;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocExpireDetail;
import com.ddmc.ims.dto.MultiSkuWarehouseInventoryWrapper;
import com.ddmc.ims.dto.SingleSkuWarehouseInventoryWrapper;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */
public class InitDate {

    private InitDate() {
    }

    public static CredentialDetail getCredentialDetail(Long skuId, BigDecimal qty, boolean todaySale,
        String usageCode) {
        CredentialDetail credentialDetail = new CredentialDetail();
        LogicInventoryLocation location = TestConstants.getCommonLocation();
        credentialDetail.setFromCargoOwnerId(location.getCargoOwnerId());
        credentialDetail.setWarehouseId(location.getWarehouseId());
        credentialDetail.setFromWarehouseId(location.getWarehouseId());
        credentialDetail.setFromLogicLocationCode(location.getLogicInventoryLocationCode());
        LogicInventoryLocation toLocation = TestConstants.EMPTY_LOGIC_INVENTORY_LOCATION;
        credentialDetail.setToCargoOwnerId(toLocation.getCargoOwnerId());
        credentialDetail.setToWarehouseId(toLocation.getWarehouseId());
        credentialDetail.setToLogicLocationCode(location.getLogicInventoryLocationCode());
        credentialDetail.setSkuId(skuId);
        credentialDetail.setQty(qty);
        credentialDetail.setCommandType(StringUtils.EMPTY);
        credentialDetail.setUsageCode(usageCode);
        credentialDetail.setTodaySale(todaySale);
        credentialDetail.setDemand(new Date());
        return credentialDetail;
    }

    public static IntransitInventoryAllocDetail getIntransitInventoryAllocDetail(Long skuId, BigDecimal qty,
        boolean todaySale, String usageCode) {
        IntransitInventoryAllocDetail allocDetail = new IntransitInventoryAllocDetail();
        LogicInventoryLocation location = TestConstants.getCommonLocation();
        allocDetail.setLogicInventoryLocationCode(location.getLogicInventoryLocationCode());
        allocDetail.setWarehouseId(location.getWarehouseId());
        allocDetail.setCargoOwnerId(location.getCargoOwnerId());
        allocDetail.setSkuId(skuId);
        allocDetail.setUsageCode(usageCode);
        allocDetail.setToAllocQty(qty);
        allocDetail.setOriDeliveryDate(TestConstants.getNowDate());
        allocDetail.setDeliveryDate(TestConstants.getNowDate());
        allocDetail.setTodaySale(todaySale);
        return allocDetail;
    }

    public static WarehouseSkuInventory getWarehouseSkuInventory(Long skuId, BigDecimal freeQty, BigDecimal lockQty,
        String usageCode) {
        WarehouseSkuInventory warehouseSkuInventory = new WarehouseSkuInventory();
        LogicInventoryLocation location = TestConstants.getCommonLocation();
        warehouseSkuInventory.setLogicInventoryLocationCode(location.getLogicInventoryLocationCode());
        warehouseSkuInventory.setSkuId(skuId);
        warehouseSkuInventory.setWarehouseId(location.getWarehouseId());
        warehouseSkuInventory.setCargoOwnerId(location.getCargoOwnerId());
        warehouseSkuInventory.setFreeQty(freeQty);
        warehouseSkuInventory.setFrozenQty(BigDecimal.ZERO);
        warehouseSkuInventory.setAllocQty(lockQty);
        warehouseSkuInventory.setUsageCode(usageCode);
        return warehouseSkuInventory;
    }

    public static WarehouseSkuInventoryAllocExpireDetail getWarehouseSkuInventoryAllocExpireDetail(Long skuId,
        BigDecimal lockQty,
        String usageCode) {
        WarehouseSkuInventoryAllocExpireDetail warehouseSkuInventory = new WarehouseSkuInventoryAllocExpireDetail();
        LogicInventoryLocation location = TestConstants.getCommonLocation();
        warehouseSkuInventory.setLogicInventoryLocationCode(location.getLogicInventoryLocationCode());
        warehouseSkuInventory.setSkuId(skuId);
        warehouseSkuInventory.setWarehouseId(location.getWarehouseId());
        warehouseSkuInventory.setCargoOwnerId(location.getCargoOwnerId());
        warehouseSkuInventory.setAllocQty(lockQty);
        warehouseSkuInventory.setUsageCode(usageCode);
        return warehouseSkuInventory;
    }



    public static MultiSkuWarehouseInventoryWrapper getMultiWrapper(
        List<WarehouseSkuInventory> warehouseSkuInventoryList) {
        return new MultiSkuWarehouseInventoryWrapper(warehouseSkuInventoryList.stream()
            .collect(Collectors.groupingBy(WarehouseSkuInventory::getSkuId))
            .values().stream()
            .map(wsi -> new SingleSkuWarehouseInventoryWrapper(
                new LogicInventoryLocationWithSku(TestConstants.getCommonLocation(), wsi.get(0).getSkuId()), wsi,
                Collections.emptyList(), Lists.newArrayList())).collect(Collectors.toList()));
    }

    public static CredentialHeader getCredentialHeader(Date date) {
        CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setOrderOperateType(OrderOperateTypeEnum.ADJUSTMENT.getCode());
        credentialHeader.setOrderType(OrderTypeEnum.AGRICULTURAL_MATERIAL_OUTBOUND.getCode());
        credentialHeader.setId(1L);
        credentialHeader.setWarehouseId(2L);
        credentialHeader.setExpectOutTime(date);
        return credentialHeader;
    }

    public static CredentialDetail getCredentialDetail(Long skuId, String usageCode, BigDecimal qty,
        InventoryStatusEnum inventoryStatusEnum, Date demand) {
        CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setUsageCode(usageCode);
        credentialDetail.setCredentialHeaderId(1L);
        credentialDetail.setFromWarehouseId(2L);
        credentialDetail.setQty(qty);
        credentialDetail.setSkuId(skuId);
        credentialDetail.setFromCargoOwnerId(1L);
        credentialDetail.setDemand(demand);
        credentialDetail.setFromLogicLocationCode(TestConstants.COMMON_LOGIC);
        credentialDetail.setInventoryStatus(inventoryStatusEnum);
        return credentialDetail;
    }


}
