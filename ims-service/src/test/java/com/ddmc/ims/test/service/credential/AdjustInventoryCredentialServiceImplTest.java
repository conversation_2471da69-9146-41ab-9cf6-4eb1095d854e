package com.ddmc.ims.test.service.credential;


import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.errorcode.CommonErrorCode;
import com.ddmc.ims.common.exception.ImsBusinessException;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.request.credential.oc.AdjustCredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.AdjustCredentialRequest;
import com.ddmc.ims.service.credential.impl.AdjustInventoryCredentialServiceImpl;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class AdjustInventoryCredentialServiceImplTest extends CommonInventoryCredentialServiceImplTest {


    @InjectMocks
    private AdjustInventoryCredentialServiceImpl adjustInventoryCredentialService;

    @Test
    public void testTryAdjust_exist() {
        AdjustCredentialRequest request = buildAdjustCredentialRequest();
        CredentialHeader credentialHeader = new CredentialHeader();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(credentialHeader);
        try {
            adjustInventoryCredentialService.tryAdjust(request);
        } catch (Exception e) {
            Assert.assertNotNull(e);
            Assert.assertTrue(e instanceof ImsBusinessException);
            Assert.assertEquals(e.getMessage(), CommonErrorCode.EXIST_CREDENTIAL.getMsg());
            return;
        }
        Assert.fail();
    }


    @Test
    public void testTryAdjust() {
        AdjustCredentialRequest request = buildAdjustCredentialRequest();
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        adjustInventoryCredentialService.tryAdjust(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkAdjustCredential(t, request)));
    }

    @Test
    public void testTryAdjust1() {
        AdjustCredentialRequest request = buildAdjustCredentialRequest("logicCode", Collections.emptyList());
        Map<String, String> map = new HashMap<>();
        map.put("logicCode", "EU");
        when(localParamConfig.getDefaultLocationUsageCodeMap()).thenReturn(map);
        when(credentialHeaderMapper.selectByIdempotentId(request.getIdempotentId())).thenReturn(null);
        adjustInventoryCredentialService.tryAdjust(request);
        Mockito.verify(credentialWrapperManager)
            .saveCredential(Mockito.argThat(t -> checkAdjustCredential1(t, request)));
    }


    private boolean checkAdjustCredential(CredentialHeader t, AdjustCredentialRequest request) {
        checkCredential(t, request);
        Assert.assertTrue(checkCredential(t, request));
        Assert.assertEquals(t.getCredentialDetailList().size(), request.getOperateDetails().size());
        return checkDetailEqual(t.getCredentialDetailList().get(0), request.getOperateDetails().get(0));
    }

    private boolean checkAdjustCredential1(CredentialHeader t, AdjustCredentialRequest request) {
        checkCredential(t, request);
        Assert.assertEquals(t.getCredentialDetailList().size(), request.getOperateDetails().size());
        Assert.assertEquals(Collections.singletonList("EU"), t.getCredentialDetailList().get(0).getUsageList());
        return checkCredential(t, request);
    }


    private AdjustCredentialRequest buildAdjustCredentialRequest() {
        return buildAdjustCredentialRequest("logicCode", Lists.newArrayList("tb", "ec"));
    }


    private AdjustCredentialRequest buildAdjustCredentialRequest(String logicCode, List<String> usages) {
        AdjustCredentialRequest request = new AdjustCredentialRequest();
        buildCredentialRequest(request);
        AdjustCredentialDetailRequest adjustCredentialDetailRequest = new AdjustCredentialDetailRequest();
        buildCredentialDetailRequest(adjustCredentialDetailRequest);
        adjustCredentialDetailRequest.setInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        adjustCredentialDetailRequest.getFromLocation().setLogicInventoryLocationCode(logicCode);
        adjustCredentialDetailRequest.setUsages(usages);
        request.setOperateDetails(Collections.singletonList(adjustCredentialDetailRequest));
        return request;
    }

}