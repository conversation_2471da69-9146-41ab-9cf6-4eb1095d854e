package com.ddmc.ims.test.manager;

import com.ddmc.duc.vo.BaseResponseVo;
import com.ddmc.duc.vo.user.BaseMeta;
import com.ddmc.ims.common.exception.ImsRemoteInvocationException;
import com.ddmc.ims.manager.SsoManager;
import com.ddmc.ims.rpc.sso.SsoApiNewClient;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class SsoManagerTest  {

    @InjectMocks
    private SsoManager ssoManager;
    @Mock
    private SsoApiNewClient ssoApiNewClient;

    @Test
    public void testCityMap() {
        BaseResponseVo<List<BaseMeta>> responseVo = new BaseResponseVo<>();
        responseVo.setSuccess(true);
        List<BaseMeta> baseMetaList = new ArrayList<>();
        BaseMeta baseMeta = new BaseMeta();
        baseMeta.setId("1");
        baseMeta.setName("上海");
        baseMetaList.add(baseMeta);
        BaseMeta baseMeta1 = new BaseMeta();
        baseMeta1.setId("2");
        baseMeta1.setName("北京");
        baseMetaList.add(baseMeta1);
        responseVo.setData(baseMetaList);
        Mockito.doReturn(responseVo).when(ssoApiNewClient).getCity();
        Map<String, String> cityMap = ssoManager.cityMap();
        Assert.assertEquals(baseMeta.getName(),cityMap.get(baseMeta.getId()));
        Assert.assertEquals(baseMeta1.getName(),cityMap.get(baseMeta1.getId()));
    }

    @Test(expected = ImsRemoteInvocationException.class)
    public void testCityMap_error() {
        BaseResponseVo<List<BaseMeta>> responseVo = new BaseResponseVo<>();
        responseVo.setSuccess(false);
        List<BaseMeta> baseMetaList = new ArrayList<>();
        BaseMeta baseMeta = new BaseMeta();
        baseMeta.setId("1");
        baseMeta.setName("上海");
        baseMetaList.add(baseMeta);
        BaseMeta baseMeta1 = new BaseMeta();
        baseMeta1.setId("2");
        baseMeta1.setName("北京");
        baseMetaList.add(baseMeta1);
        responseVo.setData(baseMetaList);
        Mockito.doReturn(responseVo).when(ssoApiNewClient).getCity();
        ssoManager.cityMap();
    }
}