package com.ddmc.ims.test.service.logicinventory;

import com.ddmc.ims.dal.mapper.ims.CargoOwnerMapper;
import com.ddmc.ims.dal.model.ims.CargoOwner;
import com.ddmc.ims.response.inventory.CargoOwnerResponse;
import com.ddmc.ims.service.inventoryconfig.impl.CargoOwnerServiceImpl;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class CargoOwnerServiceTest  {

    @InjectMocks
    private CargoOwnerServiceImpl cargoOwnerService;
    @Mock
    private CargoOwnerMapper cargoOwnerMapper;


    @Test
    public void testQueryCargoOwnerList() {
        List<CargoOwner> cargoOwners = new ArrayList<>();
        CargoOwner cargoOwner = new CargoOwner();
        cargoOwner.setCargoOwnerCode("test");
        cargoOwner.setName("test");
        cargoOwner.setId(1L);
        cargoOwners.add(cargoOwner);
        Mockito.doReturn(cargoOwners).when(cargoOwnerMapper).listAllCargoOwner();
        List<CargoOwnerResponse> cargoOwnerResponses = cargoOwnerService.queryCargoOwnerList();
        Assert.assertEquals(cargoOwners.size(), cargoOwnerResponses.size());
    }


}