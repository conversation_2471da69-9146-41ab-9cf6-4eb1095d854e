package com.ddmc.ims.test.common;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithSku;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import java.util.Calendar;
import java.util.Date;
import org.apache.commons.lang.time.DateUtils;

/**
 * <AUTHOR>
 */
public class TestConstants {


    public static final LogicInventoryLocation COMMON_LOGIC_INVENTORY_LOCATION = new LogicInventoryLocation(1L, 100000L,
        "0006");


    public static final LogicInventoryLocation EMPTY_LOGIC_INVENTORY_LOCATION = new LogicInventoryLocation(-999L, -999L,
        "DEFAULT");


    public static final LogicInventoryLocation PROCESS_LOGIC_INVENTORY_LOCATION = new LogicInventoryLocation(1L,
        100000L,
        CommonConstants.PROCESSING_LOGIC_INVENTORY_LOCATION_CODE);


    public static LogicInventoryLocation getCommonLocation() {
        return new LogicInventoryLocation(1L, 100000L, "0006");
    }


    public static LogicInventoryLocationWithSku getLogicInventoryLocationWithSku(
        WarehouseSkuInventory warehouseSkuInventory) {
        return new LogicInventoryLocationWithSku(
            new LogicInventoryLocation(warehouseSkuInventory.getWarehouseId(), warehouseSkuInventory.getCargoOwnerId(),
                warehouseSkuInventory.getLogicInventoryLocationCode()), warehouseSkuInventory.getSkuId());
    }

    public static final String USAGE = "USAGE";


    public static final String COMMON_LOGIC = "COMMON";

    public static Date getNowDate() {
        return DateUtils.truncate(new Date(), Calendar.DATE);
    }

}
