package com.ddmc.ims;


import io.swagger.models.Swagger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;
import springfox.documentation.spring.web.DocumentationCache;
import springfox.documentation.spring.web.json.Json;
import springfox.documentation.spring.web.json.JsonSerializer;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.mappers.ServiceModelToSwagger2Mapper;

/**
 * Swagger文档更新到Yapi
 */
@Ignore
@Slf4j
public class ITSwaggerPushYapi extends SpringBootTestBase {

    private static final String YAPI_TOKEN = "c8bb0c5a8b2c19a0f9793324ba6155c6103f648b3b1777f2de7a0aff7ee871df";

    @Autowired
    private DocumentationCache documentationCache;
    @Value("${app.id}")
    private String applicationName;
    @Autowired
    private ServiceModelToSwagger2Mapper mapper;
    @Autowired
    private JsonSerializer jsonSerializer;

    @Test
    public void start() {
        Swagger swagger = mapper.mapDocumentation(documentationCache.documentationByGroup(Docket.DEFAULT_GROUP_NAME));
        swagger.basePath("/" + applicationName);
        swagger.host(StringUtils.EMPTY);
        Json json = jsonSerializer.toJson(swagger);
        this.pushYapi(json.value());
        Assert.assertNotNull(json);
    }

    private void pushYapi(String jsonData) {
        String postUrl = "https://yapi.corp.100.me/api/open/import_data";
        RestTemplate restTemplate = new RestTemplate();
        LinkedMultiValueMap<String, String> param = new LinkedMultiValueMap<>();
        param.add("type", "swagger");
        //完全覆盖方式
        param.add("merge", "merge");
        param.add("token", YAPI_TOKEN);
        param.add("json", jsonData);
        log.info("更新Yapi文档: {}", restTemplate.postForObject(postUrl, param, Object.class));
    }
}
