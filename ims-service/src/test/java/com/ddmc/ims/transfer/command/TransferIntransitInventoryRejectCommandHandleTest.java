package com.ddmc.ims.transfer.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.command.TransferIntransitInventoryRejectCommandHandle;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryLotDetail;
import com.ddmc.ims.manager.inventory.TransferInTransitManager;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TransferIntransitInventoryRejectCommandHandleTest {

    @Mock
    private TransferInTransitManager mockTransferInTransitManager;
    @Mock
    private TransferTransitUsageDetailManager mockTransferTransitUsageDetailManager;

    @InjectMocks
    private TransferIntransitInventoryRejectCommandHandle transferIntransitInventoryRejectCommandHandleUnderTest;

    @Test
    public void testHandleInventoryCommand() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(getTransferIntransitInventoryCommand());

        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setFromCargoOwnerId(0L);
        transferIntransitInventory.setFromWarehouseId(0L);
        transferIntransitInventory.setFromLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToCargoOwnerId(0L);
        transferIntransitInventory.setToLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToWarehouseId(0L);
        transferIntransitInventory.setSkuId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("orderNo");
        transferIntransitInventory.setDeliveryMode(0);
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setIntransitQty(new BigDecimal("0.00"));
        transferIntransitInventory.setUsageCode("usageCode");

        // Configure TransferTransitUsageDetailManager.getInventoryLotDetailList(...).
        final TransferIntransitInventoryLotDetail lotDetail = new TransferIntransitInventoryLotDetail();
        lotDetail.setId(0L);
        lotDetail.setTransferIntransitInventoryId(0L);
        lotDetail.setFromCargoOwnerId(0L);
        lotDetail.setFromWarehouseId(0L);
        lotDetail.setFromLogicInventoryLocationCode("logicInventoryLocationCode");
        lotDetail.setToCargoOwnerId(0L);
        lotDetail.setToLogicInventoryLocationCode("logicInventoryLocationCode");
        lotDetail.setToWarehouseId(0L);
        lotDetail.setOrderSource("orderSource");
        lotDetail.setOrderNo("orderNo");
        lotDetail.setRefOrderNo("refOrderNo");
        lotDetail.setSkuId(0L);
        lotDetail.setIntransitQty(new BigDecimal("0.00"));
        lotDetail.setUsageCode("usageCode");
        lotDetail.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        transferIntransitInventoryRejectCommandHandleUnderTest.handleInventoryCommand(commands);

        // Verify the results
        verify(mockTransferInTransitManager).updateInTransitQty(Mockito.anyList(), Mockito.anyList());

    }

    @Test
    public void testHandleInventoryCommand_TransferTransitUsageDetailManagerGetTransferInTransitInventoryListReturnsNoItems() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(getTransferIntransitInventoryCommand());

        // Configure TransferTransitUsageDetailManager.getInventoryLotDetailList(...).
        final TransferIntransitInventoryLotDetail lotDetail = new TransferIntransitInventoryLotDetail();
        lotDetail.setId(0L);
        lotDetail.setTransferIntransitInventoryId(0L);
        lotDetail.setFromCargoOwnerId(0L);
        lotDetail.setFromWarehouseId(0L);
        lotDetail.setFromLogicInventoryLocationCode("logicInventoryLocationCode");
        lotDetail.setToCargoOwnerId(0L);
        lotDetail.setToLogicInventoryLocationCode("logicInventoryLocationCode");
        lotDetail.setToWarehouseId(0L);
        lotDetail.setOrderSource("orderSource");
        lotDetail.setOrderNo("orderNo");
        lotDetail.setRefOrderNo("refOrderNo");
        lotDetail.setSkuId(0L);
        lotDetail.setIntransitQty(new BigDecimal("0.00"));
        lotDetail.setUsageCode("usageCode");
        lotDetail.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        transferIntransitInventoryRejectCommandHandleUnderTest.handleInventoryCommand(commands);

        // Verify the results
        verify(mockTransferInTransitManager).updateInTransitQty(Mockito.anyList(), Mockito.anyList());

    }

    @Test
    public void testHandleInventoryCommand_TransferTransitUsageDetailManagerGetInventoryLotDetailListReturnsNoItems() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(getTransferIntransitInventoryCommand());

        // Configure TransferTransitUsageDetailManager.getTransferInTransitInventoryList(...).
        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setFromCargoOwnerId(0L);
        transferIntransitInventory.setFromWarehouseId(0L);
        transferIntransitInventory.setFromLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToCargoOwnerId(0L);
        transferIntransitInventory.setToLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToWarehouseId(0L);
        transferIntransitInventory.setSkuId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("orderNo");
        transferIntransitInventory.setDeliveryMode(0);
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setIntransitQty(new BigDecimal("0.00"));
        transferIntransitInventory.setUsageCode("usageCode");
        final List<TransferIntransitInventory> inventories = List.of(transferIntransitInventory);

        // Run the test
        transferIntransitInventoryRejectCommandHandleUnderTest.handleInventoryCommand(commands);

        // Verify the results
        verify(mockTransferInTransitManager).updateInTransitQty(Mockito.anyList(), Mockito.anyList());

    }

    @Test
    public void testGetExistTransferMap() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(getTransferIntransitInventoryCommand());

        // Configure TransferTransitUsageDetailManager.getTransferInTransitInventoryList(...).
        final TransferIntransitInventory transferIntransitInventory1 = new TransferIntransitInventory();
        transferIntransitInventory1.setId(0L);
        transferIntransitInventory1.setFromCargoOwnerId(0L);
        transferIntransitInventory1.setFromWarehouseId(0L);
        transferIntransitInventory1.setFromLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory1.setToCargoOwnerId(0L);
        transferIntransitInventory1.setToLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory1.setToWarehouseId(0L);
        transferIntransitInventory1.setSkuId(0L);
        transferIntransitInventory1.setOrderSource("orderSource");
        transferIntransitInventory1.setOrderNo("orderNo");
        transferIntransitInventory1.setDeliveryMode(0);
        transferIntransitInventory1.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory1.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory1.setIntransitQty(new BigDecimal("0.00"));
        transferIntransitInventory1.setUsageCode("usageCode");
        final List<TransferIntransitInventory> inventories = List.of(transferIntransitInventory1);
        when(mockTransferTransitUsageDetailManager
            .getTransferInTransitInventoryList(commands))
            .thenReturn(inventories);

        // Run the test
        final Map<TransferInTransitItem, TransferIntransitInventory> result = transferIntransitInventoryRejectCommandHandleUnderTest
            .getExistTransferMap(commands);

        // Verify the results
        assertThat(result).containsEntry(TransferInTransitItemConverter.convertToCondition(transferIntransitInventory1),
            transferIntransitInventory1);
    }

    @Test
    public void testGetExistTransferMap_TransferTransitUsageDetailManagerReturnsNoItems() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(getTransferIntransitInventoryCommand());
        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setFromCargoOwnerId(0L);
        transferIntransitInventory.setFromWarehouseId(0L);
        transferIntransitInventory.setFromLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToCargoOwnerId(0L);
        transferIntransitInventory.setToLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToWarehouseId(0L);
        transferIntransitInventory.setSkuId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("orderNo");
        transferIntransitInventory.setDeliveryMode(0);
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setIntransitQty(new BigDecimal("0.00"));
        transferIntransitInventory.setUsageCode("usageCode");
        when(mockTransferTransitUsageDetailManager
            .getTransferInTransitInventoryList(commands))
            .thenReturn(Collections.emptyList());

        // Run the test
        final Map<TransferInTransitItem, TransferIntransitInventory> result = transferIntransitInventoryRejectCommandHandleUnderTest
            .getExistTransferMap(commands);

        Assert.assertTrue(MapUtils.isEmpty(result));

    }

    private TransferIntransitInventoryCommand getTransferIntransitInventoryCommand() {
        return TransferIntransitInventoryCommand.builder()
            .skuId(1L)
            .orderNo("1-2-20230807-1")
            .toLocation(TestConstants.PROCESS_LOGIC_INVENTORY_LOCATION)
            .lotId("lotId")
            .qty(BigDecimal.ZERO).fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .build();
    }
}
