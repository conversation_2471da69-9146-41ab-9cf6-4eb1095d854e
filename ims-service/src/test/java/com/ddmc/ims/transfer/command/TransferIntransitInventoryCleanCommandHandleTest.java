package com.ddmc.ims.transfer.command;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferManufactureDateItem;
import com.ddmc.ims.bo.inventory.UsageCodeSkuId;
import com.ddmc.ims.command.TransferIntransitInventoryCleanCommandHandle;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryLotDetail;
import com.ddmc.ims.dto.CommandInventoryNumDto;
import com.ddmc.ims.manager.ImsConfigRemoteManager;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TransferIntransitInventoryCleanCommandHandleTest {

    @Mock
    private TransferIntransitInventoryMapper mockTransferIntransitInventoryMapper;
    @Mock
    private TransferTransitUsageDetailManager mockTransferTransitUsageDetailManager;
    @Mock
    private ImsConfigRemoteManager mockImsConfigRemoteManager;

    @InjectMocks
    private TransferIntransitInventoryCleanCommandHandle transferIntransitInventoryCleanCommandHandleUnderTest;

    @Test
    public void testHandleInventoryCommand() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(new TransferIntransitInventoryCommand());

        // Configure TransferTransitUsageDetailManager.getTransferInTransitInventoryList(...).
        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setFromCargoOwnerId(0L);
        transferIntransitInventory.setFromWarehouseId(0L);
        transferIntransitInventory.setFromLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToCargoOwnerId(0L);
        transferIntransitInventory.setToLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToWarehouseId(0L);
        transferIntransitInventory.setSkuId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("orderNo");
        transferIntransitInventory.setDeliveryMode(0);
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setIntransitQty(new BigDecimal("0.00"));
        transferIntransitInventory.setWaitAllocQty(new BigDecimal("0.00"));
        transferIntransitInventory.setUsageCode("usageCode");

        // Configure TransferTransitUsageDetailManager.getInventoryLotInfoMap(...).
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final Map<String, InventoryLotInfo> lotInfoMap = Map.ofEntries(Map.entry("value", lotInfo));

        // Configure TransferTransitUsageDetailManager.getInventoryLotDetailMap(...).
        final TransferIntransitInventoryLotDetail lotDetail = new TransferIntransitInventoryLotDetail();
        lotDetail.setId(0L);
        lotDetail.setTransferIntransitInventoryId(0L);
        lotDetail.setFromCargoOwnerId(0L);
        lotDetail.setFromWarehouseId(0L);
        lotDetail.setFromLogicInventoryLocationCode("fromLogicInventoryLocationCode");
        lotDetail.setToCargoOwnerId(0L);
        lotDetail.setToLogicInventoryLocationCode("toLogicInventoryLocationCode");
        lotDetail.setToWarehouseId(0L);
        lotDetail.setOrderSource("orderSource");
        lotDetail.setOrderNo("orderNo");
        lotDetail.setRefOrderNo("refOrderNo");
        lotDetail.setSkuId(0L);
        lotDetail.setLotId("lotId");
        lotDetail.setOutQty(new BigDecimal("100"));
        lotDetail.setOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotDetail.setIntransitQty(new BigDecimal("0.00"));
        final Map<TransferManufactureDateItem, TransferIntransitInventoryLotDetail> transferManufactureDateItemTransferIntransitInventoryLotDetailMap = Map
            .ofEntries(
                Map.entry(TransferManufactureDateItem.builder().build(), lotDetail));



        // Configure TransferTransitUsageDetailManager.getSkuSaleQtyMap(...).
        final Map<UsageCodeSkuId, BigDecimal> usageCodeSkuIdBigDecimalMap = Map.ofEntries(
            Map.entry(new UsageCodeSkuId("usageCode", 0L), new BigDecimal("0.00")));

        // Run the test
        transferIntransitInventoryCleanCommandHandleUnderTest.handleInventoryCommand(commands);



        // Verify the results
        Assert.assertTrue(true);
    }

    @Test
    public void testHandleInventoryCommand_TransferTransitUsageDetailManagerGetTransferInTransitInventoryListReturnsNoItems() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(new TransferIntransitInventoryCommand());

        // Configure TransferTransitUsageDetailManager.getInventoryLotInfoMap(...).
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final Map<String, InventoryLotInfo> lotInfoMap = Map.ofEntries(Map.entry("value", lotInfo));

        // Configure TransferTransitUsageDetailManager.getInventoryLotDetailMap(...).
        final TransferIntransitInventoryLotDetail lotDetail = new TransferIntransitInventoryLotDetail();
        lotDetail.setId(0L);
        lotDetail.setTransferIntransitInventoryId(0L);
        lotDetail.setFromCargoOwnerId(0L);
        lotDetail.setFromWarehouseId(0L);
        lotDetail.setFromLogicInventoryLocationCode("fromLogicInventoryLocationCode");
        lotDetail.setToCargoOwnerId(0L);
        lotDetail.setToLogicInventoryLocationCode("toLogicInventoryLocationCode");
        lotDetail.setToWarehouseId(0L);
        lotDetail.setOrderSource("orderSource");
        lotDetail.setOrderNo("orderNo");
        lotDetail.setRefOrderNo("refOrderNo");
        lotDetail.setSkuId(0L);
        lotDetail.setLotId("lotId");
        lotDetail.setOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotDetail.setIntransitQty(new BigDecimal("0.00"));
        final Map<TransferManufactureDateItem, TransferIntransitInventoryLotDetail> transferManufactureDateItemTransferIntransitInventoryLotDetailMap = Map
            .ofEntries(
                Map.entry(TransferManufactureDateItem.builder().build(), lotDetail));


        // Configure TransferTransitUsageDetailManager.getSkuSaleQtyMap(...).
        final Map<UsageCodeSkuId, BigDecimal> usageCodeSkuIdBigDecimalMap = Map.ofEntries(
            Map.entry(new UsageCodeSkuId("usageCode", 0L), new BigDecimal("0.00")));


        // Run the test
        transferIntransitInventoryCleanCommandHandleUnderTest.handleInventoryCommand(commands);

        // Verify the results
        Assert.assertTrue(true);
    }

    @Test
    public void testGetCommandInventoryNum() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(new TransferIntransitInventoryCommand());
        final CommandInventoryNumDto commandInventoryNumDto = new CommandInventoryNumDto();
        commandInventoryNumDto.setLocation(new LogicInventoryLocation(0L, 0L, "logicInventoryLocationCode"));
        commandInventoryNumDto.setSkuId(0L);
        commandInventoryNumDto.setLotId("lotId");
        commandInventoryNumDto.setFreeQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setFrozenQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setTransferIntransitQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setUsageCode("usageCode");
        commandInventoryNumDto.setInQty(new BigDecimal("0.00"));
        commandInventoryNumDto.setOutQty(new BigDecimal("0.00"));
        final List<CommandInventoryNumDto> expectedResult = List.of(commandInventoryNumDto);

        // Run the test
        final List<CommandInventoryNumDto> result = transferIntransitInventoryCleanCommandHandleUnderTest
            .getCommandInventoryNum(commands);

        Assert.assertTrue(true);
    }
}
