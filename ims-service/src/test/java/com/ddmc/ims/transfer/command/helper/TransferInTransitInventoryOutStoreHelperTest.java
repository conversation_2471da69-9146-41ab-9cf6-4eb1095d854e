package com.ddmc.ims.transfer.command.helper;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.command.helper.TransferInTransitInventoryOutStoreHelper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;

public class TransferInTransitInventoryOutStoreHelperTest {

    @Test
    public void testProcessTransferInTransitInventoryCommands() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(getTransferIntransitInventoryCommand());
        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setFromCargoOwnerId(0L);
        transferIntransitInventory.setFromWarehouseId(0L);
        transferIntransitInventory.setFromLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToCargoOwnerId(0L);
        transferIntransitInventory.setToLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory.setToWarehouseId(0L);
        transferIntransitInventory.setSkuId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("1-1-20230812-0");
        transferIntransitInventory.setDeliveryMode(0);
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setPlanQty(new BigDecimal("0.00"));
        transferIntransitInventory.setIntransitQty(new BigDecimal("0.00"));
        transferIntransitInventory.setAllocQty(new BigDecimal("0.00"));
        transferIntransitInventory.setWaitAllocQty(new BigDecimal("0.00"));
        transferIntransitInventory.setVersion(0);
        transferIntransitInventory.setUsageCode("toUsageCode");
        transferIntransitInventory.setToUsageCode("toUsageCode");
        final TransferIntransitInventory transferIntransitInventory1 = new TransferIntransitInventory();
        transferIntransitInventory1.setId(0L);
        transferIntransitInventory1.setFromCargoOwnerId(0L);
        transferIntransitInventory1.setFromWarehouseId(0L);
        transferIntransitInventory1.setFromLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory1.setToCargoOwnerId(0L);
        transferIntransitInventory1.setToLogicInventoryLocationCode("logicInventoryLocationCode");
        transferIntransitInventory1.setToWarehouseId(0L);
        transferIntransitInventory1.setSkuId(0L);
        transferIntransitInventory1.setOrderSource("orderSource");
        transferIntransitInventory1.setOrderNo("1-1-20230812-0");
        transferIntransitInventory1.setDeliveryMode(0);
        transferIntransitInventory1.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory1.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory1.setPlanQty(new BigDecimal("0.00"));
        transferIntransitInventory1.setIntransitQty(new BigDecimal("0.00"));
        transferIntransitInventory1.setAllocQty(new BigDecimal("0.00"));
        transferIntransitInventory1.setWaitAllocQty(new BigDecimal("0.00"));
        transferIntransitInventory1.setVersion(0);
        transferIntransitInventory1.setUsageCode("toUsageCode");
        transferIntransitInventory1.setToUsageCode("toUsageCode");

        // Run the test
        TransferInTransitInventoryOutStoreHelper
            .processTransferInTransitInventoryCommands(commands, new HashMap<>(), new HashMap<>());
        Assert.assertTrue(true);
        // Verify the results
    }


    private TransferIntransitInventoryCommand getTransferIntransitInventoryCommand() {
        return TransferIntransitInventoryCommand.builder()
            .skuId(1L)
            .orderNo("1-1-20230812-0")
            .toLocation(TestConstants.PROCESS_LOGIC_INVENTORY_LOCATION)
            .lotId("lotId")
            .qty(BigDecimal.ZERO).fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .build();
    }
}
