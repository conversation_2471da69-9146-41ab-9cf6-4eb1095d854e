package com.ddmc.ims.transfer.command;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.command.TransferIntransitInventoryInStoreCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class TransferIntransitInventoryInStoreCommandHandleTest {


    @InjectMocks
    @Spy
    private TransferIntransitInventoryInStoreCommandHandle transferIntransitInventoryInStoreCommandHandle;

    @Mock
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;
    @Mock
    private WarehouseSkuInventoryService warehouseSkuInventoryService;

    @Test
    public void inStore() {
        TransferIntransitInventoryCommand command = getCommand("1-1-20230405-0", "orderSource", 1L, "1", BigDecimal.ONE);
        TransferIntransitInventoryCommand command1 = getCommand("1-1-20230405-0", "orderSource", 2L, "1", BigDecimal.ONE);
        List<TransferIntransitInventoryCommand> commands = Lists.newArrayList(command, command1);
        doNothing().when(transferIntransitInventoryInStoreCommandHandle).handleTransferInStore(commands);
        doNothing().when(transferIntransitInventoryInStoreCommandHandle).inStoreChangInventory(commands);
        transferIntransitInventoryInStoreCommandHandle.handleInventoryCommand(commands);
        Mockito.verify(transferIntransitInventoryInStoreCommandHandle, times(1)).handleTransferInStore(commands);
        Mockito.verify(transferIntransitInventoryInStoreCommandHandle, times(1)).inStoreChangInventory(commands);
    }

    @Test
    public void inStore1() {
        TransferIntransitInventoryCommand command = getCommand("orderNo", "orderSource", 1L, "1", BigDecimal.ONE);
        TransferIntransitInventoryCommand command1 = getCommand("orderNo", "orderSource", 2L, "1", BigDecimal.ONE);
        List<TransferIntransitInventoryCommand> commands = Lists.newArrayList(command, command1);
        doNothing().when(transferIntransitInventoryInStoreCommandHandle).inStoreChangInventory(commands);
        transferIntransitInventoryInStoreCommandHandle.handleInventoryCommand(commands);
        Mockito.verify(transferIntransitInventoryInStoreCommandHandle, times(0)).handleTransferInStore(commands);
        Mockito.verify(transferIntransitInventoryInStoreCommandHandle, times(1)).inStoreChangInventory(commands);
    }

    @Test
    public void outStoreChangInventory() {
        TransferIntransitInventoryCommand command = getCommand("orderNo", "orderSource", 1L, "1", BigDecimal.ONE);
        TransferIntransitInventoryCommand command1 = getCommand("orderNo", "orderSource", 2L, "1", BigDecimal.ONE);
        List<TransferIntransitInventoryCommand> commands = Lists.newArrayList(command, command1);
        transferIntransitInventoryInStoreCommandHandle.inStoreChangInventory(commands);
        Mockito.verify(warehouseSkuInventoryService).changInventory(
            Mockito.argThat(t -> JsonUtil.toJson(t).equals(JsonUtil.toJson(getInStoreInventoryChange(commands)))));
    }




    private InventoryChange getInStoreInventoryChange(List<TransferIntransitInventoryCommand> inventoryCommands){
        InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(inventoryCommands.get(0).getFromLocation());
        List<InventoryChangeItem> inventoryChangeItems = inventoryCommands.stream().map(i -> {
            InventoryChangeItem.InventoryChangeItemBuilder item = InventoryChangeItem.builder();
            item.skuId(i.getSkuId());
            item.lotId(i.getLotId());
            item.inventoryOperateTypeEnum(InventoryOperateTypeEnum.ADD);
            item.qty(i.getQty());
            item.inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE);
            return item.build();
        }).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItems);
        return inventoryChange;
    }


    private TransferIntransitInventoryCommand getCommand(String orderNo, String orderSource, Long skuId, String lotId,
        BigDecimal qty) {
        return TransferIntransitInventoryCommand.builder()
            .toLocation(TestConstants.EMPTY_LOGIC_INVENTORY_LOCATION)
            .fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .commandType(CommandTypeEnum.TRANSFER_IN_TRANSIT_TO_AVAILABLE)
            .orderNo(orderNo)
            .orderSource(orderSource)
            .skuId(skuId)
            .lotId(lotId)
            .qty(qty)
            .build();
    }


    private TransferIntransitInventory getTransferIntransitInventory(Long id ,Long skuId, BigDecimal qty) {
        TransferIntransitInventory intransitInventory = new TransferIntransitInventory();
        intransitInventory.setId(id);
        intransitInventory.setPlanQty(qty);
        intransitInventory.setSkuId(skuId);
        intransitInventory.setIntransitQty(qty);
        intransitInventory.setWaitAllocQty(BigDecimal.ZERO);
        intransitInventory.setAllocQty(qty);
        return intransitInventory;
    }





}
