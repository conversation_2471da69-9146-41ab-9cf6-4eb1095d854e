package com.ddmc.ims.transfer.command;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.InventoryChange;
import com.ddmc.ims.bo.inventory.InventoryChangeItem;
import com.ddmc.ims.command.TransferIntransitInventoryOutStoreCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.InventoryWorkTypeEnum;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import com.ddmc.ims.service.inventory.WarehouseSkuInventoryService;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class TransferIntransitInventoryOutStoreCommandHandleTest {


    @InjectMocks
    @Spy
    private TransferIntransitInventoryOutStoreCommandHandle transferIntransitInventoryOutStoreCommandHandle;

    @Mock
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;
    @Mock
    private WarehouseSkuInventoryService warehouseSkuInventoryService;
    @Mock
    private TransferTransitUsageDetailManager transferTransitUsageDetailManager;


    @Test
    public void outStore() {
        TransferIntransitInventoryCommand command = getCommand("orderNo", "orderSource", 1L, "1", BigDecimal.ONE);
        TransferIntransitInventoryCommand command1 = getCommand("orderNo", "orderSource", 2L, "1", BigDecimal.ONE);
        List<TransferIntransitInventoryCommand> commands = Lists.newArrayList(command, command1);
        doNothing().when(transferIntransitInventoryOutStoreCommandHandle).changOutStoreInventory(commands);
        transferIntransitInventoryOutStoreCommandHandle.handleInventoryCommand(commands);
        Mockito.verify(transferIntransitInventoryOutStoreCommandHandle, times(1)).changOutStoreInventory(Mockito.any());
    }

    @Test
    public void outStoreChangInventory() {
        TransferIntransitInventoryCommand command = getCommand("orderNo", "orderSource", 1L, "1", BigDecimal.ONE);
        TransferIntransitInventoryCommand command1 = getCommand("orderNo", "orderSource", 2L, "1", BigDecimal.ONE);
        List<TransferIntransitInventoryCommand> commands = Lists.newArrayList(command, command1);
        transferIntransitInventoryOutStoreCommandHandle.changOutStoreInventory(commands);
        Mockito.verify(warehouseSkuInventoryService).changInventory(
            Mockito.argThat(t -> JsonUtil.toJson(t).equals(JsonUtil.toJson(getOutStoreInventoryChange(commands)))));
    }

    /**
     * 修改调拨在途数据
     * 调拨在途初始数据  skuId 1 数量 10
     * 本次调拨在途命令  skuId 1 数量 1  skuId2 数量 10
     * 调拨在途修改 skuId 1 数量为11  新增 skuId2 数量为10
     */
    @Test
    public void transferOutStore() {
        String orderNo = "1-2-20240806-0";
        String orderSource = "orderSource";

        TransferIntransitInventoryCommand command = getCommand(orderNo, orderSource, 1L, "1", BigDecimal.ONE);
        TransferIntransitInventoryCommand command1 = getCommand(orderNo, orderSource, 2L, "1", BigDecimal.TEN);
        List<TransferIntransitInventoryCommand> commands = Lists.newArrayList(command, command1);
        TransferIntransitInventory transferIntransitInventory = getTransferIntransitInventory(1L, BigDecimal.TEN);
        Mockito.when(transferTransitUsageDetailManager.getTransferInTransitInventoryList(commands))
            .thenReturn(Lists.newArrayList(transferIntransitInventory));
        transferIntransitInventoryOutStoreCommandHandle.handleTransferOutStore(commands);

        Mockito.verify(transferIntransitInventoryMapper).batchUpdate(Mockito.argThat(t ->
            t.size() == 1
            && t.get(0).getSkuId() == 1
            && t.get(0).getPlanQty().compareTo(new BigDecimal(10)) == 0
            && t.get(0).getIntransitQty().compareTo(new BigDecimal(11)) == 0
            && t.get(0).getAllocQty().compareTo(new BigDecimal(11)) == 0
            && t.get(0).getWaitAllocQty().compareTo(new BigDecimal(0)) == 0));

        Mockito.verify(transferIntransitInventoryMapper).batchInsert(Mockito.argThat(t -> t.size() == 1
            && t.get(0).getSkuId() == 2
            && t.get(0).getPlanQty().compareTo(new BigDecimal(0)) == 0
            && t.get(0).getIntransitQty().compareTo(new BigDecimal(10)) == 0
            && t.get(0).getAllocQty().compareTo(new BigDecimal(10)) == 0
            && t.get(0).getWaitAllocQty().compareTo(new BigDecimal(0)) == 0));

    }


    private InventoryChange getOutStoreInventoryChange(List<TransferIntransitInventoryCommand> inventoryCommands) {
        InventoryChange inventoryChange = new InventoryChange();
        inventoryChange.setLogicInventoryLocation(inventoryCommands.get(0).getFromLocation());
        List<InventoryChangeItem> inventoryChangeItems = inventoryCommands.stream().map(i -> {
            InventoryChangeItem.InventoryChangeItemBuilder item = InventoryChangeItem.builder();
            item.skuId(i.getSkuId());
            item.lotId(i.getLotId());
            item.inventoryOperateTypeEnum(InventoryOperateTypeEnum.DECREASE);
            item.qty(i.getQty());
            item.inventoryWorkTypeEnum(InventoryWorkTypeEnum.FREE);
            return item.build();
        }).collect(Collectors.toList());
        inventoryChange.setInventoryChangeItemList(inventoryChangeItems);
        return inventoryChange;
    }


    private TransferIntransitInventoryCommand getCommand(String orderNo, String orderSource, Long skuId, String lotId,
        BigDecimal qty) {
        return TransferIntransitInventoryCommand.builder()
            .toLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .commandType(CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT)
            .orderNo(orderNo)
            .orderSource(orderSource)
            .skuId(skuId)
            .lotId(lotId)
            .qty(qty)
            .build();
    }

    private TransferIntransitInventory getTransferIntransitInventory(Long skuId, BigDecimal qty) {
        TransferIntransitInventory intransitInventory = new TransferIntransitInventory();
        intransitInventory.setFromLogicInventoryLocationCode(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getLogicInventoryLocationCode());
        intransitInventory.setFromWarehouseId(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getWarehouseId());
        intransitInventory.setFromCargoOwnerId(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getCargoOwnerId());
        intransitInventory.setToLogicInventoryLocationCode(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getLogicInventoryLocationCode());
        intransitInventory.setToWarehouseId(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getWarehouseId());
        intransitInventory.setToCargoOwnerId(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION.getCargoOwnerId());
        intransitInventory.setPlanQty(qty);
        intransitInventory.setSkuId(skuId);
        intransitInventory.setIntransitQty(qty);
        intransitInventory.setWaitAllocQty(BigDecimal.ZERO);
        intransitInventory.setAllocQty(qty);
        return intransitInventory;
    }


}
