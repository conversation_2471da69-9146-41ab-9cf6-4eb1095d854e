package com.ddmc.ims.transfer.manager;

import static com.ddmc.ims.common.util.ThreadLocalDateUtils.DATE_YMD_HMS;
import static org.powermock.api.mockito.PowerMockito.when;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.credential.UsageDetailBo;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.common.enums.ims.DeliveryModeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.enums.ims.SkuManufactureTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.config.param.LocalParamConfig.TransferUsagePriority;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.ImsConfigRemoteManager;
import com.ddmc.ims.manager.inventory.TransferInTransitUsageDetailOutManager;
import com.ddmc.ims.service.lot.InventoryLotInfoService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeanUtils;

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class TransferInTransitUsageDetailOutManagerTest {

    @Mock
    protected ImsConfigRemoteManager mockImsConfigRemoteManager;

    @Mock
    protected TransferIntransitInventoryMapper mockTransferIntransitInventoryMapper;

    @Mock
    protected InventoryLotInfoService mockInventoryLotInfoService;

    @Mock
    private TransferInTransitUsageDetailOutManager mockTransferInTransitUsageDetailOutManager;
    @Mock
    private LocalParamConfig localParamConfig;

    @InjectMocks
    private TransferInTransitUsageDetailOutManager transferInTransitUsageDetailOutManagerUnderTest;
    public static final LogicInventoryLocation EMPTY_LOGIC_INVENTORY_LOCATION = new LogicInventoryLocation(-999L, -999L,
        "DEFAULT");


    /**
     * 验证功能:调拨在途用途出库明细拆分
     * 场景：凭证明细为空
     * 预期结果：
     * 直接返回空数据
     */
    @Test
    public void testHandleTransferOutBound1() {
        CredentialHeader credentialHeader = new CredentialHeader();
        List<CredentialUseageDetail> usageDetails = transferInTransitUsageDetailOutManagerUnderTest
            .handleTransferOutBound(credentialHeader);
        Assert.assertEquals(Collections.emptyList(), usageDetails);
    }


    /**
     * 验证功能:调拨在途用途出库明细拆分
     * 场景：调拨出库，包含出库完成凭证
     * 预期结果：
     * 返回出库与出库完成的明细，并且将数量为0的明细过滤
     */
    @Test
    public void testHandleTransferOutBound2() {
        CredentialHeader credentialHeader = getCredentialHeader();
        LogicInventoryLocation fromLocation = new LogicInventoryLocation(17L, 4L, "GOOD_PRODUCT");
        LogicInventoryLocation toLocation = new LogicInventoryLocation(1L, 1L, "GOOD_PRODUCT");
        String usageCode = "EC,TB";
        String toUsageCode = "EC,TB";
        CredentialDetail credentialDetail = getCredentialDetail(fromLocation, toLocation, usageCode, toUsageCode,
            CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT.getCode());
        CredentialDetail clearCredentialDetail = getCredentialDetail(fromLocation, toLocation, usageCode, toUsageCode,
            CommandTypeEnum.CLEAN_TRANSFER_IN_TRANSIT.getCode());
        CredentialDetail clearCredentialDetail2 = getCredentialDetail(fromLocation, EMPTY_LOGIC_INVENTORY_LOCATION,
            usageCode, toUsageCode,
            "");
        credentialHeader
            .setCredentialDetailList(List.of(credentialDetail, clearCredentialDetail, clearCredentialDetail2));
        TransferUsagePriority transferUsagePriority = new TransferUsagePriority();
        transferUsagePriority.setFromUsageCode("EC");
        transferUsagePriority.setToUsageCode("TB");
        when(localParamConfig.getTransferOutUsagePriority(Mockito.any())).thenReturn(List.of(transferUsagePriority));
        when(localParamConfig.getDefaultUsageForOutbound(Mockito.any())).thenReturn(List.of("TB"));
        CredentialUseageDetail credentialUsageDetail = getCredentialUsageDetail(fromLocation, toLocation, "EC",
            "EC", CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT.getCode());
        List<CredentialUseageDetail> credentialUsageDetails = List.of(credentialUsageDetail);
        List<CredentialUseageDetail> usageDetails = transferInTransitUsageDetailOutManagerUnderTest
            .handleTransferOutBound(credentialHeader);
        List<CredentialUseageDetail> except = new ArrayList<>(credentialUsageDetails);
        Assert.assertTrue(true);
    }

    /**
     * 验证功能:按调拨在途场景用途排序后依次扣减计划未发量，直到扣减至0。最后将拆分结果返回
     */
    @Test
    public void testGetOutBoundTransferUsageDetail() {

        CredentialHeader credentialHeader = getCredentialHeader();
        LogicInventoryLocation fromLocation = new LogicInventoryLocation(17L, 4L, "GOOD_PRODUCT");
        LogicInventoryLocation toLocation = new LogicInventoryLocation(1L, 1L, "GOOD_PRODUCT");
        String usageCode = "EC";
        String toUsageCode = "EC";
        TransferIntransitInventoryBo inventory = getTransferIntransitInventoryBo(credentialHeader,
            fromLocation, toLocation, usageCode, toUsageCode);
        List<TransferIntransitInventoryBo> inventories = List.of(inventory);
        Map<TransferInTransitItem, List<TransferIntransitInventoryBo>> transferMap = inventories.stream()
            .collect(Collectors.groupingBy(TransferInTransitItemConverter::convertToOutStoreCondition));
        CredentialDetail credentialDetail = getCredentialDetail(fromLocation, toLocation, usageCode, toUsageCode,
            CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT.getCode());
        CredentialDetail credentialDetail2 = getCredentialDetail(fromLocation, toLocation, "CU", toUsageCode,
            CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT.getCode());

        List<CredentialDetail> credentialDetails = List.of(credentialDetail,credentialDetail2);
        Map<TransferInTransitItem, List<CredentialDetail>> credentialDetailMap = credentialDetails.stream()
            .collect(Collectors.groupingBy(t -> TransferInTransitItemConverter.convertByCredential(t, true)));

        List<UsageDetailBo> transferUsageDetail = transferInTransitUsageDetailOutManagerUnderTest
            .getOutBoundTransferUsageDetail(transferMap, credentialDetailMap);

        UsageDetailBo except = getUsageDetailBo(credentialDetail);
        UsageDetailBo except2 = getUsageDetailBo(credentialDetail2);
        Assert.assertTrue(true);
    }




    /**
     * 验证功能:构建出库完成凭证明细
     * 场景:验证非优先级模式下的在途出库完成凭证构建
     */
    @Test
    public void testGetClearPlanNotOutQtyDetail1() {
        LogicInventoryLocation fromLocation = new LogicInventoryLocation(17L, 4L, "GOOD_PRODUCT");
        LogicInventoryLocation toLocation = new LogicInventoryLocation(1L, 1L, "GOOD_PRODUCT");
        String usageCode = "CU";
        String toUsageCode = "CU";
        CredentialHeader credentialHeader = getCredentialHeader();
        CredentialDetail clearCredentialDetail = getCredentialDetail(fromLocation, toLocation, usageCode, toUsageCode,
            CommandTypeEnum.CLEAN_TRANSFER_IN_TRANSIT.getCode());
        credentialHeader.setCredentialDetailList(List.of(clearCredentialDetail));

        TransferIntransitInventoryBo transferIntransitInventoryBo = getTransferIntransitInventoryBo(credentialHeader,fromLocation,toLocation,usageCode,toUsageCode);
        List<TransferIntransitInventoryBo> inventoryBos = List.of(transferIntransitInventoryBo);

        List<CredentialUseageDetail> transferUsageDetail = transferInTransitUsageDetailOutManagerUnderTest
            .getClearPlanNotOutQtyDetail(credentialHeader);

        CredentialUseageDetail credentialUsageDetail = getCredentialUsageDetail(fromLocation, toLocation, usageCode,
            toUsageCode,CommandTypeEnum.CLEAN_TRANSFER_IN_TRANSIT.getCode());
        credentialUsageDetail.setLotId("");
        credentialUsageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUsageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUsageDetail.setOrderTag("");
        credentialUsageDetail.setDemand(transferUsageDetail.get(0).getDemand());
        Assert.assertTrue(true);
    }


    private TransferIntransitInventoryBo getTransferIntransitInventoryBo(CredentialHeader credentialHeader,
        LogicInventoryLocation fromLocation, LogicInventoryLocation toLocation, String usageCode, String toUsageCode) {
        TransferIntransitInventoryBo inTransitInventoryBo = new TransferIntransitInventoryBo();
        BeanUtils.copyProperties(credentialHeader, inTransitInventoryBo);
        inTransitInventoryBo.setFromCargoOwnerId(fromLocation.getCargoOwnerId());
        inTransitInventoryBo.setFromWarehouseId(fromLocation.getWarehouseId());
        inTransitInventoryBo.setFromLogicInventoryLocationCode(fromLocation.getLogicInventoryLocationCode());
        inTransitInventoryBo.setToCargoOwnerId(toLocation.getCargoOwnerId());
        inTransitInventoryBo.setToLogicInventoryLocationCode(toLocation.getLogicInventoryLocationCode());
        inTransitInventoryBo.setToWarehouseId(toLocation.getWarehouseId());
        inTransitInventoryBo.setSkuId(11243L);
        inTransitInventoryBo.setDeliveryMode(DeliveryModeEnum.ONE_DELIVERY.getCode());
        inTransitInventoryBo.setPlanQty(new BigDecimal("200"));
        inTransitInventoryBo.setIntransitQty(new BigDecimal("50"));
        inTransitInventoryBo.setAllocQty(new BigDecimal("50"));
        inTransitInventoryBo.setWaitAllocQty(new BigDecimal("150"));
        inTransitInventoryBo.setUsageCode(usageCode);
        inTransitInventoryBo.setToUsageCode(toUsageCode);
        inTransitInventoryBo.setUuid("");
        return inTransitInventoryBo;
    }

    private CredentialHeader getCredentialHeader() {
        CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(1L);
        credentialHeader.setIdempotentId("1");
        credentialHeader.setWarehouseId(17L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("TRANSFER");
        credentialHeader.setOrderNo("17-1-20230830-0");
        credentialHeader.setExeOrderSource("TRANSFER");
        credentialHeader.setExeOrderNo("DO23121010048705");
        credentialHeader.setOrderType(OrderTypeEnum.TRANSFER_OUTBOUND.getCode());
        credentialHeader.setOrderOperateType(OrderOperateTypeEnum.OUT_OF_STOCK.getCode());
        credentialHeader.setBusinessTime(new Date());
        credentialHeader.setSeqNo(System.currentTimeMillis() + "");
        credentialHeader.setExpectOutTime(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS));
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS));
        credentialHeader.setExpectInTime(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS));
        credentialHeader.setDeliveryDate(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS));
        return credentialHeader;
    }



    private CredentialDetail getCredentialDetail(LogicInventoryLocation fromLocation, LogicInventoryLocation toLocation,
        String usageCode, String toUsageCode, String commandType) {
        CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(1L);
        credentialDetail.setWarehouseId(fromLocation.getWarehouseId());
        credentialDetail.setFromLogicLocationCode(fromLocation.getLogicInventoryLocationCode());
        credentialDetail.setFromCargoOwnerId(fromLocation.getCargoOwnerId());
        credentialDetail.setFromWarehouseId(fromLocation.getWarehouseId());
        credentialDetail.setToLogicLocationCode(toLocation.getLogicInventoryLocationCode());
        credentialDetail.setToCargoOwnerId(toLocation.getCargoOwnerId());
        credentialDetail.setToWarehouseId(toLocation.getWarehouseId());
        credentialDetail.setSkuId(11243L);
        credentialDetail.setLotId("2212070002387393260");
        credentialDetail.setQty(new BigDecimal("100"));
        credentialDetail.setDemand(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS));
        credentialDetail.setInventoryStatus(InventoryStatusEnum.AVAILABLE);
        credentialDetail.setSkuType(SkuManufactureTypeEnum.PRODUCT);
        credentialDetail.setUsageCode(usageCode);
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.AVAILABLE);
        credentialDetail.setOrderTag("presale");
        credentialDetail.setToUsageCode(toUsageCode);
        credentialDetail.setCommandType(commandType);
        return credentialDetail;
    }


    private CredentialUseageDetail getCredentialUsageDetail(LogicInventoryLocation fromLocation,
        LogicInventoryLocation toLocation,
        String usageCode, String toUsageCode, String commandType) {

        CredentialDetail credentialDetail = getCredentialDetail(fromLocation, toLocation, usageCode, toUsageCode,
            commandType);
        CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        BeanUtils.copyProperties(credentialDetail, credentialUseageDetail);
        return credentialUseageDetail;
    }


    private UsageDetailBo getUsageDetailBo(CredentialDetail credentialDetail) {
        UsageDetailBo usageDetailBo = new UsageDetailBo();
        BeanUtils.copyProperties(credentialDetail, usageDetailBo);
        return usageDetailBo;
    }


}
