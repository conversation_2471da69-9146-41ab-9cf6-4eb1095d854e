package com.ddmc.ims.transfer.command.helper;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.bo.inventory.TransferManufactureDateItem;
import com.ddmc.ims.command.helper.TransferInTransitInventoryInStoreHelper;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventoryLotDetail;
import com.ddmc.ims.test.common.TestConstants;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;

public class TransferInTransitInventoryInStoreHelperTest {

    @Test
    public void testProcessTransferInTransitInventoryCommands() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(getTransferIntransitInventoryCommand());
        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setFromCargoOwnerId(0L);
        transferIntransitInventory.setFromWarehouseId(0L);
        transferIntransitInventory.setFromLogicInventoryLocationCode("fromLogicInventoryLocationCode");
        transferIntransitInventory.setToCargoOwnerId(0L);
        transferIntransitInventory.setToLogicInventoryLocationCode("toLogicInventoryLocationCode");
        transferIntransitInventory.setToWarehouseId(0L);
        transferIntransitInventory.setSkuId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("orderNo");
        transferIntransitInventory.setDeliveryMode(0);
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setPlanQty(new BigDecimal("0.00"));
        transferIntransitInventory.setIntransitQty(new BigDecimal("0.00"));
        final Map<TransferInTransitItem, List<TransferIntransitInventory>> skuTransferInventoryMap = Map.ofEntries(
            Map.entry(TransferInTransitItem.builder().build(), List.of(transferIntransitInventory)));

        // Run the test
        TransferInTransitInventoryInStoreHelper
            .processTransferInTransitInventoryCommands(commands, skuTransferInventoryMap);
        Assert.assertTrue(true);
        // Verify the results
    }

    private TransferIntransitInventoryCommand getTransferIntransitInventoryCommand() {
        return TransferIntransitInventoryCommand.builder()
            .skuId(1L)
            .orderNo("1-2-20230807-1")
            .toLocation(TestConstants.PROCESS_LOGIC_INVENTORY_LOCATION)
            .lotId("lotId")
            .qty(BigDecimal.ZERO).fromLocation(TestConstants.COMMON_LOGIC_INVENTORY_LOCATION)
            .build();
    }

    @Test
    public void testProcessTransferInTransitInventoryLotDetailCommands() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(getTransferIntransitInventoryCommand());
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final Map<String, InventoryLotInfo> lotInfoMap = Map.ofEntries(Map.entry("value", lotInfo));
        final TransferIntransitInventoryLotDetail lotDetail = new TransferIntransitInventoryLotDetail();
        lotDetail.setId(0L);
        lotDetail.setTransferIntransitInventoryId(0L);
        lotDetail.setFromCargoOwnerId(0L);
        lotDetail.setFromWarehouseId(0L);
        lotDetail.setFromLogicInventoryLocationCode("fromLogicInventoryLocationCode");
        lotDetail.setToCargoOwnerId(0L);
        lotDetail.setToLogicInventoryLocationCode("toLogicInventoryLocationCode");
        lotDetail.setToWarehouseId(0L);
        lotDetail.setOrderSource("orderSource");
        lotDetail.setOrderNo("orderNo");
        lotDetail.setRefOrderNo("refOrderNo");
        lotDetail.setSkuId(0L);
        lotDetail.setLotId("lotId");
        lotDetail.setIntransitQty(new BigDecimal("0.00"));
        lotDetail.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final Map<TransferManufactureDateItem, List<TransferIntransitInventoryLotDetail>> existMap = Map.ofEntries(
            Map.entry(TransferManufactureDateItem.builder().build(), List.of(lotDetail)));

        // Run the test
        TransferInTransitInventoryInStoreHelper
            .processTransferInTransitInventoryLotDetailCommands(commands, lotInfoMap, existMap);
        Assert.assertTrue(true);
        // Verify the results
    }
}
