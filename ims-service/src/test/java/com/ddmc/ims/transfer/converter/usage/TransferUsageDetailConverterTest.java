package com.ddmc.ims.transfer.converter.usage;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.context.ConfirmContext;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.converter.usage.TransferUsageDetailConverter;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.inventory.TransferInTransitUsageDetailFinishInManager;
import com.ddmc.ims.manager.inventory.TransferInTransitUsageDetailInManager;
import com.ddmc.ims.manager.inventory.TransferInTransitUsageDetailOutManager;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TransferUsageDetailConverterTest {

    @Mock
    private TransferInTransitUsageDetailOutManager mockTransferInTransitUsageDetailOutManager;
    @Mock
    private TransferInTransitUsageDetailInManager mockTransferInTransitUsageDetailInManager;
    @Mock
    private TransferInTransitUsageDetailFinishInManager mockTransferInTransitUsageDetailFinishInManager;

    @InjectMocks
    private TransferUsageDetailConverter transferUsageDetailConverterUnderTest;

    public static final String NOT_SUPPORT_ORDER_OPERATE = "不支持的操作类型";

    /**
     * 验证功能:调拨在途凭证明细转用途维度凭证明细
     * 场景：非四要素的单子（老逻辑）
     * 预期结果：
     * 直接转换一比一转换为用途维度凭证明细
     */
    @Test
    public void testConvert_notExactlyThreeHyphens() {

        CredentialDetail credentialDetail = getCredentialDetail();
        CredentialHeader defaultHeader = getDefaultHeader("*********");
        defaultHeader.setCredentialDetailList(List.of(credentialDetail));
        ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(defaultHeader).build();

        List<CredentialUseageDetail> expectedResult = List.of(getCredentialUsageDetails());
        List<CredentialUseageDetail> result = transferUsageDetailConverterUnderTest.convert(confirmContext);

        Mockito.verify(mockTransferInTransitUsageDetailFinishInManager, Mockito.times(0))
            .handleTransferFinishInBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailOutManager, Mockito.times(0)).handleTransferOutBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailInManager, Mockito.times(0)).handleTransferInBound(any());

        assertThat(result).isEqualTo(expectedResult);
    }


    /**
     * 验证功能:调拨在途凭证明细转用途维度凭证明细
     * 场景：不支持的操作类型
     * 预期结果：
     * 抛出异常
     */
    @Test
    public void testConvert_notSupportOrderOperateType() {

        CredentialDetail credentialDetail = getCredentialDetail();
        CredentialHeader defaultHeader = getDefaultHeader("17-1-20230830-0");
        defaultHeader.setOrderOperateType(-99);
        defaultHeader.setCredentialDetailList(List.of(credentialDetail));
        ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(defaultHeader).build();
        Exception exception = null;
        try {
            transferUsageDetailConverterUnderTest.convert(confirmContext);
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
        Assert.assertEquals(NOT_SUPPORT_ORDER_OPERATE, exception.getMessage());
    }


    /**
     * 验证功能:调拨在途凭证明细转用途维度凭证明细
     * 场景：申请出库
     * 预期结果：
     * 直接转换一比一转换为用途维度凭证明细
     */
    @Test
    public void testConvert_HandleApplyOutOfStock() {

        CredentialDetail credentialDetail = getCredentialDetail();
        CredentialHeader defaultHeader = getDefaultHeader("17-1-20230830-0");
        defaultHeader.setOrderOperateType(0);
        defaultHeader.setCredentialDetailList(List.of(credentialDetail));
        ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(defaultHeader).build();

        List<CredentialUseageDetail> expectedResult = List.of(getCredentialUsageDetails());
        List<CredentialUseageDetail> result = transferUsageDetailConverterUnderTest.convert(confirmContext);

        Mockito.verify(mockTransferInTransitUsageDetailFinishInManager, Mockito.times(0))
            .handleTransferFinishInBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailOutManager, Mockito.times(0)).handleTransferOutBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailInManager, Mockito.times(0)).handleTransferInBound(any());

        assertThat(result).isEqualTo(expectedResult);
    }


    /**
     * 验证功能:调拨在途凭证明细转用途维度凭证明细
     * 场景：调拨拒收
     * 预期结果：
     * 直接转换一比一转换为用途维度凭证明细
     */
    @Test
    public void testConvert_HandleTransferReject() {

        CredentialDetail credentialDetail = getCredentialDetail();
        CredentialHeader defaultHeader = getDefaultHeader("17-1-20230830-0");
        defaultHeader.setOrderOperateType(13);
        defaultHeader.setCredentialDetailList(List.of(credentialDetail));
        ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(defaultHeader).build();

        List<CredentialUseageDetail> expectedResult = List.of(getCredentialUsageDetails());
        List<CredentialUseageDetail> result = transferUsageDetailConverterUnderTest.convert(confirmContext);

        Mockito.verify(mockTransferInTransitUsageDetailFinishInManager, Mockito.times(0))
            .handleTransferFinishInBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailOutManager, Mockito.times(0)).handleTransferOutBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailInManager, Mockito.times(0)).handleTransferInBound(any());

        assertThat(result).isEqualTo(expectedResult);
    }


    /**
     * 验证功能:调拨在途凭证明细转用途维度凭证明细
     * 场景：取消出库申请
     * 预期结果：
     * 调用TransferInTransitUsageDetailCleanManager.handleTransferCleanBound
     */
    @Test
    public void testConvert_HandleCancelOutApply() {

        CredentialDetail credentialDetail = getCredentialDetail();
        CredentialHeader defaultHeader = getDefaultHeader("17-1-20230830-0");
        defaultHeader.setOrderOperateType(2);
        defaultHeader.setCredentialDetailList(List.of(credentialDetail));
        ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(defaultHeader).build();

        List<CredentialUseageDetail> expectedResult = List.of(getCredentialUsageDetails());
        List<CredentialUseageDetail> result = transferUsageDetailConverterUnderTest.convert(confirmContext);
        Mockito.verify(mockTransferInTransitUsageDetailFinishInManager, Mockito.times(0))
            .handleTransferFinishInBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailOutManager, Mockito.times(0)).handleTransferOutBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailInManager, Mockito.times(0)).handleTransferInBound(any());

        assertThat(result).isEqualTo(expectedResult);
    }


    /**
     * 验证功能:调拨在途凭证明细转用途维度凭证明细
     * 场景：完成入库
     * 预期结果：
     * 调用TransferInTransitUsageDetailFinishInManager.handleTransferFinishInBound
     */
    @Test
    public void testConvert_HandleFinishInOfStock() {

        CredentialDetail credentialDetail = getCredentialDetail();
        CredentialHeader defaultHeader = getDefaultHeader("17-1-20230830-0");
        defaultHeader.setOrderOperateType(9);
        defaultHeader.setCredentialDetailList(List.of(credentialDetail));
        ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(defaultHeader).build();

        List<CredentialUseageDetail> expectedResult = List.of(getCredentialUsageDetails());
        when(mockTransferInTransitUsageDetailFinishInManager.handleTransferFinishInBound(defaultHeader))
            .thenReturn(expectedResult);
        List<CredentialUseageDetail> result = transferUsageDetailConverterUnderTest.convert(confirmContext);
        Mockito.verify(mockTransferInTransitUsageDetailFinishInManager, Mockito.times(1))
            .handleTransferFinishInBound(defaultHeader);
        Mockito.verify(mockTransferInTransitUsageDetailOutManager, Mockito.times(0)).handleTransferOutBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailInManager, Mockito.times(0)).handleTransferInBound(any());

        assertThat(result).isEqualTo(expectedResult);
    }


    /**
     * 验证功能:调拨在途凭证明细转用途维度凭证明细
     * 场景：发车单出库
     * 预期结果：
     * 调用TransferInTransitUsageDetailOutManager.handleTransferOutBound
     */
    @Test
    public void testConvert_HandleTransferOut() {

        CredentialDetail credentialDetail = getCredentialDetail();
        CredentialHeader defaultHeader = getDefaultHeader("17-1-20230830-0");
        defaultHeader.setOrderOperateType(3);
        defaultHeader.setOrderType("DB001");
        defaultHeader.setCredentialDetailList(List.of(credentialDetail));
        ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(defaultHeader).build();

        List<CredentialUseageDetail> expectedResult = List.of(getCredentialUsageDetails());
        when(mockTransferInTransitUsageDetailOutManager.handleTransferOutBound(defaultHeader))
            .thenReturn(expectedResult);
        List<CredentialUseageDetail> result = transferUsageDetailConverterUnderTest.convert(confirmContext);
        Mockito.verify(mockTransferInTransitUsageDetailFinishInManager, Mockito.times(0))
            .handleTransferFinishInBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailOutManager, Mockito.times(1))
            .handleTransferOutBound(defaultHeader);
        Mockito.verify(mockTransferInTransitUsageDetailInManager, Mockito.times(0)).handleTransferInBound(any());

        assertThat(result).isEqualTo(expectedResult);
    }

    /**
     * 验证功能:调拨在途凭证明细转用途维度凭证明细
     * 场景：调拨单出库
     * 预期结果：
     * 调用TransferInTransitUsageDetailOutManager.handleTransferOutBound
     */
    @Test
    public void testConvert_HandleTransferDoOut() {

        CredentialDetail credentialDetail = getCredentialDetail();
        CredentialHeader defaultHeader = getDefaultHeader("17-1-20230830-0");
        defaultHeader.setOrderOperateType(3);
        defaultHeader.setOrderType("DB002");
        defaultHeader.setCredentialDetailList(List.of(credentialDetail));
        ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(defaultHeader).build();

        List<CredentialUseageDetail> expectedResult = List.of(getCredentialUsageDetails());
        when(mockTransferInTransitUsageDetailOutManager.handleTransferOutBound(defaultHeader))
            .thenReturn(expectedResult);
        List<CredentialUseageDetail> result = transferUsageDetailConverterUnderTest.convert(confirmContext);
        Mockito.verify(mockTransferInTransitUsageDetailFinishInManager, Mockito.times(0))
            .handleTransferFinishInBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailOutManager, Mockito.times(1))
            .handleTransferOutBound(defaultHeader);
        Mockito.verify(mockTransferInTransitUsageDetailInManager, Mockito.times(0)).handleTransferInBound(any());

        assertThat(result).isEqualTo(expectedResult);
    }


    /**
     * 验证功能:调拨在途凭证明细转用途维度凭证明细
     * 场景：调拨入库
     * 预期结果：
     * 调用TransferInTransitUsageDetailInManager.handleTransferInBound
     */
    @Test
    public void testConvert_HandleTransferIn() {

        CredentialDetail credentialDetail = getCredentialDetail();
        CredentialHeader defaultHeader = getDefaultHeader("17-1-20230830-0");
        defaultHeader.setOrderOperateType(8);
        defaultHeader.setOrderType("DB003");
        defaultHeader.setCredentialDetailList(List.of(credentialDetail));
        ConfirmContext confirmContext = ConfirmContext.builder().credentialHeader(defaultHeader).build();

        List<CredentialUseageDetail> expectedResult = List.of(getCredentialUsageDetails());
        when(mockTransferInTransitUsageDetailInManager.handleTransferInBound(defaultHeader)).thenReturn(expectedResult);
        List<CredentialUseageDetail> result = transferUsageDetailConverterUnderTest.convert(confirmContext);
        Mockito.verify(mockTransferInTransitUsageDetailFinishInManager, Mockito.times(0))
            .handleTransferFinishInBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailOutManager, Mockito.times(0)).handleTransferOutBound(any());
        Mockito.verify(mockTransferInTransitUsageDetailInManager, Mockito.times(1))
            .handleTransferInBound(defaultHeader);

        assertThat(result).isEqualTo(expectedResult);
    }


    /**
     * 验证功能:转换器支持的单据类型
     * 场景：调拨入库、调拨单出库、发车单出库、其他类型
     * 预期结果：
     * 调拨入库、调拨单出库、发车单出库为true, 其他类型单据为false
     */
    @Test
    public void testIsSupport() {

        Assert.assertTrue(
            transferUsageDetailConverterUnderTest.isSupport(OrderTypeEnum.TRANSFER_OUTBOUND.getCode(), 0));
        Assert.assertTrue(
            transferUsageDetailConverterUnderTest.isSupport(OrderTypeEnum.TRANSFER_INBOUND.getCode(), 0));
        Assert.assertTrue(
            transferUsageDetailConverterUnderTest.isSupport(OrderTypeEnum.TRANSFER_DO_OUTBOUND.getCode(), 0));
        Assert.assertFalse(
            transferUsageDetailConverterUnderTest.isSupport(OrderTypeEnum.SELF_USE_OUTBOUND.getCode(), 0));

    }


    private CredentialHeader getDefaultHeader(String orderNo) {
        CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(0L);
        credentialHeader.setIdempotentId("");
        credentialHeader.setWarehouseId(0L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("");
        credentialHeader.setOrderNo(orderNo);
        credentialHeader.setExeOrderSource("");
        credentialHeader.setExeOrderNo("");
        credentialHeader.setOrderType("");
        credentialHeader.setOrderOperateType(0);
        credentialHeader.setBusinessTime(new Date());
        credentialHeader.setSeqNo("");
        credentialHeader.setExpectOutTime(new Date());
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(new Date());
        credentialHeader.setExpectInTime(new Date());
        credentialHeader.setCreateTime(new Date());
        credentialHeader.setUpdateTime(new Date());
        credentialHeader.setEndDateTime(new Date());
        credentialHeader.setDeliveryDate(new Date());
        credentialHeader.setCredentialDetailList(Lists.newArrayList());
        credentialHeader.setCredentialUseageDetailList(Lists.newArrayList());
        credentialHeader.setCredentialHeaderExtList(Lists.newArrayList());
        credentialHeader.setOriDeliveryDate(new Date());
        return credentialHeader;
    }


    private CredentialDetail getCredentialDetail() {
        CredentialDetail detail = new CredentialDetail();
        detail.setCredentialHeaderId(1L);
        detail.setFromLogicLocationCode("fromLogicLocationCode");
        detail.setFromCargoOwnerId(0L);
        detail.setFromWarehouseId(0L);
        detail.setToLogicLocationCode("toLogicLocationCode");
        detail.setToCargoOwnerId(0L);
        detail.setToWarehouseId(0L);
        detail.setSkuId(0L);
        detail.setLotId("lotId");
        detail.setQty(new BigDecimal("0.00"));
        detail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        detail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        detail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        detail.setUsageCode("usageCode");
        detail.setOrderTag("orderTag");
        detail.setToUsageCode("toUsageCode");
        detail.setCommandType("commandType");
        detail.setTodaySale(false);
        return detail;
    }


    private CredentialUseageDetail getCredentialUsageDetails() {
        CredentialUseageDetail detail = new CredentialUseageDetail();
        detail.setCredentialHeaderId(1L);
        detail.setFromLogicLocationCode("fromLogicLocationCode");
        detail.setFromCargoOwnerId(0L);
        detail.setFromWarehouseId(0L);
        detail.setToLogicLocationCode("toLogicLocationCode");
        detail.setToCargoOwnerId(0L);
        detail.setToWarehouseId(0L);
        detail.setSkuId(0L);
        detail.setLotId("lotId");
        detail.setQty(new BigDecimal("0.00"));
        detail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        detail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        detail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        detail.setUsageCode("usageCode");
        detail.setOrderTag("orderTag");
        detail.setToUsageCode("toUsageCode");
        detail.setCommandType("commandType");
        detail.setTodaySale(false);
        return detail;
    }


}
