package com.ddmc.ims.transfer.command;

import static com.ddmc.ims.common.util.ThreadLocalDateUtils.DATE_YMD_HMS;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.command.TransferIntransitInventoryPublishCommandHandle;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.DeliveryModeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.manager.inventory.TransferInTransitManager;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeanUtils;

@RunWith(MockitoJUnitRunner.class)
public class TransferIntransitInventoryPublishCommandHandleTest {

    @Mock
    private TransferInTransitManager mockTransferInTransitManager;
    @Mock
    private TransferTransitUsageDetailManager mockTransferTransitUsageDetailManager;

    @InjectMocks
    private TransferIntransitInventoryPublishCommandHandle transferIntransitInventoryPublishCommandHandleUnderTest;


    /**
     * 验证功能:调拨在途登记
     * 场景：数据库不存在同四要素的调拨在途
     * 预期结果：
     * needInsert 不为空，needUpdate为空，做插入操作
     */
    @Test
    public void testHandleInventoryCommand1() {
        LogicInventoryLocation fromLocation = new LogicInventoryLocation(17L, 1L, "GOOD_PRODUCT");
        LogicInventoryLocation toLocation = new LogicInventoryLocation(1L, 4L, "GOOD_PRODUCT");
        String usageCode = "EC";
        TransferIntransitInventoryCommand command = getTransferIntransitInventoryCommand(fromLocation, toLocation,
            usageCode);

        List<TransferIntransitInventoryCommand> commands = List.of(command);

        //数据库返回为空
        when(mockTransferTransitUsageDetailManager.getTransferInTransitInventoryList(commands))
            .thenReturn(Collections.emptyList());

        transferIntransitInventoryPublishCommandHandleUnderTest.handleInventoryCommand(commands);

        TransferIntransitInventory exceptTransferInventory = getTransferIntransitInventory(fromLocation, toLocation,
            command, command.getUsageCode());

        verify(mockTransferInTransitManager)
            .insertOrAddPlanOutWaitAllocQty(List.of(exceptTransferInventory), Collections.emptyList());
    }


    /**
     * 验证功能:调拨在途登记
     * 场景：数据库存在同四要素的调拨在途,但是用途不一样
     * 预期结果：
     * needInsert 不为空，needUpdate为空，做插入操作
     */
    @Test
    public void testHandleInventoryCommand2() {
        LogicInventoryLocation fromLocation = new LogicInventoryLocation(17L, 1L, "GOOD_PRODUCT");
        LogicInventoryLocation toLocation = new LogicInventoryLocation(1L, 4L, "GOOD_PRODUCT");
        TransferIntransitInventoryCommand command = getTransferIntransitInventoryCommand(fromLocation, toLocation,
            "EC");

        List<TransferIntransitInventoryCommand> commands = List.of(command);

        TransferIntransitInventory dbInventory = getTransferIntransitInventory(fromLocation, toLocation, command, "TB");

        //数据库返回的用途不一样
        when(mockTransferTransitUsageDetailManager.getTransferInTransitInventoryList(commands))
            .thenReturn(Collections.singletonList(dbInventory));

        transferIntransitInventoryPublishCommandHandleUnderTest.handleInventoryCommand(commands);

        TransferIntransitInventory exceptTransferInventory = getTransferIntransitInventory(fromLocation, toLocation,
            command, command.getUsageCode());

        verify(mockTransferInTransitManager)
            .insertOrAddPlanOutWaitAllocQty(List.of(exceptTransferInventory), Collections.emptyList());
    }




    /**
     * 验证功能:调拨在途登记
     * 场景：数据库存在同维度的调拨在途
     * 预期结果：
     * needInsert 为空，needUpdate为空，做更新操作
     */
    @Test
    public void testHandleInventoryCommand3() {
        LogicInventoryLocation fromLocation = new LogicInventoryLocation(17L, 1L, "GOOD_PRODUCT");
        LogicInventoryLocation toLocation = new LogicInventoryLocation(1L, 4L, "GOOD_PRODUCT");
        TransferIntransitInventoryCommand command = getTransferIntransitInventoryCommand(fromLocation, toLocation,
            "EC");

        List<TransferIntransitInventoryCommand> commands = List.of(command);

        TransferIntransitInventory dbInventory = getTransferIntransitInventory(fromLocation, toLocation, command, "EC");

        //数据库返回的用途不一样
        when(mockTransferTransitUsageDetailManager.getTransferInTransitInventoryList(commands))
            .thenReturn(Collections.singletonList(dbInventory));

        transferIntransitInventoryPublishCommandHandleUnderTest.handleInventoryCommand(commands);

        TransferIntransitInventory exceptTransferInventory = getTransferIntransitInventory(fromLocation, toLocation,
            command, command.getUsageCode());

        verify(mockTransferInTransitManager)
            .insertOrAddPlanOutWaitAllocQty(Collections.emptyList(),List.of(exceptTransferInventory));
    }


    private TransferIntransitInventoryCommand getTransferIntransitInventoryCommand(LogicInventoryLocation fromLocation,
        LogicInventoryLocation toLocation, String usageCode) {
        return TransferIntransitInventoryCommand.builder()
            .fromLocation(fromLocation)
            .fromInventoryStatus(InventoryStatusEnum.AVAILABLE).toInventoryStatus(InventoryStatusEnum.AVAILABLE)
            .orderTag("presale").toLocation(toLocation).skuId(11243L).commandType(
                CommandTypeEnum.PUBLISH_TRANSFER_IN_TRANSIT).usageCode(usageCode).toUsageCode(usageCode)
            .orderSource("TRANSFER")
            .orderNo("17-1-20230830-0").qty(new BigDecimal("100")).deliveryMode(
                DeliveryModeEnum.ONE_DELIVERY)
            .expectOutTime(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS))
            .expectInTime(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS)).lotId("2212070002387393260")
            .orderOperateType(
                OrderOperateTypeEnum.APPLY_OUT_OF_STOCK.getCode()).exeOrderNo("DO23121010048705").build();
    }


    private TransferIntransitInventory getTransferIntransitInventory(LogicInventoryLocation fromLocation,
        LogicInventoryLocation toLocation, TransferIntransitInventoryCommand command, String usageCode) {
        TransferIntransitInventory exceptTransferInventory = new TransferIntransitInventory();
        BeanUtils.copyProperties(command, exceptTransferInventory);
        exceptTransferInventory.setFromCargoOwnerId(fromLocation.getCargoOwnerId());
        exceptTransferInventory.setFromWarehouseId(fromLocation.getWarehouseId());
        exceptTransferInventory.setFromLogicInventoryLocationCode(fromLocation.getLogicInventoryLocationCode());
        exceptTransferInventory.setToCargoOwnerId(toLocation.getCargoOwnerId());
        exceptTransferInventory.setToLogicInventoryLocationCode(toLocation.getLogicInventoryLocationCode());
        exceptTransferInventory.setToWarehouseId(toLocation.getWarehouseId());
        exceptTransferInventory.setPlanQty(command.getQty());
        exceptTransferInventory.setIntransitQty(BigDecimal.ZERO);
        exceptTransferInventory.setAllocQty(BigDecimal.ZERO);
        exceptTransferInventory.setVersion(0);
        exceptTransferInventory.setDeliveryMode(DeliveryModeEnum.ONE_DELIVERY.getCode());
        Date expectOutTime = Objects.isNull(command.getExpectOutTime()) ? DateUtils.truncate(new Date(), Calendar.DATE)
            : DateUtils.truncate(command.getExpectOutTime(), Calendar.DATE);
        exceptTransferInventory.setExpectOutTime(expectOutTime);
        Date expectInTime = Objects.isNull(command.getExpectInTime()) ? DateUtils.truncate(new Date(), Calendar.DATE)
            : DateUtils.truncate(command.getExpectInTime(), Calendar.DATE);
        exceptTransferInventory.setExpectInTime(expectInTime);
        exceptTransferInventory.setWaitAllocQty(command.getQty());
        exceptTransferInventory.setUsageCode(usageCode);
        return exceptTransferInventory;
    }


}
