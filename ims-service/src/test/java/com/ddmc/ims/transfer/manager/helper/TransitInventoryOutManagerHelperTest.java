package com.ddmc.ims.transfer.manager.helper;

import static com.ddmc.ims.common.util.ThreadLocalDateUtils.DATE_YMD_HMS;

import com.ddmc.ims.bo.credential.TransferIntransitInventoryBo;
import com.ddmc.ims.bo.inventory.TransferInTransitItem;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.CredentialStatus;
import com.ddmc.ims.common.enums.ims.DeliveryModeEnum;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.converter.TransferInTransitItemConverter;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.inventory.helper.TransitInventoryOutManagerHelper;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeanUtils;

@RunWith(MockitoJUnitRunner.class)
public class TransitInventoryOutManagerHelperTest {


    /**
     * 验证功能: 将凭证依次处理，不存在则构建数据塞入insertMap,存在则更新数据塞入existMap
     * 场景：existMap为空
     * 预期结果：
     * 执行构建结果保存在insertMap,且结果为构建的调拨在途数据
     */
    @Test
    public void testProcessTransferInTransitInventory_1() {
        CredentialHeader credentialHeader = getCredentialHeader();
        LogicInventoryLocation fromLocation = new LogicInventoryLocation(17L, 4L, "GOOD_PRODUCT");
        LogicInventoryLocation toLocation = new LogicInventoryLocation(1L, 1L, "GOOD_PRODUCT");
        String usageCode = "EC";
        String toUsageCode = "EC";
        TransferIntransitInventoryBo inventory = getTransferIntransitInventoryBo(credentialHeader,
            fromLocation, toLocation, usageCode, toUsageCode);
        CredentialUseageDetail credentialUsageDetail = getCredentialUsageDetail(fromLocation, toLocation, usageCode,
            toUsageCode, CommandTypeEnum.MODIFY_INVENTORY_AVAILABLE.getCode());
        List<CredentialUseageDetail> credentialUsageDetails = List.of(credentialUsageDetail);

        Map<TransferInTransitItem, List<TransferIntransitInventoryBo>> insertMap = new HashMap<>();
        Map<TransferInTransitItem, List<TransferIntransitInventoryBo>> existMap = new HashMap<>();

        TransitInventoryOutManagerHelper
            .processTransferInTransitInventory(credentialHeader, credentialUsageDetails, insertMap, existMap);
        TransferInTransitItem item = TransferInTransitItemConverter.convertToCondition(inventory, true);

        Assert.assertEquals(inventory.getPlanQty(), insertMap.get(item).get(0).getPlanQty());

    }

    private TransferIntransitInventoryBo getTransferIntransitInventoryBo(CredentialHeader credentialHeader,
        LogicInventoryLocation fromLocation, LogicInventoryLocation toLocation, String usageCode, String toUsageCode) {
        TransferIntransitInventoryBo inTransitInventoryBo = new TransferIntransitInventoryBo();
        BeanUtils.copyProperties(credentialHeader, inTransitInventoryBo);
        inTransitInventoryBo.setFromCargoOwnerId(fromLocation.getCargoOwnerId());
        inTransitInventoryBo.setFromWarehouseId(fromLocation.getWarehouseId());
        inTransitInventoryBo.setFromLogicInventoryLocationCode(fromLocation.getLogicInventoryLocationCode());
        inTransitInventoryBo.setToCargoOwnerId(toLocation.getCargoOwnerId());
        inTransitInventoryBo.setToLogicInventoryLocationCode(toLocation.getLogicInventoryLocationCode());
        inTransitInventoryBo.setToWarehouseId(toLocation.getWarehouseId());
        inTransitInventoryBo.setSkuId(11243L);
        inTransitInventoryBo.setDeliveryMode(DeliveryModeEnum.ONE_DELIVERY.getCode());
        inTransitInventoryBo.setPlanQty(new BigDecimal("100"));
        inTransitInventoryBo.setIntransitQty(new BigDecimal("100"));
        inTransitInventoryBo.setAllocQty(new BigDecimal("100"));
        inTransitInventoryBo.setWaitAllocQty(new BigDecimal("0"));
        inTransitInventoryBo.setUsageCode(usageCode);
        inTransitInventoryBo.setToUsageCode(toUsageCode);
        inTransitInventoryBo.setUuid("");
        inTransitInventoryBo.setId(null);
        return inTransitInventoryBo;
    }

    private CredentialHeader getCredentialHeader() {
        CredentialHeader credentialHeader = new CredentialHeader();
        credentialHeader.setId(1L);
        credentialHeader.setIdempotentId("1");
        credentialHeader.setWarehouseId(17L);
        credentialHeader.setStatus(CredentialStatus.INIT);
        credentialHeader.setOrderSource("TRANSFER");
        credentialHeader.setOrderNo("17-1-20230830-0");
        credentialHeader.setExeOrderSource("TRANSFER");
        credentialHeader.setExeOrderNo("DO23121010048705");
        credentialHeader.setOrderType(OrderTypeEnum.TRANSFER_OUTBOUND.getCode());
        credentialHeader.setOrderOperateType(OrderOperateTypeEnum.OUT_OF_STOCK.getCode());
        credentialHeader.setBusinessTime(new Date());
        credentialHeader.setSeqNo(System.currentTimeMillis() + "");
        credentialHeader.setExpectOutTime(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS));
        credentialHeader.setDeliveryMode(0);
        credentialHeader.setExpectArriveTime(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS));
        credentialHeader.setExpectInTime(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS));
        credentialHeader.setDeliveryDate(ThreadLocalDateUtils.parse("2023-08-30 00:00:00", DATE_YMD_HMS));
        return credentialHeader;
    }

    private CredentialUseageDetail getCredentialUsageDetail(LogicInventoryLocation fromLocation,
        LogicInventoryLocation toLocation,
        String usageCode, String toUsageCode, String commandType) {

        CredentialUseageDetail credentialDetail = new CredentialUseageDetail();
        credentialDetail.setCredentialHeaderId(1L);
        credentialDetail.setFromLogicLocationCode(fromLocation.getLogicInventoryLocationCode());
        credentialDetail.setFromCargoOwnerId(fromLocation.getCargoOwnerId());
        credentialDetail.setFromWarehouseId(fromLocation.getWarehouseId());
        credentialDetail.setToLogicLocationCode(toLocation.getLogicInventoryLocationCode());
        credentialDetail.setToCargoOwnerId(toLocation.getCargoOwnerId());
        credentialDetail.setToWarehouseId(toLocation.getWarehouseId());
        credentialDetail.setSkuId(11243L);
        credentialDetail.setLotId("2212070002387393260");
        credentialDetail.setQty(new BigDecimal("100"));
        credentialDetail.setDemand(ThreadLocalDateUtils.parse("2023-08-30 12:23:55", DATE_YMD_HMS));
        credentialDetail.setInventoryStatus(InventoryStatusEnum.AVAILABLE);
        credentialDetail.setUsageCode(usageCode);
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.AVAILABLE);
        credentialDetail.setOrderTag("presale");
        credentialDetail.setToUsageCode(toUsageCode);
        credentialDetail.setCommandType(commandType);
        return credentialDetail;
    }


}
