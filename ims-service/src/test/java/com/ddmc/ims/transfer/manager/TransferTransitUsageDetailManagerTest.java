package com.ddmc.ims.transfer.manager;

import static org.assertj.core.api.Assertions.assertThat;

import com.ddmc.ims.bo.command.TransferIntransitInventoryCommand;
import com.ddmc.ims.bo.inventory.UsageCodeSkuId;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.InventoryLotInfo;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.manager.ImsConfigRemoteManager;
import com.ddmc.ims.manager.inventory.TransferTransitUsageDetailManager;
import com.ddmc.ims.service.lot.InventoryLotInfoService;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TransferTransitUsageDetailManagerTest {

    @Mock
    private ImsConfigRemoteManager mockImsConfigRemoteManager;
    @Mock
    private TransferIntransitInventoryMapper mockTransferIntransitInventoryMapper;
    @Mock
    private InventoryLotInfoService mockInventoryLotInfoService;

    @InjectMocks
    private TransferTransitUsageDetailManager transferTransitUsageDetailManagerUnderTest;

    @Test
    public void testGetSkuSaleQtyMap1() {
        // Setup
        final Map<String, Integer> transferScenes = Map.ofEntries(Map.entry("value", 0));
        final Map<UsageCodeSkuId, BigDecimal> expectedResult = Map.ofEntries(
            Map.entry(new UsageCodeSkuId("usageCode", 0L), new BigDecimal("0.00")));

        // Run the test
        final Map<UsageCodeSkuId, BigDecimal> result = transferTransitUsageDetailManagerUnderTest
            .getSkuToTransferInQtyMap(Set.of(0L), transferScenes);
        Assert.assertTrue(true);
    }

    @Test
    public void testGetSkuSaleQtyMap2() {
        // Setup
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicInventoryLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicInventoryLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("usageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        final List<CredentialDetail> credentialDetails = List.of(credentialDetail);
        final Map<String, Integer> transferScenes = Map.ofEntries(Map.entry("value", 0));
        final Map<UsageCodeSkuId, BigDecimal> expectedResult = Map.ofEntries(
            Map.entry(new UsageCodeSkuId("usageCode", 0L), new BigDecimal("0.00")));

        // Run the test
        final Map<UsageCodeSkuId, BigDecimal> result = transferTransitUsageDetailManagerUnderTest
            .getSkuToTransferInQtyMap(credentialDetails, transferScenes);
        Assert.assertTrue(true);
        // Verify the results
    }

    @Test
    public void testGetInventoryLotInfoMap() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(new TransferIntransitInventoryCommand());
        final InventoryLotInfo lotInfo = new InventoryLotInfo();
        lotInfo.setId(0L);
        lotInfo.setSkuId(0L);
        lotInfo.setWarehouseId(0L);
        lotInfo.setLotId("lotId");
        lotInfo.setVendorId(0L);
        lotInfo.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setPurchaseNo("purchaseNo");
        lotInfo.setRegionCodePath("regionCodePath");
        lotInfo.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final Map<String, InventoryLotInfo> expectedResult = Map.ofEntries(Map.entry("value", lotInfo));

        // Configure InventoryLotInfoService.queryInventoryLotInfo(...).
        final InventoryLotInfo lotInfo1 = new InventoryLotInfo();
        lotInfo1.setId(0L);
        lotInfo1.setSkuId(0L);
        lotInfo1.setWarehouseId(0L);
        lotInfo1.setLotId("lotId");
        lotInfo1.setVendorId(0L);
        lotInfo1.setUnsalableDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setReceiveDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setManufactureDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setPurchaseNo("purchaseNo");
        lotInfo1.setRegionCodePath("regionCodePath");
        lotInfo1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        lotInfo1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        // Run the test
        final Map<String, InventoryLotInfo> result = transferTransitUsageDetailManagerUnderTest
            .getInventoryLotInfoMap(commands);

        // Verify the results
        Assert.assertTrue(true);
    }




    @Test
    public void testGetTransferInTransitInventoryList1() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(new TransferIntransitInventoryCommand());
        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setFromCargoOwnerId(0L);
        transferIntransitInventory.setFromWarehouseId(0L);
        transferIntransitInventory.setFromLogicInventoryLocationCode("fromLogicInventoryLocationCode");
        transferIntransitInventory.setToCargoOwnerId(0L);
        transferIntransitInventory.setToLogicInventoryLocationCode("toLogicInventoryLocationCode");
        transferIntransitInventory.setToWarehouseId(0L);
        transferIntransitInventory.setSkuId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("orderNo");
        transferIntransitInventory.setDeliveryMode(0);
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setPlanQty(new BigDecimal("0.00"));
        transferIntransitInventory.setUsageCode("toUsageCode");
        final List<TransferIntransitInventory> expectedResult = List.of(transferIntransitInventory);

        // Configure TransferIntransitInventoryMapper.listByOrderSourceAndNoAndSkuIds(...).
        final TransferIntransitInventory transferIntransitInventory1 = new TransferIntransitInventory();
        transferIntransitInventory1.setId(0L);
        transferIntransitInventory1.setFromCargoOwnerId(0L);
        transferIntransitInventory1.setFromWarehouseId(0L);
        transferIntransitInventory1.setFromLogicInventoryLocationCode("fromLogicInventoryLocationCode");
        transferIntransitInventory1.setToCargoOwnerId(0L);
        transferIntransitInventory1.setToLogicInventoryLocationCode("toLogicInventoryLocationCode");
        transferIntransitInventory1.setToWarehouseId(0L);
        transferIntransitInventory1.setSkuId(0L);
        transferIntransitInventory1.setOrderSource("orderSource");
        transferIntransitInventory1.setOrderNo("orderNo");
        transferIntransitInventory1.setDeliveryMode(0);
        transferIntransitInventory1.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory1.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory1.setPlanQty(new BigDecimal("0.00"));
        transferIntransitInventory1.setUsageCode("toUsageCode");
        final List<TransferIntransitInventory> inventories = List.of(transferIntransitInventory1);

        // Run the test
        final List<TransferIntransitInventory> result = transferTransitUsageDetailManagerUnderTest
            .getTransferInTransitInventoryList(commands);

        // Verify the results
        Assert.assertTrue(true);
    }

    @Test
    public void testGetTransferInTransitInventoryList1_TransferIntransitInventoryMapperReturnsNoItems() {
        // Setup
        final List<TransferIntransitInventoryCommand> commands = List.of(new TransferIntransitInventoryCommand());


        // Run the test
        final List<TransferIntransitInventory> result = transferTransitUsageDetailManagerUnderTest
            .getTransferInTransitInventoryList(commands);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

}
