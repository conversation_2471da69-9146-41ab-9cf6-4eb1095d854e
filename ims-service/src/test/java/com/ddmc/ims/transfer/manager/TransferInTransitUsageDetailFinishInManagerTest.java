package com.ddmc.ims.transfer.manager;

import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.config.param.LocalParamConfig;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialHeader;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import com.ddmc.ims.manager.inventory.TransferInTransitUsageDetailFinishInManager;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TransferInTransitUsageDetailFinishInManagerTest {

    @InjectMocks
    private TransferInTransitUsageDetailFinishInManager transferInTransitUsageDetailFinishInManagerUnderTest;


    @Mock
    protected TransferIntransitInventoryMapper transferIntransitInventoryMapper;
    @Mock
    private LocalParamConfig localParamConfig;

    @Test
    public void testHandleTransferFinishInBound() {
        // Setup
        final CredentialHeader headerAndDetail = new CredentialHeader();
        headerAndDetail.setId(0L);
        headerAndDetail.setOrderSource("orderSource");
        headerAndDetail.setOrderNo("orderNo");
        final CredentialDetail credentialDetail = new CredentialDetail();
        credentialDetail.setCredentialHeaderId(0L);
        credentialDetail.setFromLogicLocationCode("fromLogicInventoryLocationCode");
        credentialDetail.setFromCargoOwnerId(0L);
        credentialDetail.setFromWarehouseId(0L);
        credentialDetail.setToLogicLocationCode("toLogicInventoryLocationCode");
        credentialDetail.setToCargoOwnerId(0L);
        credentialDetail.setToWarehouseId(0L);
        credentialDetail.setSkuId(0L);
        credentialDetail.setLotId("lotId");
        credentialDetail.setQty(new BigDecimal("0.00"));
        credentialDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setUsageCode("toUsageCode");
        credentialDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialDetail.setOrderTag("orderTag");
        credentialDetail.setToUsageCode("toUsageCode");
        headerAndDetail.setCredentialDetailList(List.of(credentialDetail));

        final CredentialUseageDetail credentialUseageDetail = new CredentialUseageDetail();
        credentialUseageDetail.setCredentialHeaderId(0L);
        credentialUseageDetail.setFromLogicLocationCode("fromLogicInventoryLocationCode");
        credentialUseageDetail.setFromCargoOwnerId(0L);
        credentialUseageDetail.setFromWarehouseId(0L);
        credentialUseageDetail.setToLogicLocationCode("toLogicInventoryLocationCode");
        credentialUseageDetail.setToCargoOwnerId(0L);
        credentialUseageDetail.setToWarehouseId(0L);
        credentialUseageDetail.setSkuId(0L);
        credentialUseageDetail.setLotId("lotId");
        credentialUseageDetail.setQty(new BigDecimal("0.00"));
        credentialUseageDetail.setDemand(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        credentialUseageDetail.setInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setToInventoryStatus(InventoryStatusEnum.UN_KNOW);
        credentialUseageDetail.setUsageCode("toUsageCode");
        credentialUseageDetail.setOrderTag("orderTag");
        credentialUseageDetail.setToUsageCode("toUsageCode");
        final List<CredentialUseageDetail> expectedResult = List.of(credentialUseageDetail);

        // Run the test
        final List<CredentialUseageDetail> result = transferInTransitUsageDetailFinishInManagerUnderTest
            .handleTransferFinishInBound(headerAndDetail);

        // Verify the results
        Assert.assertNotNull(result);
    }
}
