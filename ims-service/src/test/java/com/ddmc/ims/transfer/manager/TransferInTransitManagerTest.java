package com.ddmc.ims.transfer.manager;

import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.manager.inventory.TransferInTransitManager;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TransferInTransitManagerTest {

    @Mock
    private TransferIntransitInventoryMapper mockTransferIntransitInventoryMapper;

    @InjectMocks
    private TransferInTransitManager transferInTransitManagerUnderTest;

    @Test
    public void testInsertOrAddPlanOutWaitAllocQty() {
        // Setup
        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setFromCargoOwnerId(0L);
        transferIntransitInventory.setFromWarehouseId(0L);
        transferIntransitInventory.setFromLogicInventoryLocationCode("fromLogicInventoryLocationCode");
        transferIntransitInventory.setToCargoOwnerId(0L);
        transferIntransitInventory.setToLogicInventoryLocationCode("toLogicInventoryLocationCode");
        transferIntransitInventory.setToWarehouseId(0L);
        transferIntransitInventory.setSkuId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("orderNo");
        transferIntransitInventory.setDeliveryMode(0);
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setPlanQty(new BigDecimal("0.00"));
        transferIntransitInventory.setIntransitQty(new BigDecimal("0.00"));
        final List<TransferIntransitInventory> needInsert = List.of(transferIntransitInventory);
        final TransferIntransitInventory transferIntransitInventory1 = new TransferIntransitInventory();
        transferIntransitInventory1.setId(0L);
        transferIntransitInventory1.setFromCargoOwnerId(0L);
        transferIntransitInventory1.setFromWarehouseId(0L);
        transferIntransitInventory1.setFromLogicInventoryLocationCode("fromLogicInventoryLocationCode");
        transferIntransitInventory1.setToCargoOwnerId(0L);
        transferIntransitInventory1.setToLogicInventoryLocationCode("toLogicInventoryLocationCode");
        transferIntransitInventory1.setToWarehouseId(0L);
        transferIntransitInventory1.setSkuId(0L);
        transferIntransitInventory1.setOrderSource("orderSource");
        transferIntransitInventory1.setOrderNo("orderNo");
        transferIntransitInventory1.setDeliveryMode(0);
        transferIntransitInventory1.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory1.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory1.setPlanQty(new BigDecimal("0.00"));
        transferIntransitInventory1.setIntransitQty(new BigDecimal("0.00"));
        final List<TransferIntransitInventory> needUpdate = Collections.singletonList(transferIntransitInventory1);


        // Run the test
        transferInTransitManagerUnderTest.insertOrAddPlanOutWaitAllocQty(needInsert, needUpdate);
        Assert.assertTrue(true);
        // Verify the results
    }

    @Test
    public void testUpdateInTransitQty() {
        // Setup
        final TransferIntransitInventory transferIntransitInventory = new TransferIntransitInventory();
        transferIntransitInventory.setId(0L);
        transferIntransitInventory.setFromCargoOwnerId(0L);
        transferIntransitInventory.setFromWarehouseId(0L);
        transferIntransitInventory.setFromLogicInventoryLocationCode("fromLogicInventoryLocationCode");
        transferIntransitInventory.setToCargoOwnerId(0L);
        transferIntransitInventory.setToLogicInventoryLocationCode("toLogicInventoryLocationCode");
        transferIntransitInventory.setToWarehouseId(0L);
        transferIntransitInventory.setSkuId(0L);
        transferIntransitInventory.setOrderSource("orderSource");
        transferIntransitInventory.setOrderNo("orderNo");
        transferIntransitInventory.setDeliveryMode(0);
        transferIntransitInventory.setExpectOutTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setExpectInTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        transferIntransitInventory.setPlanQty(new BigDecimal("0.00"));
        transferIntransitInventory.setIntransitQty(new BigDecimal("0.00"));
        final List<TransferIntransitInventory> needUpdate = Collections.singletonList(transferIntransitInventory);

        // Run the test
        transferInTransitManagerUnderTest.updateInTransitQty(needUpdate,null);
        Assert.assertTrue(true);
        // Verify the results
    }


}
