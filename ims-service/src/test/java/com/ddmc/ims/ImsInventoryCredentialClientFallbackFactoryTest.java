package com.ddmc.ims;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class ImsInventoryCredentialClientFallbackFactoryTest {

    private ImsInventoryCredentialClientFallbackFactory imsInventoryCredentialClientFallbackFactoryUnderTest;

    @Before
    public void setUp() throws Exception {
        imsInventoryCredentialClientFallbackFactoryUnderTest = new ImsInventoryCredentialClientFallbackFactory();
    }

    @Test
    public void testCreate() {
        // Setup
        // Run the test
        final InventoryCredentialClient result = imsInventoryCredentialClientFallbackFactoryUnderTest
            .create(new Exception("message"));
        Assert.assertNotNull(result);
        // Verify the results
    }
}
