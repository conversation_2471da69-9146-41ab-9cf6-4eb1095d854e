package com.ddmc.ims.Integration.purchase;

import static com.ddmc.ims.Integration.BaseDataManager.FROM_LOCATION;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.controller.api.InboundInventoryCredentialController;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.Assert;
import org.junit.Test;

/**
 * 采购预约在途取消登记
 */
public class PurchaseBookedClearTest extends SpringBootTestBase {

    @Resource
    private BaseDataManager baseDataManager;

    @Resource
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;

    @Resource
    private InboundInventoryCredentialController inboundInventoryCredentialController;

    @Resource
    private InventoryCredentialController inventoryCredentialController;


    public Map<String, PurchaseIntransitInventory> getPurchaseInTransitInventory(Long skuId, String orderNo) {
        LambdaQueryWrapper<PurchaseIntransitInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseIntransitInventory::getOrderNo, orderNo)
            .eq(PurchaseIntransitInventory::getSkuId, skuId)
            .in(PurchaseIntransitInventory::getUsageCode, List.of("EC", "TB"));
        List<PurchaseIntransitInventory> inventories = purchaseIntransitInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(PurchaseIntransitInventory::getUsageCode, Function.identity()));
    }

    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.申请入库EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     *
     * 2.同采购单清理入库量
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              0
     * TB            5            5               0
     */
    @Test
    public void testClear1() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        doPublish(orderNo, Collections.singletonList(102243L));

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.CLEAR_BOOKED_PLAN_IN);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(1), "EC");
        request2.setOperateDetails(Collections.singletonList(ec));

        inboundInventoryCredentialController.tryCleanPlanIn(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getBookedIntransitQty());


    }


    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty  sku_id
     *
     * 1.申请入库102243:EC10个，TB5个;991383 EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty  sku_id
     * EC            10           10              10                    102243
     * TB            5            5               5                     102243
     * EC            10           10              10                    991383
     * TB            5            5               5                     991383
     *
     * 2.102243清理
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty  sku_id
     * EC            10           10              0                     102243
     * TB            5            5               0                     102243
     * EC            10           10              10                    991383
     * TB            5            5               5                     991383
     */
    @Test
    public void testClear2() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        doPublish(orderNo, Arrays.asList(102243L,991383L));

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.CLEAR_BOOKED_PLAN_IN);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(1), "EC");
        request2.setOperateDetails(Collections.singletonList(ec));
        inboundInventoryCredentialController.tryCleanPlanIn(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit2 = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit2.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit2.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit2.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit2 = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit2.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit2.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit2.getBookedIntransitQty());

        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory = getPurchaseInTransitInventory(991383L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getBookedIntransitQty());

    }



    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty  sku_id
     *
     * 1.申请入库102243:EC10个，TB5个;991383 EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty  sku_id
     * EC            10           10              10                    102243
     * TB            5            5               5                     102243
     * EC            10           10              10                    991383
     * TB            5            5               5                     991383
     *
     * 2.102243,991383清理
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty  sku_id
     * EC            10           10              0                     102243
     * TB            5            5               0                     102243
     * EC            10           10              0                     991383
     * TB            5            5               0                     991383
     */
    @Test
    public void testClear3() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        doPublish(orderNo, Arrays.asList(102243L,991383L));

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.CLEAR_BOOKED_PLAN_IN);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(1), "EC");
        CredentialDetailRequest ec2 = getCredentialDetailRequest(991383L, new BigDecimal(1), "EC");
        request2.setOperateDetails(Arrays.asList(ec,ec2));
        inboundInventoryCredentialController.tryCleanPlanIn(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit2 = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit2.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit2.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit2.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit2 = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit2.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit2.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit2.getBookedIntransitQty());

        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory = getPurchaseInTransitInventory(991383L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getBookedIntransitQty());

    }




    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty  sku_id
     *
     *
     * 1.102243,991383清理
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty  sku_id
     */
    @Test
    public void testClear4() {
        String orderNo = "TESTPO" + System.currentTimeMillis();

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.CLEAR_BOOKED_PLAN_IN);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(1), "EC");
        CredentialDetailRequest ec2 = getCredentialDetailRequest(991383L, new BigDecimal(1), "EC");
        request2.setOperateDetails(Arrays.asList(ec,ec2));
        inboundInventoryCredentialController.tryCleanPlanIn(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit2 = purchaseInTransitInventory2.get("EC");
        Assert.assertNull(ecInTransit2);
        PurchaseIntransitInventory tbInTransit2 = purchaseInTransitInventory2.get("TB");
        Assert.assertNull(tbInTransit2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory = getPurchaseInTransitInventory(991383L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory.get("EC");
        Assert.assertNull(ecInTransit);
        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory.get("TB");
        Assert.assertNull(tbInTransit);
    }

    private void doPublish(String orderNo, List<Long> skuIds) {
        String idempotentId = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId,
            orderNo, skuIds, new BigDecimal(10), new BigDecimal(5));
        inboundInventoryCredentialController.tryPublishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);
        skuIds.forEach(skuId -> {
            Map<String, PurchaseIntransitInventory> purchaseInTransitInventory = getPurchaseInTransitInventory(skuId,
                orderNo);
            PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory.get("EC");
            Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
            Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
            Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getBookedIntransitQty());

            PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory.get("TB");
            Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
            Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
            Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getBookedIntransitQty());
        });
    }


    private InboundCredentialRequest getDefaultInboundCredentialRequest(String idempotentId, String orderNo,
        List<Long> skuIds,
        BigDecimal qty, BigDecimal tbQty) {
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo,
            OrderOperateTypeEnum.APPLY_IN_OF_STOCK);

        List<CredentialDetailRequest> details = skuIds.stream().map(skuId -> Arrays
            .asList(getCredentialDetailRequest(skuId, qty, "EC"), getCredentialDetailRequest(skuId, tbQty, "TB")))
            .flatMap(Collection::stream).collect(Collectors.toList());
        request.setOperateDetails(details);

        return request;
    }

    private CredentialDetailRequest getCredentialDetailRequest(Long skuId, BigDecimal qty, String usageCode) {
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        c1.setFromLocation(FROM_LOCATION);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(BaseDataManager.TO_LOCATION);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(skuId);
        c1.setLotId("2207220009057043879");
        c1.setQty(qty);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        c1.setUsages(Arrays.asList(usageCode.split(",")));
        c1.setToUsages(Arrays.asList(usageCode.split(",")));
        c1.setTodaySale(false);
        return c1;
    }

    private InboundCredentialRequest getInboundCredentialRequest(String idempotentId, String orderNo,
        OrderOperateTypeEnum operateTypeEnum) {
        InboundCredentialRequest request = new InboundCredentialRequest();
        request.setIdempotentId(idempotentId);
        request.setOrderSource("PURCHASE");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.PURCHASE.getCode());
        request.setOrderOperateType(operateTypeEnum.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("PURCHASE");
        request.setExeOrderNo(System.currentTimeMillis() + "");
        request.setSeqNo(System.currentTimeMillis() + "");
        request.setWarehouseId(17L);
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        request.setExpectArriveTime(ThreadLocalDateUtils.getPlusStartOfDay(new Date(), 0));
        return request;
    }





}
