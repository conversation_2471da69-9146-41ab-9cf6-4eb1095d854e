package com.ddmc.ims.Integration.badrtv;

import static com.ddmc.ims.Integration.BaseDataManager.FROM_LOCATION;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.controller.api.OutboundInventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryAllocDetailMapper;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventoryAllocDetail;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class BadProductRtvCleanTest extends SpringBootTestBase {

    @Resource
    private OutboundInventoryCredentialController outboundInventoryCredentialController;

    @Resource
    private WarehouseSkuInventoryAllocDetailMapper warehouseSkuInventoryAllocDetailMapper;

    @Resource
    private InventoryCredentialController inventoryCredentialController;


    String orderNo = "RT24050800053";

    String exeOrderNo = "DO240510O0068808";

    @Before
    public void delete() {
        LambdaQueryWrapper<WarehouseSkuInventoryAllocDetail> delete = new LambdaQueryWrapper<>();
        delete.in(WarehouseSkuInventoryAllocDetail::getOrderNo, Collections.singletonList(orderNo));
        warehouseSkuInventoryAllocDetailMapper.delete(delete);

    }


    /**
     * 1.占用100
     * 2.取消，占用信息删除
     */
    @Test
    public void clean() {
        OutboundCredentialRequest request = BadProductRtvPublishTest.getPublishBadRtv(orderNo, exeOrderNo, new BigDecimal(100L));
        outboundInventoryCredentialController.tryPublishOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(request.getIdempotentId());
        inventoryCredentialController.confirm(confirmRequest);

        List<WarehouseSkuInventoryAllocDetail> warehouseSkuInventoryAllocDetails = warehouseSkuInventoryAllocDetailMapper
            .selectByOrderNoAndOrderSourceAndSkuIdIn(orderNo, "PMS", Collections.singletonList(17L));
        Assert.assertEquals(1, warehouseSkuInventoryAllocDetails.size());
        WarehouseSkuInventoryAllocDetail detail = warehouseSkuInventoryAllocDetails.get(0);
        Assert.assertEquals(new BigDecimal("100.000"), detail.getAllocQty());

        OutboundCredentialRequest request2 = getCleanBadRtv(orderNo, orderNo);
        outboundInventoryCredentialController.tryCancelOutbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(request2.getIdempotentId());
        inventoryCredentialController.confirm(confirmRequest2);

        List<WarehouseSkuInventoryAllocDetail> details = warehouseSkuInventoryAllocDetailMapper
            .selectByOrderNoAndOrderSourceAndSkuIdIn(orderNo, "PMS", Collections.singletonList(17L));
        Assert.assertEquals(0, details.size());
    }


    /**
     * 1.占用100
     * 2.取消，占用信息删除，但是没有sku信息
     */
    @Test
    public void clean2() {
        OutboundCredentialRequest request = BadProductRtvPublishTest.getPublishBadRtv(orderNo, exeOrderNo, new BigDecimal(100L));
        outboundInventoryCredentialController.tryPublishOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(request.getIdempotentId());
        inventoryCredentialController.confirm(confirmRequest);

        List<WarehouseSkuInventoryAllocDetail> warehouseSkuInventoryAllocDetails = warehouseSkuInventoryAllocDetailMapper
            .selectByOrderNoAndOrderSourceAndSkuIdIn(orderNo, "PMS", Collections.singletonList(17L));
        Assert.assertEquals(1, warehouseSkuInventoryAllocDetails.size());
        WarehouseSkuInventoryAllocDetail detail = warehouseSkuInventoryAllocDetails.get(0);
        Assert.assertEquals(new BigDecimal("100.000"), detail.getAllocQty());

        OutboundCredentialRequest request2 = getCleanBadRtv(orderNo, orderNo);
        request2.setOperateDetails(Collections.emptyList());
        outboundInventoryCredentialController.tryCancelOutbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(request2.getIdempotentId());
        inventoryCredentialController.confirm(confirmRequest2);

        List<WarehouseSkuInventoryAllocDetail> details = warehouseSkuInventoryAllocDetailMapper
            .selectByOrderNoAndOrderSourceAndSkuIdIn(orderNo, "PMS", Collections.singletonList(17L));
        Assert.assertEquals(0, details.size());
    }




    private OutboundCredentialRequest getCleanBadRtv(String orderNo, String exeOrderNo) {
        OutboundCredentialRequest request = getCleanBadRtvHeader(orderNo, exeOrderNo);

        List<CredentialDetailRequest> details = getCleanBadRtvDetail();
        request.setOperateDetails(details);

        return request;
    }

    private OutboundCredentialRequest getCleanBadRtvHeader(String orderNo, String exeOrderNo) {
        OutboundCredentialRequest request = new OutboundCredentialRequest();
        request.setDeliveryMode(0);
        String idempotentId = "BAD_RTV" + System.currentTimeMillis();
        request.setIdempotentId(idempotentId);
        request.setOrderSource("PMS");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.PURCHASE_REFUND.getCode());
        request.setOrderOperateType(OrderOperateTypeEnum.CANCEL_OUT_APPLY.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("PMS");
        request.setExeOrderNo(exeOrderNo);
        request.setSeqNo(System.currentTimeMillis() + "");
        request.setWarehouseId(17L);
        request.setExpectOutTime(new Date());
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        return request;
    }

    private List<CredentialDetailRequest> getCleanBadRtvDetail() {
        List<CredentialDetailRequest> details = new ArrayList<>();
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        c1.setFromLocation(BaseDataManager.FROM_BAD_PRODUCT_RTV_LOCATION_2);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(BaseDataManager.EMPTY_LOCATION);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(17L);
        c1.setLotId("");
        c1.setQty(BigDecimal.ZERO);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        List<String> usageCode = Collections.singletonList("DR");
        c1.setUsages(usageCode);
        c1.setToUsages(usageCode);
        c1.setTodaySale(false);
        details.add(c1);

        return details;
    }

}
