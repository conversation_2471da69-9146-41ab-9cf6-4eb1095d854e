package com.ddmc.ims.Integration.purchase;

import static com.ddmc.ims.Integration.BaseDataManager.FROM_LOCATION;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.constant.CommonConstants;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.controller.api.InboundInventoryCredentialController;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuLotInventoryMapper;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuLotInventory;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class PurchaseInStockTest extends SpringBootTestBase  {


    @Resource
    private BaseDataManager baseDataManager;

    @Resource
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;

    @Resource
    private InboundInventoryCredentialController inboundInventoryCredentialController;

    @Resource
    private InventoryCredentialController inventoryCredentialController;

    @Resource
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;

    @Resource
    private WarehouseSkuLotInventoryMapper warehouseSkuLotInventoryMapper;

    public Map<String, PurchaseIntransitInventory> getPurchaseInTransitInventory(Long skuId, String orderNo) {
        LambdaQueryWrapper<PurchaseIntransitInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseIntransitInventory::getOrderNo, orderNo)
            .eq(PurchaseIntransitInventory::getSkuId, skuId)
            .in(PurchaseIntransitInventory::getUsageCode, List.of("EC", "TB"));
        List<PurchaseIntransitInventory> inventories = purchaseIntransitInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(PurchaseIntransitInventory::getUsageCode, Function.identity()));
    }


    public Map<String, WarehouseSkuInventory> getWarehouseSkuInventory(Long skuId) {
        LambdaQueryWrapper<WarehouseSkuInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseSkuInventory::getWarehouseId, 17L)
            .eq(WarehouseSkuInventory::getSkuId, skuId)
            .in(WarehouseSkuInventory::getUsageCode, List.of("EC", "TB"));
        List<WarehouseSkuInventory> inventories = warehouseSkuInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(WarehouseSkuInventory::getUsageCode, Function.identity()));
    }


    public WarehouseSkuLotInventory getWarehouseSkuLotInventory(Long skuId) {
        LambdaQueryWrapper<WarehouseSkuLotInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseSkuLotInventory::getWarehouseId, 17L)
            .eq(WarehouseSkuLotInventory::getLotId,"2207220009057043879")
            .eq(WarehouseSkuLotInventory::getLogicInventoryLocationCode, CommonConstants.GOOD_PRODUCT)
            .eq(WarehouseSkuLotInventory::getSkuId, skuId);
        return warehouseSkuLotInventoryMapper.selectOne(queryWrapper);
    }



    @Before
    public void refreshWarehouseSkuInventory() {
        LambdaUpdateWrapper<WarehouseSkuInventory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(WarehouseSkuInventory::getFreeQty, BigDecimal.ZERO)
            .in(WarehouseSkuInventory::getSkuId, 102243L,991383L)
            .eq(WarehouseSkuInventory::getWarehouseId, 17L);
        warehouseSkuInventoryMapper.update(null, updateWrapper);

        LambdaUpdateWrapper<WarehouseSkuLotInventory> updateLotWrapper = new LambdaUpdateWrapper<>();
        updateLotWrapper.set(WarehouseSkuLotInventory::getFreeQty, BigDecimal.ZERO)
            .in(WarehouseSkuLotInventory::getSkuId, 102243L,991383L)
            .eq(WarehouseSkuLotInventory::getLotId, "2207220009057043879")
            .eq(WarehouseSkuLotInventory::getLogicInventoryLocationCode, CommonConstants.GOOD_PRODUCT)
            .eq(WarehouseSkuLotInventory::getWarehouseId, 17L);
        warehouseSkuLotInventoryMapper.update(null, updateLotWrapper);
    }





    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.申请入库EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     *
     * 2.入库EC5个
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           5               5
     * TB            5            5               5
     *
     * warehouse_sku_inventory
     *
     * usageCode      free_qty
     *
     * EC             5
     *
     * TB             0
     *
     * warehouse_sku_inventory_lot
     *
     * free_qty
     * 5
     */
    @Test
    public void testInStock1() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        doPublish(orderNo, Collections.singletonList(102243L));

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.IN_STOCK);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(5), "EC");
        request2.setOperateDetails(Collections.singletonList(ec));

        inboundInventoryCredentialController.tryInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getBookedIntransitQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventory = getWarehouseSkuInventory(102243L);
        WarehouseSkuInventory ecWarehouseSkuInventory = warehouseSkuInventory.get("EC");
        Assert.assertEquals(new BigDecimal("5.000"), ecWarehouseSkuInventory.getFreeQty());
        WarehouseSkuInventory tbWarehouseSkuInventory = warehouseSkuInventory.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbWarehouseSkuInventory.getFreeQty());

        WarehouseSkuLotInventory warehouseSkuLotInventory = getWarehouseSkuLotInventory(102243L);
        Assert.assertEquals(new BigDecimal("5.000"), warehouseSkuLotInventory.getFreeQty());

    }


    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.申请入库EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     *
     * 2.入库TB,EC5个
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            0               0
     *
     * warehouse_sku_inventory
     *
     * usageCode      free_qty
     *
     * EC             0
     *
     * TB             5
     *
     * warehouse_sku_inventory_lot
     *
     * free_qty
     * 5
     *
     */
    @Test
    public void testInStock2() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        doPublish(orderNo, Collections.singletonList(102243L));

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.IN_STOCK);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(5), "TB,EC");
        request2.setOperateDetails(Collections.singletonList(ec));

        inboundInventoryCredentialController.tryInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getBookedIntransitQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventory = getWarehouseSkuInventory(102243L);
        WarehouseSkuInventory ecWarehouseSkuInventory = warehouseSkuInventory.get("EC");
        Assert.assertEquals(new BigDecimal("0.000"), ecWarehouseSkuInventory.getFreeQty());
        WarehouseSkuInventory tbWarehouseSkuInventory = warehouseSkuInventory.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbWarehouseSkuInventory.getFreeQty());

        WarehouseSkuLotInventory warehouseSkuLotInventory = getWarehouseSkuLotInventory(102243L);
        Assert.assertEquals(new BigDecimal("5.000"), warehouseSkuLotInventory.getFreeQty());

    }


    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.申请入库EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     *
     * 2.入库TB,EC12个
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           3               3
     * TB            5            0               0
     *
     * warehouse_sku_inventory
     *
     * usageCode      free_qty
     *
     * EC             7
     *
     * TB             5
     *
     * warehouse_sku_inventory_lot
     *
     * free_qty
     * 12
     *
     */
    @Test
    public void testInStock3() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        doPublish(orderNo, Collections.singletonList(102243L));

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.IN_STOCK);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(12), "TB,EC");
        request2.setOperateDetails(Collections.singletonList(ec));

        inboundInventoryCredentialController.tryInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("3.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("3.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getBookedIntransitQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventory = getWarehouseSkuInventory(102243L);
        WarehouseSkuInventory ecWarehouseSkuInventory = warehouseSkuInventory.get("EC");
        Assert.assertEquals(new BigDecimal("7.000"), ecWarehouseSkuInventory.getFreeQty());
        WarehouseSkuInventory tbWarehouseSkuInventory = warehouseSkuInventory.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbWarehouseSkuInventory.getFreeQty());

        WarehouseSkuLotInventory warehouseSkuLotInventory = getWarehouseSkuLotInventory(102243L);
        Assert.assertEquals(new BigDecimal("12.000"), warehouseSkuLotInventory.getFreeQty());

    }



    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.申请入库EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     *
     * 2.入库TB,EC30个
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           0               0
     * TB            5            0               0
     *
     * warehouse_sku_inventory
     *
     * usageCode      free_qty
     *
     * EC             25
     *
     * TB             5
     *
     * warehouse_sku_inventory_lot
     *
     * free_qty
     * 30
     *
     */
    @Test
    public void testInStock4() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        doPublish(orderNo, Collections.singletonList(102243L));

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.IN_STOCK);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(30), "TB,EC");
        request2.setOperateDetails(Collections.singletonList(ec));

        inboundInventoryCredentialController.tryInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getBookedIntransitQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventory = getWarehouseSkuInventory(102243L);
        WarehouseSkuInventory ecWarehouseSkuInventory = warehouseSkuInventory.get("EC");
        Assert.assertEquals(new BigDecimal("25.000"), ecWarehouseSkuInventory.getFreeQty());
        WarehouseSkuInventory tbWarehouseSkuInventory = warehouseSkuInventory.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbWarehouseSkuInventory.getFreeQty());

        WarehouseSkuLotInventory warehouseSkuLotInventory = getWarehouseSkuLotInventory(102243L);
        Assert.assertEquals(new BigDecimal("30.000"), warehouseSkuLotInventory.getFreeQty());

    }



    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     *
     * 1.入库TB,EC30个
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * warehouse_sku_inventory
     * usageCode      free_qty
     *
     * EC             30
     *
     * TB             0
     *
     * warehouse_sku_inventory_lot
     *
     * free_qty
     * 30
     *
     */
    @Test
    public void testInStock5() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.IN_STOCK);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(30), "TB,EC");
        request2.setOperateDetails(Collections.singletonList(ec));

        inboundInventoryCredentialController.tryInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory2.get("EC");
        Assert.assertNull(ecInTransit);

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory2.get("TB");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventory = getWarehouseSkuInventory(102243L);
        WarehouseSkuInventory ecWarehouseSkuInventory = warehouseSkuInventory.get("EC");
        Assert.assertEquals(new BigDecimal("30.000"), ecWarehouseSkuInventory.getFreeQty());
        WarehouseSkuInventory tbWarehouseSkuInventory = warehouseSkuInventory.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbWarehouseSkuInventory.getFreeQty());

        WarehouseSkuLotInventory warehouseSkuLotInventory = getWarehouseSkuLotInventory(102243L);
        Assert.assertEquals(new BigDecimal("30.000"), warehouseSkuLotInventory.getFreeQty());

    }



    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.申请入库EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     *
     * 2.同采购单清理入库量
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              0
     * TB            5            5               0
     *
     * 3.入库TB,EC30个
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           0               0
     * TB            5            0               0
     *
     *
     * warehouse_sku_inventory
     * usageCode      free_qty
     *
     * EC             25
     *
     * TB             5
     *
     * warehouse_sku_inventory_lot
     *
     * free_qty
     * 30
     *
     */
    @Test
    public void testInStock6() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        doPublishClear(orderNo);
        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.IN_STOCK);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(30), "TB,EC");
        request2.setOperateDetails(Collections.singletonList(ec));

        inboundInventoryCredentialController.tryInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getBookedIntransitQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventory = getWarehouseSkuInventory(102243L);
        WarehouseSkuInventory ecWarehouseSkuInventory = warehouseSkuInventory.get("EC");
        Assert.assertEquals(new BigDecimal("25.000"), ecWarehouseSkuInventory.getFreeQty());
        WarehouseSkuInventory tbWarehouseSkuInventory = warehouseSkuInventory.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbWarehouseSkuInventory.getFreeQty());

        WarehouseSkuLotInventory warehouseSkuLotInventory = getWarehouseSkuLotInventory(102243L);
        Assert.assertEquals(new BigDecimal("30.000"), warehouseSkuLotInventory.getFreeQty());

    }

    private void doPublishClear(String orderNo) {
        doPublish(orderNo, Collections.singletonList(102243L));

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo,
            OrderOperateTypeEnum.CLEAR_BOOKED_PLAN_IN);
        CredentialDetailRequest ec = getCredentialDetailRequest(102243L, new BigDecimal(1), "EC");
        request2.setOperateDetails(Collections.singletonList(ec));

        inboundInventoryCredentialController.tryCleanPlanIn(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(102243L,
            orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getBookedIntransitQty());
    }


    private void doPublish(String orderNo, List<Long> skuIds) {
        String idempotentId = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId,
            orderNo, skuIds, new BigDecimal(10), new BigDecimal(5));
        inboundInventoryCredentialController.tryPublishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);
        skuIds.forEach(skuId -> {
            Map<String, PurchaseIntransitInventory> purchaseInTransitInventory = getPurchaseInTransitInventory(skuId,
                orderNo);
            PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory.get("EC");
            Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
            Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
            Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getBookedIntransitQty());

            PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory.get("TB");
            Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
            Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
            Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getBookedIntransitQty());
        });
    }


    private InboundCredentialRequest getDefaultInboundCredentialRequest(String idempotentId, String orderNo,
        List<Long> skuIds,
        BigDecimal qty, BigDecimal tbQty) {
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo,
            OrderOperateTypeEnum.APPLY_IN_OF_STOCK);

        List<CredentialDetailRequest> details = skuIds.stream().map(skuId -> Arrays
            .asList(getCredentialDetailRequest(skuId, qty, "EC"), getCredentialDetailRequest(skuId, tbQty, "TB")))
            .flatMap(Collection::stream).collect(Collectors.toList());
        request.setOperateDetails(details);

        return request;
    }

    private CredentialDetailRequest getCredentialDetailRequest(Long skuId, BigDecimal qty, String usageCode) {
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        c1.setFromLocation(FROM_LOCATION);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(BaseDataManager.TO_LOCATION);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(skuId);
        c1.setLotId("2207220009057043879");
        c1.setQty(qty);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        c1.setUsages(Arrays.asList(usageCode.split(",")));
        c1.setToUsages(Arrays.asList(usageCode.split(",")));
        c1.setTodaySale(false);
        return c1;
    }

    private InboundCredentialRequest getInboundCredentialRequest(String idempotentId, String orderNo,
        OrderOperateTypeEnum operateTypeEnum) {
        InboundCredentialRequest request = new InboundCredentialRequest();
        request.setIdempotentId(idempotentId);
        request.setOrderSource("PURCHASE");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.PURCHASE.getCode());
        request.setOrderOperateType(operateTypeEnum.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("PURCHASE");
        request.setExeOrderNo(System.currentTimeMillis() + "");
        request.setSeqNo(System.currentTimeMillis() + "");
        request.setWarehouseId(17L);
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        request.setExpectArriveTime(ThreadLocalDateUtils.getPlusStartOfDay(new Date(), 0));
        return request;
    }











}
