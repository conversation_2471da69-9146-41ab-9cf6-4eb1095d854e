package com.ddmc.ims.Integration.transfer;

import static com.ddmc.ims.Integration.BaseDataManager.FROM_LOCATION;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.controller.api.OutboundInventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * 调拨在途登记
 */
public class TransferPublishTest extends SpringBootTestBase {


    @Resource
    private BaseDataManager baseDataManager;

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private OutboundInventoryCredentialController outboundInventoryCredentialController;


    @Resource
    private InventoryCredentialController inventoryCredentialController;
    public void refreshTransferInTransitInventory(String orderNo, BigDecimal planQty,BigDecimal inTransitQty,BigDecimal allocQty, BigDecimal waitAllocQty,
        String usageCode, String toUsageCode) {
        baseDataManager
            .insertBaseTransferInTransitData(orderNo, 102243L, planQty,inTransitQty,allocQty, waitAllocQty , usageCode, toUsageCode);
    }



    public Map<String, TransferIntransitInventory> getTransferInTransitInventory(String orderNo) {
        LambdaQueryWrapper<TransferIntransitInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransferIntransitInventory::getOrderNo, orderNo)
            .eq(TransferIntransitInventory::getSkuId, 102243L)
            .in(TransferIntransitInventory::getUsageCode, List.of("EC", "TB"));
        List<TransferIntransitInventory> inventories = transferIntransitInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(
                t -> t.getUsageCode() + "," + t.getFromLogicInventoryLocationCode() + "," + t.getToUsageCode() + "," + t
                    .getToLogicInventoryLocationCode(), Function.identity()));
    }

    @Before
    public void deleteTransferInTransitInventory() {
        LambdaQueryWrapper<TransferIntransitInventory> deleteTransferInTransitInventory = new LambdaQueryWrapper<>();
        deleteTransferInTransitInventory
            .in(TransferIntransitInventory::getOrderNo, Arrays.asList("17-1-20230830-0", "17-1-20230830-1"));
        transferIntransitInventoryMapper.delete(deleteTransferInTransitInventory);

    }




    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * TB	        TB          	20	        0	            0	        20
     *
     * 凭证EC->EC登记10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC		    EC              10	        10	            0	        10
     * TB		    TB              20	        0	            0	        20
     */
    @Test
    public void testPublish1() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC->EC");
        outboundInventoryCredentialController.tryPublishOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());
    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC          	10	        0	            0	        10
     * TB	        TB          	20	        0	            0	        20
     *
     * 凭证EC->EC登记10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC		    EC              20	        0	            0	        20
     * TB		    TB              20	        0	            0	        20
     */
    @Test
    public void testPublish2() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC->EC");
        outboundInventoryCredentialController.tryPublishOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());
    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC          	10	        0	            0	        10
     * TB	        TB          	20	        0	            0	        20
     *
     * 凭证EC->TB登记10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC		    EC              10	        0	            0	        10
     * TB		    TB              20	        0	            0	        20
     * EC		    TB              10	        0	            0	        10
     */
    @Test
    public void testPublish3() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC->TB");
        outboundInventoryCredentialController.tryPublishOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbTbInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbTbInTransit.getWaitAllocQty());
    }



    private OutboundCredentialRequest getDefaultOutboundCredentialRequest(String idempotentId, String orderNo,
        BigDecimal qty,String outUsageCode) {
        OutboundCredentialRequest request = getOutboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = getCredentialDetailRequests(qty,outUsageCode);
        request.setOperateDetails(details);

        return request;
    }

    private List<CredentialDetailRequest> getCredentialDetailRequests(BigDecimal qty, String outUsageCode) {
        List<CredentialDetailRequest> details = new ArrayList<>();
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        c1.setFromLocation(FROM_LOCATION);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(BaseDataManager.TO_LOCATION);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(102243L);
        c1.setLotId("2207220009057043879");
        c1.setQty(qty);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        String[] outSplit = outUsageCode.split("->");
        c1.setUsages(List.of(outSplit[0].split(",")));
        c1.setToUsages(List.of(outSplit[1].split(",")));
        c1.setTodaySale(false);
        details.add(c1);

        return details;
    }

    private OutboundCredentialRequest getOutboundCredentialRequest(String idempotentId, String orderNo) {
        OutboundCredentialRequest request = new OutboundCredentialRequest();
        request.setDeliveryMode(0);
        request.setIdempotentId(idempotentId);
        request.setOrderSource("TRANSFER");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.TRANSFER_DO_OUTBOUND.getCode());
        request.setOrderOperateType(OrderOperateTypeEnum.APPLY_OUT_OF_STOCK.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("TRANSFER");
        request.setExeOrderNo(System.currentTimeMillis()+"");
        request.setSeqNo(System.currentTimeMillis()+"");
        request.setWarehouseId(17L);
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        return request;
    }


}
