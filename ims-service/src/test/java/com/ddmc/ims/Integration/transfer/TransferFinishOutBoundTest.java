package com.ddmc.ims.Integration.transfer;

import static com.ddmc.ims.Integration.BaseDataManager.FROM_LOCATION;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.controller.api.OutboundInventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class TransferFinishOutBoundTest extends SpringBootTestBase {

    @Resource
    private BaseDataManager baseDataManager;

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private OutboundInventoryCredentialController outboundInventoryCredentialController;

    @Resource
    private InventoryCredentialController inventoryCredentialController;

    public void refreshTransferInTransitInventory(String orderNo,Long skuId, BigDecimal planQty,BigDecimal inTransitQty,BigDecimal allocQty, BigDecimal waitAllocQty,
        String usageCode, String toUsageCode) {
        baseDataManager
            .insertBaseTransferInTransitData(orderNo, skuId, planQty,inTransitQty,allocQty, waitAllocQty , usageCode, toUsageCode);
    }



    public Map<String, TransferIntransitInventory> getTransferInTransitInventory(String orderNo) {
        LambdaQueryWrapper<TransferIntransitInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransferIntransitInventory::getOrderNo, orderNo)
            .in(TransferIntransitInventory::getSkuId, List.of(102243L,9958L))
            .in(TransferIntransitInventory::getUsageCode, List.of("EC", "TB"));
        List<TransferIntransitInventory> inventories = transferIntransitInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(
                t -> t.getSkuId() +"," + t.getUsageCode() + "," + t.getFromLogicInventoryLocationCode() + "," + t.getToUsageCode() + "," + t
                    .getToLogicInventoryLocationCode(), Function.identity()));
    }
    @Before
    public void deleteTransferInTransitInventory() {
        LambdaQueryWrapper<TransferIntransitInventory> deleteTransferInTransitInventory = new LambdaQueryWrapper<>();
        deleteTransferInTransitInventory
            .in(TransferIntransitInventory::getOrderNo, Arrays.asList("17-1-20230830-0", "17-1-20230830-1"));
        transferIntransitInventoryMapper.delete(deleteTransferInTransitInventory);

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量             商品Id
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty    skuId
     * EC	        EC	            10	        0	            0	        10                102243
     * EC	        EC          	20	        0	            0	        20                9958
     *
     * 收工,102243,9958
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty    skuId
     * EC		    EC              10	        0	            0	        0                 102243
     * TB		    TB              20	        0	            0	        0                 9958
     */
    @Test
    public void testFinishOutBound1() {
        deleteTransferInTransitInventory();
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo,102243L, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo,9958L, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "EC","EC");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo);
        CredentialDetailRequest c1 =getCredentialDetailRequests(102243L);
        CredentialDetailRequest c2 = getCredentialDetailRequests(9958L);
        request.setOperateDetails(List.of(c1,c2));

        outboundInventoryCredentialController.tryFinishOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("102243,EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("9958,EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());
    }




    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量             商品Id
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty    skuId
     * EC	        EC	            10	        0	            0	        10                102243
     * EC	        EC          	20	        0	            0	        20                9958
     *
     * 收工,102243
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty    skuId
     * EC		    EC              10	        0	            0	        10                 102243
     * TB		    TB              20	        0	            0	        0                 9958
     */
    @Test
    public void testFinishOutBound2() {
        deleteTransferInTransitInventory();
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo,102243L, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo,9958L, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "EC","EC");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo);
        CredentialDetailRequest c1 =getCredentialDetailRequests(102243L);
        request.setOperateDetails(List.of(c1));

        outboundInventoryCredentialController.tryFinishOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("102243,EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("9958,EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());
    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量             商品Id
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty    skuId
     * EC	        EC	            10	        0	            0	        10                102243
     * EC	        EC          	20	        0	            0	        20                9958
     *
     * 收工
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty    skuId
     * EC		    EC              10	        0	            0	        0                 102243
     * TB		    TB              20	        0	            0	        20                 9958
     */
    @Test
    public void testFinishOutBound3() {
        deleteTransferInTransitInventory();
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo,102243L, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo,9958L, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "EC","EC");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo);

        outboundInventoryCredentialController.tryFinishOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("102243,EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("9958,EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());
    }



    private OutboundCredentialRequest getDefaultOutboundCredentialRequest(String idempotentId, String orderNo) {
        return  getOutboundCredentialRequest(idempotentId, orderNo);
    }

    private CredentialDetailRequest getCredentialDetailRequests(Long skuId) {
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        c1.setFromLocation(FROM_LOCATION);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(BaseDataManager.TO_LOCATION);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(skuId);
        c1.setLotId("2207220009057043879");
        c1.setQty(BigDecimal.ZERO);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        String[] outSplit = "EC->EC".split("->");
        c1.setUsages(List.of(outSplit[0].split(",")));
        c1.setToUsages(List.of(outSplit[1].split(",")));
        c1.setTodaySale(false);

        return c1;
    }

    private OutboundCredentialRequest getOutboundCredentialRequest(String idempotentId, String orderNo) {
        OutboundCredentialRequest request = new OutboundCredentialRequest();
        request.setDeliveryMode(0);
        request.setIdempotentId(idempotentId);
        request.setOrderSource("TRANSFER");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.TRANSFER_OUTBOUND.getCode());
        request.setOrderOperateType(OrderOperateTypeEnum.FINISH_OUT_OF_STOCK.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("TRANSFER");
        request.setExeOrderNo(System.currentTimeMillis()+"");
        request.setSeqNo(System.currentTimeMillis()+"");
        request.setWarehouseId(17L);
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        return request;
    }



}
