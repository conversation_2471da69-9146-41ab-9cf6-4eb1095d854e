package com.ddmc.ims.Integration.purchase;

import static com.ddmc.ims.Integration.BaseDataManager.FROM_LOCATION;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.controller.api.InboundInventoryCredentialController;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.dao.DuplicateKeyException;

/**
 * 采购在途登记
 */
public class PurchaseApplyInTest  extends SpringBootTestBase {

    @Resource
    private BaseDataManager baseDataManager;

    @Resource
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;

    @Resource
    private InboundInventoryCredentialController inboundInventoryCredentialController;

    @Resource
    private InventoryCredentialController inventoryCredentialController;


    public Map<String, PurchaseIntransitInventory> getPurchaseInTransitInventory(String orderNo) {
        LambdaQueryWrapper<PurchaseIntransitInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseIntransitInventory::getOrderNo, orderNo)
            .eq(PurchaseIntransitInventory::getSkuId, 102243L)
            .in(PurchaseIntransitInventory::getUsageCode, List.of("EC", "TB"));
        List<PurchaseIntransitInventory> inventories = purchaseIntransitInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(PurchaseIntransitInventory::getUsageCode, Function.identity()));
    }


    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 申请入库EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     */
    @Test
    public void testPublish1() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        String idempotentId = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId,
            orderNo, new BigDecimal(10), new BigDecimal(5));
        inboundInventoryCredentialController.tryPublishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory = getPurchaseInTransitInventory(orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getBookedIntransitQty());
    }


    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.申请入库EC10个，TB5个
     * 2.申请入库EC10个,第二条采购在途唯一键冲突，抛异常.
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     */
    @Test
    public void testPublish2() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        String idempotentId = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId,
            orderNo, new BigDecimal(10), new BigDecimal(5));
        inboundInventoryCredentialController.tryPublishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory = getPurchaseInTransitInventory(orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getBookedIntransitQty());


        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getDefaultInboundCredentialRequest(idempotentId2,
            orderNo, new BigDecimal(10), new BigDecimal(5));
        inboundInventoryCredentialController.tryPublishInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        Exception exception = null;
        try{
            inventoryCredentialController.confirm(confirmRequest2);
        } catch (DuplicateKeyException e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
    }



    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.申请入库EC10个
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     *
     * 2.申请入库TB5个
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     */
    @Test
    public void testPublish3() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        String idempotentId = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);
        request.setOperateDetails(Collections.singletonList(getCredentialDetailRequest(new BigDecimal(10), "EC")));
        inboundInventoryCredentialController.tryPublishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory = getPurchaseInTransitInventory(orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory.get("TB");
        Assert.assertNull(tbInTransit);


        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2,orderNo);
        request2.setOperateDetails(Collections.singletonList(getCredentialDetailRequest(new BigDecimal(5), "TB")));
        inboundInventoryCredentialController.tryPublishInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(orderNo);
        PurchaseIntransitInventory ecInTransit2 = purchaseInTransitInventory2.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit2.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit2.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit2.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit2 = purchaseInTransitInventory2.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit2.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit2.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit2.getBookedIntransitQty());
    }



    private InboundCredentialRequest getDefaultInboundCredentialRequest(String idempotentId, String orderNo,
        BigDecimal qty, BigDecimal tbQty) {
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);
        List<CredentialDetailRequest> details = Arrays
            .asList(getCredentialDetailRequest(qty, "EC"), getCredentialDetailRequest(tbQty, "TB"));
        request.setOperateDetails(details);

        return request;
    }

    private CredentialDetailRequest getCredentialDetailRequest(BigDecimal qty, String usageCode) {
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        c1.setFromLocation(FROM_LOCATION);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(BaseDataManager.TO_LOCATION);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(102243L);
        c1.setLotId("2207220009057043879");
        c1.setQty(qty);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        c1.setUsages(Arrays.asList(usageCode.split(",")));
        c1.setToUsages(Arrays.asList(usageCode.split(",")));
        c1.setTodaySale(false);
        return c1;
    }

    private InboundCredentialRequest getInboundCredentialRequest(String idempotentId, String orderNo) {
        InboundCredentialRequest request = new InboundCredentialRequest();
        request.setIdempotentId(idempotentId);
        request.setOrderSource("PURCHASE");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.PURCHASE.getCode());
        request.setOrderOperateType(OrderOperateTypeEnum.APPLY_IN_OF_STOCK.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("PURCHASE");
        request.setExeOrderNo(System.currentTimeMillis()+"");
        request.setSeqNo(System.currentTimeMillis()+"");
        request.setWarehouseId(17L);
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        request.setExpectArriveTime(ThreadLocalDateUtils.getPlusStartOfDay(new Date(), 0));
        return request;
    }


}
