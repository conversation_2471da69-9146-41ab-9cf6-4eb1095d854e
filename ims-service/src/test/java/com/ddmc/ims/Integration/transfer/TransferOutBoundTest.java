package com.ddmc.ims.Integration.transfer;

import static com.ddmc.ims.Integration.BaseDataManager.FROM_LOCATION;
import static com.ddmc.ims.Integration.BaseDataManager.TO_LOCATION;
import static com.ddmc.ims.Integration.BaseDataManager.usageCodeCodeMap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.command.enums.CommandTypeEnum;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.controller.api.OutboundInventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

/**
 * 调拨出库测试，含DO单出库完成清理待发量
 */
public class TransferOutBoundTest extends SpringBootTestBase {

    @Resource
    private BaseDataManager baseDataManager;

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;

    @Resource
    private OutboundInventoryCredentialController outboundInventoryCredentialController;

    @Resource
    private InventoryCredentialController inventoryCredentialController;

    public void refreshTransferInTransitInventory(String orderNo, BigDecimal planQty,BigDecimal inTransitQty,BigDecimal allocQty, BigDecimal waitAllocQty,
         String usageCode, String toUsageCode) {
        baseDataManager
            .insertBaseTransferInTransitData(orderNo, 102243L, planQty,inTransitQty,allocQty, waitAllocQty , usageCode, toUsageCode);
    }


    public void refreshWarehouseSkuInventory(LogicInventoryLocation location, BigDecimal freeQty, BigDecimal allocQty,
        String usageCode) {
        LambdaUpdateWrapper<WarehouseSkuInventory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(WarehouseSkuInventory::getFreeQty, freeQty).set(WarehouseSkuInventory::getAllocQty, allocQty)
            .eq(WarehouseSkuInventory::getSkuId, 102243L).eq(WarehouseSkuInventory::getUsageCode, usageCode)
            .eq(WarehouseSkuInventory::getWarehouseId, location.getWarehouseId())
            .eq(WarehouseSkuInventory::getCargoOwnerId, location.getCargoOwnerId())
            .eq(WarehouseSkuInventory::getLogicInventoryLocationCode, usageCodeCodeMap.get(usageCode));
        warehouseSkuInventoryMapper.update(null, updateWrapper);
    }


    public Map<String, TransferIntransitInventory> getTransferInTransitInventory(String orderNo) {
        LambdaQueryWrapper<TransferIntransitInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransferIntransitInventory::getOrderNo, orderNo)
            .eq(TransferIntransitInventory::getSkuId, 102243L);
        List<TransferIntransitInventory> inventories = transferIntransitInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(
                t -> t.getUsageCode() + "," + t.getFromWarehouseId() + "," + t.getFromCargoOwnerId() + "," + t
                    .getFromLogicInventoryLocationCode() + "," + t.getToUsageCode() + "," + +t.getToWarehouseId() + ","
                    + t.getToCargoOwnerId() + "," + t.getToLogicInventoryLocationCode(), Function.identity()));
    }

    public Map<String, WarehouseSkuInventory> getWarehouseSkuInventory(LogicInventoryLocation location) {
        LambdaQueryWrapper<WarehouseSkuInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(WarehouseSkuInventory::getSkuId, 102243L)
            .eq(WarehouseSkuInventory::getWarehouseId, location.getWarehouseId())
            .eq(WarehouseSkuInventory::getCargoOwnerId, location.getCargoOwnerId());
        List<WarehouseSkuInventory> inventories = warehouseSkuInventoryMapper.selectList(queryWrapper);
        return inventories.stream().collect(
            Collectors.toMap(t -> t.getUsageCode() + "," + t.getLogicInventoryLocationCode(), Function.identity()));
    }

    @Before
    public void deleteTransferInTransitInventory() {
        LambdaQueryWrapper<TransferIntransitInventory> deleteTransferInTransitInventory = new LambdaQueryWrapper<>();
        deleteTransferInTransitInventory
            .in(TransferIntransitInventory::getOrderNo, Arrays.asList("17-1-20230830-0", "17-1-20230830-1"));
        transferIntransitInventoryMapper.delete(deleteTransferInTransitInventory);

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     *
     * 凭证EC->EC出10个，清理TB->TB10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        10	            10	        0	            90
     * TB		    TB              20	        0	            0	        10	            50
     */
    @Test
    public void testOutBound1() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC->EC","TB->TB");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("90.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     *
     * 凭证EC,TB->EC出10个，清理TB->TB10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        10	            10	        0	            90
     * TB		    TB              20	        0	            0	        10	            50
     */
    @Test
    public void testOutBound2() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC,TB->EC","TB->TB");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("90.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * CU                                                                                   10
     *
     * 凭证CU->EC出5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        5	            5	        5	            100
     * TB		    TB              20	        0	            0	        20	            50
     * CU                                                                                   5
     */
    @Test
    public void testOutBound3() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(10), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"CU->EC","CU->EC");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());


        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("CU,RECEIVE");
        Assert.assertEquals(new BigDecimal("5.000"), cuInventory.getFreeQty());
    }





    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * TB	        EC          	20	        0	            0	        20
     *
     * 凭证EC->EC出5个,凭证EC->EC清4个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        5	            5	        1	            95
     * TB		    TB              20	        0	            0	        20	            50
     * TB		    EC              20	        0	            0	        20
     */
    @Test
    public void testOutBound4() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(5), new BigDecimal(4), "EC->EC", "EC->EC"));
        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("1.000"), ecInTransit.getWaitAllocQty());


        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("95.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * DR	        DR	            5	        0	            0	        5	            5
     *
     * 凭证DR->DR出10个，清理TB->TB10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        0	            0	        10	            100
     * TB		    TB              20	        0	            0	        10	            50
     * DR	        DR	            5	        10	            10	        0	            -5
     */
    @Test
    public void testOutBound5() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(5),new BigDecimal(0),new BigDecimal(0), new BigDecimal(5), "DR","DR");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(5), new BigDecimal(0), "DR");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(10), new BigDecimal(10), "DR->DR", "TB->TB"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getWaitAllocQty());


        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("DR,17,1,BAD_PRODUCT_RTV,DR,1,2,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("5.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

        WarehouseSkuInventory drInventory = warehouseSkuInventoryMap.get("DR,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("-5.000"), drInventory.getFreeQty());
    }





    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            100
     * TB	        TB          	20	        0	            0	        20	            50
     *
     * 凭证EC->EC出10个，清理TB->TB10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        10	            10	        0	            90
     * TB		    TB              20	        0	            0	        10	            50
     */
    @Test
    public void testOutBound6() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC->EC","TB->TB");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("90.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * TB	        EC          	20	        0	            0	        20
     *
     * EC,TB->EC出10个，EC->EC 清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        0	            0	        5	            100
     * TB		    TB              20	        0	            0	        20	            40
     * TB		    EC              20	        10	            10	        10
     */
    @Test
    public void testOutBound7() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(10), new BigDecimal(5), "EC,TB->EC", "EC->EC"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("40.000"), tbInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * TB	        EC          	20	        0	            0	        20
     *
     * EC,TB->EC出100个，TB->EC 清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        80	            80	        0	            20
     * TB		    TB              20	        0	            0	        20	            30
     * TB		    EC              20	        20	            20	        0
     */
    @Test
    public void testOutBound8() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "EC,TB->EC", "TB->EC"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("80.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("80.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("30.000"), tbInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            `	        `	            `	        `	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * TB	        EC          	20	        0	            0	        20
     *
     * EC,TB->EC出100个，TB->EC 清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        80	            80	        0	            20
     * TB		    TB              20	        0	            0	        20	            30
     * TB		    EC              20	        20	            20	        0
     */
    @Test
    public void testOutBound9() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(0),new BigDecimal(0),new BigDecimal(0), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "EC,TB->EC", "TB->EC"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("80.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("80.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("30.000"), tbInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            `	        `	            `	        `	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * CU	        	            `	        `	            `	        `	            10
     *
     * 凭证CU->EC出5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        5	            5	        0	            100
     * TB		    TB              20	        0	            0	        20	            50
     * CU		                    	         	             	         	            5
     */
    @Test
    public void testOutBound10() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(10), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"CU->EC","CU->EC");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());


        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("CU,RECEIVE");
        Assert.assertEquals(new BigDecimal("5.000"), cuInventory.getFreeQty());


    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            `	        `	            `	        `	            100
     * TB	        TB          	`	        `	            `	        `	            50
     * CU	                      	`	        `	            `	        `	            10
     *
     * 凭证CU->EC出5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        5	            5	        0	            100
     * TB		    TB              `	        `	            `	        `	            50
     * CU	                      	`	        `	            `	        `	            5
     */
    @Test
    public void testOutBound11() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(10), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"CU->EC","CU->EC");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory cuEcInTransit = transferInTransitInventory.get("CU,RECEIVE,EC,GOOD_PRODUCT");
        Assert.assertNull(cuEcInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("CU,RECEIVE");
        Assert.assertEquals(new BigDecimal("5.000"), cuInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	·	        ·	            ·	        ·	            50
     * CU	                    	·	        ·	            ·	        ·	            10
     *
     * 凭证CU->EC出5个,CU->EC清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        5	            5	        5	            100
     * TB		    TB              ·	        ·	            ·	        ·	            50
     * CU		                    ·	        ·	            ·	        ·	            5
     */
    @Test
    public void testOutBound12() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(10), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"CU->EC","CU->EC");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("CU,RECEIVE");
        Assert.assertEquals(new BigDecimal("5.000"), cuInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * DR	        DR          	·	        ·	            ·	        ·	            50
     *
     * 凭证DR->DR出5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * DR		    DR              0	        5	            5	        0	            45
     */
    @Test
    public void testOutBound13() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "DR");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"DR->DR",null);
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory drInTransit = transferInTransitInventory.get("DR,17,1,BAD_PRODUCT_RTV,DR,1,2,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("0.000"), drInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), drInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), drInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), drInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);

        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("DR,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("45.000"), cuInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            100
     * TB	        TB          	·	        ·	            ·	        ·	            50
     *
     * EC,TB->EC出100个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        100	            100	        0	            0
     * TB		    TB              ·	        ·	            ·	        ·	            50
     */
    @Test
    public void testOutBound14() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "EC,TB->EC", null));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            100
     * TB	        TB          	·	        ·	            ·	        ·	            50
     *
     * EC,TB->EC,TB出100个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        100	            100	        0	            0
     * TB		    TB              ·	        ·	            ·	        ·	            50
     */
    @Test
    public void testOutBound15() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "EC,TB->EC,TB", null));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            100
     * TB	        TB          	·	        ·	            ·	        ·	            50
     *
     * TB,EC->TB,EC出100个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        100	            100	        0	            0
     * TB		    TB              ·	        ·	            ·	        ·	            50
     */
    @Test
    public void testOutBound16() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "TB,EC->TB,EC", null));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            100
     * TB	        TB          	·	        ·	            ·	        ·	            50
     *
     * TB,EC->EC出100个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        100	            100	        0	            0
     * TB		    TB              ·	        ·	            ·	        ·	            50
     */
    @Test
    public void testOutBound17() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "TB,EC->EC", null));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * DR	        DR	            2	        0	            0	        2	            100
     * DC                                                                                   5
     *
     * 凭证DC->DR出2个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * DR	        DR	            2	        2	            2	        0	            100
     * DC		                    ·	        ·	            ·	        ·	            3
     */
    @Test
    public void testOutBound18() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(2),new BigDecimal(0),new BigDecimal(0), new BigDecimal(2), "DR","DR");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "DR");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(5), new BigDecimal(0), "DC");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(2),"DC->DR",null);
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertNull(ecInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);


        TransferIntransitInventory drInTransit = transferInTransitInventory.get("DR,17,1,BAD_PRODUCT_RTV,DR,1,2,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("2.000"), drInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("2.000"), drInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("2.000"), drInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), drInTransit.getWaitAllocQty());


        TransferIntransitInventory dcInTransit = transferInTransitInventory.get("DC,17,1,BAD_PRODUCT_NORMAL,DC,1,2,BAD_PRODUCT_NORMAL");
        Assert.assertNull(dcInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);

        WarehouseSkuInventory drInventory = warehouseSkuInventoryMap.get("DR,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("100.000"), drInventory.getFreeQty());


        WarehouseSkuInventory dcInventory = warehouseSkuInventoryMap.get("DC,BAD_PRODUCT_NORMAL");
        Assert.assertEquals(new BigDecimal("3.000"), dcInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            0	        0	            0	        0	            100
     *
     * 凭证EC->EC出10个,凭证EC->EC再登记
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        10	            10	        0	            90
     */
    @Test
    public void testOutBound19() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(0),new BigDecimal(0),new BigDecimal(0), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC->EC",null);
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        String idempotentIdPublish = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest requestPublish = getDefaultOutboundCredentialRequest(idempotentIdPublish, orderNo,
            new BigDecimal(10),"EC->EC",null);
        requestPublish.setExeOrderNo(request.getExeOrderNo());
        requestPublish.setOrderOperateType(OrderOperateTypeEnum.APPLY_OUT_OF_STOCK.getCode());
        outboundInventoryCredentialController.tryPublishOutbound(requestPublish);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentIdPublish);
        inventoryCredentialController.confirm(confirmRequest2);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());



        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("90.000"), ecInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·           ·	            100
     * TB                                                                                   100
     *
     * TB->EC出100个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        100	            100	        0	            0
     * TB                                                                                   100
     */
    @Test
    public void testOutBound20() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "TB->EC", null));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	                                                                                100
     * TB	                                                                                100
     *
     * TB->EC出100个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        100	            100	        0	            0
     * TB		    EC              `	        `	            `	        `	            100
     */
    @Test
    public void testOutBound21() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "TB->EC", null));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), tbInventory.getFreeQty());
    }





    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	100	        0	            0	        100	            100
     *
     * EC,TB->TB,EC出110个，TB->EC 清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        10	            10	        0	            90
     * TB		    TB              100	        100	            100	        0	            0
     */
    @Test
    public void testOutBound22() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(100),new BigDecimal(0),new BigDecimal(0), new BigDecimal(100), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(110), new BigDecimal(5), "EC,TB->TB,EC", "TB->EC"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("90.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	100	        0	            0	        100	            100
     *
     * TB->TB 出110个，TB->EC 清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        10	            10	        0	            90
     * TB		    TB              100	        100	            100	        0	            0
     */
    @Test
    public void testOutBound23() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(100),new BigDecimal(0),new BigDecimal(0), new BigDecimal(100), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(110), new BigDecimal(5), "TB->TB", "TB->EC"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("90.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            100
     * TB	        TB          	100	        0	            0	        100	            100
     *
     * TB->TB 出110个，TB->EC 清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        10	            10	        0	            90
     * TB		    TB              100	        100	            100	        0	            0
     */
    @Test
    public void testOutBound24() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(100),new BigDecimal(0),new BigDecimal(0), new BigDecimal(100), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(110), new BigDecimal(5), "TB->TB", "TB->EC"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("90.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     *
     * 凭证EC->EC出200个，清理TB->TB10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        200	            200	        0	            -100
     * TB		    TB              20	        0	            0	        10	            50
     */
    @Test
    public void testOutBound25() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(200),new BigDecimal(10),"EC->EC","TB->TB");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("-100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     *
     * 凭证EC,TB->EC出200个，清理TB->TB10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        200	            200	        0	            -100
     * TB		    TB              20	        0	            0	        10	            50
     */
    @Test
    public void testOutBound26() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(200), new BigDecimal(10),"EC,TB->EC","TB->TB");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("-100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * CU                                                                                   10
     *
     * 凭证CU->EC出200个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        200	            200	        0	            100
     * TB		    TB              20	        0	            0	        20	            50
     * CU                                                                                   -190
     */
    @Test
    public void testOutBound27() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(10), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(200),"CU->EC","CU->EC");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());


        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("CU,RECEIVE");
        Assert.assertEquals(new BigDecimal("-190.000"), cuInventory.getFreeQty());
    }





    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * TB	        EC          	20	        0	            0	        20
     *
     * 凭证EC->EC出200个,凭证EC->EC清4个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        200	            200	        0	            -100
     * TB		    TB              20	        0	            0	        20	            50
     * TB		    EC              20	        0	            0	        20
     */
    @Test
    public void testOutBound28() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(200), new BigDecimal(4), "EC->EC", "EC->EC"));
        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());


        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("-100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * DR	        DR	            5	        0	            0	        5	            5
     *
     * 凭证DR->DR出200个，清理TB->TB10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        0	            0	        10	            100
     * TB		    TB              20	        0	            0	        10	            50
     * DR	        DR	            5	        200	            200	        0	            -195
     */
    @Test
    public void testOutBound29() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(5),new BigDecimal(0),new BigDecimal(0), new BigDecimal(5), "DR","DR");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(5), new BigDecimal(0), "DR");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(200), new BigDecimal(10), "DR->DR", "TB->TB"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getWaitAllocQty());


        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("DR,17,1,BAD_PRODUCT_RTV,DR,1,2,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("5.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

        WarehouseSkuInventory drInventory = warehouseSkuInventoryMap.get("DR,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("-195.000"), drInventory.getFreeQty());
    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            100
     * TB	        TB          	20	        0	            0	        20	            50
     *
     * 凭证EC->EC出200个，清理TB->TB10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        200	            200	        0	            -100
     * TB		    TB              20	        0	            0	        10	            50
     */
    @Test
    public void testOutBound30() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(200),new BigDecimal(10),"EC->EC","TB->TB");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("-100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * TB	        EC          	20	        0	            0	        20
     *
     * EC,TB->EC出-200个，EC->EC 清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        180	            180	        0	            -80
     * TB		    TB              20	        0	            0	        20	            30
     * TB		    EC              20	        20	            20	        0
     */
    @Test
    public void testOutBound31() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(200), new BigDecimal(5), "EC,TB->EC", "EC->EC"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("180.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("180.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("-80.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("30.000"), tbInventory.getFreeQty());

    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            `	        `	            `	        `	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * TB	        EC          	20	        0	            0	        20
     *
     * EC,TB->EC出200个，TB->EC 清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        180	            180	        0	            -80
     * TB		    TB              20	        0	            0	        20	            30
     * TB		    EC              20	        20	            20	        0
     */
    @Test
    public void testOutBound32() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(0),new BigDecimal(0),new BigDecimal(0), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(200), new BigDecimal(5), "EC,TB->EC", "TB->EC"));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("180.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("180.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("-80.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("30.000"), tbInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            `	        `	            `	        `	            100
     * TB	        TB          	20	        0	            0	        20	            50
     * CU	        	            `	        `	            `	        `	            10
     *
     * 凭证CU->EC出200个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        200	            200	        0	            100
     * TB		    TB              20	        0	            0	        20	            50
     * CU		                    	         	             	         	            -190
     */
    @Test
    public void testOutBound33() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20),new BigDecimal(0),new BigDecimal(0), new BigDecimal(20), "TB","TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(10), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(200),"CU->EC","CU->EC");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());


        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("CU,RECEIVE");
        Assert.assertEquals(new BigDecimal("-190.000"), cuInventory.getFreeQty());


    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            `	        `	            `	        `	            100
     * TB	        TB          	`	        `	            `	        `	            50
     * CU	                      	`	        `	            `	        `	            10
     *
     * 凭证CU->EC出200个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        5	            5	        0	            100
     * TB		    TB              `	        `	            `	        `	            50
     * CU	                      	`	        `	            `	        `	            5
     */
    @Test
    public void testOutBound34() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(10), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(200),"CU->EC","CU->EC");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory cuEcInTransit = transferInTransitInventory.get("CU,RECEIVE,EC,GOOD_PRODUCT");
        Assert.assertNull(cuEcInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("CU,RECEIVE");
        Assert.assertEquals(new BigDecimal("-190.000"), cuInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	·	        ·	            ·	        ·	            50
     * CU	                    	·	        ·	            ·	        ·	            10
     *
     * 凭证CU->EC出200个,CU->EC清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        200	            200	        0	            100
     * TB		    TB              ·	        ·	            ·	        ·	            50
     * CU		                    ·	        ·	            ·	        ·	            -190
     */
    @Test
    public void testOutBound35() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(10), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(200),"CU->EC","CU->EC");
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("CU,RECEIVE");
        Assert.assertEquals(new BigDecimal("-190.000"), cuInventory.getFreeQty());

    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * DR	        DR          	·	        ·	            ·	        ·	            50
     *
     * 凭证DR->DR出200个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * DR		    DR              0	        200	            200	        0	            -150
     */
    @Test
    public void testOutBound36() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "DR");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request = getDefaultOutboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(200),"DR->DR",null);
        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory drInTransit = transferInTransitInventory.get("DR,17,1,BAD_PRODUCT_RTV,DR,1,2,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("0.000"), drInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("200.000"), drInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("200.000"), drInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), drInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);

        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("DR,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("-150.000"), cuInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	                                                                                100
     * TB		    EC              100	        0	            0	        100	            100
     *
     * TB->EC出200个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        100	            100	        0	            0
     * TB		    EC              100	        100	            100	        0	            0
     */
    @Test
    public void testOutBound37() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(100),new BigDecimal(0),new BigDecimal(0), new BigDecimal(100), "TB","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(200), new BigDecimal(5), "TB->EC", null));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            100
     * TB	        EC          	10	        0	            0	        10	            50
     *
     * TB,EC->EC出100个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              0	        90	            90	        0	            10
     * TB		    EC              10	        10	            10	        0	            40
     */
    @Test
    public void testOutBound38() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "TB","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "TB,EC->EC", null));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("90.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("90.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("40.000"), tbInventory.getFreeQty());

    }




    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            0	        10	            100
     * TB	        TB          	·	        ·	            ·	        ·	            50
     *
     * TB->TB出100个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC		    EC              10	        100	            100	        0	            0
     * TB		    TB              ·	        ·	            ·	        ·	            50
     */
    @Test
    public void testOutBound39() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10),new BigDecimal(0),new BigDecimal(0), new BigDecimal(10), "EC","EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(100), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(FROM_LOCATION, new BigDecimal(50), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        OutboundCredentialRequest request =getOutboundCredentialRequest(idempotentId,orderNo);
        List<CredentialDetailRequest> credentialDetailRequests = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), new BigDecimal(5), "TB->TB", null));

        request.setOperateDetails(credentialDetailRequests);

        outboundInventoryCredentialController.tryOutbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("100.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(FROM_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("50.000"), tbInventory.getFreeQty());

    }








    private OutboundCredentialRequest getDefaultOutboundCredentialRequest(String idempotentId, String orderNo,
        BigDecimal qty,String outUsageCode,String cleanUsageCode) {
        OutboundCredentialRequest request = getOutboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = getCredentialDetailRequests(qty,qty,
            outUsageCode, cleanUsageCode);
        request.setOperateDetails(details);

        return request;
    }

    private OutboundCredentialRequest getDefaultOutboundCredentialRequest(String idempotentId, String orderNo,
        BigDecimal qty,BigDecimal cleanQty,String outUsageCode,String cleanUsageCode) {
        OutboundCredentialRequest request = getOutboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = getCredentialDetailRequests(qty,cleanQty,
            outUsageCode, cleanUsageCode);
        request.setOperateDetails(details);

        return request;
    }

    private List<CredentialDetailRequest> getCredentialDetailRequests(BigDecimal qty,BigDecimal cleanQty, String outUsageCode,
        String cleanUsageCode) {
        List<CredentialDetailRequest> details = new ArrayList<>();
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        String[] outSplit = outUsageCode.split("->");

        LogicInventoryLocation fromLocation = new LogicInventoryLocation(FROM_LOCATION.getWarehouseId(),
            FROM_LOCATION.getCargoOwnerId(), usageCodeCodeMap.get(outSplit[0]));
        LogicInventoryLocation toLocation = new LogicInventoryLocation(TO_LOCATION.getWarehouseId(),
            TO_LOCATION.getCargoOwnerId(), usageCodeCodeMap.get(outSplit[1]));
        c1.setFromLocation(fromLocation);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(toLocation);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(102243L);
        c1.setLotId("2207220009057043879");
        c1.setQty(qty);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        c1.setUsages(List.of(outSplit[0].split(",")));
        c1.setToUsages(List.of(outSplit[1].split(",")));
        c1.setCommandType(CommandTypeEnum.AVAILABLE_TO_TRANSFER_IN_TRANSIT.getCode());
        c1.setTodaySale(false);
        details.add(c1);

        if (StringUtils.isEmpty(cleanUsageCode)) {
            return details;
        }

        String[] cleanSplit = cleanUsageCode.split("->");
        LogicInventoryLocation cleanFromLocation = new LogicInventoryLocation(FROM_LOCATION.getWarehouseId(),
            FROM_LOCATION.getCargoOwnerId(), usageCodeCodeMap.get(cleanSplit[0]));
        LogicInventoryLocation cleanToLocation = new LogicInventoryLocation(TO_LOCATION.getWarehouseId(),
            TO_LOCATION.getCargoOwnerId(), usageCodeCodeMap.get(cleanSplit[1]));

        CredentialDetailRequest c2 = new CredentialDetailRequest();
        c2.setFromLocation(cleanFromLocation);
        c2.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c2.setToLocation(cleanToLocation);
        c2.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c2.setSkuId(102243L);
        c2.setLotId("");
        c2.setQty(cleanQty);
        c2.setDemandDate(new Date());
        c2.setOrderTag("");

        c2.setUsages(List.of(cleanSplit[0].split(",")));
        c2.setToUsages(List.of(cleanSplit[1].split(",")));
        c2.setCommandType(CommandTypeEnum.CLEAN_TRANSFER_IN_TRANSIT.getCode());
        c2.setTodaySale(false);
        details.add(c2);
        return details;
    }

    private OutboundCredentialRequest getOutboundCredentialRequest(String idempotentId, String orderNo) {
        OutboundCredentialRequest request = new OutboundCredentialRequest();
        request.setDeliveryMode(0);
        request.setIdempotentId(idempotentId);
        request.setOrderSource("TRANSFER");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.TRANSFER_DO_OUTBOUND.getCode());
        request.setOrderOperateType(OrderOperateTypeEnum.OUT_OF_STOCK.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("TRANSFER");
        request.setExeOrderNo(System.currentTimeMillis()+"");
        request.setSeqNo(System.currentTimeMillis()+"");
        request.setWarehouseId(17L);
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        return request;
    }


}
