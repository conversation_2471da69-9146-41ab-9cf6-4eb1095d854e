package com.ddmc.ims.Integration.purchase;

import static com.ddmc.ims.Integration.BaseDataManager.FROM_LOCATION;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.controller.api.InboundInventoryCredentialController;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.PurchaseIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.PurchaseIntransitInventory;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.Assert;
import org.junit.Test;

/**
 * 采购在途取消登记
 */
public class PurchaseCancelApplyTest extends SpringBootTestBase  {

    @Resource
    private BaseDataManager baseDataManager;

    @Resource
    private PurchaseIntransitInventoryMapper purchaseIntransitInventoryMapper;

    @Resource
    private InboundInventoryCredentialController inboundInventoryCredentialController;

    @Resource
    private InventoryCredentialController inventoryCredentialController;


    public Map<String, PurchaseIntransitInventory> getPurchaseInTransitInventory(String orderNo) {
        LambdaQueryWrapper<PurchaseIntransitInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseIntransitInventory::getOrderNo, orderNo)
            .eq(PurchaseIntransitInventory::getSkuId, 102243L)
            .in(PurchaseIntransitInventory::getUsageCode, List.of("EC", "TB"));
        List<PurchaseIntransitInventory> inventories = purchaseIntransitInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(PurchaseIntransitInventory::getUsageCode, Function.identity()));
    }


    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.申请入库EC10个，TB5个
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     * EC            10           10              10
     * TB            5            5               5
     *
     * 2.同采购单取消入库申请
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     */
    @Test
    public void testCancel1() {
        String orderNo = "TESTPO" + System.currentTimeMillis();
        doPublish(orderNo);

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo, OrderOperateTypeEnum.CANCEL_IN_STOCK_APPLY);
        inboundInventoryCredentialController.tryCancelInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(orderNo);
        PurchaseIntransitInventory ecInTransit2 = purchaseInTransitInventory2.get("EC");
        Assert.assertNull(ecInTransit2);

        PurchaseIntransitInventory tbInTransit2 = purchaseInTransitInventory2.get("TB");
        Assert.assertNull(tbInTransit2);
    }


    /**
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     * 1.在无采购单登记的情况下直接取消入库申请
     *
     *
     * usageCode     plan_qty     intransit_qty   booked_intransit_qty
     *
     */
    @Test
    public void testCancel2() {
        String orderNo = "TESTPO" + System.currentTimeMillis();

        String idempotentId2 = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request2 = getInboundCredentialRequest(idempotentId2, orderNo, OrderOperateTypeEnum.CANCEL_IN_STOCK_APPLY);
        inboundInventoryCredentialController.tryCancelInbound(request2);
        CredentialTryConfirmRequest confirmRequest2 = new CredentialTryConfirmRequest();
        confirmRequest2.setIdempotentId(idempotentId2);
        inventoryCredentialController.confirm(confirmRequest2);
        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory2 = getPurchaseInTransitInventory(orderNo);
        PurchaseIntransitInventory ecInTransit2 = purchaseInTransitInventory2.get("EC");
        Assert.assertNull(ecInTransit2);

        PurchaseIntransitInventory tbInTransit2 = purchaseInTransitInventory2.get("TB");
        Assert.assertNull(tbInTransit2);
    }




    private void doPublish(String orderNo) {
        String idempotentId = "Purchase" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId,
            orderNo, new BigDecimal(10), new BigDecimal(5));
        inboundInventoryCredentialController.tryPublishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, PurchaseIntransitInventory> purchaseInTransitInventory = getPurchaseInTransitInventory(orderNo);
        PurchaseIntransitInventory ecInTransit = purchaseInTransitInventory.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getBookedIntransitQty());

        PurchaseIntransitInventory tbInTransit = purchaseInTransitInventory.get("TB");
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getBookedIntransitQty());
    }


    private InboundCredentialRequest getDefaultInboundCredentialRequest(String idempotentId, String orderNo,
        BigDecimal qty, BigDecimal tbQty) {
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo,OrderOperateTypeEnum.APPLY_IN_OF_STOCK);
        List<CredentialDetailRequest> details = Arrays
            .asList(getCredentialDetailRequest(qty, "EC"), getCredentialDetailRequest(tbQty, "TB"));
        request.setOperateDetails(details);

        return request;
    }

    private CredentialDetailRequest getCredentialDetailRequest(BigDecimal qty, String usageCode) {
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        c1.setFromLocation(FROM_LOCATION);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(BaseDataManager.TO_LOCATION);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(102243L);
        c1.setLotId("2207220009057043879");
        c1.setQty(qty);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        c1.setUsages(Arrays.asList(usageCode.split(",")));
        c1.setToUsages(Arrays.asList(usageCode.split(",")));
        c1.setTodaySale(false);
        return c1;
    }

    private InboundCredentialRequest getInboundCredentialRequest(String idempotentId, String orderNo, OrderOperateTypeEnum operateTypeEnum) {
        InboundCredentialRequest request = new InboundCredentialRequest();
        request.setIdempotentId(idempotentId);
        request.setOrderSource("PURCHASE");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.PURCHASE.getCode());
        request.setOrderOperateType(operateTypeEnum.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("PURCHASE");
        request.setExeOrderNo(System.currentTimeMillis()+"");
        request.setSeqNo(System.currentTimeMillis()+"");
        request.setWarehouseId(17L);
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        request.setExpectArriveTime(ThreadLocalDateUtils.getPlusStartOfDay(new Date(), 0));
        return request;
    }




}
