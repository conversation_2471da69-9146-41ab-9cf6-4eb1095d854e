package com.ddmc.ims.Integration.transfer;

import static com.ddmc.ims.Integration.BaseDataManager.FROM_LOCATION;
import static com.ddmc.ims.Integration.BaseDataManager.TO_LOCATION;
import static com.ddmc.ims.Integration.BaseDataManager.usageCodeCodeMap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.controller.api.InboundInventoryCredentialController;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class TransferInBoundClearTest extends SpringBootTestBase {

    @Resource
    private BaseDataManager baseDataManager;

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private InboundInventoryCredentialController inboundInventoryCredentialController;

    @Resource
    private InventoryCredentialController inventoryCredentialController;


    public void refreshTransferInTransitInventory(String orderNo, BigDecimal planQty,BigDecimal inTransitQty,BigDecimal allocQty, BigDecimal waitAllocQty,
        String usageCode, String toUsageCode) {
        baseDataManager
            .insertBaseTransferInTransitData(orderNo, 102243L, planQty,inTransitQty,allocQty, waitAllocQty , usageCode, toUsageCode);
    }

    public Map<String, TransferIntransitInventory> getTransferInTransitInventory(String orderNo) {
        LambdaQueryWrapper<TransferIntransitInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransferIntransitInventory::getOrderNo, orderNo)
            .eq(TransferIntransitInventory::getSkuId, 102243L);
        List<TransferIntransitInventory> inventories = transferIntransitInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(
                t -> t.getUsageCode() + "," + t.getFromLogicInventoryLocationCode() + "," + t.getToUsageCode() + "," + t
                    .getToLogicInventoryLocationCode(), Function.identity()));
    }


    @Before
    public void deleteTransferInTransitInventory() {
        LambdaQueryWrapper<TransferIntransitInventory> deleteTransferInTransitInventory = new LambdaQueryWrapper<>();
        deleteTransferInTransitInventory
            .in(TransferIntransitInventory::getOrderNo, Arrays.asList("17-1-20230830-0", "17-1-20230830-1"));
        transferIntransitInventoryMapper.delete(deleteTransferInTransitInventory);


    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        10	            10	        0
     * TB	        TB	            20	        20	            20	        0
     *
     * 凭证EC->EC清10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        0	            10	        0
     * TB	        TB	            20	        20	            20	        0
     *
     */
    @Test
    public void testInBoundClear1() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");

        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundClearCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC->EC");
        inboundInventoryCredentialController.tryFinishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        10	            10	        0
     * TB	        TB	            20	        20	            20	        0
     *
     * 凭证EC,TB->EC清10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        0	            10	        0
     * TB	        TB	            20	        20	            20	        0
     */
    @Test
    public void testInBoundClear2() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");

        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundClearCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC,TB->EC");
        inboundInventoryCredentialController.tryFinishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        10	            10	        0
     * TB	        TB	            20	        20	            20	        0
     *
     * 凭证EC->CU清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        5	            10	        0
     * TB	        TB	            20	        20	            20	        0
     */
    @Test
    public void testInBoundClear3() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");

        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundClearCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"EC->CU");
        inboundInventoryCredentialController.tryFinishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

    }




    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        10	            10	        0
     * TB	        TB	            20	        20	            20	        0
     * TB	        EC	            20	        20	            20	        0
     *
     * 凭证EC->CU清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        5	            10	        0
     * TB	        TB	            20	        20	            20	        0
     * TB	        EC	            20	        20	            20	        0
     */
    @Test
    public void testInBoundClear4() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","EC");

        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);
        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(5), "EC->CU"));
        request.setOperateDetails(details);

        inboundInventoryCredentialController.tryFinishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());


    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        10	            10	        0
     * TB	        TB	            20	        20	            20	        0
     * DR	        DR	            20	        20	            20	        0
     *
     * 凭证DR->DR清10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        10	            10	        0
     * TB	        TB	            20	        20	            20	        0
     * DR	        DR	            20	        10	            20	        0
     */
    @Test
    public void testInBoundClear5() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "DR","DR");

        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundClearCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"DR->DR");
        inboundInventoryCredentialController.tryFinishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory drInTransit = transferInTransitInventory.get("DR,BAD_PRODUCT_RTV,DR,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("20.000"), drInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), drInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), drInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), drInTransit.getWaitAllocQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            ·	        ·	            ·	        ·
     * TB	        TB	            20	        20	            20	        0
     *
     * 凭证EC->EC清10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            ·	        ·	            ·	        ·
     * TB	        TB	            20	        20	            20	        0
     */
    @Test
    public void testInBoundClear6() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");

        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundClearCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC->EC");
        inboundInventoryCredentialController.tryFinishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertNull(ecInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        10	            10	        0
     * TB	        TB	            20	        20	            20	        0
     * TB	        EC	            20	        20	            20	        0
     *
     * 凭证TB->EC清5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty
     * EC	        EC	            10	        10	            10	        0
     * TB	        TB	            20	        20	            20	        0
     * TB	        EC	            20	        15	            20	        0
     */
    @Test
    public void testInBoundClear7() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","EC");

        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);
        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(5), "TB->EC"));
        request.setOperateDetails(details);

        inboundInventoryCredentialController.tryFinishInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,GOOD_PRODUCT,EC,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());


    }



    private InboundCredentialRequest getDefaultInboundClearCredentialRequest(String idempotentId, String orderNo,
        BigDecimal qty, String usageCode) {
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId,
            orderNo);

        List<CredentialDetailRequest> details = getCredentialDetailRequests(qty,
            usageCode);
        request.setOperateDetails(details);
        return request;
    }

    private List<CredentialDetailRequest> getCredentialDetailRequests(BigDecimal qty, String usageCode) {
        List<CredentialDetailRequest> details = new ArrayList<>();
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        String[] outSplit = usageCode.split("->");
        LogicInventoryLocation fromLocation = new LogicInventoryLocation(FROM_LOCATION.getWarehouseId(),
            FROM_LOCATION.getCargoOwnerId(), usageCodeCodeMap.get(outSplit[0]));
        LogicInventoryLocation toLocation = new LogicInventoryLocation(TO_LOCATION.getWarehouseId(),
            TO_LOCATION.getCargoOwnerId(), usageCodeCodeMap.get(outSplit[1]));

        c1.setFromLocation(fromLocation);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(toLocation);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(102243L);
        c1.setLotId("2308300000017711597");
        c1.setQty(qty);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        String[] split = usageCode.split("->");
        c1.setUsages(List.of(split[0].split(",")));
        c1.setToUsages(List.of(split[1].split(",")));
        c1.setCommandType("");
        c1.setTodaySale(false);
        details.add(c1);
        return details;
    }

    private InboundCredentialRequest getInboundCredentialRequest(String idempotentId, String orderNo) {
        InboundCredentialRequest request = new InboundCredentialRequest();
        request.setIdempotentId(idempotentId);
        request.setOrderSource("TRANSFER");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.TRANSFER_INBOUND.getCode());
        request.setOrderOperateType(OrderOperateTypeEnum.FINISH_IN_OF_STOCK.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("TRANSFER");
        request.setExeOrderNo(System.currentTimeMillis()+"");
        request.setSeqNo(System.currentTimeMillis()+"");
        request.setWarehouseId(17L);
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        return request;
    }

}
