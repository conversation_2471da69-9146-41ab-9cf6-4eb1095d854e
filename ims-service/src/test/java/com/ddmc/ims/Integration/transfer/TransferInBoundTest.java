package com.ddmc.ims.Integration.transfer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ddmc.ims.Integration.BaseDataManager;
import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.enums.ims.InventoryStatusEnum;
import com.ddmc.ims.common.enums.ims.OrderOperateTypeEnum;
import com.ddmc.ims.common.enums.ims.OrderTypeEnum;
import com.ddmc.ims.controller.api.InboundInventoryCredentialController;
import com.ddmc.ims.controller.api.InventoryCredentialController;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.mapper.ims.WarehouseSkuInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import com.ddmc.ims.dal.model.ims.WarehouseSkuInventory;
import com.ddmc.ims.request.credential.oc.CredentialDetailRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ddmc.ims.Integration.BaseDataManager.*;

/**
 * 调拨入库测试
 */
public class TransferInBoundTest extends SpringBootTestBase {

    @Resource
    private BaseDataManager baseDataManager;

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private WarehouseSkuInventoryMapper warehouseSkuInventoryMapper;

    @Resource
    private InboundInventoryCredentialController inboundInventoryCredentialController;

    @Resource
    private InventoryCredentialController inventoryCredentialController;


    public void refreshTransferInTransitInventory(String orderNo, BigDecimal planQty,BigDecimal inTransitQty,BigDecimal allocQty, BigDecimal waitAllocQty,
        String usageCode, String toUsageCode) {
        baseDataManager
            .insertBaseTransferInTransitData(orderNo, 102243L, planQty,inTransitQty,allocQty, waitAllocQty , usageCode, toUsageCode);
    }


    public void refreshWarehouseSkuInventory(LogicInventoryLocation location, BigDecimal freeQty, BigDecimal allocQty,
        String usageCode) {
        LambdaUpdateWrapper<WarehouseSkuInventory> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(WarehouseSkuInventory::getFreeQty, freeQty).set(WarehouseSkuInventory::getAllocQty, allocQty)
            .eq(WarehouseSkuInventory::getSkuId, 102243L).eq(WarehouseSkuInventory::getUsageCode, usageCode)
            .eq(WarehouseSkuInventory::getWarehouseId, location.getWarehouseId())
            .eq(WarehouseSkuInventory::getCargoOwnerId, location.getCargoOwnerId())
            .eq(WarehouseSkuInventory::getLogicInventoryLocationCode, location.getLogicInventoryLocationCode());
        warehouseSkuInventoryMapper.update(null, updateWrapper);
    }



    public Map<String, TransferIntransitInventory> getTransferInTransitInventory(String orderNo) {
        LambdaQueryWrapper<TransferIntransitInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TransferIntransitInventory::getOrderNo, orderNo)
            .eq(TransferIntransitInventory::getSkuId, 102243L);
        List<TransferIntransitInventory> inventories = transferIntransitInventoryMapper.selectList(queryWrapper);
        return inventories.stream()
            .collect(Collectors.toMap(
                t -> t.getUsageCode() + "," + t.getFromWarehouseId() + "," + t.getFromCargoOwnerId() + "," + t
                    .getFromLogicInventoryLocationCode() + "," + t.getToUsageCode() + "," + +t.getToWarehouseId() + ","
                    + t.getToCargoOwnerId() + "," + t.getToLogicInventoryLocationCode(), Function.identity()));

    }

    public Map<String, WarehouseSkuInventory> getWarehouseSkuInventory(LogicInventoryLocation location) {
        LambdaQueryWrapper<WarehouseSkuInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(WarehouseSkuInventory::getSkuId, 102243L)
            .eq(WarehouseSkuInventory::getWarehouseId, location.getWarehouseId())
            .eq(WarehouseSkuInventory::getCargoOwnerId, location.getCargoOwnerId())
            .eq(WarehouseSkuInventory::getLogicInventoryLocationCode, location.getLogicInventoryLocationCode());
        List<WarehouseSkuInventory> inventories = warehouseSkuInventoryMapper.selectList(queryWrapper);
        return inventories.stream().collect(Collectors.toMap(WarehouseSkuInventory::getUsageCode, Function.identity()));
    }


    @Before
    public void deleteTransferInTransitInventory() {
        LambdaQueryWrapper<TransferIntransitInventory> deleteTransferInTransitInventory = new LambdaQueryWrapper<>();
        deleteTransferInTransitInventory
            .in(TransferIntransitInventory::getOrderNo, Arrays.asList("17-1-20230830-0", "17-1-20230830-1"));
        transferIntransitInventoryMapper.delete(deleteTransferInTransitInventory);


    }


    /**
     * 大仓到大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     *
     * 凭证EC->EC入10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10              10	        0	            20
     * TB	        TB	            20	        10	            20	        0	            0
     */
    @Test
    public void testInBound1() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC->EC");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("20.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     *
     * 凭证EC,TB->EC入10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            20
     * TB	        TB	            20	        10	            20	        0	            0
     */
    @Test
    public void testInBound2() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(10),"EC,TB->EC");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("20.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     *
     * 凭证EC->CU入5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        5	            10	        0	            10
     * TB	        TB	            20	        15	            20	        0	            0
     */
    @Test
    public void testInBound3() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(TO_RECEIVE_LOCATION, new BigDecimal(0), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"EC->CU");
        request.getOperateDetails().forEach(d -> d.setToLocation(TO_RECEIVE_LOCATION));
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("15.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());


        Map<String, WarehouseSkuInventory> receiveWarehouseSkuInventoryMap = getWarehouseSkuInventory(TO_RECEIVE_LOCATION);
        WarehouseSkuInventory cuInventory = receiveWarehouseSkuInventoryMap.get("CU");
        Assert.assertEquals(new BigDecimal("5.000"), cuInventory.getFreeQty());
    }

    /**
     * 大仓到大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            11	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     *
     * 凭证EC->EC入30个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            11	        0	            30
     * TB	        TB	            20	        0	            20	        0	            0
     */
    @Test
    public void testInBound36() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(11), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(30),"EC->EC");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("40.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 大仓到大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            11	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     *
     * 凭证EC->EC入31个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            11	        0	            41
     * TB	        TB	            20	        0	            20	        0	            0
     */
    @Test
    public void testInBound13() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(11), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(31),"EC->EC");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("41.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 大仓到大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            11	        0	            10
     *
     * 凭证EC->EC入11个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            11	        0	            21
     */
    @Test
    public void testInBound28() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(11), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(11),"EC->EC");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("21.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 大仓到大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        TB	            10	        10	            11	        0	            10
     *
     * 凭证EC->EC入11个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        TB	            10	        0	            11	        0	            10
     */
    @Test
    public void testInBound29() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(11), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(11),"EC->EC");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertNull(ecInTransit);


        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("21.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }


    /**
     * 大仓-大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     *
     * 凭证EC,TB->EC,TB入31个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            10	        0	            21
     * TB	        TB	            20	        0	            20	        0	            20
     */
    @Test
    public void testInBound14() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(31),"EC,TB->EC,TB");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("21.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("20.000"), tbInventory.getFreeQty());
    }

    /**
     * 大仓-大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     *
     * 凭证EC,TB->EC,TB入7个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            20	        13	            20	        0	            7
     */
    @Test
    public void testInBound24() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(7),"EC,TB->EC,TB");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("13.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("7.000"), tbInventory.getFreeQty());
    }

    /**
     * 大仓-大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     *
     * 凭证EC,TB->EC,TB入30个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            10	        0	            20
     * TB	        TB	            20	        0	            20	        0	            20
     */
    @Test
    public void testInBound30() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(30),"EC,TB->EC,TB");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("20.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("20.000"), tbInventory.getFreeQty());
    }


    /**
     * 大仓-大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        TB	            10	        10	            10	        0	            10
     *
     * 凭证EC,TB->EC,TB入31个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        TB	            10	        0	            10	        0	            41
     */
    @Test
    public void testInBound31() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(31),"EC,TB->EC,TB");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertNull(ecInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("31.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("10.000"), tbInventory.getFreeQty());
    }

    /**
     * 大仓-大仓
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     *
     * 凭证EC,TB->EC,TB入31个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            10	        0	            41
     */
    @Test
    public void testInBound27() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(31),"EC,TB->EC,TB");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("41.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     * DR	        DR	            20	        20	            20	        0               5
     *
     * 凭证DR->DR入10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            20	        20	            20	        0	            0
     * DR	        DR	            20	        10	            20	        0               15
     */
    @Test
    public void testInBound5() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "DR","DR");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(TO_BAD_PRODUCT_RTV_LOCATION, new BigDecimal(5), new BigDecimal(0), "DR");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(10), "DR->DR"));
        details.forEach(d -> {
            d.setFromLocation(FROM_BAD_PRODUCT_RTV_LOCATION);
            d.setToLocation(TO_BAD_PRODUCT_RTV_LOCATION);
        });

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbDrInTransit = transferInTransitInventory.get("DR,17,1,BAD_PRODUCT_RTV,DR,1,2,BAD_PRODUCT_RTV");
        Assert.assertEquals(new BigDecimal("20.000"), tbDrInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbDrInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbDrInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbDrInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());


        Map<String, WarehouseSkuInventory> badWarehouseSkuInventoryMap = getWarehouseSkuInventory(TO_BAD_PRODUCT_RTV_LOCATION);
        WarehouseSkuInventory drInventory = badWarehouseSkuInventoryMap.get("DR");
        Assert.assertEquals(new BigDecimal("15.000"), drInventory.getFreeQty());

    }





    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        EC	            20	        20	            20	        0
     *
     * EC,TB->EC入10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            20
     * TB	        EC	            20	        10	            20	        0
     */
    @Test
    public void testInBound7() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(10), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());


        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertNull(ecTbInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("20.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }


    /**
     *
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        EC	            20	        20	            20	        0
     *
     * EC,TB->EC入100个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            10	        0	            110
     * TB	        EC	            20	        0	            20	        0
     */
    @Test
    public void testInBound8() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(100), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());


        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertNull(ecTbInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("110.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     *
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        EC	            20	        20	            20	        0
     *
     * EC,TB->EC入30个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            10	        0	            110
     * TB	        EC	            20	        0	            20	        0
     */
    @Test
    public void testInBound32() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(30), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());


        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertNull(ecTbInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("40.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }


    /**
     *
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        EC	            *	        *	            *	        *
     *
     * EC,TB->EC入10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            10	        0	            20
     * TB	        EC	            *	        *               *	        *
     */
    @Test
    public void testInBound15() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(10), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());


        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertNull(ecTbInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbEcInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("20.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     *
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            10	        10	            10	        0	            10
     * TB	        EC	            *	        *	            *	        *
     *
     * EC,TB->EC入10个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            10	        0	            10	        0	            20
     */
    @Test
    public void testInBound33() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "TB","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(10), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());


        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("20.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10           0               10
     * TB	        EC	            20	        20	            20	         0
     *
     * EC->EC入13个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10          0                  23
     * TB	        EC	            20	        7	            20	        0                  0
     */
    @Test
    public void testInBound18() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(13), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertNull(ecTbInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("7.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("23.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10           0               10
     * TB	        EC	            20	        20	            20	         0
     *
     * EC->EC入23个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        7	            10          0                  33
     * TB	        EC	            20	        0	            20	        0                  0
     */
    @Test
    public void testInBound19() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(23), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("7.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertNull(ecTbInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("33.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10           0               10
     * TB	        EC	            20	        20	            20	         0
     *
     * EC->EC入33个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            10          0                  43
     * TB	        EC	            20	        0	            20	        0                  0
     */
    @Test
    public void testInBound20() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(33), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertNull(ecTbInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory tbEcInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbEcInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbEcInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("43.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            20	        20	            20	         0
     *
     * EC->EC入23个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            20	        0	            20	        0                  0
     */
    @Test
    public void testInBound37() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(23), "EC->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("EC,GOOD_PRODUCT,TB,GOOD_PRODUCT");
        Assert.assertNull(ecTbInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("33.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入13个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        9	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound38() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(13), "EC->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("9.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("23.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入27个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        6	            11	         0
     * TB	        TB	            22	        0	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound39() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(27), "EC->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("6.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("37.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入42个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        0	            11	         0
     * TB	        TB	            22	        0	            22	         0
     * EC	        EC	            10	        1	            20	         0
     */
    @Test
    public void testInBound40() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(42), "EC->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("1.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("52.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入43个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound41() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(43), "EC->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("53.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入44个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound42() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(44), "EC->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("54.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入23个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound59() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(13), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("9.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("23.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入23个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound58() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(27), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("6.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("37.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入42个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound45() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(42), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("1.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("52.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入23个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound57() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(43), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("53.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入23个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound56() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(44), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("54.000"), tbInventory.getFreeQty());
    }


    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入23个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound60() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(13), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("9.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("23.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入23个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound55() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(27), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("6.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("37.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入42个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        0	            11	         0
     * TB	        TB	            22	        0	            22	         0
     * EC	        EC	            10	        1	            20	         0
     */
    @Test
    public void testInBound53() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(42), "EC,TB->EC,TB"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("1.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("22.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("30.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入43个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound52() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(43), "EC,TB->EC,TB"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("22.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("31.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	       入库仓入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     *
     * EC->EC入23个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * TB	        EC	            11	        11	            11	         0
     * TB	        TB	            22	        22	            22	         0
     * EC	        EC	            10	        10	            20	         0
     */
    @Test
    public void testInBound51() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(11), new BigDecimal(11), new BigDecimal(11), new BigDecimal(0), "TB","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(22), new BigDecimal(22), new BigDecimal(22), new BigDecimal(0), "TB","TB");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId, orderNo);

        List<CredentialDetailRequest> details = new ArrayList<>(
            getCredentialDetailRequests(new BigDecimal(44), "EC,TB->EC"));

        request.setOperateDetails(details);
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("22.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory ecTbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("11.000"), ecTbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecTbInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("54.000"), tbInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            10
     * TB	        TB	            20	        20	            20	        0	            0
     *
     * 凭证EC->CU入5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            15
     * TB	        TB	            20	        15	            20	        0	            0
     */
    @Test
    public void testInBound10() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(20), new BigDecimal(20), new BigDecimal(20), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(TO_RECEIVE_LOCATION, new BigDecimal(0), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"EC->CU");
        request.getOperateDetails().forEach(d -> {
            d.setToLocation(TO_RECEIVE_LOCATION);
        });
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertNull(ecInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("15.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("20.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

        Map<String, WarehouseSkuInventory> receiveWarehouseSkuInventoryMap = getWarehouseSkuInventory(TO_RECEIVE_LOCATION);
        WarehouseSkuInventory cuInventory = receiveWarehouseSkuInventoryMap.get("CU");
        Assert.assertEquals(new BigDecimal("5.000"), cuInventory.getFreeQty());

    }



    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            10
     * TB	        TB	            ·	        ·	            ·	        ·	            0
     * EC	        CU	            ·	        ·	            ·	        ·	            0
     *
     * 凭证EC->CU入5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            ·	        ·	            ·	        ·	            10
     * TB	        TB	            ·	        ·	            ·	        ·	            0
     * EC	        CU	            ·	        ·	            ·	        ·	            5
     */
    @Test
    public void testInBound11() {
        String orderNo = "17-1-20230830-0";
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"EC->CU");
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertNull(ecInTransit);

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);

        TransferIntransitInventory ecCuInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,CU,1,2,RECEIVE");
        Assert.assertNull(ecCuInTransit);

        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

        WarehouseSkuInventory cuInventory = warehouseSkuInventoryMap.get("CU");
        Assert.assertEquals(new BigDecimal("5.000"), cuInventory.getFreeQty());
    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            ·           ·	            ·	        ·	            0
     *
     * 凭证EC->CU入5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        5	            10	        0	            10
     * TB	        TB	            ·	        ·	            ·	        ·	            0
     */
    @Test
    public void testInBound12() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(TO_RECEIVE_LOCATION, new BigDecimal(0), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"EC->CU");
        request.getOperateDetails().forEach(t -> t.setToLocation(TO_RECEIVE_LOCATION));
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertNull(tbInTransit);


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

        Map<String, WarehouseSkuInventory> receiveWarehouseSkuInventoryMap = getWarehouseSkuInventory(TO_RECEIVE_LOCATION);
        WarehouseSkuInventory cuInventory = receiveWarehouseSkuInventoryMap.get("CU");
        Assert.assertEquals(new BigDecimal("5.000"), cuInventory.getFreeQty());

    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            10          10	            10	        0	            0
     *
     * 凭证EC->CU入5个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            10	        5               10	        0	            0
     */
    @Test
    public void testInBound34() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(TO_RECEIVE_LOCATION, new BigDecimal(0), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(5),"EC->CU");
        request.getOperateDetails().forEach(t -> t.setToLocation(TO_RECEIVE_LOCATION));
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("5.000"), tbInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), tbInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), tbInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

        Map<String, WarehouseSkuInventory> receiveWarehouseSkuInventoryMap = getWarehouseSkuInventory(TO_RECEIVE_LOCATION);
        WarehouseSkuInventory cuInventory = receiveWarehouseSkuInventoryMap.get("CU");
        Assert.assertEquals(new BigDecimal("5.000"), cuInventory.getFreeQty());

    }

    /**
     * 来源用途	    目标用途	        计划量	    在途量	        已出量	    待出量	        入库仓在库库存
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        10	            10	        0	            10
     * TB	        TB	            10          10	            10	        0	            0
     *
     * 凭证EC->CU入21个
     * usage_code	to_usage_code	plan_qty	intransit_qty	alloc_qty	wait_alloc_qty	free_qty
     * EC	        EC	            10	        0	            10	        0	            10
     * TB	        TB	            10	        0              10	        0	            0
     */
    @Test
    public void testInBound35() {
        String orderNo = "17-1-20230830-0";
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "EC","EC");
        refreshTransferInTransitInventory(orderNo, new BigDecimal(10), new BigDecimal(10), new BigDecimal(10), new BigDecimal(0), "TB","TB");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(10), new BigDecimal(0), "EC");
        refreshWarehouseSkuInventory(TO_LOCATION, new BigDecimal(0), new BigDecimal(0), "TB");
        refreshWarehouseSkuInventory(TO_RECEIVE_LOCATION, new BigDecimal(0), new BigDecimal(0), "CU");
        String idempotentId = "Transfer" + System.currentTimeMillis();
        InboundCredentialRequest request = getDefaultInboundCredentialRequest(idempotentId, orderNo,
            new BigDecimal(21),"EC->CU");
        request.getOperateDetails().forEach(t -> t.setToLocation(TO_RECEIVE_LOCATION));
        inboundInventoryCredentialController.tryInbound(request);
        CredentialTryConfirmRequest confirmRequest = new CredentialTryConfirmRequest();
        confirmRequest.setIdempotentId(idempotentId);
        inventoryCredentialController.confirm(confirmRequest);

        Map<String, TransferIntransitInventory> transferInTransitInventory = getTransferInTransitInventory(orderNo);
        TransferIntransitInventory ecInTransit = transferInTransitInventory.get("EC,17,1,GOOD_PRODUCT,EC,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());

        TransferIntransitInventory tbInTransit = transferInTransitInventory.get("TB,17,1,GOOD_PRODUCT,TB,1,2,GOOD_PRODUCT");
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getPlanQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getIntransitQty());
        Assert.assertEquals(new BigDecimal("10.000"), ecInTransit.getAllocQty());
        Assert.assertEquals(new BigDecimal("0.000"), ecInTransit.getWaitAllocQty());


        Map<String, WarehouseSkuInventory> warehouseSkuInventoryMap = getWarehouseSkuInventory(TO_LOCATION);
        WarehouseSkuInventory ecInventory = warehouseSkuInventoryMap.get("EC");
        Assert.assertEquals(new BigDecimal("10.000"), ecInventory.getFreeQty());

        WarehouseSkuInventory tbInventory = warehouseSkuInventoryMap.get("TB");
        Assert.assertEquals(new BigDecimal("0.000"), tbInventory.getFreeQty());

        Map<String, WarehouseSkuInventory> receiveWarehouseSkuInventoryMap = getWarehouseSkuInventory(TO_RECEIVE_LOCATION);
        WarehouseSkuInventory cuInventory = receiveWarehouseSkuInventoryMap.get("CU");
        Assert.assertEquals(new BigDecimal("21.000"), cuInventory.getFreeQty());

    }

    private InboundCredentialRequest getDefaultInboundCredentialRequest(String idempotentId, String orderNo,
        BigDecimal qty, String usageCode) {
        InboundCredentialRequest request = getInboundCredentialRequest(idempotentId,
            orderNo);

        List<CredentialDetailRequest> details = getCredentialDetailRequests(qty, usageCode);

        request.setOperateDetails(details);
        return request;
    }

    private List<CredentialDetailRequest> getCredentialDetailRequests(BigDecimal qty, String usageCode) {
        List<CredentialDetailRequest> details = new ArrayList<>();
        CredentialDetailRequest c1 = new CredentialDetailRequest();
        c1.setFromLocation(FROM_LOCATION);
        c1.setFromInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setToLocation(TO_LOCATION);
        c1.setToInventoryStatus(InventoryStatusEnum.AVAILABLE.getCode());
        c1.setSkuId(102243L);
        c1.setLotId("2308300000017711597");
        c1.setQty(qty);
        c1.setDemandDate(new Date());
        c1.setOrderTag("");
        String[] split = usageCode.split("->");
        c1.setUsages(List.of(split[0].split(",")));
        c1.setToUsages(List.of(split[1].split(",")));
        c1.setCommandType("");
        c1.setTodaySale(false);
        details.add(c1);
        return details;
    }


    private InboundCredentialRequest getInboundCredentialRequest(String idempotentId, String orderNo) {
        InboundCredentialRequest request = new InboundCredentialRequest();
        request.setIdempotentId(idempotentId);
        request.setOrderSource("TRANSFER");
        request.setOrderNo(orderNo);
        request.setOrderType(OrderTypeEnum.TRANSFER_INBOUND.getCode());
        request.setOrderOperateType(OrderOperateTypeEnum.IN_STOCK.getCode());
        request.setBusinessTime(new Date());
        request.setExeOrderSource("TRANSFER");
        request.setExeOrderNo(System.currentTimeMillis()+"");
        request.setSeqNo(System.currentTimeMillis()+"");
        request.setWarehouseId(17L);
        request.setDayEndTime(new Date());
        request.setDeliveryDate(new Date());
        return request;
    }

}
