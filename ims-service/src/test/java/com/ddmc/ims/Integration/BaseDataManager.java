package com.ddmc.ims.Integration;

import com.ddmc.ims.SpringBootTestBase;
import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.util.ThreadLocalDateUtils;
import com.ddmc.ims.dal.mapper.ims.IntransitInventoryAllocDetailMapper;
import com.ddmc.ims.dal.mapper.ims.TransferIntransitInventoryMapper;
import com.ddmc.ims.dal.model.ims.TransferIntransitInventory;
import org.junit.Ignore;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

@Ignore
@Component
public class BaseDataManager extends SpringBootTestBase {

    public static final LogicInventoryLocation FROM_LOCATION = new LogicInventoryLocation(17L, 1L, "GOOD_PRODUCT");
    public static final LogicInventoryLocation TO_LOCATION = new LogicInventoryLocation(1L, 2L, "GOOD_PRODUCT");

    public static final LogicInventoryLocation TO_RECEIVE_LOCATION = new LogicInventoryLocation(1L, 2L, "RECEIVE");
    public static final LogicInventoryLocation EMPTY_LOCATION = new LogicInventoryLocation(-999L,-999L,"DEFAULT");

    public static final LogicInventoryLocation FROM_BAD_PRODUCT_RTV_LOCATION = new LogicInventoryLocation(17L, 1L, "BAD_PRODUCT_RTV");;
    public static final LogicInventoryLocation FROM_BAD_PRODUCT_RTV_LOCATION_2 = new LogicInventoryLocation(17L, 4L, "BAD_PRODUCT_RTV");;
    public static final LogicInventoryLocation TO_BAD_PRODUCT_RTV_LOCATION = new LogicInventoryLocation(1L, 2L, "BAD_PRODUCT_RTV");;



    public static AtomicLong IDEMPOTENT_ID = new AtomicLong(1);

    @Resource
    private TransferIntransitInventoryMapper transferIntransitInventoryMapper;

    @Resource
    private IntransitInventoryAllocDetailMapper intransitInventoryAllocDetailMapper;

    public static Map<String,String> usageCodeCodeMap = new HashMap<>();

    @PostConstruct
    public static void init() {
        usageCodeCodeMap.put("TB,EC","GOOD_PRODUCT");
        usageCodeCodeMap.put("EC,TB","GOOD_PRODUCT");
        usageCodeCodeMap.put("EC","GOOD_PRODUCT");
        usageCodeCodeMap.put("TB","GOOD_PRODUCT");
        usageCodeCodeMap.put("ECP","PROCESSING");
        usageCodeCodeMap.put("TBP","PROCESSING");
        usageCodeCodeMap.put("DCP","PROCESSING");
        usageCodeCodeMap.put("CU","RECEIVE");
        usageCodeCodeMap.put("DR","BAD_PRODUCT_RTV");
        usageCodeCodeMap.put("DC","BAD_PRODUCT_NORMAL");
        usageCodeCodeMap.put("DL","BAD_PRODUCT_LOSS");
    }


    public TransferIntransitInventory insertBaseTransferInTransitData(String orderNo, Long skuId, BigDecimal planQty,
        BigDecimal inTransitQty, BigDecimal allocQty, BigDecimal waitAllocQty, String usageCode, String toUsageCode) {
        TransferIntransitInventory inventory = new TransferIntransitInventory();
        String[] split = orderNo.split("-");
        inventory.setFromCargoOwnerId(FROM_LOCATION.getCargoOwnerId());
        inventory.setFromWarehouseId(Long.parseLong(split[0]));
        inventory.setFromLogicInventoryLocationCode(usageCodeCodeMap.get(usageCode));
        inventory.setToCargoOwnerId(TO_LOCATION.getCargoOwnerId());
        inventory.setToLogicInventoryLocationCode(usageCodeCodeMap.get(toUsageCode));
        inventory.setToWarehouseId(Long.parseLong(split[1]));
        inventory.setSkuId(skuId);
        inventory.setOrderSource("TRANSFER");
        inventory.setOrderNo(orderNo);
        inventory.setDeliveryMode(Integer.parseInt(split[3]));
        inventory.setExpectOutTime(ThreadLocalDateUtils.parse(split[2], ThreadLocalDateUtils.DATE_YMD_2));
        inventory.setExpectInTime(ThreadLocalDateUtils.parse(split[2], ThreadLocalDateUtils.DATE_YMD_2));
        inventory.setPlanQty(planQty);
        inventory.setIntransitQty(inTransitQty);
        inventory.setAllocQty(allocQty);
        inventory.setWaitAllocQty(waitAllocQty);
        inventory.setUsageCode(usageCode);
        inventory.setToUsageCode(toUsageCode);
        transferIntransitInventoryMapper.insert(inventory);
        return inventory;
    }


}
