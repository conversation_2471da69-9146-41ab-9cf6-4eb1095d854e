package com.ddmc.ims.config;

import com.ctrip.framework.apollo.enums.PropertyChangeType;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
@Slf4j
public class RateLimiterConfigTest {

    private RateLimiterConfig rateLimiterConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        rateLimiterConfigUnderTest = new RateLimiterConfig();
    }

    @Test
    public void testMqSwitchRuleOnChangeListener() {
        // Setup
        final ConfigChangeEvent changeEvent = new ConfigChangeEvent("namespace", Map.ofEntries(
            Map.entry("value",
                new ConfigChange("namespace", "propertyName", "oldValue", "newValue", PropertyChangeType.ADDED))));

        // Run the test
        rateLimiterConfigUnderTest.mqSwitchRuleOnChangeListener(changeEvent);
        Assert.assertTrue(true);
        // Verify the results
    }

    @Test
    public void testAfterPropertiesSet() {
        // Setup
        // Run the test
        rateLimiterConfigUnderTest.afterPropertiesSet();

        Assert.assertTrue(true);

        // Verify the results
    }


    @Test
    public void testGetRateLimiterByKey() {
        rateLimiterConfigUnderTest.getRateLimiterByKey("key");
        Assert.assertTrue(true);
        // Verify the results
    }
}
