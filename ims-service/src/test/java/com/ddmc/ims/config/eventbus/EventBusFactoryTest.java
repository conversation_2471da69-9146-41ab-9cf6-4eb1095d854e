package com.ddmc.ims.config.eventbus;

import com.ddmc.ims.common.enums.common.EventExecuteServiceNameEnum;
import com.google.common.eventbus.EventBus;
import org.junit.Assert;
import org.junit.Test;

public class EventBusFactoryTest {

    @Test
    public void testGetSyncEventBus() {
        // Setup
        // Run the test
        final EventBus result = EventBusFactory.getSyncEventBus();
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testGetAsyncEventBus() {
        // Setup
        // Run the test
        final EventBus result = EventBusFactory.getAsyncEventBus();
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testGetSkuSourceAsyncEventBus() {
        // Setup
        // Run the test
        final EventBus result = EventBusFactory.getSkuSourceAsyncEventBus();
        Assert.assertNotNull(result);
        // Verify the results
    }

    @Test
    public void testGetAsyncEventBusByEventExecuteServiceName() {
        // Setup
        // Run the test
        final EventBus result = EventBusFactory
            .getAsyncEventBusByEventExecuteServiceName(EventExecuteServiceNameEnum.SNAPSHOT_INVENTORY_DIFF);
        Assert.assertNotNull(result);
        // Verify the results
    }
}
