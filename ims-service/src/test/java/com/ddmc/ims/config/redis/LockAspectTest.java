package com.ddmc.ims.config.redis;

import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

@RunWith(MockitoJUnitRunner.class)
public class LockAspectTest {


    @InjectMocks
    private LockAspect lockAspectUnderTest;


    @Test
    public void testProcessAround_ThrowsThrowable() {
        // Setup
        final ProceedingJoinPoint pjp = null;
        // Run the test
        assertThatThrownBy(() -> lockAspectUnderTest.processAround(pjp)).isInstanceOf(Throwable.class);
    }
}
