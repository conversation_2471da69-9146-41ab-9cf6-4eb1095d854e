package com.ddmc.ims.config.interceptor;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Before;
import org.junit.Test;

public class RequestCostInterceptorTest {

    private RequestCostInterceptor requestCostInterceptorUnderTest;

    @Before
    public void setUp() throws Exception {
        requestCostInterceptorUnderTest = new RequestCostInterceptor();
    }


    @Test
    public void testAround_ThrowsThrowable() {
        // Setup
        final ProceedingJoinPoint pjp = null;

        // Run the test
        assertThatThrownBy(() -> requestCostInterceptorUnderTest.around(pjp)).isInstanceOf(Throwable.class);
    }
}
