package com.ddmc.ims.config.feign;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class HystrixCredentialsContextTest {

    private HystrixCredentialsContext hystrixCredentialsContextUnderTest;

    @Before
    public void setUp() throws Exception {
        hystrixCredentialsContextUnderTest = new HystrixCredentialsContext();
    }



    @Test
    public void testOnSuccess() {
        // Setup
        // Run the test
        hystrixCredentialsContextUnderTest.onSuccess(null);
        Assert.assertTrue(true);
        // Verify the results
    }
}
