package com.ddmc.ims.config.eventbus;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ddmc.ims.common.enums.common.EventEntityStatusEnum;
import com.ddmc.ims.common.enums.common.EventExecuteServiceNameEnum;
import com.ddmc.ims.dal.condition.ListEventCondition;
import com.ddmc.ims.dal.mapper.ims.EventMapper;
import com.ddmc.ims.dal.model.ims.EventEntity;
import com.ddmc.ims.dal.model.ims.EventId;
import com.ddmc.ims.service.common.AlertService;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.concurrent.ExecutorService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DbEventStorageTest {

    @Mock
    private EventMapper mockEventMapper;
    @Mock
    private AlertService mockAlertService;
    @Mock
    private ExecutorService mockExecutorService;

    @InjectMocks
    private DbEventStorage dbEventStorageUnderTest;

    @Test
    public void testGet1() {
        // Setup
        final ListEventCondition condition = new ListEventCondition();
        condition.setEventId(0L);
        condition.setEventIdList(List.of(0L));
        condition.setParentEventId(0L);
        condition.setStatus(0);
        condition.setStatusList(List.of(0));
        condition.setCreateTimeStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        condition.setCreateTimeEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        condition.setTriggerCount(0);
        condition.setTriggerCountStart(0);
        condition.setTriggerCountEnd(0);
        condition.setLimit(0);
        condition.setKeyType("keyType");
        condition.setKeyTypeList(List.of("value"));
        condition.setKeyId("keyId");

        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(0L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("keyType");
        eventEntity.setKeyId("keyId");
        eventEntity.setType("type");
        eventEntity.setEvent("event");
        eventEntity.setStatus(0);
        eventEntity.setTriggerCount(0);
        eventEntity.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<EventEntity> expectedResult = List.of(eventEntity);

        // Configure EventMapper.listByCondition(...).
        final EventEntity eventEntity1 = new EventEntity();
        eventEntity1.setId(0L);
        eventEntity1.setParentId(0L);
        eventEntity1.setKeyType("keyType");
        eventEntity1.setKeyId("keyId");
        eventEntity1.setType("type");
        eventEntity1.setEvent("event");
        eventEntity1.setStatus(0);
        eventEntity1.setTriggerCount(0);
        eventEntity1.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<EventEntity> eventEntities = List.of(eventEntity1);
        when(mockEventMapper.listByCondition(condition)).thenReturn(eventEntities);

        // Run the test
        final List<EventEntity> result = dbEventStorageUnderTest.get(condition);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGet1_EventMapperReturnsNoItems() {
        // Setup
        final ListEventCondition condition = new ListEventCondition();
        condition.setEventId(0L);
        condition.setEventIdList(List.of(0L));
        condition.setParentEventId(0L);
        condition.setStatus(0);
        condition.setStatusList(List.of(0));
        condition.setCreateTimeStart(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        condition.setCreateTimeEnd(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        condition.setTriggerCount(0);
        condition.setTriggerCountStart(0);
        condition.setTriggerCountEnd(0);
        condition.setLimit(0);
        condition.setKeyType("keyType");
        condition.setKeyTypeList(List.of("value"));
        condition.setKeyId("keyId");

        when(mockEventMapper.listByCondition(condition)).thenReturn(Collections.emptyList());

        // Run the test
        final List<EventEntity> result = dbEventStorageUnderTest.get(condition);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGet2() {

        // Configure EventMapper.listByCondition(...).
        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(23L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("SEND_ASYNC_CONFIRM");
        eventEntity.setKeyId("keyId");
        eventEntity.setType("com.ddmc.ims.event.entity.inventory.SendAsyncConfirmEvent");
        eventEntity.setEvent(
            "{\"id\":null,\"parentId\":0,\"keyType\":\"SEND_ASYNC_CONFIRM\",\"keyId\":\"68178\",\"earliestExeDate\":\"2023-01-01T00:00:00Z\",\"skuSourceThreadPool\":false,\"eventExecuteServiceName\":null,\"idempotentId\":null,\"warehouseId\":null}");
        eventEntity.setStatus(0);
        eventEntity.setTriggerCount(0);
        eventEntity.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<EventEntity> eventEntities = List.of(eventEntity);
        ListEventCondition condition = new ListEventCondition();
        condition.setEventId(23L);
        condition.setStatus(EventEntityStatusEnum.DEFAULT.getCode());
        when(mockEventMapper.listByCondition(condition)).thenReturn(eventEntities);

        // Run the test
        final EventId result = dbEventStorageUnderTest.get(23L);

        // Verify the results
        Assert.assertEquals(eventEntity.getId(), result.getId());
    }

    @Test
    public void testGet2_EventMapperReturnsNoItems() {
        // Setup

        EventEntity eventEntity = getEventEntity();
        ListEventCondition condition = new ListEventCondition();
        condition.setEventId(eventEntity.getId());
        condition.setStatus(EventEntityStatusEnum.DEFAULT.getCode());
        when(mockEventMapper.listByCondition(condition)).thenReturn(Collections.singletonList(eventEntity));

        // Run the test
        final EventId result = dbEventStorageUnderTest.get(eventEntity.getId());

        // Verify the results
        assertThat(result.getId()).isEqualTo(eventEntity.getId());
    }

    @Test
    public void testGet3() {

        // Configure EventMapper.listByCondition(...).
        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(12L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("com.ddmc.ims.event.entity.inventory.SendAsyncConfirmEvent");
        eventEntity.setKeyId("keyId");
        eventEntity.setType("com.ddmc.ims.event.entity.inventory.SendAsyncConfirmEvent");
        eventEntity.setEvent(
            "{\"id\":null,\"parentId\":0,\"keyType\":\"SEND_ASYNC_CONFIRM\",\"keyId\":\"68178\",\"earliestExeDate\":\"2023-01-01T00:00:00Z\",\"skuSourceThreadPool\":false,\"eventExecuteServiceName\":null,\"idempotentId\":null,\"warehouseId\":null}");
        eventEntity.setStatus(0);
        eventEntity.setTriggerCount(0);
        eventEntity.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<EventEntity> eventEntities = List.of(eventEntity);

        when(mockEventMapper.listByCondition(Mockito.any())).thenReturn(eventEntities);

        // Run the test
        final List<EventId> result = dbEventStorageUnderTest.get(0, 0, 0);

        // Verify the results
        assertThat(result.get(0).getId()).isEqualTo(12);
    }

    @Test
    public void testGet3_EventMapperReturnsNoItems() {
        // Setup
        // Run the test
        final List<EventId> result = dbEventStorageUnderTest.get(0, 0, 0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListUnhandled() {
        // Setup
        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(0L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("keyType");
        eventEntity.setKeyId("keyId");
        eventEntity.setType("type");
        eventEntity.setEvent("event");
        eventEntity.setStatus(0);
        eventEntity.setTriggerCount(0);
        eventEntity.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<EventEntity> expectedResult = List.of(eventEntity);

        // Configure EventMapper.listUnHandled(...).
        final EventEntity eventEntity1 = new EventEntity();
        eventEntity1.setId(0L);
        eventEntity1.setParentId(0L);
        eventEntity1.setKeyType("keyType");
        eventEntity1.setKeyId("keyId");
        eventEntity1.setType("type");
        eventEntity1.setEvent("event");
        eventEntity1.setStatus(0);
        eventEntity1.setTriggerCount(0);
        eventEntity1.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<EventEntity> eventEntities = List.of(eventEntity1);
        when(mockEventMapper.listUnHandled(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(eventEntities);

        // Run the test
        final List<EventEntity> result = dbEventStorageUnderTest
            .listUnhandled(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListUnhandled_EventMapperReturnsNoItems() {
        // Setup
        when(mockEventMapper.listUnHandled(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()))
            .thenReturn(Collections.emptyList());

        // Run the test
        final List<EventEntity> result = dbEventStorageUnderTest
            .listUnhandled(0, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }


    @Test
    public void testComplete() {
        // Setup
        final EventId event = new EventId();
        event.setId(0L);
        event.setParentId(0L);
        event.setKeyType("keyType");
        event.setKeyId("keyId");
        event.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        event.setSkuSourceThreadPool(false);
        event.setEventExecuteServiceName(EventExecuteServiceNameEnum.SNAPSHOT_INVENTORY_DIFF);

        when(mockEventMapper.update(0L,
            EventEntityStatusEnum.CONSUMED.getCode(),
            EventEntityStatusEnum.PROCESSING.getCode())).thenReturn(1);

        // Run the test
        dbEventStorageUnderTest.complete(event);
        Mockito.verify(mockAlertService, times(0)).alertWarning(Mockito.any(), Mockito.any());

    }

    @Test
    public void testCompleteAndAsyncTriggerSubEvent() {
        // Setup
        final EventId event = new EventId();
        event.setId(0L);
        event.setParentId(0L);
        event.setKeyType("keyType");
        event.setKeyId("keyId");
        event.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        event.setSkuSourceThreadPool(false);
        event.setEventExecuteServiceName(EventExecuteServiceNameEnum.SNAPSHOT_INVENTORY_DIFF);

        when(mockEventMapper.update(0L,
            EventEntityStatusEnum.CONSUMED.getCode(),
            EventEntityStatusEnum.PROCESSING.getCode())).thenReturn(1);

        // Configure EventMapper.listByCondition(...).
        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(0L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("keyType");
        eventEntity.setKeyId("keyId");
        eventEntity.setType("type");
        eventEntity.setEvent("event");
        eventEntity.setStatus(0);
        eventEntity.setTriggerCount(0);
        eventEntity.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        // Run the test
        dbEventStorageUnderTest.completeAndAsyncTriggerSubEvent(event);
        Assert.assertTrue(true);
    }

    @Test
    public void testCompleteAndAsyncTriggerSubEvent_EventMapperListByConditionReturnsNoItems() {
        // Setup
        final EventId event = new EventId();
        event.setId(0L);
        event.setParentId(0L);
        event.setKeyType("keyType");
        event.setKeyId("keyId");
        event.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        event.setSkuSourceThreadPool(false);
        event.setEventExecuteServiceName(EventExecuteServiceNameEnum.SNAPSHOT_INVENTORY_DIFF);

        try {
            dbEventStorageUnderTest.completeAndAsyncTriggerSubEvent(event);
        } catch (Exception e) {
            // Verify the results
            verify(mockAlertService).alertWarning("完成eventBus异常", "完成eventBus异常, 请及时处理, eventId = " + event.getId());
            Assert.assertTrue(true);
            return;
        }
        Assert.fail();


    }

    @Test
    public void testPlusTriggerCount() {
        // Setup
        final EventId eventId = new EventId();
        eventId.setId(0L);
        eventId.setParentId(0L);
        eventId.setKeyType("keyType");
        eventId.setKeyId("keyId");
        eventId.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventId.setSkuSourceThreadPool(false);
        eventId.setEventExecuteServiceName(EventExecuteServiceNameEnum.SNAPSHOT_INVENTORY_DIFF);

        when(mockEventMapper.plusTriggerCount(0L)).thenReturn(0);

        // Run the test
        dbEventStorageUnderTest.plusTriggerCount(eventId);

        // Verify the results
        verify(mockEventMapper).plusTriggerCount(0L);
    }

    @Test
    public void testPlusTriggerCountAndStatusProcessing() {
        // Setup
        final EventId eventId = new EventId();
        eventId.setId(0L);
        eventId.setParentId(0L);
        eventId.setKeyType("keyType");
        eventId.setKeyId("keyId");
        eventId.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventId.setSkuSourceThreadPool(false);
        eventId.setEventExecuteServiceName(EventExecuteServiceNameEnum.SNAPSHOT_INVENTORY_DIFF);

        when(mockEventMapper.plusTriggerCountAndStatusProcessing(0L)).thenReturn(0);

        // Run the test
        final boolean result = dbEventStorageUnderTest.plusTriggerCountAndStatusProcessing(eventId);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testBatchPlusTriggerCountAndStatusProcessing() {
        // Setup
        when(mockEventMapper.batchPlusTriggerCountAndStatusProcessing(List.of(0L))).thenReturn(0);

        // Run the test
        dbEventStorageUnderTest.batchPlusTriggerCountAndStatusProcessing(List.of(0L));

        // Verify the results
        verify(mockEventMapper).batchPlusTriggerCountAndStatusProcessing(List.of(0L));
    }

    @Test
    public void testGetClazz() {
        assertThat(dbEventStorageUnderTest.getClazz("com.ddmc.ims.config.eventbus.DbEventStorage"))
            .isEqualTo(DbEventStorage.class);
    }

    @Test
    public void testExecuteEvent() {
        // Setup
        EventEntity eventEntity = getEventEntity();
        eventEntity.setEarliestExeDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        eventEntity.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<EventEntity> eventEntityList = List.of(eventEntity);
        when(mockEventMapper.plusTriggerCountAndStatusProcessing(eventEntity.getId())).thenReturn(1);

        // Run the test
        dbEventStorageUnderTest.executeEvent(eventEntityList);

        Assert.assertTrue(true);
        // Verify the results
    }

    @Test
    public void testSelectNowMilliSecond() {
        // Setup
        // Configure EventMapper.selectNowMilliSecond(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockEventMapper.selectNowMilliSecond()).thenReturn(date);

        // Run the test
        final Date result = dbEventStorageUnderTest.selectNowMilliSecond();

        // Verify the results
        assertThat(result).isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }


    private EventEntity getEventEntity() {
        final EventEntity eventEntity = new EventEntity();
        eventEntity.setId(23L);
        eventEntity.setParentId(0L);
        eventEntity.setKeyType("SEND_ASYNC_CONFIRM");
        eventEntity.setKeyId("keyId");
        eventEntity.setType("com.ddmc.ims.event.entity.inventory.SendAsyncConfirmEvent");
        eventEntity.setEvent(
            "{\"id\":666,\"parentId\":0,\"keyType\":\"SEND_ASYNC_CONFIRM\",\"keyId\":\"68178\",\"earliestExeDate\":\"2023-01-01T00:00:00Z\",\"skuSourceThreadPool\":false,\"eventExecuteServiceName\":null,\"idempotentId\":null,\"warehouseId\":null}");
        eventEntity.setStatus(0);
        eventEntity.setTriggerCount(0);
        return eventEntity;
    }

}
