package com.ddmc.ims.config.auditlog;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.ddmc.ims.service.common.AlertService;
import java.util.concurrent.ExecutorService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AuditLogAspectTest {

    @Mock
    private AlertService mockAlertService;
    @Mock
    private AuditLogKafkaProducer mockAuditLogKafkaProducer;
    @Mock
    private ExecutorService mockAuditLogKafkaExecutor;

    @InjectMocks
    private AuditLogAspect auditLogAspectUnderTest;


    @Test
    public void testProcessAround_ThrowsThrowable() {
        // Setup
        final ProceedingJoinPoint pjp = null;
        // Run the test
        assertThatThrownBy(() -> auditLogAspectUnderTest.processAround(pjp)).isInstanceOf(Throwable.class);
        Assert.assertTrue(true);
    }
}
