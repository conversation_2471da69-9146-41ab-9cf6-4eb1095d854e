package com.ddmc.ims.config.eventbus;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class MonitoredThreadPoolExecutorTest {

    @Mock
    private BlockingQueue<Runnable> mockWorkQueue;
    @Mock
    private ThreadFactory mockThreadFactory;
    @Mock
    private RejectedExecutionHandler mockHandler;

    private MonitoredThreadPoolExecutor monitoredThreadPoolExecutorUnderTest;

    @Before
    public void setUp() throws Exception {
        monitoredThreadPoolExecutorUnderTest = new MonitoredThreadPoolExecutor(1, 10, 0L, TimeUnit.MILLISECONDS,
            mockWorkQueue, mockThreadFactory, mockHandler);
    }


    @Test
    public void testBeforeExecute() {
        // Setup
        final Thread t = new Thread("name");
        final Runnable r = ()-> log.info("test");

        // Run the test
        monitoredThreadPoolExecutorUnderTest.beforeExecute(t, r);
        Assert.assertTrue(true);
        // Verify the results
    }
}
