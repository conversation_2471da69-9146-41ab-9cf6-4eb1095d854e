package com.ddmc.ims.config.executor;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class ExecutorMetricsConfigTest {

    private ExecutorMetricsConfig executorMetricsConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        executorMetricsConfigUnderTest = new ExecutorMetricsConfig();
    }

    @Test
    public void testStartExecutorMetrics() {
        // Setup
        // Run the test
        executorMetricsConfigUnderTest.startExecutorMetrics();
        Assert.assertTrue(true);
        // Verify the results
    }


    @Test
    public void testAround_ThrowsThrowable() {
        // Setup
        final ProceedingJoinPoint joinPoint = null;

        // Run the test
        assertThatThrownBy(() -> executorMetricsConfigUnderTest.around(joinPoint)).isInstanceOf(Throwable.class);
    }
}
