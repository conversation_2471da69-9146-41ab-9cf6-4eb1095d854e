package com.ddmc.ims.config.feign;

import static org.mockito.Mockito.when;

import feign.Logger.Level;
import feign.Request;
import feign.Request.HttpMethod;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

@RunWith(MockitoJUnitRunner.class)
public class BriefFeignLoggerTest {

    @Mock
    private Logger mockLogger;

    private BriefFeignLogger briefFeignLoggerUnderTest;

    @Before
    public void setUp() throws Exception {
        briefFeignLoggerUnderTest = new BriefFeignLogger(mockLogger);
    }

    @Test
    public void testLogRequest() {
        // Setup
        final Request request = Request
            .create(HttpMethod.GET, "url", Map.ofEntries(Map.entry("value", List.of("value"))), "content".getBytes(),
                StandardCharsets.UTF_8);

        // Run the test
        briefFeignLoggerUnderTest.logRequest("configKey", Level.NONE, request);
        Assert.assertTrue(true);
        // Verify the results
    }



    @Test
    public void testLogIOException() {
        // Setup
        final IOException ioe = new IOException("message", new Exception("message"));
        when(mockLogger.isInfoEnabled()).thenReturn(false);

        briefFeignLoggerUnderTest.logIOException("configKey", Level.NONE, ioe, 0L);

        Assert.assertTrue(true);
    }

    @Test
    public void testLog() {
        // Setup
        when(mockLogger.isInfoEnabled()).thenReturn(false);

        // Run the test
        briefFeignLoggerUnderTest.log("configKey", "format", "args");
        Assert.assertTrue(true);
    }
}
