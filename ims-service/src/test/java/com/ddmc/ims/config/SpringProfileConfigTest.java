package com.ddmc.ims.config;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.Before;
import org.junit.Test;

public class SpringProfileConfigTest {

    private SpringProfileConfig springProfileConfigUnderTest;

    @Before
    public void setUp() throws Exception {
        springProfileConfigUnderTest = new SpringProfileConfig();
    }


    @Test
    public void testIsDev() {
        assertThat(springProfileConfigUnderTest.isDev()).isFalse();
    }

    @Test
    public void testIsPe() {
        assertThat(springProfileConfigUnderTest.isPe()).isFalse();
    }

    @Test
    public void testGetProfile() {
        assertThat(springProfileConfigUnderTest.getProfile()).isNull();
    }

    @Test
    public void testIsTest() {
        assertThat(springProfileConfigUnderTest.isTest()).isFalse();
    }
}
