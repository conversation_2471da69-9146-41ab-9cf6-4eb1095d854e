package com.ddmc.ims.config.param;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.ddmc.ims.common.bo.BizOrderOperatorCommandConfig;
import com.ddmc.ims.common.bo.DefaultLocationUsageCodeConfig;
import com.ddmc.ims.common.util.JsonUtil;
import com.ddmc.ims.config.sharding.ShardingProperties;
import com.ddmc.scm.common.config.client.service.LocalParamService;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class LocalParamConfigTest {

    @Mock
    private LocalParamService mockLocalParamService;
    @Mock
    private ShardingProperties mockShardingProperties;

    @InjectMocks
    private LocalParamConfig localParamConfigUnderTest;




    @Test
    public void testIsOnlyClearSnapshotInfo() {
        // Setup
        when(mockLocalParamService.getBooleanValue("ims.snapshotPerHourOnlyClear", false)).thenReturn(false);

        // Run the test
        final boolean result = localParamConfigUnderTest.isOnlyClearSnapshotInfo();

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetSnapshotPerHourSleepInterval() {
        // Setup
        when(mockLocalParamService.getLongValue("ims.snapshotPerHourSleepInterval", 100L)).thenReturn(0L);

        // Run the test
        final Long result = localParamConfigUnderTest.getSnapshotPerHourSleepInterval();

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    public void testIsInterruptSnapshotPerHour() {
        // Setup
        when(mockLocalParamService.getBooleanValue("ims.isInterruptSnapshotPerHour", false)).thenReturn(false);

        // Run the test
        final boolean result = localParamConfigUnderTest.isInterruptSnapshotPerHour();

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetSnapShotPerHourDate() throws Exception {
        // Setup
        // Configure LocalParamService.getDateTimeValue(...).
        final Date date = new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime();
        when(mockLocalParamService.getDateTimeValue("ims.snapshotPerHourDateTime",
            null)).thenReturn(date);

        // Run the test
        final Date result = localParamConfigUnderTest.getSnapShotPerHourDate();

        // Verify the results
        assertThat(result).isEqualTo(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
    }

    @Test
    public void testGetSnapShotPerHourDate_LocalParamServiceThrowsParseException() throws Exception {
        // Setup
        when(mockLocalParamService.getDateTimeValue("ims.snapshotPerHourDateTime",
             null))
            .thenThrow(ParseException.class);

        // Run the test
        final Date result = localParamConfigUnderTest.getSnapShotPerHourDate();

        // Verify the results
        assertThat(result).isNull();
    }



    @Test
    public void testGetBizOrderOperatorCommandConfig() {
        // Setup
        final BizOrderOperatorCommandConfig expectedResult = new BizOrderOperatorCommandConfig();
        expectedResult.setBizOrder("PO001");
        expectedResult.setOrderOperateType(5);
        expectedResult.setCommands(List.of("PUBLISH_PURCHASE_IN_TRANSIT"));
        String json = "[{\"bizOrder\":\"PO001\",\"orderOperateType\":5,\"commands\":[\"PUBLISH_PURCHASE_IN_TRANSIT\"]},{\"bizOrder\":\"PO001\",\"orderOperateType\":6,\"commands\":[\"CLEAN_PURCHASE_IN_TRANSIT\"]},{\"bizOrder\":\"PO001\",\"orderOperateType\":7,\"commands\":[\"MODIFY_PURCHASE_ARRIVAL_TIME\"]},{\"bizOrder\":\"PO001\",\"orderOperateType\":8,\"commands\":[\"PURCHASE_IN_TRANSIT_TO_AVAILABLE\"]},{\"bizOrder\":\"PO001\",\"orderOperateType\":9,\"commands\":[\"PURCHASE_IN_TRANSIT_TO_AVAILABLE\"]},{\"bizOrder\":\"PO001\",\"orderOperateType\":14,\"commands\":[\"CLEAN_PURCHASE_IN_TRANSIT\"]},{\"bizOrder\":\"DB003\",\"orderOperateType\":8,\"commands\":[\"TRANSFER_IN_TRANSIT_TO_AVAILABLE\"]},{\"bizOrder\":\"DB003\",\"orderOperateType\":9,\"commands\":[\"CLEAN_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB001\",\"orderOperateType\":4,\"commands\":[\"CLEAN_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB002\",\"orderOperateType\":3,\"commands\":[\"AVAILABLE_TO_TRANSFER_IN_TRANSIT\",\"CLEAN_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"MM001\",\"orderOperateType\":4,\"commands\":[\"RELEASE_ALLOC\",\"TRANSFER_INVENTORY\"]},{\"bizOrder\":\"MM001\",\"orderOperateType\":3,\"commands\":[\"TRANSFER_INVENTORY\"]},{\"bizOrder\":\"MM008\",\"orderOperateType\":4,\"commands\":[\"TRANSFER_INVENTORY\"]},{\"bizOrder\":\"MM007\",\"orderOperateType\":4,\"commands\":[\"TRANSFER_INVENTORY\"]},{\"bizOrder\":\"MM007\",\"orderOperateType\":8,\"commands\":[\"TRANSFER_INVENTORY\"]},{\"bizOrder\":\"MM002\",\"orderOperateType\":4,\"commands\":[\"MODIFY_INVENTORY_AVAILABLE\"]},{\"bizOrder\":\"DB001\",\"orderOperateType\":0,\"commands\":[\"PUBLISH_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB002\",\"orderOperateType\":0,\"commands\":[\"PUBLISH_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB002\",\"orderOperateType\":2,\"commands\":[\"CLEAN_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB001\",\"orderOperateType\":2,\"commands\":[\"CLEAN_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB001\",\"orderOperateType\":3,\"commands\":[\"AVAILABLE_TO_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB002\",\"orderOperateType\":13,\"commands\":[\"TRANSFER_REJECT\"]},{\"bizOrder\":\"MM005\",\"orderOperateType\":10,\"commands\":[\"TRANSFER_INVENTORY\"]},{\"bizOrder\":\"MM010\",\"orderOperateType\":3,\"commands\":[\"TRANSFER_INVENTORY\"]},{\"bizOrder\":\"MM010\",\"orderOperateType\":4,\"commands\":[\"TRANSFER_INVENTORY\"]},{\"bizOrder\":\"MM011\",\"orderOperateType\":8,\"commands\":[\"TRANSFER_INVENTORY\"]},{\"bizOrder\":\"MM011\",\"orderOperateType\":9,\"commands\":[\"TRANSFER_INVENTORY\"]}]";
        when(mockLocalParamService.getStringValue("ims.bizOrderOperatorCommandNew", "")).thenReturn(json);

        // Run the test
        final BizOrderOperatorCommandConfig result = localParamConfigUnderTest
            .getBizOrderOperatorCommandConfig("PO001", 5);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetBizOrderOperatorCommandConfigByTransferCompatible() {
        // Setup
        final BizOrderOperatorCommandConfig expectedResult = new BizOrderOperatorCommandConfig();
        expectedResult.setBizOrder("DB003");
        expectedResult.setOrderOperateType(8);
        expectedResult.setCommands(List.of("TRANSFER_IN_TRANSIT_TO_AVAILABLE"));
        String json = "[{\"bizOrder\":\"DB003\",\"orderOperateType\":8,\"commands\":[\"TRANSFER_IN_TRANSIT_TO_AVAILABLE\"]},{\"bizOrder\":\"DB003\",\"orderOperateType\":9,\"commands\":[\"PUBLISH_ALLOC\"]},{\"bizOrder\":\"DB001\",\"orderOperateType\":4,\"commands\":[\"AVAILABLE_TO_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB001\",\"orderOperateType\":3,\"commands\":[\"AVAILABLE_TO_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB002\",\"orderOperateType\":3,\"commands\":[\"AVAILABLE_TO_TRANSFER_IN_TRANSIT\"]},{\"bizOrder\":\"DB002\",\"orderOperateType\":4,\"commands\":[\"AVAILABLE_TO_TRANSFER_IN_TRANSIT\"]}]";
        when(mockLocalParamService.getStringValue("ims.bizOrderOperatorCommandCompatible", "")).thenReturn(json);

        // Run the test
        final BizOrderOperatorCommandConfig result = localParamConfigUnderTest
            .getBizOrderOperatorCommandConfigByTransferCompatible("DB003", 8);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetDefaultLocationUsageCodeMap() {
        // Setup
        final Map<String, String> expectedResult = Map.ofEntries(Map.entry("locationCode", "usageCode"));
        DefaultLocationUsageCodeConfig codeConfig = new DefaultLocationUsageCodeConfig();
        codeConfig.setLocationCode("locationCode");
        codeConfig.setUsageCode("usageCode");
        when(mockLocalParamService.getStringValue("ims.defaultLocationUsageCodeConfig", "")).thenReturn(JsonUtil.toJson(Collections.singleton(codeConfig)));

        // Run the test
        final Map<String, String> result = localParamConfigUnderTest.getDefaultLocationUsageCodeMap();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testExcludeWarehouseForSnapshot() {
        // Setup
        when(mockLocalParamService.getLongSetValue("ims.snapshotExcludeWarehouseId", Collections.emptySet()))
            .thenReturn(Set.of(0L));

        // Run the test
        final boolean result = localParamConfigUnderTest.excludeWarehouseForSnapshot(0L);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testExcludeWarehouseForSnapshot_LocalParamServiceReturnsNoItems() {
        // Setup
        when(mockLocalParamService.getLongSetValue("ims.snapshotExcludeWarehouseId",  Collections.emptySet()))
            .thenReturn(Collections.emptySet());

        // Run the test
        final boolean result = localParamConfigUnderTest.excludeWarehouseForSnapshot(0L);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testEnableInsertUsageSnapshot() {
        // Setup
        when(mockLocalParamService.getBooleanValue("ims.enableInsertUsageSnapshot", false)).thenReturn(false);

        // Run the test
        final boolean result = localParamConfigUnderTest.enableInsertUsageSnapshot();

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testFilterProcessingLocationSortCodes() {
        // Setup
        when(mockShardingProperties.getLogicInventoryLocationCodeRouteMap()).thenReturn(Map.ofEntries(
            Map.entry("value", 0)));

        // Run the test
        final List<String> result = localParamConfigUnderTest.filterProcessingLocationSortCodes();

        // Verify the results
        assertThat(result).isEqualTo(List.of("value"));
    }
}
