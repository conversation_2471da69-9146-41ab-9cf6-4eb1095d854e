package com.ddmc.ims.config.interceptor;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

import com.ddmc.ims.config.SpringProfileConfig;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

@RunWith(MockitoJUnitRunner.class)
public class UserContextInterceptorTest {

    @Mock
    private SpringProfileConfig mockProfileConfig;

    @InjectMocks
    private UserContextInterceptor userContextInterceptorUnderTest;



    @Test
    public void testPreHandle_ThrowsException() {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();
        final HttpServletResponse response = new MockHttpServletResponse();
        when(mockProfileConfig.isDev()).thenReturn(false);
        when(mockProfileConfig.isTest()).thenReturn(false);

        // Run the test
        assertThatThrownBy(() -> userContextInterceptorUnderTest.preHandle(request, response, "handler"))
            .isInstanceOf(Exception.class);
    }

    @Test
    public void testAfterCompletion() {
        // Setup
        final HttpServletRequest request = new MockHttpServletRequest();
        final HttpServletResponse response = new MockHttpServletResponse();

        // Run the test
        userContextInterceptorUnderTest.afterCompletion(request, response, "handler", new Exception("message"));
        Assert.assertTrue(true);
        // Verify the results
    }






}
