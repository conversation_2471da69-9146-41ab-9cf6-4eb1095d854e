
set jar_file=%1%

if not exist "%jar_file%"  (
    @echo "配置的jar文件路径不正确"
    @pause
    exit
)

set ACTIVE_PROFILE=default
set APOLLO_ENV=DEV
set APOLLO_CLUSTER=DEV

set XMS_MEM=1024
set XMN_MEM=1024
set MAX_META_MEM=256
set APP_LOG=d:\\data\\dump\\archetype
set APP_MONITOR_PORT=5050
set JAVA_OPTS= -Denv=%APOLLO_ENV%  -Dapollo.cluster=%APOLLO_CLUSTER%  -server   -Xms%XMS_MEM%M    -Xmx%XMS_MEM%M   -Xmn%XMN_MEM%M    -Xss1M -XX:MetaspaceSize=%MAX_META_MEM%M    -XX:MaxMetaspaceSize=%MAX_META_MEM%M   -XX:+DisableExplicitGC     -XX:+UseConcMarkSweepGC     -XX:+CMSParallelInitialMarkEnabled   -XX:+CMSParallelRemarkEnabled    -XX:+UseFastAccessorMethods   -XX:+UseCMSInitiatingOccupancyOnly   -XX:CMSInitiatingOccupancyFraction=70   -XX:+HeapDumpOnOutOfMemoryError   -XX:HeapDumpPath=%APP_LOG%    -Duser.timezone=GMT+8   -Djava.security.egd=multipartFile:/dev/./urandom     -Dfile.encoding=UTF-8    -Dcom.sun.management.jmxremote    -Dcom.sun.management.jmxremote.port=%APP_MONITOR_PORT%   -Dcom.sun.management.jmxremote.rmi.port=%APP_MONITOR_PORT%   -Dcom.sun.management.jmxremote.ssl=false    -Dcom.sun.management.jmxremote.authenticate=false

java  %JAVA_OPTS%  -jar  -javaagent:D:\\setup\\apache-skywalking-apm-bin\\agent\\skywalking-agent.jar  -DSW_AGENT_NAME=archetype-service  -DSW_AGENT_COLLECTOR_BACKEND_SERVICES=localhost:11800  %jar_file%  --spring.profiles.active=%ACTIVE_PROFILE%

