version = '1.0.1-SNAPSHOT'
dependencies {
    api project(':ims-client')
    api project(':ims-common')
    api project(':ims-dal')
    api project(':ims-lock-strategy')

    api('org.springframework.boot:spring-boot-starter-actuator')
    api('org.springframework.boot:spring-boot-starter-web')

    api('org.springframework.cloud:spring-cloud-starter-openfeign')
    api('io.github.openfeign.form:feign-form')
    api('io.github.openfeign.form:feign-form-spring')
    api('io.github.openfeign:feign-gson')

    api('org.springframework.cloud:spring-cloud-starter-zookeeper-discovery') {
        exclude group: 'org.apache.zookeeper', module: 'zookeeper'
    }
    api('org.apache.zookeeper:zookeeper') {
        exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    }

    api('org.slf4j:log4j-over-slf4j')

    api("com.ctrip.framework.apollo:apollo-core")
    api("com.ctrip.framework.apollo:apollo-client")

//    // 腾讯云
//    api('com.qcloud:cos_api:5.5.5') {
//        exclude group: 'org.slf4j', module: 'slf4j-log4j12'
//    }

    api('redis.clients:jedis:2.10.2')

//    api('org.redisson:redisson:3.12.0')

    // xxl-job
    api 'com.xuxueli:xxl-job-core'

    // swagger2
    api 'io.springfox:springfox-swagger2'
    api 'io.springfox:springfox-swagger-ui'

    api('org.springframework.kafka:spring-kafka')
    api('com.github.danielwegener:logback-kafka-appender')

    api group: 'io.github.openfeign', name: 'feign-httpclient'

    //日志
    api('com.ddmc:ddmc-log')
    api('com.ddmc:core:1.1.1-SNAPSHOT'){
        exclude group: 'com.alibaba', module: 'druid'
    }

    //config-metadata
    api "org.springframework.boot:spring-boot-configuration-processor"

    //服务治理
    api('com.ddmc.soa:spring-cloud-ddmc')

    //rate limit
    api('com.ddmc:util-ratelimit-starter:1.0.0-SNAPSHOT')

    // rocket mq
    implementation group: 'com.alibaba.cloud', name: 'spring-cloud-starter-stream-rocketmq', version: '2.2.1.RELEASE'

    testImplementation('org.awaitility:awaitility:3.0.0')
    testImplementation('org.awaitility:awaitility-proxy:3.0.0')
//    testImplementation('com.h2database:h2')

    annotationProcessor("org.projectlombok:lombok")
    compileOnly("org.projectlombok:lombok")
    testAnnotationProcessor('org.projectlombok:lombok')
    testCompileOnly("org.projectlombok:lombok")

//    // Mybatis 查询结果行监控插件
//    api('com.ddmc:util-pms-monitor-starter:1.0.0-SNAPSHOT')
    //新版Redis 监控SDK
    implementation('csoss.redis:spring-data-redis-client'){
        exclude group: 'com.csoss', module: 'monitor-agent'
        exclude group: 'com.ddmc', module: 'gzs-client'
    }

    // dal jdbc pool
    api 'com.ddmc:ddmc-jdbc-pool'

    api 'com.ddmc:gzs-client'


    // 新sso client
    api('com.ddmc:duc-client:1.1.5-RELEASE')

    // 引入pms-common
    api('com.ddmc:pms-client:1.0.20-RELEASE')

    //pes服务client
    api('com.ddmc:pes-client:2.0.1-RELEASE')

    //lot服务client
    api('com.ddmc:lot-client:2.0.5-SNAPSHOT')


    api('com.ddmc:scm-com-client:1.0.2-RELEASE')

    //单据中心
    api('com.ddmc:ocs-core-client:1.0.3-RELEASE')
    //死信服务
    api('com.ddmc:util-mq-helper-starter:1.0.4-RELEASE')


    // fms entity
    api('com.ddmc:fms-inv-entity:1.0.4-SNAPSHOT')
    api('com.ddmc:fms-inv-client:1.0.4-SNAPSHOT')

    api('com.alibaba:fastjson')
    api('org.apache.logging.log4j:log4j-core')
    api('org.apache.logging.log4j:log4j-api')
    //库存策略配置服务
    api('com.ddmc:ims-config-client:1.0.0-SNAPSHOT')
    //商品中心接口
    api('com.ddmc.greenhouse:carbon-admin-third-api-client:1.4.13-RELEASE'){
        exclude group: 'com.github.pagehelper', module: 'pagehelper'
        exclude group: 'org.example.common'
    }

//    //vms服务client
//    api('com.ddmc:vms-client:1.2.15-SNAPSHOT')

    //vms服务client
    api('com.ddmc:ims-sale-client:1.0.3-SNAPSHOT')


    api('com.ddmc:sku-api-client:1.0.22-RELEASE')

    api("com.ddmc:util-multi-cloud-rpc-starter:1.0.1-SNAPSHOT")

    api("com.ddmc:util-multi-cloud-mq-starter:1.0.2-SNAPSHOT")

    //单测增强包
    testImplementation group: 'org.powermock', name: 'powermock-module-junit4', version: '2.0.9'
    testImplementation group: 'org.powermock', name: 'powermock-api-mockito2', version: '2.0.9'
    testImplementation 'org.mockito:mockito-core:3.12.4'
}

compileJava {
    options.compilerArgs = [
            '-Amapstruct.suppressGeneratorTimestamp=true',
            '-Amapstruct.suppressGeneratorVersionInfoComment=true',
            '-Amapstruct.unmappedTargetPolicy=IGNORE'
    ]
}
apply from: "../gradle/include/sonar.gradle"

test {
    ignoreFailures true
}

buildscript {
    repositories {
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath "gradle.plugin.ua.eshepelyuk:ManifestClasspath:1.0.0"
    }
}

apply plugin: "ua.eshepelyuk.ManifestClasspath"