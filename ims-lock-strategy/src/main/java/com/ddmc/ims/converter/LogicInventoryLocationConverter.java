package com.ddmc.ims.converter;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithUsage;
import com.ddmc.ims.common.bo.LogicInventoryLocationWithUsageAndTodaySale;
import com.ddmc.ims.dal.model.ims.IntransitInventoryAllocDetail;

/**
 * <AUTHOR>
 */
public class LogicInventoryLocationConverter {

    private LogicInventoryLocationConverter() {
    }


    public static LogicInventoryLocationWithUsageAndTodaySale getLocationWithUsageAndTodaySaleByAllocDetails(IntransitInventoryAllocDetail allocDetail) {
        LogicInventoryLocation logicInventoryLocation = new LogicInventoryLocation(
            allocDetail.getWarehouseId(), allocDetail.getCargoOwnerId(),
            allocDetail.getLogicInventoryLocationCode());
        return new LogicInventoryLocationWithUsageAndTodaySale (new LogicInventoryLocationWithUsage(logicInventoryLocation, allocDetail.getSkuId(),
            allocDetail.getUsageCode()),allocDetail.isTodaySale());
    }

}
