package com.ddmc.ims.converter;

import com.ddmc.ims.dal.model.ims.CredentialDetail;
import com.ddmc.ims.dal.model.ims.CredentialUseageDetail;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class CredentialUsageDetailConverter {

    private CredentialUsageDetailConverter() {
    }

    public static CredentialUseageDetail getCredentialUsageDetail(CredentialDetail credentialDetail, BigDecimal qty,
        String usageCode, String toUsageCode) {
        CredentialUseageDetail usageDetail = getDefaultCredentialUsageDetail(credentialDetail);
        usageDetail.setUsageCode(usageCode);
        usageDetail.setQty(qty);
        usageDetail.setToUsageCode(toUsageCode);
        return usageDetail;
    }


    public static CredentialUseageDetail getCredentialUsageDetail(CredentialDetail credentialDetail, String usage,
        BigDecimal qty) {
        return getCredentialUsageDetail(credentialDetail, qty, usage, credentialDetail.getToUsageList().get(0));
    }


    public static CredentialUseageDetail getSingleCredentialUsageDetail(CredentialDetail credentialDetail) {
        CredentialUseageDetail usageDetail = getDefaultCredentialUsageDetail(credentialDetail);
        usageDetail.setUsageCode(credentialDetail.getUsageList().get(0));
        usageDetail.setToUsageCode(credentialDetail.getToUsageList().get(0));
        usageDetail.setQty(credentialDetail.getQty());
        return usageDetail;
    }

    private static CredentialUseageDetail getDefaultCredentialUsageDetail(CredentialDetail credentialDetail) {
        CredentialUseageDetail usageDetail = new CredentialUseageDetail();
        usageDetail.setCredentialHeaderId(credentialDetail.getCredentialHeaderId());
        usageDetail.setDemand(credentialDetail.getDemand());
        usageDetail.setFromCargoOwnerId(credentialDetail.getFromCargoOwnerId());
        usageDetail.setFromWarehouseId(credentialDetail.getFromWarehouseId());
        usageDetail.setFromLogicLocationCode(credentialDetail.getFromLogicLocationCode());
        usageDetail.setLotId(credentialDetail.getLotId());
        usageDetail.setSkuId(credentialDetail.getSkuId());
        usageDetail.setToCargoOwnerId(credentialDetail.getToCargoOwnerId());
        usageDetail.setToLogicLocationCode(credentialDetail.getToLogicLocationCode());
        usageDetail.setToInventoryStatus(credentialDetail.getToInventoryStatus());
        usageDetail.setToWarehouseId(credentialDetail.getToWarehouseId());
        usageDetail.setInventoryStatus(credentialDetail.getInventoryStatus());
        usageDetail.setOrderTag(credentialDetail.getOrderTag());
        usageDetail.setToUsageCode(credentialDetail.getToUsageCode());
        usageDetail.setCommandType(credentialDetail.getCommandType());
        usageDetail.setTodaySale(credentialDetail.getTodaySale());
        return usageDetail;
    }


    public static CredentialUseageDetail copyCredentialUseageDetail(CredentialUseageDetail useageDetail) {
        CredentialUseageDetail result = new CredentialUseageDetail();
        result.setCredentialHeaderId(useageDetail.getCredentialHeaderId());
        result.setDemand(useageDetail.getDemand());
        result.setFromCargoOwnerId(useageDetail.getFromCargoOwnerId());
        result.setFromWarehouseId(useageDetail.getFromWarehouseId());
        result.setFromLogicLocationCode(useageDetail.getFromLogicLocationCode());
        result.setLotId(useageDetail.getLotId());
        result.setSkuId(useageDetail.getSkuId());
        result.setToCargoOwnerId(useageDetail.getToCargoOwnerId());
        result.setToLogicLocationCode(useageDetail.getToLogicLocationCode());
        result.setToInventoryStatus(useageDetail.getToInventoryStatus());
        result.setToWarehouseId(useageDetail.getToWarehouseId());
        result.setInventoryStatus(useageDetail.getInventoryStatus());
        result.setOrderTag(useageDetail.getOrderTag());
        result.setCommandType(useageDetail.getCommandType());
        result.setTodaySale(useageDetail.getTodaySale());
        result.setQty(useageDetail.getQty());
        result.setUsageCode(useageDetail.getUsageCode());
        result.setToUsageCode(useageDetail.getToUsageCode());
        result.setToDemand(useageDetail.getToDemand());
        return result;
    }

}
