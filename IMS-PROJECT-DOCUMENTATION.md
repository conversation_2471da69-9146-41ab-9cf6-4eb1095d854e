# IMS (Inventory Management System) Project Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Core Business Functions](#core-business-functions)
4. [Technical Stack](#technical-stack)
5. [Module Structure](#module-structure)
6. [Business Logic Flow](#business-logic-flow)
7. [API Interface Design](#api-interface-design)
8. [Database Design](#database-design)
9. [Development Guide](#development-guide)
10. [Deployment Guide](#deployment-guide)

---

## Project Overview

### Project Background
The IMS (Inventory Management System) is an internal management system designed for inventory and warehouse operations, supporting logistics and supply chain operations. It provides comprehensive APIs and services for handling inventory credentials, snapshots, and integrations with external systems like FMS (Facility Management System).

### Core Value Proposition
- **Centralized Inventory Management**: Unified platform for all inventory operations
- **Real-time Inventory Tracking**: Live tracking of inventory movements and states
- **Multi-dimensional Snapshots**: Support for business, financial, and operational reporting
- **System Integration**: Seamless integration with external warehouse and ERP systems
- **Audit Trail**: Complete audit trail for inventory movements and adjustments

### Key Business Problems Solved
- Managing inventory credentials for inbound, outbound, and adjustment operations
- Tracking inventory snapshots for reporting and reconciliation
- Supporting integration with external systems through fallback mechanisms
- Ensuring data consistency across distributed warehouse operations

---

## System Architecture

### Overall Architecture Pattern
The system follows a **microservices architecture** pattern with modular components designed for scalability and maintainability.

```mermaid
graph TB
    subgraph "External Systems"
        A[OMS Order System]
        B[WMS Warehouse System]
        C[FMS Facility System]
        D[ERP System]
    end
    
    subgraph "IMS Core System"
        E[ims-client<br/>API Client Layer]
        F[ims-service<br/>Business Logic Layer]
        G[ims-common<br/>Shared Components]
        H[ims-dal<br/>Data Access Layer]
        I[ims-lock-strategy<br/>Locking Strategy]
    end
    
    subgraph "Data Storage"
        J[(MySQL Database)]
        K[(Redis Cache)]
        L[(Message Queue)]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    H --> J
    F --> K
    F --> L
```

### Design Patterns Implementation
- **Command Pattern**: Inventory operation command processing
- **Factory Pattern**: Command handler factory for different operation types
- **Strategy Pattern**: Different inventory locking and processing strategies
- **Circuit Breaker Pattern**: Fallback mechanisms for external service calls
- **Observer Pattern**: Event-driven snapshot and notification processing

---

## Core Business Functions

### 1. Inventory Credential Management

#### 1.1 Inbound Operations
- **Purchase Inbound**: `PURCHASE("PO001", "采购入库")`
- **Transfer Inbound**: `TRANSFER_INBOUND("DB003", "调拨入库")`
- **Product Inbound**: `PRODUCT_INBOUND("MM002", "成品入库")`
- **Material Return**: `MATERIAL_RETURN("MM007", "原料退回入库")`

#### 1.2 Outbound Operations
- **Sales Outbound**: `DO("SO001", "C端销售")`
- **Business Sale**: `BUSINESS_SALE("SO003", "B端销售")`
- **Transfer Outbound**: `TRANSFER_OUTBOUND("DB001", "调拨出库发货单")`
- **Material Usage**: `MANUFACTURE_SELF_USE("MM001", "生产领用")`

#### 1.3 Adjustment Operations
- **Inventory Count**: `COUNT("PD001", "盘点")`
- **Profit Loss**: `PROFIT_LOSS_OUTBOUND("SY001", "损益出库")`
- **Lot Adjustment**: `LOT_ADJUST("PCTZ001", "批次调整")`

#### 1.4 Manufacturing Operations
- **Positive Manufacturing**: `POSITIVE_MANUFACTURE("MM005", "正加工制造")`
- **Reverse Manufacturing**: `REVERSE_MANUFACTURE("MM006", "反加工制造")`
- **Basic Manufacturing**: `BASIC_POSITIVE_MANUFACTURE("MM003", "基础正向制造")`

### 2. Inventory Snapshot Management

#### 2.1 Snapshot Types
- **Hourly Business Snapshot**: Real-time inventory status per hour
- **Transfer In-Transit Snapshot**: In-transit inventory for transfers
- **Financial Daily Snapshot**: Financial perspective daily aggregation
- **In-Out Snapshot**: Inventory movement tracking

#### 2.2 Snapshot Processing Flow
```mermaid
graph LR
    A[Credential Confirmation] --> B[Command Execution]
    B --> C[Inventory Change]
    C --> D[Snapshot Task Creation]
    D --> E[Hourly Snapshot Generation]
    E --> F[Financial Snapshot Aggregation]
    F --> G[Difference Check & Fix]
```

---

## Technical Stack

### Core Technologies
- **Backend Framework**: Spring Boot 2.1.18.RELEASE
- **Programming Language**: Java 8+
- **Build Tool**: Gradle 7.2
- **Database**: MySQL (MyBatis ORM)
- **Caching**: Redis
- **Message Queue**: RabbitMQ/Kafka
- **Service Discovery**: Eureka/Nacos

### Development Tools
- **Code Quality**: SonarQube
- **Code Generation**: Lombok
- **API Documentation**: Swagger
- **Testing Framework**: JUnit 4.12
- **Monitoring**: Spring Boot Actuator

### Version Requirements
- **Spring Boot**: 2.1.18.RELEASE
- **Middleware BOM**: 1.2.9-RELEASE
- **Third-party BOM**: 1.1.9-RELEASE
- **Gradle**: 7.2 (enforced)

---

## Module Structure

### ims-client
**Purpose**: API client layer for external system integration
```
ims-client/
├── src/main/java/com/ddmc/ims/
│   ├── common/bo/                    # Business objects
│   ├── fallback/                     # Circuit breaker fallbacks
│   ├── request/                      # Request DTOs
│   ├── response/                     # Response DTOs
│   ├── CargoOwnerClient.java         # Cargo owner API client
│   ├── ImsFmsSnapshotClient.java     # FMS snapshot client
│   ├── InventoryCredentialClient.java # Credential API client
│   └── LotClient.java                # Lot management client
```

### ims-service
**Purpose**: Core business logic and service implementation
```
ims-service/
├── src/main/java/com/ddmc/ims/
│   ├── controller/                   # REST API controllers
│   ├── service/                      # Business service layer
│   ├── manager/                      # Business logic managers
│   ├── command/                      # Command pattern handlers
│   ├── converter/                    # Data converters
│   ├── job/                         # Scheduled jobs
│   └── config/                      # Configuration classes
```

### ims-common
**Purpose**: Shared utilities and common components
```
ims-common/
├── src/main/java/com/ddmc/ims/common/
│   ├── bo/                          # Business objects
│   ├── constant/                    # Constants
│   ├── enums/                       # Enumeration definitions
│   ├── errorcode/                   # Error codes
│   ├── exception/                   # Custom exceptions
│   ├── util/                        # Utility classes
│   └── view/                        # View objects
```

### ims-dal
**Purpose**: Data access layer with MyBatis mappers
```
ims-dal/
├── src/main/java/com/ddmc/ims/dal/
│   ├── mapper/                      # MyBatis mappers
│   └── model/                       # Database entities
└── src/main/resources/
    └── sqlmap/                      # SQL mapping files
```

### ims-lock-strategy
**Purpose**: Inventory locking strategy converters
```
ims-lock-strategy/
├── src/main/java/com/ddmc/ims/converter/
│   ├── CredentialUsageDetailConverter.java
│   └── LogicInventoryLocationConverter.java
```

---

## Business Logic Flow

### 1. Credential Processing Flow

```mermaid
graph TB
    A[API Request] --> B[Parameter Validation]
    B --> C[Business Rule Check]
    C --> D[Credential Creation]
    D --> E[Status: INIT]
    E --> F[Confirmation Request]
    F --> G{Credential Status Check}
    G -->|INIT| H[Execute Confirmation]
    G -->|CONFIRMED| I[Idempotent Response]
    G -->|CANCELLED| J[Throw Exception]
    H --> K[Generate Inventory Commands]
    K --> L[Apply Distributed Locks]
    L --> M[Execute Inventory Changes]
    M --> N[Update Status: CONFIRMED]
    N --> O[Record Snapshot Differences]
    O --> P[Send Event Notifications]
```

### 2. Inventory Command Processing

#### Command Types
```java
public enum CommandTypeEnum {
    MODIFY_INVENTORY_AVAILABLE,      // Modify available inventory
    TRANSFER_INVENTORY,              // Transfer between locations
    PUBLISH_PURCHASE_IN_TRANSIT,     // Publish purchase in-transit
    TRANSFER_IN_TRANSIT_TO_AVAILABLE, // Convert in-transit to available
    CLEAN_TRANSFER_IN_TRANSIT,       // Clean transfer in-transit
    // ... more command types
}
```

#### Processing Pipeline
```mermaid
graph LR
    A[Credential Input] --> B[Command Generation]
    B --> C[Command Grouping]
    C --> D[Handler Factory]
    D --> E[Specific Handler]
    E --> F[Inventory Update]
    F --> G[Snapshot Recording]
```

### 3. Snapshot Generation Process

```mermaid
graph TB
    subgraph "Real-time Processing"
        A[Credential Confirmation] --> B[Command Execution]
        B --> C[Inventory Change]
    end
    
    subgraph "Batch Processing"
        D[Hourly Job Trigger] --> E[Collect Credentials]
        E --> F[Generate Commands]
        F --> G[Create Snapshots]
    end
    
    subgraph "Financial Processing"
        H[Daily Job Trigger] --> I[Aggregate Hourly Data]
        I --> J[Generate Financial Snapshots]
    end
    
    C --> D
    G --> H
    J --> K[Data Reconciliation]
```

---

## API Interface Design

### Core REST Endpoints

#### Credential Management APIs
```http
# Credential confirmation
POST /api/inventory/confirm
Content-Type: application/json
{
  "idempotentId": "string"
}

# Credential cancellation  
POST /api/inventory/cancel
Content-Type: application/json
{
  "idempotentId": "string"
}
```

#### Inbound Operations
```http
# Inbound operation
POST /api/inventory/tryInbound
Content-Type: application/json
{
  "idempotentId": "string",
  "warehouseId": "long",
  "orderType": "string",
  "orderOperateType": "integer",
  "businessTime": "date",
  "operateDetails": [...]
}

# Publish inbound
POST /api/inventory/tryPublishInbound

# Finish inbound
POST /api/inventory/tryFinishInbound

# Cancel inbound
POST /api/inventory/tryCancelInbound
```

#### Outbound Operations
```http
# Outbound operation
POST /api/inventory/tryOutbound

# Publish outbound
POST /api/inventory/tryPublishOutbound

# Finish outbound
POST /api/inventory/tryFinishOutbound

# Cancel outbound
POST /api/inventory/tryCancelOutbound

# Transfer reject
POST /api/inventory/tryTransferReject
```

#### Adjustment Operations
```http
# Inventory adjustment
POST /api/inventory/tryAdjust
Content-Type: application/json
{
  "idempotentId": "string",
  "warehouseId": "long",
  "adjustmentDetails": [...]
}
```

### Response Format
```json
{
  "success": true,
  "code": "200",
  "message": "Success",
  "data": null,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

---

## Database Design

### Key Database Tables

#### credential_header
**Purpose**: Store inventory credential header information
```sql
CREATE TABLE credential_header (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  idempotent_id VARCHAR(64) UNIQUE NOT NULL,
  warehouse_id BIGINT NOT NULL,
  status TINYINT NOT NULL,
  order_source VARCHAR(32) NOT NULL,
  order_no VARCHAR(64) NOT NULL,
  order_type VARCHAR(32) NOT NULL,
  order_operate_type INT NOT NULL,
  business_time DATETIME NOT NULL,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### credential_detail
**Purpose**: Store credential detail items
```sql
CREATE TABLE credential_detail (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  credential_header_id BIGINT NOT NULL,
  sku_id BIGINT NOT NULL,
  lot_id VARCHAR(64),
  qty DECIMAL(20,6) NOT NULL,
  from_warehouse_id BIGINT,
  to_warehouse_id BIGINT,
  from_logic_location_code VARCHAR(32),
  to_logic_location_code VARCHAR(32)
);
```

#### warehouse_sku_inventory
**Purpose**: Store warehouse SKU inventory data
```sql
CREATE TABLE warehouse_sku_inventory (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  warehouse_id BIGINT NOT NULL,
  sku_id BIGINT NOT NULL,
  logic_location_code VARCHAR(32) NOT NULL,
  cargo_owner_id BIGINT NOT NULL,
  free_qty DECIMAL(20,6) DEFAULT 0,
  frozen_qty DECIMAL(20,6) DEFAULT 0,
  transfer_intransit_qty DECIMAL(20,6) DEFAULT 0
);
```

#### sku_inventory_snapshot_per_hour
**Purpose**: Store hourly inventory snapshots
```sql
CREATE TABLE sku_inventory_snapshot_per_hour (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  sku_id BIGINT NOT NULL,
  warehouse_id BIGINT NOT NULL,
  logic_location_code VARCHAR(32) NOT NULL,
  cargo_owner_id BIGINT NOT NULL,
  lot_id VARCHAR(64),
  free_qty DECIMAL(20,6) DEFAULT 0,
  frozen_qty DECIMAL(20,6) DEFAULT 0,
  transfer_intransit_qty DECIMAL(20,6) DEFAULT 0,
  snapshot_date_time DATETIME NOT NULL
);
```

---

## Development Guide

### Environment Setup
```bash
# Prerequisites
- JDK 8+
- Gradle 7.2
- MySQL 5.7+
- Redis 3.2+

# Clone repository
git clone http://git.ddxq.mobi/ddmc-pms/ims.ddxq.mobi.git
cd ims.ddxq.mobi

# Build project
./gradlew build

# Run tests
./gradlew test

# Start local development
./start-local.sh
```

### Code Standards
- Follow Google Java Code Style
- Use Lombok annotations for boilerplate code
- Implement proper exception handling with custom exceptions
- Write comprehensive unit tests (target coverage > 80%)
- Use meaningful variable and method names
- Add JavaDoc for public APIs

### Key Development Patterns

#### 1. Service Layer Pattern
```java
@Service
@Transactional(value = CommonConstants.IMS_DB_TRANSACTION_MANAGER, rollbackFor = Exception.class)
public class InventoryCredentialServiceImpl implements InventoryCredentialService {
    // Implementation
}
```

#### 2. Command Pattern Usage
```java
public interface InventoryCommandHandle<T extends SkuInventoryCommand> {
    void handleInventoryCommand(List<T> commands);
    List<CommandInventoryNumDto> getCommandInventoryNum(List<T> commands);
}
```

#### 3. Error Handling
```java
public class ImsBusinessException extends RuntimeException {
    private ErrorCode errorCode;
    // Implementation
}
```

### Testing Guidelines
- Unit tests for service layer logic
- Integration tests for database operations
- Mock external dependencies using Mockito
- Use TestContainers for database integration tests

---

## Deployment Guide

### Build Commands
```bash
# Build all modules
./gradlew build

# Publish to repository
./gradlew publish

# Skip tests during build
./gradlew build -x test
```

### Deployment Scripts

#### Local Development
```bash
# Start local development environment
./start-local.sh
```

#### Production Deployment
```bash
# PAAS deployment
./onepaas_start.sh
```

### Configuration Management

#### Application Properties
```properties
# Database configuration
spring.datasource.url=*******************************
spring.datasource.username=ims_user
spring.datasource.password=ims_password

# Redis configuration
spring.redis.host=localhost
spring.redis.port=6379

# Application specific
ims.lockWarehouseModeLock=true
ims.lockWarehouseInventoryLock=true
ims.enableNotifyBooking=true
```

#### Environment Variables
```bash
export IMS_DB_HOST=database-host
export IMS_REDIS_HOST=redis-host
export IMS_LOG_LEVEL=INFO
```

### Monitoring and Health Checks
- Spring Boot Actuator endpoints: `/actuator/health`
- Custom business metrics via Micrometer
- Application logs in structured JSON format
- Database connection pool monitoring

---

## Performance Considerations

### Concurrent Access Control
- Distributed locking using Redis
- Database row-level locking for inventory updates
- Rate limiting for high-frequency operations
- Asynchronous processing for heavy operations

### Performance Optimizations
- Batch processing for large datasets
- Connection pooling for database access
- Local caching for configuration data
- Lazy loading for related entities

### Scalability Features
- Horizontal scaling support through stateless design
- Database sharding by warehouse_id
- Message queue for asynchronous processing
- Load balancing for high availability

---

## Security Considerations

### Data Protection
- Sensitive data encryption at rest
- Secure communication using HTTPS
- Input validation and sanitization
- SQL injection prevention through parameterized queries

### Access Control
- Role-based access control (RBAC)
- API authentication and authorization
- Audit logging for all operations
- Session management and timeout

---

## Troubleshooting Guide

### Common Issues

#### 1. Credential Confirmation Failures
```
Error: Credential already confirmed
Solution: Check idempotent_id and credential status
```

#### 2. Inventory Lock Timeout
```
Error: Unable to acquire inventory lock
Solution: Check concurrent operations and retry mechanism
```

#### 3. Snapshot Generation Delays
```
Error: Snapshot task timeout
Solution: Check database performance and job configuration
```

### Monitoring Dashboards
- Application performance monitoring (APM)
- Database query performance
- Business metrics dashboard
- Error rate and success rate tracking

---

## Appendices

### A. API Reference
Complete API documentation available via Swagger UI: `/swagger-ui.html`

### B. Error Codes
Reference to `ErrorCode` enums in `ims-common` module

### C. Business Rules
Detailed business rules documentation in separate business requirement documents

### D. Integration Guides
Step-by-step guides for integrating with external systems

---

*Document Version: 1.0*  
*Last Updated: 2024-01-01*  
*Maintained by: IMS Development Team*