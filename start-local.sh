#!/bin/bash

# You can give a custom port for local start!
export SERVER_PORT=8080

export APOLLO_META=http://config-meta.test.corp.100.me
export CLUSTER=TE

java -Dcom.ddmc.middleware.foundation.FoundationInfoProvider=com.ddmc.middleware.foundation.legacy.LegacyFoundationInfoProvider \
    -Dlog.pattern.json.enable=false \
    -Dspring.cloud.ddmc.service-registry.auto-registration.enabled=false \
    -Dapollo.meta=$APOLLO_META \
    -Dapollo.cluster=$CLUSTER \
    -Dserver.port=$SERVER_PORT \
    -jar scm-ims-service/build/libs/scm-ims-service-*.jar > svc.log 2>&1 &