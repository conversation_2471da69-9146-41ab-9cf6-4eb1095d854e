package com.ddmc.ims;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.request.credential.oc.AdjustCredentialRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import com.ddmc.ims.request.credential.oc.ManufactureCredentialRequest;
import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 调用失败 FallbackFactory
 */
@Component
@Slf4j
public class ImsInventoryCredentialClientFallbackFactory implements FallbackFactory<InventoryCredentialClient> {

    @Override
    public InventoryCredentialClient create(Throwable cause) {
        log.error("CredentialClient req error", cause);
        return new InventoryCredentialClient() {
            @Override
            public ResponseBaseVo<Void> tryPublishOutbound(OutboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryPublishOutbound 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryTransferReject(OutboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryTransferReject 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryModifyOutboundPlanQty(OutboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryModifyOutboundPlanQty 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryCancelOutbound(OutboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryCancelOutbound 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryOutbound(OutboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryOutbound 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryFinishOutbound(OutboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryFinishOutbound 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryPublishInbound(InboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryPublishInbound 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryInbound(InboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryInbound 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryModifyExpectArriveTime(InboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryModifyExpectArriveTime 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryCancelInbound(InboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryCancelInbound 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryFinishInbound(InboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryFinishInbound 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> confirm(CredentialTryConfirmRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.confirm 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> cancel(CredentialTryConfirmRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.cancel 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryAdjust(AdjustCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryAdjust 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryManufacture(ManufactureCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryManufacture 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryModifyExpectOutTime(OutboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.tryModifyExpectOutTime 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> tryCleanPlanIn(InboundCredentialRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("CredentialClient.cleanPlanIn 请求异常：%s", cause), null);
            }
        };


    }
}
