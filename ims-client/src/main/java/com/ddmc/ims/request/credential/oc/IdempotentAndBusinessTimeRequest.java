package com.ddmc.ims.request.credential.oc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class IdempotentAndBusinessTimeRequest {

    @ApiModelProperty("幂等主键")
    @NotNull(message = "幂等主键id不能为空")
    private String idempotentId;

    @ApiModelProperty("需求日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "需求日期不能为空")
    private Date businessTime;

}
