package com.ddmc.ims.request.credential.oc;

import com.ddmc.ims.common.bo.LogicInventoryLocation;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CredentialDetailRequest {

    @ApiModelProperty("来源逻辑库位")
    @NotNull(message = "来源逻辑库位不能为空")
    private LogicInventoryLocation fromLocation;

    @ApiModelProperty("来源库存状态:1-可用，2-不可用")
    private Integer fromInventoryStatus;

    @ApiModelProperty("目标逻辑库位")
    @NotNull(message = "目标逻辑库位不能为空")
    private LogicInventoryLocation toLocation;

    @ApiModelProperty("目标库存状态:1-可用，2-不可用")
    private Integer toInventoryStatus;

    @ApiModelProperty("货品id")
    @NotNull(message = "货品id不能为空")
    private Long skuId;

    @ApiModelProperty("批次id")
    private String lotId;

    @ApiModelProperty("货品数量")
    @NotNull(message = "货品数量不能为空")
    private BigDecimal qty;

    @ApiModelProperty("需求日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date demandDate;

    @ApiModelProperty("单据标记：预售-presale,非预售-none")
    private String orderTag;

    @ApiModelProperty("用途")
    private List<String> usages;

    @ApiModelProperty("目标用途")
    private List<String> toUsages;

    @ApiModelProperty("命令类型")
    private String commandType;

    @ApiModelProperty("是否今日上架")
    private Boolean todaySale;


}
