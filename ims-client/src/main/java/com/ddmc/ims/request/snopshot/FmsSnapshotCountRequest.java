package com.ddmc.ims.request.snopshot;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "财务快照数量请求")
public class FmsSnapshotCountRequest {

    @ApiModelProperty(value = "快照日期", required = true)
    @NotNull(message = "快照日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date snapshotDate;

    @ApiModelProperty(value = "仓库id", required = true)
    @NotNull(message = "仓库id不能为空")
    private Long warehouseId;


}
