package com.ddmc.ims.request.snopshot;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "财务快照请求")
public class FmsSnapshotQueryRequest {

    @ApiModelProperty(value = "快照日期", required = true)
    @NotNull(message = "快照日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date snapshotDate;

    @ApiModelProperty(value = "仓库id", required = true)
    @NotNull(message = "仓库id不能为空")
    private Long warehouseId;


    @ApiModelProperty(value = "最小skuId", required = true)
    @NotNull(message = "最小skuId不能为空")
    private Long skuId;

    @ApiModelProperty(value = "分批数量", required = true)
    @NotNull(message = "分批数量不能为空")
    @Min(value = 0, message = "分批数量不能小于0")
    @Max(value = 1000, message = "分批数量不能大于1000")
    private Integer limit = 100;




}
