package com.ddmc.ims;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.response.CargoOwnerResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
@FeignClient(name = "scm-ims-service", contextId = "ocsCargoOwnerClient", fallbackFactory = LotClientFallbackFactory.class)
public interface CargoOwnerClient {

    @PostMapping("/api/cargoOwner/queryCargoOwnerList")
    @ApiOperation("获取所有的货主")
    ResponseBaseVo<List<CargoOwnerResponse>> queryCargoOwnerList();
}
