package com.ddmc.ims;

import com.ddmc.core.view.compat.ResponseBaseVo;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * pes 效期调用失败 FallbackFactory
 */
@Component
public class LotClientFallbackFactory implements FallbackFactory<LotClient> {

    @Override
    public LotClient create(Throwable cause) {
        return request -> ResponseBaseVo
            .fail(-1, String.format("请求ims效期服务-更新货品临期日期 异常: %s", cause), null);
    }
}
