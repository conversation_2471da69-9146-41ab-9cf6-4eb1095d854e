package com.ddmc.ims.common.bo;

import java.util.Objects;
import lombok.Getter;
import lombok.Setter;

/**
 * 逻辑库位,不可变类，请勿添加set方法
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class LogicInventoryLocation {

    private Long warehouseId;

    private Long cargoOwnerId;

    private String logicInventoryLocationCode;

    public LogicInventoryLocation() {

    }

    public LogicInventoryLocation(Long warehouseId, Long cargoOwnerId, String logicInventoryLocationCode) {
        this.warehouseId = warehouseId;
        this.cargoOwnerId = cargoOwnerId;
        this.logicInventoryLocationCode = logicInventoryLocationCode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogicInventoryLocation that = (LogicInventoryLocation) o;
        return Objects.equals(warehouseId, that.warehouseId) && Objects.equals(cargoOwnerId, that.cargoOwnerId)
            && Objects.equals(logicInventoryLocationCode, that.logicInventoryLocationCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(warehouseId, cargoOwnerId, logicInventoryLocationCode);
    }
}
