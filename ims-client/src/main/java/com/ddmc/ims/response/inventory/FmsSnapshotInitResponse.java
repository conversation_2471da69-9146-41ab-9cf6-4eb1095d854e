package com.ddmc.ims.response.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "财务快照初始化响应")
public class FmsSnapshotInitResponse {

    @ApiModelProperty(value = "快照日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date snapshotDate;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "仓库主体")
    private String corporationCode;


    @ApiModelProperty(value = "货品id")
    private Long skuId;

    @ApiModelProperty(value = "货品名称")
    private String skuName;

    @ApiModelProperty(value = "采购单位")
    private String procurementUnit;


    @ApiModelProperty(value = "快照日库存总量")
    private BigDecimal qty;




}
