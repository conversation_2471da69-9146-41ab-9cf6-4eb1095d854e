package com.ddmc.ims.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class CargoOwnerResponse {

    @ApiModelProperty(value = "货主id")
    private Long cargoOwnerId;

    @ApiModelProperty(value = "货主编码")
    private String cargoOwnerCode;

    @ApiModelProperty(value = "货主名称")
    private String cargoOwnerName;

}
