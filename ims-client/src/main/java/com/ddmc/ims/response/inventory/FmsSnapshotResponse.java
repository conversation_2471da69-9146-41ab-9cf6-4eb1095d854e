package com.ddmc.ims.response.inventory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "财务快照响应")
public class FmsSnapshotResponse {

    @ApiModelProperty(value = "快照日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date snapshotDate;

    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;


    @ApiModelProperty(value = "仓库id")
    private Long skuId;

    @ApiModelProperty(value = "快照日库存总量")
    private BigDecimal qty;

    @ApiModelProperty(value = "当日入库数量")
    private BigDecimal inQty;


    @ApiModelProperty(value = "当日出库数量")
    private BigDecimal outQty;


    @ApiModelProperty(value = "加工中数量")
    private BigDecimal processingQty;

    @ApiModelProperty(value = "在途数量")
    private BigDecimal transferIntransitQty;

    @ApiModelProperty(value = "采购部分入库数量")
    private BigDecimal purchasePartInQty;

    @ApiModelProperty(value = "调拨部分入数量")
    private BigDecimal transferPartInQty;

    @ApiModelProperty(value = "反加工未完工提前入库数量")
    private BigDecimal reverseProcessingQty;

    @ApiModelProperty(value = "其他差异数量")
    private BigDecimal otherDiffQty;


}
