package com.ddmc.ims;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.request.UpdateUnsalableDateRequest;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "scm-ims-service", contextId = "imsLotClient", fallbackFactory = LotClientFallbackFactory.class)
public interface LotClient {

    @PostMapping("/api/lot/updateUnsalableDate")
    @ApiOperation("更新临期日期")
    ResponseBaseVo<Void> updateUnsalableDate(@RequestBody @Validated UpdateUnsalableDateRequest request);
}
