package com.ddmc.ims;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.request.credential.oc.AdjustCredentialRequest;
import com.ddmc.ims.request.credential.oc.CredentialTryConfirmRequest;
import com.ddmc.ims.request.credential.oc.InboundCredentialRequest;
import com.ddmc.ims.request.credential.oc.ManufactureCredentialRequest;
import com.ddmc.ims.request.credential.oc.OutboundCredentialRequest;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "scm-ims-service", contextId = "inventoryCredentialClient", fallbackFactory = ImsInventoryCredentialClientFallbackFactory.class)
public interface InventoryCredentialClient {

    @PostMapping("/api/inventory/tryPublishOutbound")
    @ApiOperation("发布出库申请")
    ResponseBaseVo<Void> tryPublishOutbound(@RequestBody @Validated OutboundCredentialRequest request);

    @PostMapping("/api/inventory/tryTransferReject")
    @ApiOperation("调拨拒收")
    ResponseBaseVo<Void> tryTransferReject(@RequestBody @Validated OutboundCredentialRequest request);

    @PostMapping("/api/inventory/tryModifyOutboundPlanQty")
    @ApiOperation("修改出库计划量")
    ResponseBaseVo<Void> tryModifyOutboundPlanQty(@RequestBody @Validated OutboundCredentialRequest request);

    @PostMapping("/api/inventory/tryCancelOutbound")
    @ApiOperation("取消出库")
    ResponseBaseVo<Void> tryCancelOutbound(@RequestBody @Validated OutboundCredentialRequest request);

    @PostMapping("/api/inventory/tryOutbound")
    @ApiOperation("出库操作")
    ResponseBaseVo<Void> tryOutbound(@RequestBody @Validated OutboundCredentialRequest request);

    @PostMapping("/api/inventory/tryFinishOutbound")
    @ApiOperation("完成出库")
    ResponseBaseVo<Void> tryFinishOutbound(@RequestBody @Validated OutboundCredentialRequest request);

    @PostMapping("/api/inventory/tryPublishInbound")
    @ApiOperation("发布入库单")
    ResponseBaseVo<Void> tryPublishInbound(@RequestBody @Validated InboundCredentialRequest request);

    @PostMapping("/api/inventory/tryInbound")
    @ApiOperation("入库操作")
    ResponseBaseVo<Void> tryInbound(@RequestBody @Validated InboundCredentialRequest request);

    @PostMapping("/api/inventory/tryModifyExpectArriveTime")
    @ApiOperation("修改期望到货时间")
    ResponseBaseVo<Void> tryModifyExpectArriveTime(@RequestBody @Validated InboundCredentialRequest request);

    @PostMapping("/api/inventory/tryCancelInbound")
    @ApiOperation("取消入库")
    ResponseBaseVo<Void> tryCancelInbound(@RequestBody @Validated InboundCredentialRequest request);

    @PostMapping("/api/inventory/tryFinishInbound")
    @ApiOperation("完成入库操作")
    ResponseBaseVo<Void> tryFinishInbound(@RequestBody @Validated InboundCredentialRequest request);

    @PostMapping("/api/inventory/confirm")
    @ApiOperation("凭证确认")
    ResponseBaseVo<Void> confirm(@RequestBody @Validated CredentialTryConfirmRequest request);


    @PostMapping("/api/inventory/cancel")
    @ApiOperation("凭证取消")
    ResponseBaseVo<Void> cancel(@RequestBody @Validated CredentialTryConfirmRequest request);


    @PostMapping("/api/inventory/tryAdjust")
    @ApiOperation("调整")
    ResponseBaseVo<Void> tryAdjust(@RequestBody @Validated AdjustCredentialRequest request);

    @PostMapping("/api/inventory/tryManufacture")
    @ApiOperation("制造单")
    ResponseBaseVo<Void> tryManufacture(@RequestBody @Validated ManufactureCredentialRequest request);

    @PostMapping("/api/inventory/tryModifyExpectOutTime")
    @ApiOperation("修改期望出库日期")
    ResponseBaseVo<Void> tryModifyExpectOutTime(@RequestBody @Validated OutboundCredentialRequest request);


    @PostMapping("/api/inventory/tryCleanPlanIn")
    @ApiOperation("清理采购在途")
    ResponseBaseVo<Void> tryCleanPlanIn(@RequestBody @Validated InboundCredentialRequest request);


}
