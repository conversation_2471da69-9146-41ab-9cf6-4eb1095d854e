package com.ddmc.ims.fallback;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.ImsFmsSnapshotClient;
import com.ddmc.ims.request.snopshot.DayEndTimeNoticeRequest;
import com.ddmc.ims.request.snopshot.FmsSnapshotCountRequest;
import com.ddmc.ims.request.snopshot.FmsSnapshotQueryRequest;
import com.ddmc.ims.response.inventory.FmsSnapshotInitResponse;
import com.ddmc.ims.response.inventory.FmsSnapshotResponse;
import feign.hystrix.FallbackFactory;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 调用失败 FallbackFactory
 */
@Component
@Slf4j
public class ImsFmsSnapshotClientFallbackFactory implements FallbackFactory<ImsFmsSnapshotClient> {

    @Override
    public ImsFmsSnapshotClient create(Throwable cause) {
        log.error("ImsFmsSnapshotClient req error", cause);
        return new ImsFmsSnapshotClient() {
            @Override
            public ResponseBaseVo<List<FmsSnapshotResponse>> queryByWarehouseAndSnapshotDate(
                FmsSnapshotQueryRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("ImsFmsSnapshotClient.queryByWarehouseAndSnapshotDate 请求异常：%s", cause), null);
            }


            @Override
            public ResponseBaseVo<Void> dayEndTimeNotice(DayEndTimeNoticeRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("ImsFmsSnapshotClient.dayEndTimeNotice 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<List<FmsSnapshotInitResponse>> queryInitByWarehouseAndSnapshotDate(
                FmsSnapshotQueryRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("ImsFmsSnapshotClient.queryInitByWarehouseAndSnapshotDate 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Integer> countByWarehouseAndSnapshotDate(
                FmsSnapshotCountRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("ImsFmsSnapshotClient.countByWarehouseAndSnapshotDate 请求异常：%s", cause), null);
            }

            @Override
            public ResponseBaseVo<Void> compareResult(DayEndTimeNoticeRequest request) {
                return ResponseBaseVo
                    .fail(-1,  String.format("ImsFmsSnapshotClient.compareResult 请求异常：%s", cause), null);
            }


        };


    }
}
