package com.ddmc.ims;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.ims.fallback.ImsFmsSnapshotClientFallbackFactory;
import com.ddmc.ims.request.snopshot.DayEndTimeNoticeRequest;
import com.ddmc.ims.request.snopshot.FmsSnapshotCountRequest;
import com.ddmc.ims.request.snopshot.FmsSnapshotQueryRequest;
import com.ddmc.ims.response.inventory.FmsSnapshotInitResponse;
import com.ddmc.ims.response.inventory.FmsSnapshotResponse;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "scm-ims-service", contextId = "imsFmsSnapshotClient", fallbackFactory = ImsFmsSnapshotClientFallbackFactory.class)
public interface ImsFmsSnapshotClient {


    @PostMapping("/api/fmsSnapshot/queryByWarehouseAndSnapshotDate")
    @ApiOperation("查询财务库存快照")
    ResponseBaseVo<List<FmsSnapshotResponse>> queryByWarehouseAndSnapshotDate(@RequestBody @Validated FmsSnapshotQueryRequest request);


    @PostMapping("/api/fmsSnapshot/dayEndTimeNotice")
    @ApiOperation("日结通知")
    ResponseBaseVo<Void> dayEndTimeNotice(@RequestBody @Validated DayEndTimeNoticeRequest request);

    @PostMapping("/api/fmsSnapshot/queryInitByWarehouseAndSnapshotDate")
    @ApiOperation("查询财务库存初始快照")
    ResponseBaseVo<List<FmsSnapshotInitResponse>> queryInitByWarehouseAndSnapshotDate(@RequestBody @Validated FmsSnapshotQueryRequest request);



    @PostMapping("/api/fmsSnapshot/countByWarehouseAndSnapshotDate")
    @ApiOperation("查询财务快照数量")
    ResponseBaseVo<Integer> countByWarehouseAndSnapshotDate(@RequestBody @Validated FmsSnapshotCountRequest request);



    @PostMapping("/api/fmsSnapshot/compareResult")
    @ApiOperation("财务对账结果")
    ResponseBaseVo<Void> compareResult(@RequestBody @Validated DayEndTimeNoticeRequest request);

}
