version = '1.0.9-SNAPSHOT'
jar.enabled = true
bootJar.enabled = false
java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

dependencies {

    api('org.springframework.cloud:spring-cloud-starter-openfeign')
    api('com.ddmc:core:1.1.1-SNAPSHOT'){
        exclude group: 'com.alibaba', module: 'druid'
    }
    compileOnly('io.swagger:swagger-annotations:1.5.9')
}
